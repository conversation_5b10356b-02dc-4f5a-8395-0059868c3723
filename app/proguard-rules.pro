# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
-dontwarn android.app.IActivityManager
-dontwarn android.app.ActivityManager$TaskSnapshot
-dontwarn android.app.ActivityTaskManager
-dontwarn android.app.ActivityThread
-dontwarn android.app.IActivityTaskManager$Stub
-dontwarn android.app.IActivityTaskManager
-dontwarn android.graphics.GraphicBuffer
-dontwarn android.util.Singleton
-dontwarn android.util.Slog
-dontwarn android.view.IWindowManager$Stub
-dontwarn android.view.IWindowManager
-dontwarn android.view.RemoteAnimationDefinition
-dontwarn com.android.internal.statusbar.IStatusBarService$Stub
-dontwarn com.android.internal.statusbar.IStatusBarService
-dontwarn android.os.ServiceManager$ServiceNotFoundException
-dontwarn android.os.ServiceManager
-dontwarn android.os.SystemProperties
-dontwarn android.annotation.SystemApi
-dontwarn android.compat.annotation.UnsupportedAppUsage
-dontwarn com.alipay.sdk.app.H5PayCallback
-dontwarn com.alipay.sdk.app.PayTask
-dontwarn com.download.library.DownloadImpl
-dontwarn com.download.library.DownloadListenerAdapter
-dontwarn com.download.library.DownloadTask
-dontwarn com.download.library.ResourceRequest
-dontwarn com.gwm.gdc.widget.scrollable.VrNestedScrollView
-dontwarn com.gwm.gdc.widget.scrollable.VrScrollView
-dontwarn com.gwm.gdc.widget.scrollable.adaptable.VrRecyclerView
-dontwarn com.gwm.gdc.widget.scrollable.GwmNestedScrollView
-dontwarn com.gwm.gdc.widget.scrollable.adaptable.VrListView
-dontwarn com.gwm.gdc.widget.viewpager.VrViewPager
-dontwarn com.gwm.server.privacy.IGwmPrivacyActiveAppChangeListener$Stub
-dontwarn com.gwm.server.privacy.IGwmPrivacyActiveAppChangeListener
-dontwarn com.gwm.server.privacy.IGwmPrivacyPkgStateChangeListener$Stub
-dontwarn com.gwm.server.privacy.IGwmPrivacyPkgStateChangeListener
-dontwarn com.gwm.server.privacy.IGwmPrivacyService$Stub
-dontwarn com.gwm.server.privacy.IGwmPrivacyService
-dontwarn com.gwm.server.privacy.IGwmPrivacyStateChangeListener$Stub
-dontwarn com.gwm.server.privacy.IGwmPrivacyStateChangeListener
-dontwarn org.bouncycastle.jsse.BCSSLParameters
-dontwarn org.bouncycastle.jsse.BCSSLSocket
-dontwarn org.bouncycastle.jsse.provider.BouncyCastleJsseProvider
-dontwarn org.conscrypt.Conscrypt$Version
-dontwarn org.conscrypt.Conscrypt
-dontwarn org.conscrypt.ConscryptHostnameVerifier
-dontwarn org.openjsse.javax.net.ssl.SSLParameters
-dontwarn org.openjsse.javax.net.ssl.SSLSocket
-dontwarn org.openjsse.net.ssl.OpenJSSE
-dontwarn org.slf4j.impl.StaticLoggerBinder
-dontwarn org.slf4j.impl.StaticMDCBinder
-dontwarn org.slf4j.impl.StaticMarkerBinder
-dontwarn com.google.devtools.build.android.desugar.runtime.ThrowableExtension

-keep class com.cincc.** {*;}
-keep class org.** {*;}
-keepclasseswithmembers,includedescriptorclasses class com.tencent.mmkv.** {
    native <methods>;
    long nativeHandle;
    private static *** onMMKVCRCCheckFail(***);
    private static *** onMMKVFileLengthError(***);
    private static *** mmkvLogImp(...);
    private static *** onContentChangedByOuterProcess(***);
}

-keepattributes Signature
-keepattributes *Annotation*
-keepattributes Exceptions

-keep class com.google.gson.** { *; }
-keep class io.reactivex.** { *; }

-dontwarn okhttp3.**
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }


# Retrofit
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-keep interface retrofit2.** { *; }
# 保留所有实现 retrofit2.CallAdapter 和 retrofit2.Converter 的类
-keep class * implements retrofit2.CallAdapter { *; }
-keep class * implements retrofit2.Converter { *; }
# Keep generic signature of Call, Response (R8 full mode strips signatures from non-kept items).
 -keep,allowobfuscation,allowshrinking interface retrofit2.Call
 -keep,allowobfuscation,allowshrinking class retrofit2.Response

 # With R8 full mode generic signatures are stripped for classes that are not
 # kept. Suspend functions are wrapped in continuations where the type argument
 # is used.
 -keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

-keep class io.agora.**{*;}

-keep class com.healthlink.hms.** { *; }
-keep class com.gwm.**.**.**.** { *; }
-keep class android.app.** { *; }

# agentWeb
-keep class com.just.agentweb.** {
    *;
}
-dontwarn com.just.agentweb.**
-dontwarn javax.annotation.**
-dontwarn javax.inject.**
-dontwarn javax.lang.**
-dontwarn javax.tools.**

-keep class androidx.startup.** { *; }
-dontwarn androidx.startup.**

# 保留MultiDex相关类
-keep class androidx.multidex.MultiDex
-keep class androidx.multidex.MultiDexApplication

# 保留所有包含泛型信息的类
-keepclassmembers class ** {
    *;
}

###自定义控件onApplySkin()方法不混淆
#-keep public class * extends android.view.View {
#    public void onApplySkin();
#    public void dispatchApplySkin();
#}
#
## 不混淆VR控制相关方法
#-keepclasseswithmembers class * {
#    *** onVrOperation(android.view.View,java.lang.String,android.os.Bundle);
#}
#
#-keepclasseswithmembers class * {
#    boolean onVrOperation(android.view.View,java.lang.String);
#}
#
#-keepclasseswithmembers class * {
#    *** *Operation*(***);
#}
#
#-keepclasseswithmembers class * {
#    *** *Operation();
#}


#-keep class android.widget.** { *; }
#-keep class android.view.View { *; }
#-keepclassmembers class android.widget.ToastUtils { *; }
#

#-dontobfuscate
