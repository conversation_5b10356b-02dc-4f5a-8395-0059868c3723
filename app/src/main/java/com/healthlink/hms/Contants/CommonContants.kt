package com.healthlink.hms.Contants

/**
 * Created by imaginedays on 2024/6/3
 *  公共常量
 */



val timeName = arrayOf("日", "周", "月", "年")
enum class TimeCode(val timeCode: String) {
    TIME_CODE_DAY("day"),
    TIME_CODE_WEEK("week"),
    TIME_CODE_MONTH("month"),
    TIME_CODE_YEAR("year")
}

enum class VehicleServiceModeType(val modeName: String) {
    // 温暖模式 、护腰模式 、小憩模式 放松模式
    WARN("加热模式"),
    COOL("清爽模式"),
    REST("休憩模式"),
    RELAX("舒缓模式"),
    MED_AROMATHERAPY("中医香薰"),
    AI_CHAIR("智脊管家")
}

/**
 * 车机功能对应的Code
 */
enum class VehicleServiceModeCode(val modeCode: String) {
    FN_SEAT_VENTILATION("fn_seat_ventilation"), // 座椅通风
    FN_FRAGRANCE("fn_fragrance"), // 香氛
    FN_SEAT_MASSAGE("fn_massage"), // 座椅按摩
    FN_WHEEL_HEAT("fn_wheel_hoting"), // 方向盘加热
    FN_SEAT_HEAT("fn_seat_hoting"), // 座椅加热
    FN_REST("fn_rest_mode"), // 小憩模式
}