package com.healthlink.hms.sdks.map.gwm

import android.content.Context
import com.blankj.utilcode.util.LogUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.gwm.map.sdk.MapCallback
import com.gwm.map.sdk.MapServiceProxy
import com.gwm.map.sdk.entity.ProtocolEntity
import com.healthlink.hms.sdks.map.gwm.baseDTO.GWMMapBaseSearchParams
import com.healthlink.hms.sdks.map.gwm.baseDTO.GWMMapRespArrBaseDTO
import com.healthlink.hms.sdks.map.gwm.baseDTO.GWMMapRespBaseDTO
import com.healthlink.hms.utils.MMKVUtil

/**
 * Created by imaginedays on 2024/7/18
 *
 *
 */
object GWMMapManager {
    enum class MapProtocolRespStatusCode(val code: Int) {
        SUCCESS(20000),
        // 检索成功，但没有检索结果
        SEARCH_NO_RESULT(32003),
        // 当前状态不支持该操作
        STATUS_NOT_SUPPORT(10008),
        // 公司地址未设置
        COMPANY_ADDRESS_NOT_SET(36002),
        // 服务不可用
        SERVICE_UNAVAILABLE(10011),
        // 服务异常
    }
    const val mTag = "GWMMapManager"
    // GWM 的地图服务连接是否已经建立
    private var isMapServiceConnected = false
    /**
     *  导航原子能力是否可用
     *  地图初始化完成、地图激活成功、用户已同意地图应用的隐私政策条款、地图具有定位权限，同时满足这4个条件时，将“原子能力 服务正常”
     */
    private var isMapAtomicCapability = false
    // 透出原子能力服务状态(19012)
    const val MAP_PROTOCOL_ID_ATOMIC_CAPABILITY = "19012"
    // 查询原子能力服务状态(19021)
    const val MAP_PROTOCOL_ID_QUERY_ATOMIC_CAPABILITY = "19021"
    // 关键字搜索
    const val MAP_PROTOCOL_ID_KEYWORD_SEARCH = "10031" // 10031 沿途搜索 11011 关键字搜索
    const val MAP_PROTOCOL_ID_LOCATION = "14041" // 查询自车或者指定位置坐标的方法ID
    const val MAP_PROTOCOL_ID_HOME_OR_COMPANY_LOCATION = "15021" // 查询家或者公司的方法ID
    const val MAP_PROTOCOL_ID_NAVIGATION = "11032" // 透出导航动作
    const val MAP_PROTOCOL_ID_NAVIGATION_GUIDE = "11041" // 查询引导状态(11041)
    const val MAP_PROTOCOL_ID_ESITMATE_TO_ARRIVAL = "11121" // 导航剩余时间和距离
    const val MAP_PROTOCOL_ID_ROAD_LEVEL = "11211" // 查询当前道路等级(11211)
    const val MAP_PROTOCOL_ID_ALL_SAPA = "11291" // 查询路径上剩余全部服务区&收费站(11291)
    const val MAP_PROTOCOL_ID_RETURN_TO = "14011" // 返回到自车位置
    const val MAP_PROTOCOL_ID_SEARCH_SAPA = "10031" // 沿途搜索(10031)
    const val MAP_PROTOCOL_ID_DESTINATION_INFO = "12071" // 查询目的地信息(12071)
    const val MAP_VERSION = "2.0.4" // 地图版本号
    var MAP_PACKAGE_NAME = "com.gwm.map" // 地图包名
    // 车辆位置距离公司是否小于500M
    var isLessThan500M: Boolean = false
    // 导航目的地剩余时间是否大于一小时
    var isMoreThanAnHourToArraival: Boolean = false
    // 道路类型是否是高速公路
    var isHighwayLevel: Boolean = false
    // 连续驾驶时长超过2小时
    var isDrivingDuration2: Boolean = false


    public fun initGWMMapManager(context: Context) {
        MAP_PACKAGE_NAME = context.packageName
        initMapService(context)
    }

    /**
     * 地图服务
     * @param
     */
    private fun initMapService(context: Context?) {
        if (context == null) {
            return
        }
        MapServiceProxy.getInstance().init(context) { connected ->
            isMapServiceConnected = connected
            var protocolIds =  arrayListOf(
                MAP_PROTOCOL_ID_KEYWORD_SEARCH,
                MAP_PROTOCOL_ID_ATOMIC_CAPABILITY,
                MAP_PROTOCOL_ID_QUERY_ATOMIC_CAPABILITY,
                MAP_PROTOCOL_ID_NAVIGATION,
                MAP_PROTOCOL_ID_NAVIGATION_GUIDE,
                MAP_PROTOCOL_ID_LOCATION,
                MAP_PROTOCOL_ID_HOME_OR_COMPANY_LOCATION,
                MAP_PROTOCOL_ID_ESITMATE_TO_ARRIVAL,
                MAP_PROTOCOL_ID_DESTINATION_INFO
                )
            // 设置回调
            MapServiceProxy.getInstance().subscribe(
                MAP_PACKAGE_NAME, protocolIds.toTypedArray()
            )
            MapServiceProxy.getInstance().registerCallBack(MAP_PACKAGE_NAME, mapServiceCallback)
//            sendCurrentLocationAsyncReq()
//            sendQueryCompanyAsyncReq()
//            sendQueryEsitmateToArrivalAsyncReq()
//            sendQueryRoadLevelAsyncReq()
            sendQuerySAPAAsyncReq()
        }
    }

    fun isMapServiceConnected(): Boolean {
        return isMapServiceConnected
    }

    /**
     * 地图异步方法回调
     * MAP_PROTOCOL_ID_SEARCH_LOCATION_TARGET 指定位置的经纬度
     * MAP_PROTOCOL_ID_SEARCH_HOME_OR_COMPANY_LOCATION_TARGET 家或者公司
     */
    private val mapServiceCallback = object : MapCallback.Stub() {
        override fun onResponse(p0: String?) {
            LogUtils.i("mapServiceCallback onResponse: $p0")
            if (p0.isNullOrEmpty()) {
                return
            }
            if (p0 == "null") {
                return
            }

            if (p0.contains(MAP_PROTOCOL_ID_ATOMIC_CAPABILITY)) {
                val type = object : TypeToken<GWMMapRespBaseDTO<GWMMapATOMICDTO>>() {}.type
                val result: GWMMapRespBaseDTO<GWMMapATOMICDTO> = Gson().fromJson(p0, type)
                if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
                    result.data?.let {
                        isMapAtomicCapability = it.state == 1
                    }
                }
                return
            } else if (p0.contains(MAP_PROTOCOL_ID_QUERY_ATOMIC_CAPABILITY)) {
                val type = object : TypeToken<GWMMapRespBaseDTO<GWMMapATOMICDTO>>() {}.type
                val result: GWMMapRespBaseDTO<GWMMapATOMICDTO> = Gson().fromJson(p0, type)
                if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
                    result.data?.let {
                        isMapAtomicCapability = it.state == 1
                    }
                }
                return

            } else if (p0.contains(MAP_PROTOCOL_ID_LOCATION)) {
                val type = object : TypeToken<GWMMapRespBaseDTO<GWMMapLocDTO>>() {}.type
                val result: GWMMapRespBaseDTO<GWMMapLocDTO> = Gson().fromJson(p0, type)
                if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
                    result.data?.let {
                        if (it.lon != 0.0 && it.lat != 0.0) {
                            var mLongitude = it.lon
                            var mLatitude = it.lat
                            MMKVUtil.storeLon(it.lon)
                            MMKVUtil.storeLat(it.lat)
                        }
                    }
                }
            } else if (p0.contains(MAP_PROTOCOL_ID_HOME_OR_COMPANY_LOCATION)) {
                // 公司地址
                val type = object : TypeToken<GWMMapRespArrBaseDTO<PoiDTO>>() {}.type
                val result: GWMMapRespArrBaseDTO<PoiDTO> = Gson().fromJson(p0, type)
                if (result.statusCode == MapProtocolRespStatusCode.COMPANY_ADDRESS_NOT_SET.code) {
                    // 公司地址未设置
                    return
                }
                if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
                    result.data?.let {
                        if (it.isNotEmpty()) {
                            it[0].let {
                                if (it.distance < 500) {
                                    isLessThan500M = true
                                }
                            }
                        }
                    }
                }

            } else if (p0.contains(MAP_PROTOCOL_ID_ESITMATE_TO_ARRIVAL)) {
                // 需要在导航模式下才有数据 导航剩余时间是否大于1小时， 单位是秒
                var type = object : TypeToken<GWMMapRespBaseDTO<GWMMapArrivalDTO>>(){}.type
                var result: GWMMapRespBaseDTO<GWMMapArrivalDTO> = Gson().fromJson(p0, type)
                if (result.statusCode == MapProtocolRespStatusCode.STATUS_NOT_SUPPORT.code) {
                    // 当前状态不支持该操作
                    return
                }

                if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
                    result.data?.let {
                        if (it.esitmateToArrival > 3600) {
                            // 导航
                            isMoreThanAnHourToArraival = true
                        }
                    }
                }
            } else if (p0.contains(MAP_PROTOCOL_ID_ROAD_LEVEL)) {
                var type = object : TypeToken<GWMMapRespBaseDTO<GWMMapRoadInfoDTO>>() {}.type
                var result: GWMMapRespBaseDTO<GWMMapRoadInfoDTO> = Gson().fromJson(p0, type)
                if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
                    result.data?.let {
                        if (it.roadLevel == RoadLevel.HIGHWAY.roadLevel) {
                            isHighwayLevel = true
                        }
                    }
                }
            } else if (p0.contains(MAP_PROTOCOL_ID_ALL_SAPA)) {
                var type = object : TypeToken<GWMMapRespBaseDTO<GWMMapAllSapaDTO>>() {}.type
                var result: GWMMapRespBaseDTO<GWMMapAllSapaDTO> = Gson().fromJson(p0, type)
                if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
                }
            } else if (p0.contains(MAP_PROTOCOL_ID_RETURN_TO)) {
//                LogUtils.i("MAP_PROTOCOL_ID_RETURN_TO: $p0")
            } else if (p0.contains(MAP_PROTOCOL_ID_SEARCH_SAPA)) {
//                LogUtils.i("MAP_PROTOCOL_ID_SEARCH_SAPA: $p0")
            } else if (p0.contains(MAP_PROTOCOL_ID_NAVIGATION_GUIDE)) {
//                LogUtils.i("MAP_PROTOCOL_ID_NAVIGATION_GUIDE: $p0")
            } else if (p0.contains(MAP_PROTOCOL_ID_KEYWORD_SEARCH)) {
//                LogUtils.i("MAP_PROTOCOL_ID_KEYWORD_SEARCH: $p0")
            } else if (p0.contains(MAP_PROTOCOL_ID_DESTINATION_INFO)) {
                // 查询目的地信息
                val type = object : TypeToken<GWMMapRespBaseDTO<PoiDTO>>() {}.type
                val result: GWMMapRespBaseDTO<PoiDTO> = Gson().fromJson(p0, type)

                if (result.statusCode == MapProtocolRespStatusCode.STATUS_NOT_SUPPORT.code) {
                    // 当前状态不支持该操作
                    return
                }

                if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
                    // 目的地信息
                   LogUtils.i("MAP_PROTOCOL_ID_DESTINATION_INFO: lon =  ${result.data.lon} lat = ${result.data.lat} name = ${result.data.name}")
                }

            }
        }
    }
    /**
     * 获取当前位置的经纬度
     * 向地图模块发送查询指定位置坐标的异步请求
     * 成功会给mLongitude和mLatitude赋值
     */
    fun sendCurrentLocationAsyncReq() {
        if (isMapServiceConnected && isMapAtomicCapability) {
            val searchEntity = ProtocolEntity<SearchLocInfoParams>()
            searchEntity.pkgName = MAP_PACKAGE_NAME
            searchEntity.verName = MAP_VERSION
            searchEntity.protocolId = MAP_PROTOCOL_ID_LOCATION
            searchEntity.data = SearchLocInfoParams().apply {
                type = 1
            }
            // 发送请求
            MapServiceProxy.getInstance().requestAsync(Gson().toJson(searchEntity))
        }
    }

    /**
     * 查询公司地址信息
     * 0:获取家地址
     * 1:获取公司地址
     * 2:获取家和公司地址
     * @return 公司poi信息
     */
    private fun sendQueryCompanyAsyncReq() {
        if (isMapServiceConnected && isMapAtomicCapability) {
            val searchEntity = ProtocolEntity<SearchCompanyInfoParams>()
            searchEntity.pkgName = MAP_PACKAGE_NAME
            searchEntity.verName = MAP_VERSION
            searchEntity.protocolId = MAP_PROTOCOL_ID_HOME_OR_COMPANY_LOCATION
            searchEntity.data = SearchCompanyInfoParams().apply {
                intent = 1 // 查询公司的poi
            }
            // 发送请求
            MapServiceProxy.getInstance().requestAsync(Gson().toJson(searchEntity))
        }
    }

    /**
     * 查询导航剩余时间/剩余距离 到达目的地剩余时间大于1小时
     * 必须在导航模式下才能获取
     */
    private fun sendQueryEsitmateToArrivalAsyncReq() {
        if (isMapServiceConnected && isMapAtomicCapability) {
            val searchEntity = ProtocolEntity<SearchCompanyInfoParams>()
            searchEntity.pkgName = MAP_PACKAGE_NAME
            searchEntity.verName = MAP_VERSION
            searchEntity.protocolId = MAP_PROTOCOL_ID_ESITMATE_TO_ARRIVAL
            // 发送请求
            MapServiceProxy.getInstance().requestAsync(Gson().toJson(searchEntity))
        }
    }

    /**
     * 查询道路类型
     */
    private fun sendQueryRoadLevelAsyncReq() {
        if (isMapServiceConnected && isMapAtomicCapability) {
            val searchEntity = ProtocolEntity<GWMMapBaseSearchParams>()
            searchEntity.pkgName = MAP_PACKAGE_NAME
            searchEntity.verName = MAP_VERSION
            searchEntity.protocolId = MAP_PROTOCOL_ID_ROAD_LEVEL
            // 发送请求
            MapServiceProxy.getInstance().requestAsync(Gson().toJson(searchEntity))
        }
    }

    /**
     * 查询剩余路段的SAPA
     */
    fun sendQuerySAPAAsyncReq() {
        if (isMapServiceConnected && isMapAtomicCapability) {
            val searchEntity = ProtocolEntity<SearchSapaParams>()
            searchEntity.pkgName = MAP_PACKAGE_NAME
            searchEntity.verName = MAP_VERSION
            searchEntity.protocolId = MAP_PROTOCOL_ID_ALL_SAPA
//            searchEntity.data = SearchSapaParams().apply {
//                keyword = "服务区"
//                showOnMap = 1
//                count = 10
//                sortType = 0
//            }
            // 发送请求
            MapServiceProxy.getInstance().requestAsync(Gson().toJson(searchEntity))
        }
    }

    /**
     * 查询引导状态
     */
    fun sendQueryGuideStatusAsyncReq() {
        if (isMapServiceConnected && isMapAtomicCapability) {
            val searchEntity = ProtocolEntity<GWMMapBaseSearchParams>()
            searchEntity.pkgName = MAP_PACKAGE_NAME
            searchEntity.verName = MAP_VERSION
            searchEntity.protocolId = MAP_PROTOCOL_ID_NAVIGATION_GUIDE
            // 发送请求
            MapServiceProxy.getInstance().requestAsync(Gson().toJson(searchEntity))
        }
    }
    /**
     * 关键字搜索
     * 关键字：服务区药店
     */
    fun sendQueryKeyworkAsyncReq(mKeyword: String) {
        if (isMapServiceConnected && isMapAtomicCapability) {
            val searchEntity = ProtocolEntity<KeyworkSearchParams>()
            searchEntity.pkgName = MAP_PACKAGE_NAME
            searchEntity.verName = MAP_VERSION
            searchEntity.protocolId = MAP_PROTOCOL_ID_KEYWORD_SEARCH
            searchEntity.data = KeyworkSearchParams().apply {
                keyword = mKeyword
                count = 10
                lon = MMKVUtil.getLon() ?: 116.461822899
                lat = MMKVUtil.getLat() ?: 39.89350563
                showOnMap = 1
            }
            // 发送请求
            MapServiceProxy.getInstance().requestAsync(Gson().toJson(searchEntity))
        }
    }

    /**
     * 查询目的地信息(12071)
     */
    fun sendQueryDestinationAsyncReq() {
        if (isMapServiceConnected && isMapAtomicCapability) {
            val searchEntity = ProtocolEntity<GWMMapBaseSearchParams>()
            searchEntity.pkgName = MAP_PACKAGE_NAME
            searchEntity.verName = MAP_VERSION
            searchEntity.protocolId = MAP_PROTOCOL_ID_DESTINATION_INFO
            // 发送请求
            MapServiceProxy.getInstance().requestAsync(Gson().toJson(searchEntity))
        }
    }

    /**
     * 连续驾驶时长是否大于2个小时
     */
    private fun isDrivingDurationOver2Hours(): Boolean {
        // 获取最后一次挂P档的时间和当前时间比较是否大于2个小时
        val lastPTime = MMKVUtil.getLastPTime() ?: return false
        val lastPTimeLong = lastPTime.toLong()
        val currentTimeLong = System.currentTimeMillis()
        return currentTimeLong - lastPTimeLong >= 2 * 60 * 60 * 1000
    }
}