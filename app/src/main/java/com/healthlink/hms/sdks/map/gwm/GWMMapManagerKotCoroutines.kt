package com.healthlink.hms.sdks.map.gwm

import android.content.Context
import android.util.Log
import android.widget.Toast
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.gwm.map.sdk.MapCallback
import com.gwm.map.sdk.MapServiceProxy
import com.gwm.map.sdk.entity.ProtocolEntity
import com.healthlink.hms.HmsSettings
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.sceneEngine.dto.SceneGearStatusInfoDTO
import com.healthlink.hms.sceneEngine.dto.SceneNavigationInfoDTO
import com.healthlink.hms.sceneEngine.scenes.SceneLongDriveCare2Impl
import com.healthlink.hms.sdks.map.gwm.baseDTO.GWMMapBaseSearchParams
import com.healthlink.hms.sdks.map.gwm.baseDTO.GWMMapRespArrBaseDTO
import com.healthlink.hms.sdks.map.gwm.baseDTO.GWMMapRespBaseDTO
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.TimeUtils
import com.healthlink.hms.utils.ToastUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import kotlin.coroutines.Continuation
import kotlin.coroutines.resume

/**
 * Created by imaginedays on 2024/7/18
 * 协程版本
 */
object GWMMapManagerKotCoroutines {
    enum class MapProtocolRespStatusCode(val code: Int) {
        SUCCESS(20000),
        // 检索成功，但没有检索结果
        SEARCH_NO_RESULT(32003),
        // 当前状态不支持该操作
        STATUS_NOT_SUPPORT(10008),
        // 公司地址未设置
        COMPANY_ADDRESS_NOT_SET(36002),
        // 服务不可用
        SERVICE_UNAVAILABLE(10011),
        // 服务异常
    }

    const val TAG = "SceneManager"
    // GWM 的地图服务连接是否已经建立
    private var isMapServiceConnected = false
    /**
     *  导航原子能力是否可用
     *  地图初始化完成、地图激活成功、用户已同意地图应用的隐私政策条款、地图具有定位权限，同时满足这4个条件时，将“原子能力 服务正常”
     */
    private var isMapAtomicCapability = false
    // 透出原子能力服务状态(19012)
    const val MAP_PROTOCOL_ID_ATOMIC_CAPABILITY = "19012"
    // 查询原子能力服务状态(19021)
    const val MAP_PROTOCOL_ID_QUERY_ATOMIC_CAPABILITY = "19021"
    // 关键字搜索
    const val MAP_PROTOCOL_ID_KEYWORD_SEARCH = "10031" // 10031 沿途搜索 11011 关键字搜索
    const val MAP_PROTOCOL_ID_LOCATION = "14041" // 查询自车或者指定位置坐标的方法ID
    const val MAP_PROTOCOL_ID_HOME_OR_COMPANY_LOCATION = "15021" // 查询家或者公司的方法ID
    const val MAP_PROTOCOL_ID_NAVIGATION = "11032" // 透出导航动作
    const val MAP_PROTOCOL_ID_NAVIGATION_GUIDE_POST = "11052" // 主动透出引导状态(11052)
    const val MAP_PROTOCOL_ID_NAVIGATION_GUIDE = "11041" // 查询引导状态(11041)

    const val MAP_PROTOCOL_ID_ESITMATE_TO_ARRIVAL = "11121" // 导航剩余时间和距离
    const val MAP_PROTOCOL_ID_ROAD_LEVEL = "11211" // 查询当前道路等级(11211)
    const val MAP_PROTOCOL_ID_ALL_SAPA = "11291" // 查询路径上剩余全部服务区&收费站(11291)
    const val MAP_PROTOCOL_ID_RETURN_TO = "14011" // 返回到自车位置
    const val MAP_PROTOCOL_ID_SEARCH_SAPA = "10031" // 沿途搜索(10031)
    const val MAP_PROTOCOL_ID_DESTINATION_INFO = "12071" // 查询目的地信息(12071)
    const val MAP_VERSION = "2.0.4" // 地图版本号

    var MAP_PACKAGE_NAME = "com.gwm.map" // 地图包名
    // 车辆位置距离公司是否小于500M
    var isLessThan500M: Boolean = false
    // 导航目的地剩余时间是否大于一小时
    var isMoreThanAnHourToArraival: Boolean = false
    // 道路类型是否是高速公路
    var isHighwayLevel: Boolean = false

    private var protocolIds =  arrayListOf(
        MAP_PROTOCOL_ID_KEYWORD_SEARCH,
        MAP_PROTOCOL_ID_ATOMIC_CAPABILITY,
        MAP_PROTOCOL_ID_QUERY_ATOMIC_CAPABILITY,
        MAP_PROTOCOL_ID_NAVIGATION,
        MAP_PROTOCOL_ID_NAVIGATION_GUIDE,
        MAP_PROTOCOL_ID_LOCATION,
        MAP_PROTOCOL_ID_HOME_OR_COMPANY_LOCATION,
        MAP_PROTOCOL_ID_ESITMATE_TO_ARRIVAL,
        MAP_PROTOCOL_ID_DESTINATION_INFO
    )

    private val continuationMap = mutableMapOf<Long, Continuation<String?>>()
    fun initMapManager(context: Context) {
        MAP_PACKAGE_NAME = context.packageName
        MapServiceProxy.getInstance().init(context) { connected ->
            isMapServiceConnected = connected
            Log.i(TAG,"mapServiceConnected: $connected")
            if (connected) {
                // 设置监听方法
                MapServiceProxy.getInstance().subscribe(
                    MAP_PACKAGE_NAME, protocolIds.toTypedArray()
                )
                // 注册回调
                MapServiceProxy.getInstance().registerCallBack(MAP_PACKAGE_NAME, registryCallback)
            }
        }
    }

    private val registryCallback = object : MapCallback.Stub() {
        override fun onResponse(p0: String?) {
            Log.i(TAG,"mapManager onResponse: $p0")
            if (p0.isNullOrEmpty() || p0 == "null") {
                continuationMap?.let {
                    for (mutableEntry in it) {
                        mutableEntry.value.resume(null)
                    }
                }
                Log.i(TAG,"没有返回结果 onResponse: $p0")
            } else {
                // 解析json
                val type = object : TypeToken<GWMMapRespBaseDTO<*>>() {}.type
                val result: GWMMapRespBaseDTO<*> = Gson().fromJson(p0, type)
                // 获取ID
                val protocolId = result.protocolId
                val mReqId =  result.reqId

                // 记录一些主动透出的能力
                when (protocolId) {
                    MAP_PROTOCOL_ID_ATOMIC_CAPABILITY -> handleAtomicCapabilityResponse(p0)
                    MAP_PROTOCOL_ID_NAVIGATION -> parseIsNavigationGuideStatus(p0)
                    MAP_PROTOCOL_ID_NAVIGATION_GUIDE_POST -> parseGuideStatus(p0)
                }

                synchronized(continuationMap) {
                    if(continuationMap[mReqId] != null) {
                        if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
                            result.data?.let {
                                continuationMap[mReqId]?.resume(p0)
                                continuationMap.remove(mReqId)
                            }
                        } else {
                            continuationMap[mReqId]?.resume(null)
                            continuationMap.remove(mReqId)
                        }
                    } else {
                        Log.i(TAG," 没有找到对应的协议ID: $protocolId $mReqId ")
                    }
                }
            }
        }
    }

    private suspend fun requestAsync(data: String, reqId: Long): String? {
        return suspendCancellableCoroutine { continuation ->
            synchronized(continuationMap) {
                // 将 continuation 保存到 map 中
                continuationMap[reqId] = continuation
                MapServiceProxy.getInstance().requestAsync(data)

            }
        }
    }


    /**
     * 主动透出引导状态
     * 0:非导航状态
     * 1:真实导航状态(标准)
     * 2:真实导航状态(车道级)
     * 3:模拟导航状态
     * 4:巡航状态
     * 5:探路状态
     */
    fun parseGuideStatus(p0: String) {
        Log.i(TAG,"主动透出引导状态 $p0")
        var type = object : TypeToken<GWMMapRespBaseDTO<GWMMapGuideStatusDTO>>() {}.type
        var result: GWMMapRespBaseDTO<GWMMapGuideStatusDTO> = Gson().fromJson(p0, type)
        if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
            result.data?.let {
                // guideState = 0 没有导航 其他为导航
                val isOpenNavigation = it.guideState != 0
                MMKVUtil.storeNavigationGuideStatus(isOpenNavigation)
            }
        }
    }

    /**
     * 解析主动透出导航状态数据，判断是否在导航行驶状态
     * 0: 开始导航
     * 1: 结束导航
     */
    fun parseIsNavigationGuideStatus(p0: String?) {
        Log.i(TAG,"主动透出导航状态 $p0")
        val type = object : TypeToken<GWMMapRespBaseDTO<GWMMapATOMICDTO>>() {}.type
        val result: GWMMapRespBaseDTO<GWMMapATOMICDTO> = Gson().fromJson(p0, type)
        if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
            result.data?.let { it ->
                val isNavigationGuide = it.state == 0
                MMKVUtil.storeNavigationGuideStatus(isNavigationGuide)
                val dto = SceneNavigationInfoDTO().apply {
                    status = it.state
                    timestamp = System.currentTimeMillis()
                }
                val json = Gson().toJson(dto)
                MMKVUtil.storeNavigationInfo(json)
            }
        }
    }


    // sdk主动透出原子能力
    fun handleAtomicCapabilityResponse(p0: String) {
        Log.i(TAG,"sdk主动透出原子能力: $p0")
        val type = object : TypeToken<GWMMapRespBaseDTO<GWMMapATOMICDTO>>() {}.type
        val result: GWMMapRespBaseDTO<GWMMapATOMICDTO> = Gson().fromJson(p0, type)
        if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
            result.data?.let {
                isMapAtomicCapability = it.state == 1
                if(isMapAtomicCapability) {
                    SceneLongDriveCare2Impl.resetJourneyAfterConnectedMapService()
                }
            }
        }
        // 特殊处理 透出导航原子能力时，如果是导航状态，且没有记录过导航信息，则记录导航信息
//        GlobalScope.launch {
//            if (isMapAtomicCapability) {
//                val navigationInfoJson = MMKVUtil.getNavigationInfo()
//                if (navigationInfoJson.isNullOrEmpty()) {sendQueryGuideStatusAsyncReq()
//                } else {
//
//                }
//
//            }
//            if (isMapAtomicCapability && MMKVUtil.getNavigationInfo().isNullOrEmpty()) sendQueryGuideStatusAsyncReq()
//        }
    }

    // 查询sdk是否支持原子能力
    private fun handleQueryAtomicCapabilityResponse(p0: String) {
        val type = object : TypeToken<GWMMapRespBaseDTO<GWMMapATOMICDTO>>() {}.type
        val result: GWMMapRespBaseDTO<GWMMapATOMICDTO> = Gson().fromJson(p0, type)
        if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
            result.data?.let {
                isMapAtomicCapability = it.state == 1
            }
        }
    }

    //region 导航查询相关接口

    /**
     * 获取当前位置的经纬度
     * 向地图模块发送查询指定位置坐标的异步请求
     * 成功会给mLongitude和mLatitude赋值
     */
    suspend fun sendCurrentLocationAsyncReq() : GWMMapRespBaseDTO<GWMMapLocDTO>? {
        val mReqId = System.currentTimeMillis()
        val searchEntity = ProtocolEntity<SearchLocInfoParams>().apply {
            pkgName = MAP_PACKAGE_NAME
            verName = MAP_VERSION
            protocolId = MAP_PROTOCOL_ID_LOCATION
            data = SearchLocInfoParams().apply {
                type = 1
            }
            reqId = mReqId
        }

        var response: String? = null
        try {
            withTimeoutOrNull(3000) {
                response = requestAsync(Gson().toJson(searchEntity),mReqId)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Log.i(TAG, "sendCurrentLocationAsyncReq: ${e.message}")
        }

        response?.let {
            val type = object : TypeToken<GWMMapRespBaseDTO<GWMMapLocDTO>>() {}.type
            val result: GWMMapRespBaseDTO<GWMMapLocDTO> = Gson().fromJson(response, type)
            if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
                result.data?.let {
                    if (it.lon != 0.0 && it.lat != 0.0) {
                        return result
                    }
                }
            }
        }
        return null
    }

    /**
     * 查询指定位置信息
     * @param mLongitude 经度
     * @param mLatitude 纬度
     * @return 成功返回指定位置的LocationInfo,失败返回null
     */
    suspend fun sendCustomLocationAsyncReq(mLongitude: Double, mLatitude: Double) : GWMMapRespBaseDTO<GWMMapLocDTO>? {
        val mReqId = System.currentTimeMillis()
        val searchEntity = ProtocolEntity<SearchLocInfoParams>().apply {
            pkgName = MAP_PACKAGE_NAME
            verName = MAP_VERSION
            protocolId = MAP_PROTOCOL_ID_LOCATION
            data = SearchLocInfoParams().apply {
                type = 2
                lon = mLongitude
                lat = mLatitude
            }
            reqId = mReqId
        }

        var response: String? = null
        try {
            withTimeoutOrNull(3000) {
                response = requestAsync(Gson().toJson(searchEntity),mReqId)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Log.i(TAG, "sendCustomLocationAsyncReq: ${e.message}")
        }

        response?.let {
            val type = object : TypeToken<GWMMapRespBaseDTO<GWMMapLocDTO>>() {}.type
            val result: GWMMapRespBaseDTO<GWMMapLocDTO> = Gson().fromJson(response, type)
            if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
                result.data?.let {
                    if (it.lon != 0.0 && it.lat != 0.0) {
                        return result
                    }
                }
            }
        }
        return null
    }

    /**
     * 查询公司地址信息
     * 0:获取家地址
     * 1:获取公司地址
     * 2:获取家和公司地址
     * @return 公司poi信息
     */
    suspend fun sendQueryCompanyAsyncReq() : Boolean {
        val mReqId = System.currentTimeMillis()
        val searchEntity = ProtocolEntity<SearchCompanyInfoParams>().apply {
            pkgName = MAP_PACKAGE_NAME
            verName = MAP_VERSION
            protocolId = MAP_PROTOCOL_ID_HOME_OR_COMPANY_LOCATION
            data = SearchCompanyInfoParams().apply {
                intent = 1 // 查询公司的poi
            }
            reqId = mReqId
        }
        var response: String? = null
        try {
            withTimeoutOrNull(3000) {
                response = requestAsync(Gson().toJson(searchEntity),mReqId)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Log.i(TAG, "sendQueryCompanyAsyncReq: ${e.message}")
        }
        response?.let {
            val type = object : TypeToken<GWMMapRespArrBaseDTO<PoiDTO>>() {}.type
            val result: GWMMapRespArrBaseDTO<PoiDTO> = Gson().fromJson(response, type)
            if (result.statusCode == MapProtocolRespStatusCode.COMPANY_ADDRESS_NOT_SET.code) {
                // 公司地址未设置
                return false
            }
            if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
                result.data?.let {
                    if (it.isNotEmpty()) {
                        it[0].let { innerPoi ->
                            if (innerPoi.distance < 500) {
                                isLessThan500M = true
                                return true
                            }
                        }
                    }
                }
            }
        }
        return false
    }

    /**
     * 查询导航剩余时间/剩余距离 到达目的地剩余时间大于1小时
     * 必须在导航模式下才能获取
     */
    suspend fun sendQueryEsitmateToArrivalAsyncReq() : Boolean {
        val mReqId = System.currentTimeMillis()
        val searchEntity = ProtocolEntity<SearchCompanyInfoParams>().apply {
            pkgName = MAP_PACKAGE_NAME
            verName = MAP_VERSION
            protocolId = MAP_PROTOCOL_ID_ESITMATE_TO_ARRIVAL
            reqId =  mReqId
        }
        var response: String? = null
        try {
            withTimeoutOrNull(3000) {
                response = requestAsync(Gson().toJson(searchEntity),mReqId)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Log.i(TAG, "sendQueryEsitmateToArrivalAsyncReq: ${e.message}")
            return false
        }
        response?.let {
            // 需要在导航模式下才有数据 导航剩余时间是否大于1小时， 单位是秒
            var type = object : TypeToken<GWMMapRespBaseDTO<GWMMapArrivalDTO>>(){}.type
            var result: GWMMapRespBaseDTO<GWMMapArrivalDTO> = Gson().fromJson(response, type)
            if (result.statusCode == MapProtocolRespStatusCode.STATUS_NOT_SUPPORT.code) {
                // 当前状态不支持该操作
                return false
            } else if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
                result.data?.let {
                    if (it.esitmateToArrival > 3600) {
                        // 导航
                        return true
                    }
                }
            }
        }
        return false
    }

    /**
     * 查询道路类型
     * @return true : 高速
     */
    suspend fun sendQueryRoadLevelAsyncReq() : Boolean {
        val mReqId = System.currentTimeMillis()
        val searchEntity = ProtocolEntity<GWMMapBaseSearchParams>().apply {
            pkgName = MAP_PACKAGE_NAME
            verName = MAP_VERSION
            protocolId = MAP_PROTOCOL_ID_ROAD_LEVEL
            reqId = mReqId
        }
        var response : String? = null
        try {
            withTimeoutOrNull(3000) {
                response = requestAsync(Gson().toJson(searchEntity),mReqId)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Log.i(TAG, "sendQueryRoadLevelAsyncReq: ${e.message}")
        }

        response?.let {
            var type = object : TypeToken<GWMMapRespBaseDTO<GWMMapRoadInfoDTO>>() {}.type
            var result: GWMMapRespBaseDTO<GWMMapRoadInfoDTO> = Gson().fromJson(response, type)
            if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
                result.data?.let {
                    return it.roadLevel == RoadLevel.HIGHWAY.roadLevel
                }
            }
        }
        return false
    }

    /**
     * 查询引导状态
     * 0:非导航状态
     * 1:真实导航状态(标准)
     * 2:真实导航状态(车道级)
     * 3:模拟导航状态
     * 4:巡航状态
     * 5:探路状态
     * @return true : 导航 false : 非导航
     */
    suspend fun sendQueryGuideStatusAsyncReq() : Boolean {
        val mReqId = System.currentTimeMillis()
        val searchEntity = ProtocolEntity<GWMMapBaseSearchParams>().apply {
            pkgName = MAP_PACKAGE_NAME
            verName = MAP_VERSION
            protocolId = MAP_PROTOCOL_ID_NAVIGATION_GUIDE
            reqId =  mReqId
        }
        val response = requestAsync(Gson().toJson(searchEntity),mReqId)
        response?.let {
            var type = object : TypeToken<GWMMapRespBaseDTO<GWMMapGuideStatusDTO>>() {}.type
            var result: GWMMapRespBaseDTO<GWMMapGuideStatusDTO> = Gson().fromJson(response, type)
            if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
                result.data?.let {
                    // 非导航状态 认为0是非导航，其他都是导航
                    Log.i(TAG,"sendQueryGuideStatusAsyncReq 查询导航状态 ${it.guideState} = ${mapGuideStatus(it.guideState)} 查询地图状态 ${it.naviState} = ${mapNaviStatus(it.naviState)}")
                    val isOpenNavigationGuide = it.guideState != 0
                    return isOpenNavigationGuide
                }
            }
        }
        return false
    }

    /**
     * 匹配引导状态文字
     */
    private fun mapGuideStatus(guideType: Int) : String {
        return when (guideType) {
            0 -> "非导航状态"
            1 -> "真实导航状态(标准)"
            2 -> "真实导航状态(车道级)"
            3 -> "模拟导航状态"
            4 -> "巡航状态"
            5 -> "探路状态"
            else -> "未知导航状态"
        }
    }

    /**
     * 匹配导航状态文字
     */
    private fun mapNaviStatus(naviType: Int) : String {
        return when (naviType) {
            0 -> "初始状态"
            1 -> "巡航状态"
            2 -> "路径规划状态"
            3 -> "引导状态"
            4 -> "智驾导航状态"
            5 -> "模拟导航状态"
            6 -> "驾驶报告状态(引导结束显示驾驶报告页面)"
            else -> "未知导航状态"
        }
    }

    /**
     * 关键字搜索
     * 关键字：服务区、药店
     * 调用这个接口会跳转到导航App
     */
    suspend fun sendQueryKeywordAsyncReq(mKeyword: String) : Boolean {
        val mReqId = System.currentTimeMillis()
        val searchEntity = ProtocolEntity<KeyworkSearchParams>().apply {
            pkgName = MAP_PACKAGE_NAME
            verName = MAP_VERSION
            protocolId = MAP_PROTOCOL_ID_KEYWORD_SEARCH
            data = KeyworkSearchParams().apply {
                keyword = mKeyword
                count = 10
                showOnMap = 1
            }
            reqId = mReqId
        }
        val response = requestAsync(Gson().toJson(searchEntity),mReqId)
        if (null == response){
            withContext(Dispatchers.Main) {
                ToastUtil.makeText(HmsApplication.appContext,"当前状态不支持", Toast.LENGTH_SHORT).show()
            }
            return false
        } else {
            val type = object : TypeToken<GWMMapRespArrBaseDTO<GWMMapAllSapaDTO>>() {}.type
            val result: GWMMapRespArrBaseDTO<GWMMapAllSapaDTO> = Gson().fromJson(response, type)
            if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
                // 应该跳转到导航App
                return  true
            } else if (result.statusCode == MapProtocolRespStatusCode.STATUS_NOT_SUPPORT.code) {
                withContext(Dispatchers.Main) {
                    ToastUtil.makeText(HmsApplication.appContext,"当前状态不支持", Toast.LENGTH_SHORT).show()
                }
                return false
            }
        }
        return false
    }


    /**
     * 查询目的地信息(12071)
     */
    suspend fun sendQueryDestinationAsyncReq() : GWMMapRespBaseDTO<GWMMapLocDTO>?  {
        val mReqId = System.currentTimeMillis()
        val searchEntity = ProtocolEntity<GWMMapBaseSearchParams>().apply {
            pkgName = MAP_PACKAGE_NAME
            verName = MAP_VERSION
            protocolId = MAP_PROTOCOL_ID_DESTINATION_INFO
            reqId = mReqId
        }

        var response : String? = null
        try {
            withTimeoutOrNull(3000) {
                response = requestAsync(Gson().toJson(searchEntity),mReqId)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Log.i(TAG, "sendQueryDestinationAsyncReq: ${e.message}")
        }

        response?.let {
            val type = object : TypeToken<GWMMapRespBaseDTO<GWMMapLocDTO>>() {}.type
            val result: GWMMapRespBaseDTO<GWMMapLocDTO> = Gson().fromJson(response, type)
            if (result.statusCode == MapProtocolRespStatusCode.STATUS_NOT_SUPPORT.code) {
                // 当前状态不支持该操作
                return null
            }
            if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
                // 目的地信息
                return result
            }
        }
        return null
    }

    /**
     * 连续驾驶时长是否大于2个小时
     */
    fun isDrivingDurationOver2Hours(): Boolean {
        // 记录导航状态和开始时间，如果是开启导航状态的话，判断是否超过2个小时
        if (MMKVUtil.getNavigationInfo().isNullOrEmpty()) {
            Log.i(TAG,"存储的开始导航导航时间信息为空")
            return false
        } else {
            MMKVUtil.getNavigationInfo()?.let {
                try {
                    var gson =  Gson()
                    val dto =gson.fromJson(it, SceneNavigationInfoDTO::class.java)

                    // 获取最新档位信息
                    var gearStatusInfoStr = MMKVUtil.getLastGearStatus()
                    var gearStatusInfoDTO = SceneGearStatusInfoDTO()
                    if(gearStatusInfoStr!=null && gearStatusInfoStr.isNotBlank() && gearStatusInfoStr.isNotEmpty()){
                        gearStatusInfoDTO = gson.fromJson(gearStatusInfoStr,SceneGearStatusInfoDTO::class.java)
                    }

                    dto?.let { innerDto ->
                        var currentTime = System.currentTimeMillis()
                        var journeyStartTime = innerDto.timestamp
                        var driveTime = currentTime - journeyStartTime

                        // 减去行程中休息时长
                        var totalRestTimeInJourney = gearStatusInfoDTO.totalRestTimeInJourney
                        if(totalRestTimeInJourney > 0){
                            driveTime -= totalRestTimeInJourney
                        }

                        Log.i(TAG, "isDrivingDurationOver2Hours: 当前时间:${currentTime} 记录导航状态:${mapNavigationStatus(innerDto.status)} 记录导航时间：${innerDto.timestamp} 累计休息时间：${totalRestTimeInJourney}")
                        Log.i(TAG,"计算连续驾驶时长：${TimeUtils.formatMilliseconds(driveTime)}")
                        // 如果大于2小时 2 * 60 * 60 * 1000
                        if (innerDto.status == 0 && driveTime >= HmsSettings.SCENE_LONG_DRIVE_2_DRIVING_TIME) {
                            Log.i(TAG, "驾驶时长超过2小时")
                            return true
                        }
                    } ?: Log.i(TAG, "SceneNavigationInfoDTO为空")
                } catch (e: Exception) {
                    Log.i(TAG, "获取连续驾驶时长大于2小时对象失败: ${e.message}")
                }
            } ?: Log.i(TAG, "存储的开始导航导航时间信息为空")
        }
        return false
    }

    private fun mapNavigationStatus(naviType: Int) : String {
        return when (naviType) {
            0 -> "导航中"
            1 -> "未开启导航"
            else -> "未知导航状态"
        }
    }

    //endregion

    /**
     * 查询剩余路段的SAPA 服务区和收费站
     * !!!该方法不会跳转到导航App 应该跳转才对
     *
     */
    suspend fun sendQuerySAPAAsyncReq() {
        val mReqId = System.currentTimeMillis()
        val searchEntity = ProtocolEntity<SearchSapaParams>().apply {
            pkgName = MAP_PACKAGE_NAME
            verName = MAP_VERSION
            protocolId = MAP_PROTOCOL_ID_ALL_SAPA
            data.apply {
                keyword = "服务区"
                showOnMap = 1
                count = 10
                sortType = 0
            }
            reqId = mReqId
        }
        val response = requestAsync(Gson().toJson(searchEntity),mReqId)
        var type = object : TypeToken<GWMMapRespBaseDTO<GWMMapAllSapaDTO>>() {}.type
        var result: GWMMapRespBaseDTO<GWMMapAllSapaDTO> = Gson().fromJson(response, type)
        if (result.statusCode == MapProtocolRespStatusCode.SUCCESS.code) {
            // 应该跳转到导航App
        }
    }
}