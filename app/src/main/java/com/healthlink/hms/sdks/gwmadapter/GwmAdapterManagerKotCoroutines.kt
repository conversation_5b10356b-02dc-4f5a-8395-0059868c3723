package com.healthlink.hms.sdks.gwmadapter

import android.app.ActivityOptions
import android.app.GwmFeatureManager
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.gwm.android.adapter.IDataChangedListener
import com.gwm.android.adapter.client.DataChangeListener
import com.gwm.android.adapter.client.GwmAdapterClient
import com.gwm.android.adapter.client.ServiceStateListener
import com.healthlink.hms.BuildConfig
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.reciever.HMSAction
import com.healthlink.hms.sceneEngine.SceneManager
import com.healthlink.hms.sdks.map.gwm.GWMMapLocDTO
import com.healthlink.hms.sdks.map.gwm.GWMMapManagerKotCoroutines
import com.healthlink.hms.sdks.map.gwm.GWMWeatherAirCurrentDTO
import com.healthlink.hms.sdks.map.gwm.GWMWeatherLifeIndexDTO
import com.healthlink.hms.sdks.map.gwm.GWMWeatherRespArrDTO
import com.healthlink.hms.sdks.map.gwm.GWMWeatherRespObjectDTO
import com.healthlink.hms.sdks.map.gwm.baseDTO.GWMMapRespBaseDTO
import com.healthlink.hms.utils.StringUtil
import com.healthlink.hms.utils.ToastUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import kotlin.coroutines.resume


object GwmAdapterManagerKotCoroutines {

    const val TAG = "GwmAdapterManagerKotCoroutines"

    const val TBOX_CALL = "sys.tbox.cur_call_info"; // 3CALL当前状态信息
    const val TBOX_CALL_ACTION = "sys.tbox.call_action";
    const val AVM_DEVICE_SUPPORT = "sys.avm.device_support"; // 当前车型是否支持360全景功能 0 不支持 1 支持
    const val POWER_MODE = "car.basic.power_mode"; // 汽车电源状态
    const val ENGINE_STATE = "car.basic.engine_state";  //发动机工作状态
    const val GEAR_STATUS = "car.basic.gear_status";  //档位状态
    const val VECHILE_SPEED = "car.basic.vehicle_speed";  //车速
    const val VECHILE_SN = "persist.gwm.vehicle.sn"  // android 设备可以用SN
    const val VECHILE_VIN = "persist.gwm.vehicle.vin"  // android 设备可以用SN
    const val VECHILE_MODE_1 = "persist.gwm.car.mode1"  // 车型ID
    const val VECHILE_MODE_2 = "persist.gwm.car.mode2"  // 车型ID 预留字段 使用VECHILE_MODE_1就行
    const val COFFEE_OS_VERSION = "ro.vendor.gwm.coffee_os_version"  // 咖啡机版本
    const val SYSTEM_PROPERTY_WAIST_SUPPORT =
        "persist.vendor.gwm.cfg.driver.waist.supporter"  // 电动腰托配置字
    const val SERVER_ID_APP_STORE = "gwm_adapter_appstore" // Appstore服务ID
    const val DATA_ID_APP_STORE_QUERY_INFO = "appstore.query.info" // 下发DATA_ID

    // 本次行程里程Key
    const val JOURNEY_ODOMETER = "car.basic.cur_journey_odometer"

    // 仪表盘总里程数据ID
    const val TOTAL_ODOMETER = "car.ev_info.total_odometer"

    /**
     * 方向盘加热：1 - 加热；0 - 关闭
     */
    const val CAR_BASIC_STEER_WHEEL_HEATING = "car.basic.steer_wheel_heating_enable"

    //    /**
//     * 主驾座椅加热： 分为 3 level 和 9 level ，v4 是 9 level，v3 是 3 level
//     * 数据值定义：
//     * -1 未知/无效值
//     * 0-max
//     * 测试数据：
//     * 0:0
//     * 1:1
//     * 2:2
//     * 3:3
//     * 数据类型：
//     * Int
//     */
    // 主驾座椅加热
    const val CAR_BASIC_SEAT_HEATING_LEVEL =
        "car.comfort_setting.driver_seat_heating_level_nine"

    // 主驾座椅通风 分为3档和9档
    // 3挡通风DataID
    const val CAR_BASE_SEAT_VENTILATION_3_LEVEL =
        "car.comfort_setting.driver_seat_ventilation_level"

    // 9挡通风DataID
    const val CAR_BASE_SEAT_VENTILATION_9_LEVEL =
        "car.comfort_setting.driver_seat_ventilation_level_nine"

    // 车外温度
    private const val CAR_BASIC_OUTSIDE_TEMP = "car.basic.outside_temp"

    /**
     * V35
     * 小憩模式：1 打开，0：关闭
     */
//    const val CAR_COMFORT_SETTING_REST_MODE_ENABLE = "car.comfort_setting.rest_mode_enable"
    /**
     * V35 Version 1
     * 小憩模式 1、打开，0：关闭
     */
    const val CAR_COMFORT_SETTING_REST_MODE_ENABLE = "izone.mode.command_status"

    /**
     * 智能腰托数据ID：
     * 数据值定义：
     * -1：信号丢失
     * 0：前
     * 1：后
     * 2：上
     * 3：下
     * 4: 释放
     */
    const val CAR_COMFORT_SETTING_SEAT_WAIST_DIRECTION_CONFIG_ENABLE =
        "car.comfort.setting.seat_waist_direction_config"

    /**
     * car.comfort.setting.seat_massage_level 座椅按摩
     * 数据值定义：
     * 0: 关闭
     * 1：低
     * 2：中
     * 3：高
     */
    const val CAR_COMFORT_SETTING_SEAT_MESSAGE_LEVEL = "car.comfort.setting.seat_massage_level"

    /**
     * 香氛系统
     * 数据值定义：
     * 0：关闭
     * 1：开启
     * car.fragrance.cur_channel_type 1，2，3取值，0关闭
     *  */
    const val CAR_COMFORT_SETTING_FRAGRANCE_SYSTEM = "car.comfort.setting.fragrance_system"

    /**
     * 氛围灯设置数据ID
     * 数据值定义：
     * 1 静态氛围灯
     * 2 动态氛围灯
     * 3 音乐律动
     * 4 呼吸灯
     * 5 流水灯
     * 数据类型：
     * Int
     */
    const val CAR_LIGHT_SETTING_AMBIENT_LIGHT_SCENE_SELECTION =
        "car.light_setting.ambient_light.scene_selection"

    /**
     * 乘员监控系统-成员座位状态
     * exist 0 不在位 1 在位 {副驾、后左成员、后中成员、后右成员}
     * 情形一： 0,1,1,0
     * intArray
     */
    const val CAR_FRS_SETTING_OMS_EXIST_STATE = "car.frs_setting.oms_exist_state"

    /**
     * 乘员数量监控系统 oms
     * 数据值定义：
     * 0 无人
     * 1 1人
     * 2 2人
     * 3 3人
     * 4 4人
     * 5 5人
     */
    const val CAR_OMS_FRS_OMS_NUMBER = "car.oms.frs.oms_number"

    /**
     * 副驾驶座椅传感器信息，后排座位没有座椅传感信号
     * 0x0: 无人
     * Ox1: 有人
     */
    const val CAR_HVAC_SEAT_PASS_SPER_STS = "car.hvac.seat_pass_sper_sts"

    /**
     * 车辆座位人员状态
     * '-1 未知/无效值
     * 0 座位没人
     * 1 座位有人
     * {主驾驶位、副驾驶位、后左、后中、后右、三排左，三排中、三排右｝
     */
    const val CAR_BASIC_SEAT_PERSON_STATUS = "car.basic.seat_person_status"

    private var mGwmAdapterClient: GwmAdapterClient = GwmAdapterClient.getInstance()

    /**
     * 服务是否联通
     */
    private var mServiceConnected = false

    //region 天气相关
    const val DATA_ID_LIFE_INDEX = "weather.life_index.current" // 当前生活指数
    const val DATA_ID_WEATHER_AIR_CURRENT = "weather.air.current" // 当前空气质量
    const val DATA_ID_WEATHER_FORECAST = "weather.forecast.sync" // 同步天气
    const val KEY_LONGITUDE = "longitude"
    const val KEY_LATITUDE = "latitude"
    const val KEY_TYPE_UV = "uv" // 紫外线字数
    const val KEY_TYPE_FLU = "flu" // 感冒指数
    const val KEY_TYPE_AIR = "air" // 空气质量指数
    //endregion

    private var mContext: Context? = null


    fun providerGwmAdapterServiceIds(): ArrayList<String> {
        return arrayListOf(
            "gwm_adapter_weather",
            "gwm_adapter_media",
            "gwm_adapter"
        ).apply {
            if (isSupportRestMode()) {
                add("gwm_adapter_izone")
            }
        }
    }

    private suspend fun connectAdapterService(context: Context): Boolean {
        return suspendCancellableCoroutine<Boolean> { continuation ->
            val servers = providerGwmAdapterServiceIds()
            if (!mGwmAdapterClient.isServiceConnected) {
                mGwmAdapterClient.init(
                    context,
                    servers.toTypedArray(),
                    object : ServiceStateListener {
                        override fun onServiceConnected() {
                            Log.i(TAG, "GwmAdapter is connected")
                            mServiceConnected = true
                            continuation.resume(true)
                        }

                    override fun onServiceDisconnected() {
                        Log.i(TAG, "GwmAdapter is disconnected")
                        mServiceConnected = false
                        continuation.resume(false)
                    }
                })
            } else {
                // DO nothing
            }
        }
    }

    suspend fun initGwmAdapter(context: Context): Boolean {
        mContext = context
        val mServiceConnected = connectAdapterService(context)
        if (mServiceConnected) {
            // 监听dataId数据变化
            // 座椅加热(CAR_BASIC_SEAT_HEATING_LEVEL)、
            // 方向盘加热(CAR_BASIC_STEER_WHEEL_HEATING)、
            // 座椅按摩(CAR_COMFORT_SETTING_SEAT_MESSAGE_LEVEL)
            // 主驾座椅通风(CAR_BASE_SEAT_VENTILATION_3_LEVEL || CAR_BASE_SEAT_VENTILATION_9_LEVEL）
            // 天气应用 生活指数变化监听 DATA_ID_LIFE_INDEX
            // 小憩模式 v35 CAR_COMFORT_SETTING_REST_MODE_ENABLE
            val dataIds = arrayOf(
                CAR_BASIC_SEAT_HEATING_LEVEL,
                CAR_BASIC_STEER_WHEEL_HEATING,
                CAR_COMFORT_SETTING_SEAT_MESSAGE_LEVEL,
                CAR_BASE_SEAT_VENTILATION_3_LEVEL,
                CAR_BASE_SEAT_VENTILATION_9_LEVEL,
                CAR_COMFORT_SETTING_REST_MODE_ENABLE
//                DATA_ID_LIFE_INDEX
            )
            registerDataChangeListener(dataIds)
//            mGwmAdapterClient.getData(DATA_ID_WEATHER_FORECAST)
        }
        return mServiceConnected
    }

    private var dataChangeSeatHeatRes: String? = null
    private var dataChangeSteerWheelHeatRes: String? = null
    private var dataChangeSeatMassageRes: String? = null
    private var dataChangeSeatVentilationRes: String? = null
    private val dataChangedListener = DataChangeListener { dataId, dataValue ->
        Log.i(TAG, "数据变化监听：GwmAdapter data changed: dataId=$dataId, dataValue=$dataValue")
//        Toast.makeText(mContext, "GwmAdapter data changed: key=$dataId, value=$dataValue", Toast.LENGTH_LONG).show()
        when (dataId) {
            CAR_BASIC_SEAT_HEATING_LEVEL -> {
                // 座椅加热(CAR_BASIC_SEAT_HEATING_LEVEL)
                dataChangeSeatHeatRes = dataValue
            }

            CAR_BASIC_STEER_WHEEL_HEATING -> {
                // 方向盘加热(CAR_BASIC_STEER_WHEEL_HEATING)
                dataChangeSteerWheelHeatRes = dataValue
            }

            CAR_COMFORT_SETTING_SEAT_MESSAGE_LEVEL -> {
                // 座椅按摩(CAR_COMFORT_SETTING_SEAT_MESSAGE_LEVEL)
                dataChangeSeatMassageRes = dataValue
            }

            CAR_BASE_SEAT_VENTILATION_3_LEVEL -> {
                // 主驾座椅通风3档
                dataChangeSeatVentilationRes = dataValue
            }

            CAR_BASE_SEAT_VENTILATION_9_LEVEL -> {
                // 主驾座椅通风9档
                dataChangeSeatVentilationRes = dataValue
            }

            DATA_ID_LIFE_INDEX -> {
                // 天气应用 生活指数变化监听 DATA_ID_LIFE_INDEX
                Log.i(TAG, "天气生活指数数据变化 $dataValue")
            }
            // 取消小憩模式监听
//            CAR_COMFORT_SETTING_REST_MODE_ENABLE -> {
//                // 小憩模式
//                Log.i(TAG, "小憩模式数据变化 $dataValue")
//            }
        }

        var intent = Intent(HMSAction.ACTION_HMS_UPDATE_VEHICLE_SERVICE)
        intent.putExtra("DATA_ID", dataId)
        // 温暖模式
        if(dataChangeSteerWheelHeatRes != null || dataChangeSeatHeatRes != null){
            mContext?.sendBroadcast(intent)
        } else if (dataChangeSeatMassageRes != null) {
            // 放松模式
            mContext?.sendBroadcast(intent)
        } else if (dataChangeSeatVentilationRes != null) {
            // 清凉模式
            mContext?.sendBroadcast(intent)
        }
    }

    private fun registerDataChangeListener(dataId: Array<String>) {
        mGwmAdapterClient.registerDataChangeListener(dataId, dataChangedListener)
    }

    /**
     * 设置车辆档位
     */
    fun setGearStatus(level: String) {
        setVehicleSettingValue(GEAR_STATUS, level);
    }

    /**
     * 获取车辆档位
     */
    fun getGearStatus(): String? {
        return mGwmAdapterClient.getData(GEAR_STATUS);
    }

    //region 天气服务接口

    suspend fun sendWeatherLiveIndexReq(sceneManager: SceneManager): MutableMap<String, String?>? {
        val weatherLifeMap = mutableMapOf<String, String?>()
        var currentLocation: GWMMapRespBaseDTO<GWMMapLocDTO>? = null
        try {
            withTimeoutOrNull(3000) {
                currentLocation = GWMMapManagerKotCoroutines.sendCurrentLocationAsyncReq()
                Log.i(TAG, "sendWeatherLiveIndexReq $currentLocation")
            }
        } catch (e: Exception) {
            return weatherLifeMap
        }

        Log.i(TAG, "sendWeatherLiveIndexReq $currentLocation")

        currentLocation?.let {
            if (it.data?.lon == 0.0 || it.data?.lat == 0.0) {
                return weatherLifeMap
            }
        }

        val data = Bundle().apply {
            putDouble(KEY_LONGITUDE, StringUtil.formatDouble(currentLocation?.data?.lon!!)!!)
            putDouble(KEY_LATITUDE, StringUtil.formatDouble(currentLocation?.data?.lat!!)!!)
        }

//        var response:String? = "{\"code\":\"0\",\"data\":[{\"date\":\"2024-07-16\",\"desc\":\"天气较好，较适宜进行各种运动，但因天气热，请适当减少运动时间，降低运动强度。\",\"level\":\"较适宜\",\"type\":\"sport\"},{\"date\":\"2024-07-16\",\"desc\":\"较不宜洗车，明天可能有雨，擦拭一新的汽车可能无法保持太长时间。\",\"level\":\"较不宜\",\"type\":\"cw\"},{\"date\":\"2024-07-16\",\"desc\":\"天气炎热，建议着短衫、短裙、短裤、薄型T恤衫等清凉夏季服装。\",\"level\":\"炎热\",\"type\":\"drsg\"},{\"date\":\"2024-07-16\",\"desc\":\"天气太热，不适合垂钓。\",\"level\":\"不宜\",\"type\":\"fsh\"},{\"date\":\"2024-07-16\",\"desc\":\"紫外线强度较弱，建议出门前涂擦SPF在12-15之间、PA+的防晒护肤品。\",\"level\":\"弱\",\"type\":\"uv\"},{\"date\":\"2024-07-16\",\"desc\":\"天气较好，微风，虽天气稍热，却仍适宜旅游，不要错过机会呦！\",\"level\":\"适宜\",\"type\":\"trav\"},{\"date\":\"2024-07-16\",\"desc\":\"天气条件不易诱发过敏，可放心外出，除特殊体质外，无需担心过敏问题。\",\"level\":\"不易发\",\"type\":\"ag\"},{\"date\":\"2024-07-16\",\"desc\":\"白天天气多云，同时会感到有些热，不舒适。\",\"level\":\"不舒适\",\"type\":\"comf\"},{\"date\":\"2024-07-16\",\"desc\":\"各项气象条件适宜，发生感冒机率较低。但请避免长期处于空调房间中，以防感冒。\",\"level\":\"少发\",\"type\":\"flu\"},{\"date\":\"2024-07-16\",\"desc\":\"气象条件对空气污染物稀释、扩散和清除无明显影响，易感人群应适当减少室外活动时间。\",\"level\":\"良\",\"type\":\"air\"},{\"date\":\"2024-07-16\",\"desc\":\"天气热，到中午的时候您将会感到有点热，因此建议在午后较热时开启制冷空调。\",\"level\":\"部分时间开启\",\"type\":\"ac\"},{\"date\":\"2024-07-16\",\"desc\":\"白天太阳辐射较强，建议佩戴透射比1级且标注UV380-UV400的浅色太阳镜\",\"level\":\"必要\",\"type\":\"gl\"},{\"date\":\"2024-07-16\",\"desc\":\"天气炎热，用防脱水防晒化妆品，少用粉底和胭脂，常补粉。\",\"level\":\"防脱水防晒\",\"type\":\"mu\"},{\"date\":\"2024-07-16\",\"desc\":\"天气不错，较适宜晾晒,赶紧把久未见阳光的衣物搬出来吸收一下太阳的味道吧！\",\"level\":\"适宜\",\"type\":\"airc\"},{\"date\":\"2024-07-16\",\"desc\":\"天气较好，路面干燥，交通气象条件良好，车辆可以正常行驶。\",\"level\":\"良好\",\"type\":\"ptfc\"},{\"date\":\"2024-07-16\",\"desc\":\"属中等强度紫外辐射天气，外出时应注意防护，建议涂擦SPF指数高于15，PA+的防晒护肤品。\",\"level\":\"中等\",\"type\":\"spi\"}],\"msg\":\"SUCCESS\"}"
        var response: String? = null
        withTimeoutOrNull(3000) {
            response = suspendCancellableCoroutine { it ->
                mGwmAdapterClient.getData(
                    DATA_ID_LIFE_INDEX,
                    data,
                    object : IDataChangedListener.Stub() {
                        override fun onChanged(dataId: String?, dataValue: String?, p2: Bundle?) {
                            if (dataValue != null) {
                                it.resume(dataValue)
                            } else {
                                it.resume(null)
                            }
                        }
                    })
            }
        }
        Log.i(TAG, "getLifeIndex $response")

        response?.let {
            val type = object : TypeToken<GWMWeatherRespArrDTO<GWMWeatherLifeIndexDTO>>() {}.type
            val weatherLifeIndex: GWMWeatherRespArrDTO<GWMWeatherLifeIndexDTO> =
                Gson().fromJson(it, type)
            if (weatherLifeIndex.code == "0" && weatherLifeIndex.msg == "SUCCESS") {
                weatherLifeIndex.data?.let {
                    if (it.isNotEmpty()) {
                        for (item in it) when (item.type) {
                            KEY_TYPE_UV, KEY_TYPE_FLU -> {
                                weatherLifeMap[item.type] = item.level
                            }
                        }
                    }
                }
            }
        }
        return weatherLifeMap
    }

    /**
     * 当前空气质量
     */
    suspend fun sendWeatherAirCurrentReq(): MutableMap<String, String?>? {
        val weatherAirMap = mutableMapOf<String, String?>()

        var currentLocation: GWMMapRespBaseDTO<GWMMapLocDTO>? = null
        try {
            withTimeoutOrNull(3000) {
                currentLocation = GWMMapManagerKotCoroutines.sendCurrentLocationAsyncReq()
                Log.i(TAG, "获取当前位置经纬度 $currentLocation")
            }
        } catch (e: Exception) {
            return weatherAirMap
        }

        Log.i(TAG, "sendWeatherAirCurrentReq 获取经纬度 $currentLocation")

        currentLocation?.let {
            if (it.data?.lon == 0.0 || it.data?.lat == 0.0) {
                return weatherAirMap
            }
        }

        val data = Bundle().apply {
            putDouble(KEY_LONGITUDE, StringUtil.formatDouble(currentLocation?.data?.lon!!)!!)
            putDouble(KEY_LATITUDE, StringUtil.formatDouble(currentLocation?.data?.lat!!)!!)
        }

        var response: String? = null
        withTimeoutOrNull(3000) {
            response = suspendCancellableCoroutine { it ->
                mGwmAdapterClient.getData(
                    DATA_ID_WEATHER_AIR_CURRENT,
                    data,
                    object : IDataChangedListener.Stub() {
                        override fun onChanged(dataId: String?, dataValue: String?, p2: Bundle?) {
                            if (dataValue != null) {
                                it.resume(dataValue)
                            } else {
                                it.resume(null)
                            }
                        }
                    })
            }
        }
        Log.i(TAG, "getAirCurrent $response")

        response?.let { it ->
            val type = object : TypeToken<GWMWeatherRespObjectDTO<GWMWeatherAirCurrentDTO>>() {}.type
            val weatherAirCurrentIndex: GWMWeatherRespObjectDTO<GWMWeatherAirCurrentDTO> =
                Gson().fromJson(it, type)
            Log.i(TAG,"weatherAirCurrentIndex = $weatherAirCurrentIndex")
            if (weatherAirCurrentIndex.code == "0" && weatherAirCurrentIndex.msg == "SUCCESS") {
                weatherAirCurrentIndex.data.let {
                    if (it.category.isNotEmpty()) {
                        weatherAirMap[KEY_TYPE_AIR] = it.category
                    }
                }
            } else {
                Log.i(TAG, "sendWeatherAirCurrentReq $it ")
            }
        } ?: Log.i(TAG,"response = null")
        return weatherAirMap
    }


    //endregion

    //region 车机模式相关
    fun isWarnModeOpen(): Boolean {
        var isWarnModeOpen = false
        // 是否支持方向盘加热
        var isSupportSteerWheelHeating = isSupportSteerWheelHeating()
        // 是否支持座椅加热
        var isSupportSeatHeat = isSupportSeatHeat()

        // 都支持
        if (isSupportSteerWheelHeating && isSupportSeatHeat) {
            isWarnModeOpen = isOpenSeatHeat() && isOpenSteerWheelHeating()
        }

        // 只支持方向盘加热
        if (isSupportSteerWheelHeating && !isSupportSeatHeat) {
            isWarnModeOpen = isOpenSteerWheelHeating()
        }

        // 只支持座椅加热
        if (!isSupportSteerWheelHeating && isSupportSeatHeat) {
            isWarnModeOpen = isOpenSeatHeat()
        }

        Log.i(TAG, "isWarnModeOpen $isWarnModeOpen isSupportSteerWheelHeating = $isSupportSteerWheelHeating  isSupportSeatHeat = $isSupportSeatHeat ")

        return isWarnModeOpen
    }

    /**
     * 打开温暖模式
     * 方向盘加热 1档
     * 主驾座椅加热 3档
     * 注解说明：示例：打开方向盘加热1档时，如果方向盘加热已经打开则不下发命令，
     * getWarnModeStatus 方法执行时，则获取不到监听数据变化,则使用 mGwmAdapterClient.getData 获取 方向盘加热变化数据，如果是打开则返回1，
     * 如果没打开则返回 0
     * @see GwmAdapterManagerKotCoroutines.getWarnModeStatus
     */
    fun openWarnMode(steerWheelLevel: String, seatHeatLevel: String) {

        val isSupportSteerWheel = isSupportSteerWheelHeating()
        val isSupportSeatHeat = isSupportSeatHeat()
        Log.i(TAG,"打开温暖模式：steerWheelLevel $steerWheelLevel, seatHeatLevel $seatHeatLevel isSupportSteerWheel $isSupportSteerWheel isSupportSeatHeat $isSupportSeatHeat")
        Log.i(TAG,"打开温暖模式 isSupportSteerWheel $isSupportSteerWheel isSupportSeatHeat $isSupportSeatHeat")
        if (isSupportSteerWheel && isSupportSeatHeat) {
            if(!isOpenSteerWheelHeating() && !isOpenSeatHeat()) {
                mGwmAdapterClient.setData(CAR_BASIC_STEER_WHEEL_HEATING,steerWheelLevel)
                mGwmAdapterClient.setData(CAR_BASIC_SEAT_HEATING_LEVEL,seatHeatLevel)
            } else if (isOpenSteerWheelHeating() && !isOpenSeatHeat()) {
                mGwmAdapterClient.setData(CAR_BASIC_SEAT_HEATING_LEVEL,seatHeatLevel)
            } else if (!isOpenSteerWheelHeating() && isOpenSeatHeat()) {
                mGwmAdapterClient.setData(CAR_BASIC_STEER_WHEEL_HEATING,steerWheelLevel)
            }

        } else if (isSupportSteerWheel && !isSupportSeatHeat) {
            mGwmAdapterClient.setData(CAR_BASIC_STEER_WHEEL_HEATING,steerWheelLevel)
        } else if (!isSupportSteerWheel && isSupportSeatHeat) {
            mGwmAdapterClient.setData(CAR_BASIC_SEAT_HEATING_LEVEL,seatHeatLevel)
        }
    }

    /**
     * 关闭温暖模式
     */
    fun closeWarnMode(closeLevel: String){
        val isSupportSteerWheel = isSupportSteerWheelHeating()
        val isSupportSeatHeat = isSupportSeatHeat()
        Log.i(TAG,"关闭温暖模式：$closeLevel isSupportSteerWheel $isSupportSteerWheel isSupportSeatHeat $isSupportSeatHeat")
        if (isSupportSteerWheel && isSupportSeatHeat) {
            mGwmAdapterClient.setData(CAR_BASIC_STEER_WHEEL_HEATING,closeLevel)
            mGwmAdapterClient.setData(CAR_BASIC_SEAT_HEATING_LEVEL,closeLevel)
        } else if (isSupportSteerWheel && !isSupportSeatHeat) {
            mGwmAdapterClient.setData(CAR_BASIC_STEER_WHEEL_HEATING,closeLevel)
        } else if (!isSupportSteerWheel && isSupportSeatHeat) {
            mGwmAdapterClient.setData(CAR_BASIC_SEAT_HEATING_LEVEL,closeLevel)
        }
    }

    /**
     * 温暖模式状态
     * 方向盘加热 1 开
     * 座椅加热 3 档
     * @return first 方向盘加热结果 second 座椅加热结果
     * 返回结果有三种情况
     * 1、优先使用 dataChangeSteerWheelHeatRes dataChangeSeatHeatRes
     * 2、如果 dataChangeSteerWheelHeatRes dataChangeSeatHeatRes 为空，
     *    则使用 mGwmAdapterClient.getData 获取 方向盘加热和座椅加热结果 如果是打开的话就返回 1 3
     *    如果是关闭的话就返回 0 0
     */
    fun getWarnModeStatus() : Pair<String?, String?> {
        // 如果监听状态变化的变量值为空，则使用 mGwmAdapterClient.getData 获取 方向盘加热和座椅加热结果
        val steerWheelRes =  dataChangeSteerWheelHeatRes ?: (if (isOpenSteerWheelHeating()) "1" else "0")
        val seatHeatRes =  dataChangeSeatHeatRes ?: (if (isOpenSeatHeat()) "3" else "0")
        Log.i(TAG,"获取温暖模式状态 方向盘:$dataChangeSteerWheelHeatRes 座椅：$dataChangeSeatHeatRes")
        return Pair(steerWheelRes,seatHeatRes)
    }

    /**
     * 清凉模式是否打开
     */
    fun isCoolModeOpen(): Boolean {
        var isOpenCool = isOpenSeatVentilation()
        Log.i(TAG, "isCoolModeOpen $isOpenCool")
        return isOpenCool
    }

    /**
     * 打开清凉模式
     */
    fun openCoolMode(level: String) {
        Log.i(TAG,"打开清凉模式 $level")
        mGwmAdapterClient.setData(getSupportSeatVentilationDataId(),level)
    }

    /**
     * 关闭清凉模式
     */
    fun closeCoolMode(level: String) {
        Log.i(TAG,"关闭清凉模式 $level")
        mGwmAdapterClient.setData(getSupportSeatVentilationDataId(),level)
    }

    /**
     * 获取清凉模式状态
     */
    fun getCoolModeStatus() : String? {

        if (dataChangeSeatVentilationRes!= null) {
            Log.i(TAG,"获取清凉模式状态:$dataChangeSeatVentilationRes")
            return dataChangeSeatVentilationRes
        }

        if (!isSupportSeatVentilation()) return "0"

        if (isSupportSeatVentilation()) {
            val seatVentilationRes = mGwmAdapterClient.getData(getSupportSeatVentilationDataId())
            Log.i(TAG, "isOpenSeatVentilation $seatVentilationRes")
            seatVentilationRes?.let {
                return it
            }
        }
        Log.i(TAG,"获取清凉模式状态:空")
        return null
    }

    // 小憩模式是否打开
    fun isRestModeOpen(): Boolean {
        return isSupportRestMode()
    }

    /**
     * 是否打开了放松模式
     */
    fun isRelaxModeOpen(): Boolean {
        // 座椅按摩是否打开
        var isOpenSeatMassage = isOpenSeatMessage()
        return isOpenSeatMassage
    }

    /**
     * 打开放松模式 等于打开座椅按摩
     */
    fun openRelaxMode(level: String = "3") {
        Log.i(TAG, "打开放松模式：$level")
        mGwmAdapterClient.setData(CAR_COMFORT_SETTING_SEAT_MESSAGE_LEVEL,level)
    }

    /**
     * 关闭放松模式
     */
    fun closeRelaxMode(level: String) {
        Log.i(TAG, "关闭放松模式：$level")
        mGwmAdapterClient.setData(CAR_COMFORT_SETTING_SEAT_MESSAGE_LEVEL,level)
    }

    /**
     * 获取放松模式状态
     * 1、优先使用 dataChangeSeatMassageRes
     * 2、如果不支持座椅按摩则默认返回 0
     * 3、读取存储的座椅按摩
     */
    fun getRelaxModeStatus() : String? {
        if(dataChangeSeatMassageRes!= null) {
            Log.i(TAG,"获取放松模式状态:$dataChangeSeatMassageRes")
            return dataChangeSeatMassageRes
        }
        if (!isSupportSeatMassage()) return "0"

        val seatMessageRes = mGwmAdapterClient.getData(CAR_COMFORT_SETTING_SEAT_MESSAGE_LEVEL)
        seatMessageRes?.let { return it }
        return null
    }
    //endregion

    //region 车机功能相关
    /**
     * 是否打开主驾座椅按摩
     */
    fun isOpenSeatMessage(): Boolean {
        var isOpenSeatMassage = false
        if (!isSupportSeatMassage()) return isOpenSeatMassage
        val seatMessageRes = mGwmAdapterClient.getData(CAR_COMFORT_SETTING_SEAT_MESSAGE_LEVEL)
        seatMessageRes?.let { if (it != "0" && it != "-1") isOpenSeatMassage = true }
        return isOpenSeatMassage
    }

    /**
     * 主驾座椅按摩功能
     */
    fun isSupportSeatMassage(): Boolean {
        val massageCfg = getSystemPropertyInt("persist.vendor.gwm.cfg.seat.massage.switch.type", -1)
        val massagePattern =
            getSystemPropertyInt("persist.vendor.gwm.cfg.driver.seat.massage.pattern", -1)
        return massageCfg > 1 && massagePattern > 0
    }


    /**
     * 方向盘加热
     * @return true：支持，false：不支持
     */
    fun isSupportSteerWheelHeating(): Boolean {
        val heat_cfg = getSystemPropertyInt("persist.vendor.gwm.cfg.steering.heating", -1)
        val type_cfg =
            getSystemPropertyInt("persist.vendor.gwm.cfg.wheel.heat.switch.type", -1)
        return (heat_cfg > 0) && (type_cfg > 1)
    }

    /**
     * 方向盘加热是否打开
     */
    fun isOpenSteerWheelHeating(): Boolean {
        if (!isSupportSteerWheelHeating()) return false
        val isOpenWheelHeatRes = mGwmAdapterClient.getData(CAR_BASIC_STEER_WHEEL_HEATING)
        return isOpenWheelHeatRes == "1"
    }

    /**
     * 主驾座椅加热 是否支持
     * @return true：支持，false：不支持
     */
    fun isSupportSeatHeat(): Boolean {
        // 3档 || 9档
        var threeLevelSupport = isSeatHeatSupport3Level()
        var nineLevelSupport = isSeatHeatSupport9Level()

        return threeLevelSupport || nineLevelSupport
    }

    /**
     * 主驾座椅加热是否支持3档
     */
    private fun isSeatHeatSupport3Level(): Boolean {
        return getSystemPropertyInt("persist.vendor.gwm.cfg.seat.heating", -1) > 0 &&
                getSystemPropertyInt("persist.vendor.gwm.cfg.frt.seat.heat.switch.type", -1) > 1 &&
                getSystemPropertyInt("persist.vendor.gwm.cfg.frt.seat.heating.level.type", -1) == 1
    }

    /**
     * 主驾座椅加热是否支持9档
     */
    private fun isSeatHeatSupport9Level(): Boolean {
        return getSystemPropertyInt("persist.vendor.gwm.cfg.seat.heating", -1) > 0 &&
                getSystemPropertyInt("persist.vendor.gwm.cfg.frt.seat.heat.switch.type", -1) > 1 &&
                getSystemPropertyInt("persist.vendor.gwm.cfg.frt.seat.heating.level.type", -1) > 1
    }

    /**
     * 主驾座椅加热是否打开
     */
    fun isOpenSeatHeat(): Boolean {

        var isSupportSeatHeat = isSupportSeatHeat()
        if (isSupportSeatHeat) {
            var openRes = mGwmAdapterClient.getData(CAR_BASIC_SEAT_HEATING_LEVEL)
            openRes?.let {
                // 0 座椅加热关闭
                if (it != "0" && it != "-1") {
                    // 座椅加热已打开
                    return true
                }
            }
        }
        return false
    }

    /**
     * 主驾座椅加热 打开
     */
    fun setSeatHeatingEnable(level: String = "3") {
        setVehicleSettingValue(CAR_BASIC_SEAT_HEATING_LEVEL, level);
    }

    /**
     * 主驾座椅加热 关闭
     */
    fun setSeatHeatingDisable() {
        setVehicleSettingValue(CAR_BASIC_SEAT_HEATING_LEVEL, "0")
    }

    /**
     * 是否支持香氛
     * @return true：支持，false：不支持
     */
    fun isSupportFragrance(): Boolean {
        return getSystemPropertyInt("persist.vendor.gwm.cfg.fragrance.system", -1) == 1
    }

    /**
     * 打开香氛
     */
    fun setFranganceEnable(level: String = "1") {
        setVehicleSettingValue(CAR_COMFORT_SETTING_FRAGRANCE_SYSTEM, level);
    }

    fun setFragranceDisable() {
        setVehicleSettingValue(CAR_COMFORT_SETTING_FRAGRANCE_SYSTEM, "0");
    }

    /**
     * 是否支持电动腰托
     * @return true：支持，false：不支持
     */
    fun isSupportWaistSupporter(): Boolean {
        return getSystemPropertyInt("persist.vendor.gwm.cfg.driver.waist.supporter", -1) > 0
    }

    /**
     * 是否打开了座椅通风
     */
    fun isOpenSeatVentilation(): Boolean {
        var isOpenSeatVentilation = false
        if (isSupportSeatVentilation()) {
            val seatVentilationRes = mGwmAdapterClient.getData(getSupportSeatVentilationDataId())
            Log.i(TAG, "isOpenSeatVentilation $seatVentilationRes")
            seatVentilationRes?.let {
                if (it != "0" && it != "-1") {
                    isOpenSeatVentilation = true
                }
            }
        }
        return isOpenSeatVentilation
    }

    /**
     * 是否支持座椅通风
     * 3档或者9档座椅通风 return true
     */
    fun isSupportSeatVentilation(): Boolean {
        return isSupportSeatVentilation3Level() || isSupportSeatVentilation9Level()
    }

    /**
     * 获取支持座椅通风的档位的DataId
     */
    private fun getSupportSeatVentilationDataId(): String {
//        if (isSupportSeatVentilation3Level()) {
//            return CAR_BASE_SEAT_VENTILATION_3_LEVEL
//        }
//        if (isSupportSeatVentilation9Level()) {
//            return CAR_BASE_SEAT_VENTILATION_9_LEVEL
//        }
        return CAR_BASE_SEAT_VENTILATION_9_LEVEL
    }

    /**
     * 是否支持3挡座椅通风
     */
    private fun isSupportSeatVentilation3Level(): Boolean {

        var varOne =
            getSystemPropertyInt("persist.vendor.gwm.cfg.frt.seat.cooling.frt.seat.massage", -1)
        var varTwo = getSystemPropertyInt("persist.vendor.gwm.cfg.driver.seat.cooling", -1)
        var varThree =
            getSystemPropertyInt("persist.vendor.gwm.cfg.frt.seat.cooling.level.type", -1)

        var isSuportSeatVentilation3Level =
            (varOne == 1 || varOne == 3 || varOne == 4) && varTwo == 1 && varThree == 1
        Log.i(TAG, "isSupportSeatVentilation3Level $isSuportSeatVentilation3Level")
        return isSuportSeatVentilation3Level
    }

    /**
     * 是否支持9档座椅通风
     */
    private fun isSupportSeatVentilation9Level(): Boolean {
        var varOne =
            getSystemPropertyInt("persist.vendor.gwm.cfg.frt.seat.cooling.frt.seat.massage", -1)
        var varTwo = getSystemPropertyInt("persist.vendor.gwm.cfg.driver.seat.cooling", -1)
        var varThree =
            getSystemPropertyInt("persist.vendor.gwm.cfg.frt.seat.cooling.level.type", -1)

        var isSupportSeatVentilation9Level =
            (varOne == 1 || varOne == 3 || varOne == 4) && varTwo == 1 && varThree == 2
        Log.i(TAG, "isSupportSeatVentilation9Level $isSupportSeatVentilation9Level")
        return isSupportSeatVentilation9Level
    }

    //endregion 车机功能相关

    fun sceneIsOpenWaist(): Boolean {
        // 是否支持电动腰托
        val isSupportWaistSupporter = isSupportWaistSupporter()
        if (!isSupportWaistSupporter) return false
        val waistLevel =
            mGwmAdapterClient.getData(CAR_COMFORT_SETTING_SEAT_WAIST_DIRECTION_CONFIG_ENABLE)
        Log.i(TAG, "isOpenWaistSupporter $waistLevel")
        return waistLevel == "-1" || waistLevel == "4"
    }

    suspend fun getDataCoroutines(dataId: String, data: Bundle): String? =
        suspendCancellableCoroutine { continuation ->
            Log.i(TAG, "getDataCoroutines $dataId $data")
            mGwmAdapterClient.getData(dataId, data, object : IDataChangedListener.Stub() {
                override fun onChanged(dataId: String?, dataValue: String?, p2: Bundle?) {
                    Log.i(TAG, "getDataCoroutines $dataId $dataValue")
                    if (dataId != null && dataValue != null) {
                        continuation.resume(dataValue)
                    } else {
                        continuation.resume(null)
                    }
                }
            })
        }

    /**
     * 获取设备SN
     */
    fun getVechileSN(context: Context): String {
        return getSystemPropertyString(VECHILE_SN, "")
    }

    /**
     * 查询车机可以设备VIN
     */
    fun getVechileVIN(context: Context): String {
        // TODO 需要还原
        return "LGWN2TXLBGA983361"//return getSystemPropertyString(VECHILE_VIN, "LGWN2TXLBGA983365")
    }

    /**
     * 车型ID
     */
    fun getVechileModelID(context: Context): String {
        return getSystemPropertyString(VECHILE_MODE_1, "test")
    }

    /**
     * 获取coffee os 版本
     * 默认 310
     */
    fun getCoffeeOSVersion(): Int {
        return getSystemPropertyInt(COFFEE_OS_VERSION, 310)
    }

    /**
     * 车外温度是否是大于38
     */
    fun isOver38(): Boolean {
        val temperaturerRes = mGwmAdapterClient.getData(CAR_BASIC_OUTSIDE_TEMP)
        Log.i(TAG, "isOver38 $temperaturerRes")
        try {
            if (temperaturerRes != null && temperaturerRes.toDouble() > 38F) {
                return true
            }
        } catch (e: Exception) {
            Log.i(TAG, "parseDouble isOver38: ", e)
        }
        return false
    }

    /**
     * 是否独自驾车
     */
    fun isSelfDrive(): Boolean {
        var result = false
        try {
            // car.ev_info.cycle_km_consume
            // car.basic.accumulated_odometer
            // CAR_OMS_FRS_OMS_NUMBER
            val oms_number = mGwmAdapterClient.getData("car.basic.total_odometer")
            Log.i(TAG, "isSelfDrive $oms_number")
            if (oms_number == "1") {
                result = true
            }
        } catch (e: Exception) {
            Log.i(TAG, "isSelfDrive: ", e)
            result = false
        }
//        try {
//            val seat_person_status_IntArray = mGwmAdapterClient.getData(CAR_BASIC_SEAT_PERSON_STATUS)
//            if(seat_person_status_IntArray.isNotEmpty()) {
//                // 判断intArray
//                val intArray = seat_person_status_IntArray.split(",").map { it.toInt() }.toIntArray()
//                // 遍历intArray数组，数组第一项为1且其他项为0，为独自驾驶 result = true
//                result = judgeSingleDriving(intArray)
//            }
//        } catch (e: Exception) {
//            Log.e(TAG, "isSelfDrive: ", e)
//        }

        return result
    }

    private fun judgeSingleDriving(arr: IntArray): Boolean {
        if (arr.isEmpty() || arr[0] != 1) {
            return false // 数组为空或第一项不为1
        }

        for (i in 1 until arr.size) {
            if (arr[i] != 0) {
                return false // 发现非零项
            }
        }

        return true // 所有条件满足
    }


    /**
     * 获取系统配置字
     * @param key
     * @param defaultValue
     * @return 返回Int类型
     */
    private fun getSystemPropertyInt(key: String, defaultValue: Int): Int {
        return try {
            val clazz = Class.forName("android.os.SystemProperties")
            val method =
                clazz.getDeclaredMethod("getInt", String::class.java, Int::class.javaPrimitiveType)
            method.isAccessible = true
            method.invoke(null, key, defaultValue) as Int
        } catch (e: Exception) {
            defaultValue
        }
    }

    /**
     * 获取系统配置字
     * @param key
     * @param
     */
    private fun getSystemPropertyString(key: String, defaultValue: String): String {
        return try {
            val clazz = Class.forName("android.os.SystemProperties")
            val method = clazz.getDeclaredMethod("get", String::class.java, String::class.java)
            method.isAccessible = true
            method.invoke(null, key, defaultValue) as String
        } catch (e: Exception) {
            defaultValue
        }
    }

    // 下发指令
    private fun setVehicleSettingValue(dataId: String, value: String) {
        if (mGwmAdapterClient != null) {
            mGwmAdapterClient.setData(dataId, value)
        }
    }

    // 下发指令 协程
    suspend fun setVehicleSettingValueSync(dataId: String, value: String) = suspendCancellableCoroutine<String?> {
        mGwmAdapterClient.setData(dataId, value, Bundle(), object : IDataChangedListener.Stub() {
            override fun onChanged(dataId: String?, dataValue: String?, p2: Bundle?) {
                Log.i(TAG, "setVehicleSettingValueSync $dataId $dataValue")
                if (dataValue != null) {
                    it.resume(dataValue)
                } else {
                    it.resume(null)
                }
            }
        })
    }


    /**
     * 读取数据
     */
    suspend fun getVehicleSettingValue(dataId: String): String {
        Log.i(TAG, "getVehicleSettingValue $dataId")
        return suspendCancellableCoroutine {  continuation ->
            mGwmAdapterClient.getData(dataId,null, object : IDataChangedListener.Stub() {
                override fun onChanged(dataId: String?, dataValue: String?, p2: Bundle?) {
                    Log.i(TAG, "getVechileSettingValue $dataId $dataValue")
                    if (dataId != null && dataValue != null) {
                        continuation.resume(dataValue)
                    } else {
                        continuation.resume("")
                    }
                }
            })
        }
    }
    /**
     * 方向盘加热 打开
     */
    fun setSteerWheelHeatingEnable() {
        setVehicleSettingValue(CAR_BASIC_STEER_WHEEL_HEATING,"1");
    }

    /**
     * 方向盘加热 关闭
     */
    fun setSteerWheelHeatingDisable() {
        setVehicleSettingValue(CAR_BASIC_STEER_WHEEL_HEATING, "0");
    }

    /**
     * 开启智能腰托
     */
    fun setSeatWaistDirectionConfigEnable(level: String = "1") {
        setVehicleSettingValue(CAR_COMFORT_SETTING_SEAT_WAIST_DIRECTION_CONFIG_ENABLE, level);
    }

    /**
     * 关闭智能腰托
     */
    fun setSeatWaistDirectionConfigDisable() {
        setVehicleSettingValue(CAR_COMFORT_SETTING_SEAT_WAIST_DIRECTION_CONFIG_ENABLE, "0");
    }

    /**
     * 开启座椅通风
     */
    fun setSeatVentilationConfigEnable(level: String = "3") {
        val dataId = getSupportSeatVentilationDataId();
        if (dataId.isNotBlank()){
            setVehicleSettingValue(dataId, level);
        }
    }

    /**
     * 关闭座椅通风
     */
    fun setSeatVentilationConfigDisable() {
        val dataId = getSupportSeatVentilationDataId();
        if (dataId.isNotBlank()){
            setVehicleSettingValue(dataId, "0");
        }
    }

    /**
     * 打开座椅按摩
     *  数据值定义：
     *      * 0: 关闭
     *      * 1：低
     *      * 2：中
     *      * 3：高
     *
     */
    fun setSeatMessageLevelConfigEnable(level: String =  "3") {
        setVehicleSettingValue(CAR_COMFORT_SETTING_SEAT_MESSAGE_LEVEL, level);
    }

    /**
     * 关闭座椅按摩
     *  数据值定义：
     *      * 0: 关闭
     *      * 1：低
     *      * 2：中
     *      * 3：高
     *
     */
    fun setSeatMessageLevelConfigDisable() {
        setVehicleSettingValue(CAR_COMFORT_SETTING_SEAT_MESSAGE_LEVEL, "0");
    }

    /**
     * 开启氛围灯
     *
     */
    fun setCarLightModeEnable(context: Context) {
        setVehicleSettingValue(CAR_LIGHT_SETTING_AMBIENT_LIGHT_SCENE_SELECTION, "4");
    }

    /**
     * 关闭氛围灯
     */
    fun setCarLightModeDisable(context: Context) {
        setVehicleSettingValue(CAR_LIGHT_SETTING_AMBIENT_LIGHT_SCENE_SELECTION, "0");
    }

    /**
     * 查询氛围灯
     */
    suspend fun getCarLightMode(context: Context): String {
        return getVehicleSettingValue(CAR_LIGHT_SETTING_AMBIENT_LIGHT_SCENE_SELECTION)
    }

    /**
     * 是否支持小憩模式
     */
    fun isSupportRestMode(): Boolean {
        mContext?.let {
            try {
                val gwmFeatureManager = it.getSystemService(GwmFeatureManager::class.java)
                if(gwmFeatureManager!=null) {
                    val isSupportCoffeeSpace =
                        gwmFeatureManager.isSupport("GWM_FEATURE_SW_THIRD_SPACE_COFFEE_SPACE_SUPPORT")
                    val isSupportRestMode =
                        gwmFeatureManager.isSupport("GWM_FEATURE_SW_THIRD_SPACE_REST_SPACE_REST_MODE_SUPPORT")
                    return isSupportCoffeeSpace && isSupportRestMode
                }else{
                    return false
                }
            } catch (e: Exception) {
                Log.i(TAG, "isSupportRestMode: ", e)
                return false
            }
        }
        return false
    }


    /**
     * 小憩模式：开/关  TODO V35和V4的调用方式是否一样
     *
     * TODO 调用小憩模式前，是否要读取当前模式是否是在小憩模式？如果是在小憩模式，则是关闭小憩模式？
     */
    fun setRestModeEnable(context: Context) {
        if(BuildConfig.PLATFORM_CODE == "V4"){
            // val uri = Uri.parse("gwmv4://izone/scene?sceneld=3&sceneOp=1")
            var uri = Uri.parse("gwmv4://izone/scene")
            val builder = uri.buildUpon()
            builder.appendQueryParameter("sceneId", "3")
            builder.appendQueryParameter("sceneOp", "1")
            uri= builder.build()

            val intent = Intent(Intent.ACTION_VIEW, uri)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            val options = ActivityOptions.makeBasic()
            options.launchDisplayId = 0
            context.startActivity (intent, options.toBundle())
        } else if(BuildConfig.PLATFORM_CODE == "V35"){
            if(isSupportRestMode()) {
                if (mGwmAdapterClient != null) {
                    /**
                     * [来源id,执行类型id,模式id,动作类型id,位置id,执行结果id]
                     * 参数1：来源id： 0:语音 其他待定
                     * 参数2：执行类型id: 0 通知第三空间，1，第三空间结果返回
                     * 参数3：模式id： 1 首页、2、沉浸模式、3、小憩模式
                     * 参数4：动作类型：0： 打开模式 1：关闭模式
                     */
                    val intParamsStr = "[0,0,3,0,0,0]"
                    mGwmAdapterClient.setData(CAR_COMFORT_SETTING_REST_MODE_ENABLE, intParamsStr, null,object : IDataChangedListener.Stub(){
                        override fun onChanged(p0: String?, p1: String?, p2: Bundle?) {
                            Log.i(TAG,"打开小憩模式 $p0 p1 = $p1 p2 = $p2")
                            if (p1 != null && p1.startsWith("[") && p1.endsWith("]")) {
                                val numbers = p1.trimStart('[').trimEnd(']')
                                    .split(", ")
                                    .map { it.toInt() } // 转换为 Int 列表 // 获取最后一项
                                val lastItem = numbers.last()
                                var errStr = "打开失败"
                                if(103 ==lastItem) {
                                    errStr = "打开失败，需要电源状态为ON"
                                } else if (102 == lastItem) {
                                    errStr = "打开失败，需要处于P挡状态"
                                } else if (105 == lastItem) {
                                    errStr = "打开失败，当前电量低"
                                } else if (104 == lastItem) {
                                    errStr = "打开失败，需要手刹锁止"
                                } else if (-1 == lastItem) {
                                    errStr = "打开失败"
                                }
                                GlobalScope.launch(Dispatchers.Main) {
                                    ToastUtil.makeText(context.applicationContext, errStr, Toast.LENGTH_SHORT).show()
                                }
                            } else {
                                Log.i(TAG, "setRestModeEnable: 打开小憩模式失败")
                            }
                        }
                    })
                }
            } else {
                Log.i(TAG, "setRestModeEnable: 不支持小憩模式")
            }
        }
    }

    /**
     * App Store查询App是否有升级信息
     * DATA_ID_APP_QUERY_APP
     */
    @Deprecated("已废弃 获取App升级信息改为由接口获取")
    fun queryAppInfoReq(context: Context) : Boolean {
        val bundle = Bundle().apply {
            putString("query", "queryUpdate")
            putString("packageName", context.packageName)
            putString("appVersion", BuildConfig.VERSION_CODE.toString())
        }
        var hasAppUpdate = false
        mGwmAdapterClient.getData(DATA_ID_APP_STORE_QUERY_INFO, bundle, object : IDataChangedListener.Stub() {
            override fun onChanged(dataId: String?, dataValue: String?, p2: Bundle?) {
                Log.i(TAG, "queryAppInfoReq $dataId $dataValue")
                hasAppUpdate = bundle.getBoolean("hasAppUpdate")
            }
        })
        return hasAppUpdate
    }

    /**
     * 跳转到 App Store
     * 启动到应用商店app的详情界面
     * @param packageName 目标App的包名
     */
    fun launchToGwmAppStoreDetail(context : Context) {
        val uri = Uri.parse("market://details?id=${context.packageName}")
        val intent = Intent(Intent.ACTION_VIEW, uri)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
        try {
            context.startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            Log.i("launchToGwmAppStoreDetail launch fail ", e.toString())
        }
    }

    /**
     * 设置音乐疗愈
     * 设置搜索播放某一首歌 彭显森的音乐疗愈
     */
    fun setMusicModeEnable(context: Context) {
        val bundle = Bundle()
        /**
         * extras 加入单曲循环模式"playMode":2
         * TODO: 需要确认 callSource 的非debug
         * 调用方，如语音VOICE，用于debug
         */
        val data =
            "{\"displayId\":0,\"mediaType\":17,\"callSource\":\"VOICE\",\"protocol\":\"earth\",\"extras\":{\"song\":\"音乐疗愈\",\"singer\":\"彭显森\"}}"
        bundle.putString("REQUEST_PARAMS", data)
        mGwmAdapterClient.setData("media.play.search", "", bundle)
    }

    /**
     * 设置音乐疗愈
     * 单曲循环模式
     */
    fun setMusicPlayModeEnable(context: Context) {
        val bundle = Bundle()
        /**
         * extras 加入单曲循环模式"playMode":2
         * TODO: 需要确认 callSource 的非debug
         * 调用方，如语音VOICE，用于debug
         */
        val data =
            "{\"displayId\":0,\"mediaType\":17,\"callSource\":\"VOICE\",\"protocol\":\"earth\",\"extras\":{\"playMode\":1}}"
        bundle.putString("REQUEST_PARAMS", data)
        mGwmAdapterClient.setData("media.base.play_set_mode", "", bundle)
    }

    /**
     * 播放本地音乐
     */
    fun setMusicPlayLocalMode(context: Context) {
        val bundle = Bundle()
        val data =
            "{\"displayId\":0,\"mediaType\":4,\"callSource\":\"VOICE\",\"protocol\":\"earth\",\"extras\":{}}"
        bundle.putString("REQUEST_PARAMS", data)
        GwmAdapterClient.getInstance().setData("media.play.auto", "", bundle)
    }

}