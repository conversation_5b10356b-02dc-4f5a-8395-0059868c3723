package com.healthlink.hms.sdks.gwmadapter

import android.app.ActivityOptions
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.gwm.android.adapter.IDataChangedListener
import com.gwm.android.adapter.client.GwmAdapterClient
import com.gwm.android.adapter.client.ServiceStateListener
import com.healthlink.hms.sdks.map.gwm.GWMMapManager
import com.healthlink.hms.sdks.map.gwm.GWMMapManagerKotCoroutines
import com.healthlink.hms.sdks.map.gwm.GWMWeatherLifeIndexDTO
import com.healthlink.hms.sdks.map.gwm.GWMWeatherRespArrDTO
import com.healthlink.hms.utils.MMKVUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.InputStreamReader
import java.util.regex.Matcher
import java.util.regex.Pattern


object GwmAdapterManager {

    const val mTag = "GwmAdapterManager"

    const val TBOX_CALL = "sys.tbox.cur_call_info"; // 3CALL当前状态信息
    const val TBOX_CALL_ACTION = "sys.tbox.call_action";
    const val AVM_DEVICE_SUPPORT = "sys.avm.device_support"; // 当前车型是否支持360全景功能 0 不支持 1 支持
    const val POWER_MODE = "car.basic.power_mode"; // 汽车电源状态
    const val ENGINE_STATE = "car.basic.engine_state";  //发动机工作状态
    const val GEAR_STATUS = "car.basic.gear_status";  //档位状态
    const val VECHILE_SPEED = "car.basic.vehicle_speed";  //车速
    const val VECHILE_SN = "persist.gwm.vehicle.sn"  // android 设备可以用SN
    const val VECHILE_VIN = "persist.gwm.vehicle.vin"  // android 设备可以用SN
    const val VECHILE_MODE_1 = "persist.gwm.car.mode1"  // 车型ID
    const val VECHILE_MODE_2 = "persist.gwm.car.mode2"  // 车型ID 预留字段 使用VECHILE_MODE_1就行
    const val COFFEE_OS_VERSION = "ro.vendor.gwm.coffee_os_version"  // 咖啡机版本
    const val SYSTEM_PROPERTY_WAIST_SUPPORT = "persist.vendor.gwm.cfg.driver.waist.supporter"  // 电动腰托配置字
    // 本次行程里程Key
    const val JOURNEY_ODOMETER = "car.basic.cur_journey_odometer"
    // 仪表盘总里程数据ID
    const val TOTAL_ODOMETER = "car.ev_info.total_odometer"



    /**
     * 方向盘加热：1 - 加热；0 - 关闭
     */
    const val CAR_BASIC_STEER_WHEEL_HEATING = "car.basic.steer_wheel_heating_enable"
    /**
     * 主驾座椅加热：1 - 加热；0 - 关闭
     */
    const val CAR_BASIC_SEAT_HEATING = "car.basic.steer_wheel_heating_enable"

    /**
     * 小憩模式：1 打开，0：关闭
     *
     */
    const val CAR_COMFORT_SETTING_REST_MODE_ENABLE = "car.comfort_setting.rest_mode_enable"
    /**
     * 智能腰托数据ID：
     * 数据值定义：
     * -1：信号丢失
     * 0：前
     * 1：后
     * 2：上
     * 3：下
     * 4: 释放
     */
    const val CAR_COMFORT_SETTING_SEAT_WAIST_DIRECTION_CONFIG_ENABLE="car.comfort.setting.seat_waist_direction_config"

    /**
     * car.comfort.setting.seat_massage_level 座椅按摩
     * 数据值定义：
     * 0: 关闭
     * 1：低
     * 2：中
     * 3：高
     */
    const val CAR_COMFORT_SETTING_SEAT_MESSAGE_LEVEL="car.comfort.setting.seat_massage_level"
    /**
     * 香氛系统
     * 数据值定义：
     * 0：关闭
     * 1：开启
     *  */
    const val CAR_COMFORT_SETTING_FRAGRANCE_SYSTEM = "car.comfort.setting.fragrance_system"
    /**
     * 氛围灯设置数据ID
     * 数据值定义：
     * 1 静态氛围灯
     * 2 动态氛围灯
     * 3 音乐律动
     * 4 呼吸灯
     * 5 流水灯
     * 数据类型：
     * Int
     */
    const val CAR_LIGHT_SETTING_AMBIENT_LIGHT_SCENE_SELECTION = "car.light_setting.ambient_light.scene_selection"

    private var mGwmAdapterClient: GwmAdapterClient = GwmAdapterClient.getInstance()

    /**
     * 服务是否联通
     */
    private var mServiceConnected = false

    //region 天气相关
    const val DATA_ID_LIFE_INDEX = "weather.life_index.current" // 当前生活指数
    const val KEY_LONGITUDE = "longitude"
    const val KEY_LATITUDE = "latitude"
    const val KEY_TYPE_UV = "uv" // 紫外线字数
    const val KEY_TYPE_FLU = "cough" // 感冒指数
    const val KEY_TYPE_AIR = "airQuality" // 空气质量指数
    var weatherLifeMap = mutableMapOf<String, String>()
    //endregion

    private var mContext: Context? = null

     fun initGwmAdapter(context: Context) {
        mContext = context
        val servers = arrayListOf("gwm_adapter_weather","gwm_adapter_media","gwm_adapter")
        mGwmAdapterClient.init(context, servers.toTypedArray(), object : ServiceStateListener {
            override fun onServiceConnected() {
                Log.i(mTag, "GwmAdapter is connected")
                mServiceConnected = true
            }

            override fun onServiceDisconnected() {
                Log.i(mTag, "GwmAdapter is disconnected")
                mServiceConnected = false
            }
        })

        // 监听车辆档位数据变化
        mGwmAdapterClient.registerDataChangeListener(GEAR_STATUS) { dataId, dataValue ->
            Log.i(mTag,"GwmAdapter data changed: key=$dataId, value=$dataValue")

            if (dataId == GEAR_STATUS && dataValue.isNotEmpty() && dataValue.equals("3")) {
                // 挂P档，存储当前时间戳
                MMKVUtil.storeLastPTime(System.currentTimeMillis().toString())
            }
        }
    }

    //region 天气服务接口
    private var adapterDataChangeCallback = object: IDataChangedListener.Stub() {
        override fun onChanged(dataId: String?, dataValue: String?, p2: Bundle?) {
            if (dataId == null || dataValue == null) {
                return
            }

            Log.i(mTag, "GwmAdapter data changed: key=$dataId, value=$dataValue")

            val dataValue = "{\"code\":\"0\",\"data\":[{\"date\":\"2024-07-16\",\"desc\":\"天气较好，较适宜进行各种运动，但因天气热，请适当减少运动时间，降低运动强度。\",\"level\":\"较适宜\",\"type\":\"sport\"},{\"date\":\"2024-07-16\",\"desc\":\"较不宜洗车，明天可能有雨，擦拭一新的汽车可能无法保持太长时间。\",\"level\":\"较不宜\",\"type\":\"cw\"},{\"date\":\"2024-07-16\",\"desc\":\"天气炎热，建议着短衫、短裙、短裤、薄型T恤衫等清凉夏季服装。\",\"level\":\"炎热\",\"type\":\"drsg\"},{\"date\":\"2024-07-16\",\"desc\":\"天气太热，不适合垂钓。\",\"level\":\"不宜\",\"type\":\"fsh\"},{\"date\":\"2024-07-16\",\"desc\":\"紫外线强度较弱，建议出门前涂擦SPF在12-15之间、PA+的防晒护肤品。\",\"level\":\"弱\",\"type\":\"uv\"},{\"date\":\"2024-07-16\",\"desc\":\"天气较好，微风，虽天气稍热，却仍适宜旅游，不要错过机会呦！\",\"level\":\"适宜\",\"type\":\"trav\"},{\"date\":\"2024-07-16\",\"desc\":\"天气条件不易诱发过敏，可放心外出，除特殊体质外，无需担心过敏问题。\",\"level\":\"不易发\",\"type\":\"ag\"},{\"date\":\"2024-07-16\",\"desc\":\"白天天气多云，同时会感到有些热，不舒适。\",\"level\":\"不舒适\",\"type\":\"comf\"},{\"date\":\"2024-07-16\",\"desc\":\"各项气象条件适宜，发生感冒机率较低。但请避免长期处于空调房间中，以防感冒。\",\"level\":\"少发\",\"type\":\"flu\"},{\"date\":\"2024-07-16\",\"desc\":\"气象条件对空气污染物稀释、扩散和清除无明显影响，易感人群应适当减少室外活动时间。\",\"level\":\"良\",\"type\":\"air\"},{\"date\":\"2024-07-16\",\"desc\":\"天气热，到中午的时候您将会感到有点热，因此建议在午后较热时开启制冷空调。\",\"level\":\"部分时间开启\",\"type\":\"ac\"},{\"date\":\"2024-07-16\",\"desc\":\"白天太阳辐射较强，建议佩戴透射比1级且标注UV380-UV400的浅色太阳镜\",\"level\":\"必要\",\"type\":\"gl\"},{\"date\":\"2024-07-16\",\"desc\":\"天气炎热，用防脱水防晒化妆品，少用粉底和胭脂，常补粉。\",\"level\":\"防脱水防晒\",\"type\":\"mu\"},{\"date\":\"2024-07-16\",\"desc\":\"天气不错，较适宜晾晒,赶紧把久未见阳光的衣物搬出来吸收一下太阳的味道吧！\",\"level\":\"适宜\",\"type\":\"airc\"},{\"date\":\"2024-07-16\",\"desc\":\"天气较好，路面干燥，交通气象条件良好，车辆可以正常行驶。\",\"level\":\"良好\",\"type\":\"ptfc\"},{\"date\":\"2024-07-16\",\"desc\":\"属中等强度紫外辐射天气，外出时应注意防护，建议涂擦SPF指数高于15，PA+的防晒护肤品。\",\"level\":\"中等\",\"type\":\"spi\"}],\"msg\":\"SUCCESS\"}"

            // 生活指数
            if (dataId == DATA_ID_LIFE_INDEX) {
                val type = object : TypeToken<GWMWeatherRespArrDTO<GWMWeatherLifeIndexDTO>>() {}.type
                val weatherLifeIndex: GWMWeatherRespArrDTO<GWMWeatherLifeIndexDTO> = Gson().fromJson(dataValue, type)
                if(weatherLifeIndex != null && weatherLifeIndex.code == "0" && weatherLifeIndex.msg == "SUCCESS") {
                    weatherLifeIndex.data?.let {
                        if(it.isNotEmpty()){
                            for (item in it) when (item.type) {
                                KEY_TYPE_UV -> {
                                    weatherLifeMap[item.type] = item.level
                                }
                                KEY_TYPE_FLU -> {
                                    weatherLifeMap[item.type] = item.level
                                }
                                KEY_TYPE_AIR -> {
                                    weatherLifeMap[item.type] = item.level
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    /**
     * 设置车辆档位
     */
    fun setGearStatus(level: String) {
        setVechileSettingValue(GEAR_STATUS, level);
    }

    /**
     * 生活指数
     * 当前需要从地图获取经纬度
     * 包含：紫外线、感冒、空气质量
     */
    fun getWeatherLifeIndex() {
        if (!GWMMapManager.isMapServiceConnected()) {
            mContext?.let {
                GWMMapManager.initGWMMapManager(it)
                return
            }
        }

        if (MMKVUtil.getLon() == 0.0 || MMKVUtil.getLat() == 0.0) {
            GWMMapManager.sendCurrentLocationAsyncReq()
            return
        }

        val data = Bundle().apply {
            putDouble(KEY_LONGITUDE, MMKVUtil.getLon())
            putDouble(KEY_LATITUDE, MMKVUtil.getLat())
        }

        // 当前生活指数
         mGwmAdapterClient.getData(DATA_ID_LIFE_INDEX,data,adapterDataChangeCallback)
    }

    // 数据监听回调封装成 suspend 函数
    private suspend fun getDataAsync(dataId: String, data: Bundle): String? =
        suspendCancellableCoroutine { continuation ->
            mGwmAdapterClient.getData(dataId, data, object : IDataChangedListener.Stub() {
                override fun onChanged(dataId: String?, dataValue: String?, p2: Bundle?) {
                    val dataValue = "{\"code\":\"0\",\"data\":[{\"date\":\"2024-07-16\",\"desc\":\"天气较好，较适宜进行各种运动，但因天气热，请适当减少运动时间，降低运动强度。\",\"level\":\"较适宜\",\"type\":\"sport\"},{\"date\":\"2024-07-16\",\"desc\":\"较不宜洗车，明天可能有雨，擦拭一新的汽车可能无法保持太长时间。\",\"level\":\"较不宜\",\"type\":\"cw\"},{\"date\":\"2024-07-16\",\"desc\":\"天气炎热，建议着短衫、短裙、短裤、薄型T恤衫等清凉夏季服装。\",\"level\":\"炎热\",\"type\":\"drsg\"},{\"date\":\"2024-07-16\",\"desc\":\"天气太热，不适合垂钓。\",\"level\":\"不宜\",\"type\":\"fsh\"},{\"date\":\"2024-07-16\",\"desc\":\"紫外线强度较弱，建议出门前涂擦SPF在12-15之间、PA+的防晒护肤品。\",\"level\":\"弱\",\"type\":\"uv\"},{\"date\":\"2024-07-16\",\"desc\":\"天气较好，微风，虽天气稍热，却仍适宜旅游，不要错过机会呦！\",\"level\":\"适宜\",\"type\":\"trav\"},{\"date\":\"2024-07-16\",\"desc\":\"天气条件不易诱发过敏，可放心外出，除特殊体质外，无需担心过敏问题。\",\"level\":\"不易发\",\"type\":\"ag\"},{\"date\":\"2024-07-16\",\"desc\":\"白天天气多云，同时会感到有些热，不舒适。\",\"level\":\"不舒适\",\"type\":\"comf\"},{\"date\":\"2024-07-16\",\"desc\":\"各项气象条件适宜，发生感冒机率较低。但请避免长期处于空调房间中，以防感冒。\",\"level\":\"少发\",\"type\":\"flu\"},{\"date\":\"2024-07-16\",\"desc\":\"气象条件对空气污染物稀释、扩散和清除无明显影响，易感人群应适当减少室外活动时间。\",\"level\":\"良\",\"type\":\"air\"},{\"date\":\"2024-07-16\",\"desc\":\"天气热，到中午的时候您将会感到有点热，因此建议在午后较热时开启制冷空调。\",\"level\":\"部分时间开启\",\"type\":\"ac\"},{\"date\":\"2024-07-16\",\"desc\":\"白天太阳辐射较强，建议佩戴透射比1级且标注UV380-UV400的浅色太阳镜\",\"level\":\"必要\",\"type\":\"gl\"},{\"date\":\"2024-07-16\",\"desc\":\"天气炎热，用防脱水防晒化妆品，少用粉底和胭脂，常补粉。\",\"level\":\"防脱水防晒\",\"type\":\"mu\"},{\"date\":\"2024-07-16\",\"desc\":\"天气不错，较适宜晾晒,赶紧把久未见阳光的衣物搬出来吸收一下太阳的味道吧！\",\"level\":\"适宜\",\"type\":\"airc\"},{\"date\":\"2024-07-16\",\"desc\":\"天气较好，路面干燥，交通气象条件良好，车辆可以正常行驶。\",\"level\":\"良好\",\"type\":\"ptfc\"},{\"date\":\"2024-07-16\",\"desc\":\"属中等强度紫外辐射天气，外出时应注意防护，建议涂擦SPF指数高于15，PA+的防晒护肤品。\",\"level\":\"中等\",\"type\":\"spi\"}],\"msg\":\"SUCCESS\"}"
                    if (dataId != null && dataValue != null) {
                        continuation.resume(dataValue) {
                            continuation.cancel()
                        }
                    } else {
                        continuation.resume(null) {
                            continuation.cancel()
                        }
                    }
                }
            })
        }

//    suspend fun sendWeatherLiveIndexReq() : Map<String, Any>? {
//        val weatherLifeMap = mutableMapOf<String, String>()
//        if (!GWMMapManagerKotCoroutines.isMapServiceConnected()) {
//            mContext?.let {
//                //TODO 这地方有问题，SceneManager()不应该初始化并传递
//                GWMMapManagerKotCoroutines.initMapManager(it, SceneManager())
//                return null
//            }
//        }
//
//        if (MMKVUtil.getLon() == 0.0 || MMKVUtil.getLat() == 0.0) {
////            GWMMapManagerKotCoroutines.sendCurrentLocationAsyncReq()
//            return null
//        }
//
//        val data = Bundle().apply {
//            putDouble(KEY_LONGITUDE, MMKVUtil.getLon())
//            putDouble(KEY_LATITUDE, MMKVUtil.getLat())
//        }
//
//        val dataValue = withContext(Dispatchers.IO) {
//            getDataAsync(DATA_ID_LIFE_INDEX, data)
//        }
//
//        if (dataValue != null) {
//            val type = object : TypeToken<GWMWeatherRespArrDTO<GWMWeatherLifeIndexDTO>>() {}.type
//            val weatherLifeIndex: GWMWeatherRespArrDTO<GWMWeatherLifeIndexDTO> = Gson().fromJson(dataValue, type)
//            if (weatherLifeIndex.code == "0" && weatherLifeIndex.msg == "SUCCESS") {
//                weatherLifeIndex.data?.let {
//                    if (it.isNotEmpty()) {
//                        for (item in it) when (item.type) {
//                            KEY_TYPE_UV, KEY_TYPE_FLU, KEY_TYPE_AIR -> {
//                                weatherLifeMap[item.type] = item.level
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        return weatherLifeMap
//    }

    //endregion

    //region 车机模式相关
    fun isWarnModeOpen(): Boolean {
        // 方向盘加热是否打开
        // 主驾座椅加热是否打开
        return false
    }

    fun isWristModeOpen(): Boolean {
        // 电动腰托是否打开
        return true
    }

    fun isRestModeOpen(): Boolean {
        // 小憩模式是否打开
        return false
    }

    fun isRelaxModeOpen(): Boolean {
        // 座椅按摩是否打开
        // 香氛是否打开
        return true
    }

    //enregion

    /**
     * 是否支持座椅按摩功能
     * 主驾座椅按摩的属性值
     */
    fun isSupportSeatMassage(): Boolean {
        val massageCfg = getSystemPropertyInt("persist.vendor.gwm.cfg.driver.seat.massage", -1)
        return massageCfg > 0
    }

    /**
     * 是否支持座椅通风
     * @return true：支持，false：不支持
     */
    fun isSupportSeatVentilate(): Boolean {
        val ventilateMassageCfg = getSystemPropertyInt("persist.vendor.gwm.cfg.frt.seat.cooling.frt.seat.massage", -1)
        val ventilateCfg = getSystemPropertyInt("persist.vendor.gwm.cfg.driver.seat.cooling", -1)
        return (ventilateMassageCfg > 0 && ventilateMassageCfg != 2) && (ventilateCfg > 0)
    }

    /**
     * 是否支持方向盘加热
     * @return true：支持，false：不支持
     */
    fun isSupportSteerWheelHeating(): Boolean {
        val heat_cfg = getSystemPropertyInt("persist.vendor.gwm.cfg.steering.heating", -1)
        val type_cfg =
            getSystemPropertyInt("persist.vendor.gwm.cfg.wheel.heat.switch.type", -1)
        return (heat_cfg > 0) && (type_cfg > 1)
    }

    /**
     * 是否支持座椅加热
     * @return true：支持，false：不支持
     */
    fun isSupportSeatHeat(): Boolean {
        val heat_cfg: Int = getSystemPropertyInt("persist.vendor.gwm.cfg.seat.heating", -1)
        val type_cfg: Int =
            getSystemPropertyInt("persist.vendor.gwm.cfg.frt.seat.heat.switch.type", -1)
        return (heat_cfg > 0) && (type_cfg > 1)
    }

    /**
     * 主驾座椅加热：1 - 加热；0 - 关闭
     * 主驾座椅加热 打开
     */
    fun setSetHeatingEnable(context: Context) {
        setVechileSettingValue(CAR_BASIC_SEAT_HEATING,"1");
    }

    /**
     * 主驾座椅加热 关闭
     */
    fun setSetHeatingDisable(context: Context) {
        setVechileSettingValue(CAR_BASIC_SEAT_HEATING, "0");
    }
    /**
     * 是否支持香氛
     * @return true：支持，false：不支持
     */
    fun isSupportFragrance(): Boolean {
        return getSystemPropertyInt("persist.vendor.gwm.cfg.fragrance.system", -1) == 1
    }

    /**
     * 打开香氛
     */
    fun setFranganceEnable(context: Context,level: String = "1") {
        setVechileSettingValue(CAR_COMFORT_SETTING_FRAGRANCE_SYSTEM,level);
    }

    fun setFragranceDisable(context: Context) {
        setVechileSettingValue(CAR_COMFORT_SETTING_FRAGRANCE_SYSTEM, "0");
    }

    /**
     * 是否支持电动腰托
     * @return true：支持，false：不支持
     */
    fun isSupportWaistSupporter(): Boolean {
        return getSystemPropertyInt("persist.vendor.gwm.cfg.driver.waist.supporter", -1) > 0
    }

    /**
     * 是否打开了电动腰托
     * 假设支持电动腰托
     */
    fun isOpenWaistSupporter(): Boolean {
        setSeatWaistDirectinConfigEnable(mContext!!, "1")
        return getSystemPropertyInt("persist.vendor.gwm.cfg.driver.waist.supporter", -1) > 0
    }
    /**
     * 设置电动腰托档位
     * @param level
     */
    fun setWaistSupporterLevel(level: String) {
//        setVechileSettingValue(CAR_COMFORT_SETTING_WAIST_SUPPORTER_LEVEL, level);
    }

    /**
     * 获取设备SN
     */
    fun getVechileSN(context: Context): String {
        return getSystemPropertyString(VECHILE_SN,"")
    }

    /**
     * 查询车机可以设备VIN
     */
    fun getVechileVIN(): String {
        return getSystemPropertyString(VECHILE_VIN,"")
    }

    /**
     * 车型ID
     */
    fun getVechileModelID(context: Context): String {
        return getSystemPropertyString(VECHILE_MODE_1,"test")
    }

    /**
     * 获取coffee os 版本
     */
    fun getCoffeeOSVersion(context: Context): Int {
        return getSystemPropertyInt(COFFEE_OS_VERSION,300)
    }

    /**
     * 获取系统配置字
     * @param key
     * @param defaultValue
     * @return 返回Int类型
     */
    private fun getSystemPropertyInt(key: String, defaultValue: Int): Int {
        return try {
            val clazz = Class.forName("android.os.SystemProperties")
            val method = clazz.getDeclaredMethod("getInt", String::class.java, Int::class.javaPrimitiveType)
            method.isAccessible = true
            method.invoke(null, key, defaultValue) as Int
        } catch (e: Exception) {
            defaultValue
        }
    }

    /**
     * 获取系统配置字
     * @param key
     * @param
     */
    private fun getSystemPropertyString(key: String, defaultValue: String): String {
        return try {
            val clazz = Class.forName("android.os.SystemProperties")
            val method = clazz.getDeclaredMethod("get", String::class.java, String::class.java)
            method.isAccessible = true
            method.invoke(null, key, defaultValue) as String
        } catch (e: Exception) {
            defaultValue
        }
    }

    //下发指令
    public fun setVechileSettingValue(dataId: String, value: String) {
        if (mGwmAdapterClient != null) {
            mGwmAdapterClient.setData(dataId, value);
        }
    }

    /**
     * 读取数据
     */
    public fun getVechileSettingValue(dataId: String): String {
        var result = ""
        if (mGwmAdapterClient != null) {
            mGwmAdapterClient.getData(dataId)
        }
        return result
    }
    /**
     * 方向盘加热 打开
     */
    fun setSteerWheelHeatingEnable(context: Context) {
        setVechileSettingValue(CAR_BASIC_STEER_WHEEL_HEATING,"1");
    }

    /**
     * 方向盘加热 关闭
     */
    fun setSteerWheelHeatingDisable(context: Context) {
        setVechileSettingValue(CAR_BASIC_STEER_WHEEL_HEATING, "0");
    }

    /**
     * 开启智能腰托
     */
    fun setSeatWaistDirectinConfigEnable(context: Context,level: String) {
        setVechileSettingValue(CAR_COMFORT_SETTING_SEAT_WAIST_DIRECTION_CONFIG_ENABLE, level);
    }

    /**
     * 关闭智能腰托
     */
    fun setSeatWaistDirectinConfigDisable(context: Context) {
        setVechileSettingValue(CAR_COMFORT_SETTING_SEAT_WAIST_DIRECTION_CONFIG_ENABLE, "0");
    }

    /**
     * 打开座椅按摩
     *  数据值定义：
     *      * 0: 关闭
     *      * 1：低
     *      * 2：中
     *      * 3：高
     *
     */
    fun setSeatMessageLevelConfigEnable(context: Context, level: String = "1") {
        setVechileSettingValue(CAR_COMFORT_SETTING_SEAT_MESSAGE_LEVEL, level);
    }

    /**
     * 关闭座椅按摩
     *  数据值定义：
     *      * 0: 关闭
     *      * 1：低
     *      * 2：中
     *      * 3：高
     *
     */
    fun setSeatMessageLevelConfigUnable(context: Context) {
        setVechileSettingValue(CAR_COMFORT_SETTING_SEAT_MESSAGE_LEVEL, "0");
    }

    /**
     * 开启氛围灯
     *
     */
    fun setCarLightModeEnable(context: Context) {
        setVechileSettingValue(CAR_LIGHT_SETTING_AMBIENT_LIGHT_SCENE_SELECTION, "4");
    }

    /**
     * 关闭氛围灯
     */
    fun setCarLightModeDisable(context: Context) {
        setVechileSettingValue(CAR_LIGHT_SETTING_AMBIENT_LIGHT_SCENE_SELECTION, "0");
    }

    /**
     * 查询氛围灯
     */
    fun getCarLightMode(context: Context): String {
        return getVechileSettingValue(CAR_LIGHT_SETTING_AMBIENT_LIGHT_SCENE_SELECTION)
    }


    /**
     * 小憩模式：开/关  TODO V35和V4的调用方式是否一样
     *
     * TODO 调用小憩模式前，是否要读取当前模式是否是在小憩模式？如果是在小憩模式，则是关闭小憩模式？
     */
    fun setRestModeEnable(context: Context) {
        var isV35 = false
        if(isV35) {
                setVechileSettingValue(CAR_COMFORT_SETTING_REST_MODE_ENABLE , "1");
        }else{
//            val uri = Uri.parse("gwmv4://izone/scene?sceneld=3&sceneOp=1")
            var uri = Uri.parse("gwmv4://izone/scene")
            val builder = uri.buildUpon()
            builder.appendQueryParameter("sceneId", "3")
            builder.appendQueryParameter("sceneOp", "1")
            uri= builder.build()

            val intent = Intent(Intent.ACTION_VIEW, uri)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            val options = ActivityOptions.makeBasic()
            options.launchDisplayId = 0
            context.startActivity (intent, options.toBundle())
        }
    }

    /**
     * 设置音乐疗愈
     * 设置搜索播放某一首歌 彭显森的音乐疗愈
     */
    fun setMusicModeEnable(context: Context) {
        val bundle = Bundle()
        /**
         * extras 加入单曲循环模式"playMode":2
         * TODO: 需要确认 callSource 的非debug
         * 调用方，如语音VOICE，用于debug
          */
        val data =
            "{\"displayId\":0,\"mediaType\":17,\"callSource\":\"VOICE\",\"protocol\":\"earth\",\"extras\":{\"song\":\"音乐疗愈\",\"singer\":\"彭显森\"}}"
        bundle.putString("REQUEST_PARAMS", data)
        mGwmAdapterClient.setData("media.play.search", "", bundle)
    }

    /**
     * 设置音乐疗愈
     * 单曲循环模式
     */
    fun setMusicPlayModeEnable(context: Context) {
        val bundle = Bundle()
        /**
         * extras 加入单曲循环模式"playMode":2
         * TODO: 需要确认 callSource 的非debug
         * 调用方，如语音VOICE，用于debug
         */
        val data =
            "{\"displayId\":0,\"mediaType\":17,\"callSource\":\"VOICE\",\"protocol\":\"earth\",\"extras\":{\"playMode\":1}}"
        bundle.putString("REQUEST_PARAMS", data)
        mGwmAdapterClient.setData("media.base.play_set_mode", "", bundle)
    }

    /**
     * 播放本地音乐
     */
    fun setMusicPlayLocalMode(context: Context) {
        val bundle = Bundle()
        val data =
            "{\"displayId\":0,\"mediaType\":4,\"callSource\":\"VOICE\",\"protocol\":\"earth\",\"extras\":{}}"
        bundle.putString("REQUEST_PARAMS", data)
        GwmAdapterClient.getInstance().setData("media.play.auto", "", bundle)
    }

}
