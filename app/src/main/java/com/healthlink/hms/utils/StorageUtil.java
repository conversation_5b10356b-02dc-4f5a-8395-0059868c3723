package com.healthlink.hms.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;


import com.healthlink.hms.base.BaseContext;

import java.lang.reflect.Type;

/**
 * 独立存储帮助类
 */
public class StorageUtil {

    private static final String MYINFO = "APP_INFO";
    private static StorageUtil mInstance;
    private SharedPreferences mDiskStorage;

    public StorageUtil() {
        if (BaseContext.sAppContext == null) {
            LogUtil.e("this BaseContext is not init");
            return;
        }
        /**
         * 私有模式（魔法数字改为API变量）
         */
        mDiskStorage = BaseContext.sAppContext.getSharedPreferences(MYINFO, Context.MODE_PRIVATE);
    }

    public static StorageUtil getInstance() {
        if (mInstance == null) {
            synchronized (StorageUtil.class) {
                if (mInstance == null) {
                    mInstance = new StorageUtil();
                }
            }
        }
        return mInstance;
    }

    /**
     * 将类保存到独立存储中
     *
     * @param clazz
     * @return
     */
    public <T> T getClass(Class<T> clazz) {
        if (mDiskStorage == null) {
            return null;
        }
        try {
            String value = mDiskStorage.getString(clazz.getName(), null);
            return JsonUtil.jsonToObject(value, clazz);
        } catch (Exception e) {

        }
        return null;
    }

    /**
     * 在独立存储中读取类
     *
     * @param clazz
     * @return
     */
    public <T> void putClass(T obj, Class<T> clazz) {
        if (mDiskStorage == null) {
            return;
        }
        try {
            String value = JsonUtil.objectToJson(obj);
            mDiskStorage.edit().putString(clazz.getName(), value).commit();
        } catch (Exception e) {
        }
    }

    /**
     * 将类保存到独立存储中
     *
     * @param key
     * @param clazz
     * @return
     */
    public <T> T getData(String key, Class<T> clazz) {
        if (mDiskStorage == null) {
            return null;
        }
        try {
            String value = mDiskStorage.getString(key, null);
            if (TextUtils.isEmpty(value)) {
                return null;
            }
            return JsonUtil.jsonToObject(value, clazz);
        } catch (Exception e) {

        }
        return null;
    }

    /**
     * 在独立存储中读取类
     *
     * @param key
     * @param obj
     * @return
     */
    public <T> void putData(String key, T obj) {
        if (mDiskStorage == null) {
            return;
        }
        if (obj == null) {
            mDiskStorage.edit().putString(key, "").commit();
        }
        try {
            String value = JsonUtil.objectToJson(obj);
            mDiskStorage.edit().putString(key, value).commit();
        } catch (Exception e) {
        }
    }


    /**
     * 获取列表
     *
     * @param key
     * @param clazz
     * @return
     */
    public <T> T getList(String key, Type clazz) {
        if (mDiskStorage == null) {
            return null;
        }
        try {
            String value = mDiskStorage.getString(key, null);
            if (TextUtils.isEmpty(value)) {
                return null;
            }
            return JsonUtil.jsonToObject(value, clazz);
        } catch (Exception e) {

        }
        return null;
    }

    /**
     * 根据键值获取数据
     *
     * @param key
     * @param defaultValue
     * @return
     */
    public boolean getBoolean(String key, boolean defaultValue) {
        if (mDiskStorage == null) {
            return defaultValue;
        }
        return mDiskStorage.getBoolean(key, defaultValue);
    }

    /**
     * 存储数据
     *
     * @param key
     * @param value
     */
    public void putBoolean(String key, Boolean value) {
        if (mDiskStorage == null) {
            return;
        }
        mDiskStorage.edit().putBoolean(key, value).commit();
    }

    /**
     * 根据键值获取数据
     *
     * @param key
     * @param defaultValue
     * @return
     */
    public String getString(String key, String defaultValue) {
        if (mDiskStorage == null) {
            return defaultValue;
        }
        return mDiskStorage.getString(key, defaultValue);
    }

    /**
     * 存储数据
     *
     * @param key
     * @param value
     */
    public void putString(String key, String value) {
        if (mDiskStorage == null) {
            return;
        }
        mDiskStorage.edit().putString(key, value).commit();
    }

    /**
     * 根据键值获取数据
     *
     * @param key
     * @param defaultValue
     * @return
     */
    public int getInt(String key, int defaultValue) {
        if (mDiskStorage == null) {
            return defaultValue;
        }
        return mDiskStorage.getInt(key, defaultValue);
    }

    /**
     * 存储数据
     *
     * @param key
     * @param value
     */
    public void putInt(String key, int value) {
        if (mDiskStorage == null) {
            return;
        }
        mDiskStorage.edit().putInt(key, value).commit();
    }

}
