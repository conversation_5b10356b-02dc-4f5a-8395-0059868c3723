package com.healthlink.hms.utils

import android.util.Log
import com.gwm.datatrack.client.DataTrackManager
import com.healthlink.hms.application.HmsApplication

object DataTrackUtil {
    const val TAG = "DataTrackUtil"
    //点击
    fun dtClick(action: String, pageMap: HashMap<String, String>, pageName: String = "") {
        try {
            Log.i(TAG, "dtClick: action: $action pageMap: $pageMap")
            postUITouchEvent(
                action,
                DataTrackManager.TouchEvent.TOUCH_EVENT_CLICK, pageName, pageMap
            )
        }catch (ex:Exception){
            Log.i(TAG,"DataTrackManager dtClick($action) fail")
        }
    }

    fun dtClick(action: String, pageName: String = "") {
        try {
            Log.i(TAG, "dtClick: $action")
            postUITouchEvent(
                action,
                DataTrackManager.TouchEvent.TOUCH_EVENT_CLICK, pageName, userIDMap()
            )
        }catch (ex:Exception){
            Log.i(TAG,"DataTrackManager dtClick($action) fail")
        }
    }

    fun dtClickWithMode(action: String, pageName: String = "") {
        try {
            Log.i(TAG, "dtClick: $action")
            postUITouchEvent(
                action,
                DataTrackManager.TouchEvent.TOUCH_EVENT_CLICK, pageName, userIDModeMap()
            )
        }catch (ex:Exception){
            Log.i(TAG,"DataTrackManager dtClickWithMode($action) fail")
        }
    }


    //曝光事件(自定义事件)
    fun dtScroll(action: String, pageMap: HashMap<String, String>) {
        try {
            Log.i(TAG, "dtScroll: action: $action pageMap: $pageMap")
            postCustomEvent(
                action,
                DataTrackManager.TouchEvent.TOUCH_EVENT_SCROLL, pageMap
            )
        }catch (ex:Exception){
            Log.i(TAG,"DataTrackManager dtScroll($action) fail")
        }
    }

    fun dtScroll(action: String) {
        try {
            Log.i(TAG, "dtScroll: $action")
            postCustomEvent(
                action,
                DataTrackManager.TouchEvent.TOUCH_EVENT_SCROLL, userIDMap()
            )
        }catch (ex:Exception){
            Log.i(TAG,"DataTrackManager dtScroll($action) fail")
        }

    }

    fun dtScrollWithMode(action: String, pageName: String = "") {
        try {
            Log.i(TAG, "dtScroll: $action")
            postCustomEvent(
                action,
                DataTrackManager.TouchEvent.TOUCH_EVENT_SCROLL, userIDMap()
            )
        }catch (ex:Exception){
            Log.i(TAG,"DataTrackManager dtScrollWithMode($action) fail")
        }
    }

    /**
     * 主动交互场景触发事件
     * @param action
     * @param pageMap 默认值 是userIdMap
     */
    fun dtTrigger(action: String, pageMap: HashMap<String, String> = userIDMap()) {
        try {
            Log.i(TAG, "dtTrigger: action: $action, pageMap: $pageMap")
            postCustomEvent(
                action,
                DataTrackManager.TouchEvent.TOUCH_EVENT_OTHER, pageMap
            )
        }catch (ex:Exception){
            Log.i(TAG,"DataTrackManager dtTrigger($action) fail")
        }
    }


    /**
     * 进入页面
     */
    fun dtEnterPage(action: String, pageMap: HashMap<String, String>) {
        try {
            Log.i(TAG, "dtEnterPage: action: $action pageMap: $pageMap")
            postPageEvent(
                action,
                DataTrackManager.PageAction.PAGE_ACTION_ENTER, pageMap
            )
        }catch (ex:Exception){
            Log.i(TAG,"DataTrackManager dtEnterPage($action) fail")
        }

    }

    fun dtEnterPageWithMode(action: String) {
        try {
            postPageEvent(
                action,
                DataTrackManager.PageAction.PAGE_ACTION_ENTER, userIDModeMap()
            )
        }catch (ex:Exception){
            Log.i(TAG,"DataTrackManager dtEnterPageWithMode($action) fail")
        }

    }


    fun dtEnterPage(action: String) {
        try {
            Log.i(TAG, "dtEnterPage: $action")
            postPageEvent(
                action,
                DataTrackManager.PageAction.PAGE_ACTION_ENTER, userIDMap()
            )
        }catch (ex:Exception){
            Log.i(TAG,"DataTrackManager dtEnterPage($action) fail")
        }

    }

    /**
     * 退出页面
     */
    fun dtExitPage(action: String, pageMap: HashMap<String, String>) {
        try {
            Log.i(TAG, "dtExitPage: action: $action pageMap: $pageMap")
            postPageEvent(
                action,
                DataTrackManager.PageAction.PAGE_ACTION_EXIT, pageMap
            )
        }catch (ex:Exception){
            Log.i(TAG,"DataTrackManager dtExitPage($action) fail")
        }
    }

    fun dtExitPage(action: String) {
        try {
            Log.i(TAG, "dtExitPage: $action")
            postPageEvent(
                action,
                DataTrackManager.PageAction.PAGE_ACTION_EXIT, userIDMap()
            )
        }catch (ex:Exception){
            Log.i(TAG,"DataTrackManager dtExitPage($action) fail")
        }
    }

    fun dtExitPageWithMode(action: String) {
        try {
            postPageEvent(
                action,
                DataTrackManager.PageAction.PAGE_ACTION_EXIT, userIDModeMap()
            )
        }catch (ex:Exception){
            Log.i(TAG,"DataTrackManager dtExitPageWithMode($action) fail")
        }
    }

    //点击事件
    /**
     * @param ctrlName 控件名称
    @param touchEventType 触控事件类型
    （1） { #@seeDataTrackManager.TouchEvent.TOUCH_EVENT_CLICK} 点击
    （2）
    { #DataTrackManager.TouchEvent.TouchEvent.TOUCH_EVENT_LONG_CLICK} 长
    按
    （3） { #DataTrackManager.TouchEvent.TouchEvent.TOUCH_EVENT_SCROLL} 滑
    动
    （3） { #DataTrackManager.TouchEvent.TouchEvent.TOUCH_EVENT_OTHER} 其
    它
    @param pageName 页面名称
    @param args 扩展参数
    @return 操作结果
     */
    private fun postUITouchEvent(
        ctrlName: String, touchEventType: Int,
        pageName: String, args: HashMap<String, String>
    ) {
        try {
            DataTrackManager.getInstance().postUITouchEvent(
                ctrlName,
                touchEventType, pageName, args
            )
        }catch (ex:Exception){
            Log.i(TAG,"DataTrackManager postUITouchEvent($ctrlName) fail")
        }
    }

    //界面操作事件
    /**
     * @param pageName 页面名称
    @param pageAction 页面动作
    （1） { #DataTrackManager.PageAction.PAGE_ACTION_ENTER} 页面进入
    （2） { #DataTrackManager.PageAction.PAGE_ACTION_EXIT} 页面退出
    @param args 扩展参数
    @return 操作结果
     */
    private fun postPageEvent(pageName: String, pageAction: Int, args: HashMap<String, String>) {
        try {
            DataTrackManager.getInstance().postPageEvent(
                pageName,
                pageAction, args
            )
        }catch (ex:Exception){
            Log.i(TAG,"DataTrackManager postPageEvent($pageName) fail")
        }
    }

    /**
     * 发送自定义事件埋点
    @param customEventName 自定义事件名称
    @param args 扩展参数，最大长度限制 500，超过长度，args 将被全部丢弃输
    出，异常 error 替换
    @return 操作结果
     */
    private fun postCustomEvent(pageName: String, pageAction: Int, args: HashMap<String, String>) {
        try {
            DataTrackManager.getInstance().postCustomEvent(
                pageName,
                args
            )
        }catch (ex:Exception){
            Log.i(TAG,"DataTrackManager postCustomEvent($pageName) fail")
        }
    }


    fun userIDMap(userID: String = ""): HashMap<String, String> {
        var map = HashMap<String, String>()
        map.put("userid#1", userID.ifBlank { MMKVUtil.getUserId().toString() })
        return map
    }

    /**
     * 设置页用户通知打开与关闭
     * state: 1打开通知 2关闭通知
     */
    fun userIDAnNotifyMap(userID: String = "", isOpen: String): HashMap<String, String> {
        var map = HashMap<String, String>()
        map.put("userid#1", userID.ifBlank { MMKVUtil.getUserId().toString() })
        map.put("state#1", isOpen)
        return map
    }

    fun userIDModeMap(): HashMap<String, String> {
        var map = HashMap<String, String>()
        map.put("userid#1", MMKVUtil.getUserId().toString())
        var mode: Int = 1;
        if (MMKVUtil.isVisitorMode()) {
            mode = 2
        } else if (HmsApplication.isPrivacyModeEnabled()) {
            mode = 3
        }
        HmsApplication.isPrivacyModeEnabled()
        map.put("state#1", mode.toString())
        return map
    }
}
