package com.healthlink.hms.utils

import android.app.Activity
import android.app.Dialog
import android.app.UiModeManager
import android.content.Context
import android.graphics.drawable.Animatable2
import android.graphics.drawable.AnimatedVectorDrawable
import android.graphics.drawable.Drawable
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.View.GONE
import android.view.View.MeasureSpec
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.view.WindowManager
import android.view.animation.AccelerateDecelerateInterpolator
import android.webkit.RenderProcessGoneDetail
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebSettings
import android.webkit.WebView
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.ScrollView
import android.widget.TextView
import androidx.annotation.LayoutRes
import androidx.constraintlayout.widget.ConstraintLayout
import com.healthlink.hms.R
import com.healthlink.hms.activity.viewmodel.MainDataModel
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.views.AnyViewOutlineProvider
import com.healthlink.hms.views.ImmersiveDialog
import com.healthlink.hms.views.dialog.HmsDialog
import com.hieupt.android.standalonescrollbar.StandaloneScrollBar
import com.hieupt.android.standalonescrollbar.attachTo
import com.hieupt.android.standalonescrollbar.view.ScrollView2
import com.just.agentweb.AgentWeb
import com.just.agentweb.BaseIndicatorView
import com.just.agentweb.IndicatorController
import com.just.agentweb.WebChromeClient
import com.just.agentweb.WebViewClient
import me.jessyan.autosize.AutoSizeCompat


/**
 * Created by imaginedays on 2024/7/12
 * 对话框单例
 */
object HMSDialogUtils {
    private var dialog: HmsDialog? = null
    private const val TAG = "HMSDialogUtils"
    /**
     * 显示自定义对话框
     * @param layoutId 布局必须包含 hms_dialog_message TextView positiveButton Button negativeButton Button
     * @param msg 显示消息
     * @param positiveBtnTitle 左边确认按钮文字
     * @param negativeBtnTitle 右边取消按钮文字
     */
    fun showHMSDialog(
        context: Context,
        @LayoutRes layoutId: Int,
        msg: String,
        positiveBtnTitle: String,
        negativeBtnTitle: String,
        btnClickCallback: (isPositive: Boolean) -> Unit
    ) {
        // 如果对话框已经显示，先关闭它
        try {
            if ((dialog != null) && dialog!!.isShowing) {
                dialog!!.dismiss();
            }
        } catch (e: Exception) {
            e.printStackTrace();
        } finally {
            dialog = null;
        }

        // 创建新的对话框
        dialog = ImmersiveDialog(
            context,
            R.style.MyDialogStyle
        )
        var view: View = LayoutInflater.from(context).inflate(layoutId, null)
        val contentView = view.findViewById<ViewGroup>(R.id.dialog_content)
        // 设置对话框标题
        val titleTextView: TextView = view.findViewById(R.id.hms_dialog_message)
        titleTextView.text = msg

        // 设置确认按钮
        val positiveButton: Button = view.findViewById(R.id.positiveButton)
        positiveButton.text = positiveBtnTitle
        positiveButton.setOnClickListener {
            dialog?.dismiss()
            btnClickCallback(true)
        }

        // 设置取消按钮
        val negativeButton: Button = view.findViewById(R.id.negativeButton)
        negativeButton.text = negativeBtnTitle
        negativeButton.setOnClickListener {
            dialog?.dismiss()
            btnClickCallback(false)
        }

        dialog?.setContentView(view)
        dialog?.setCanceledOnTouchOutside(true)
        // 使对话框全屏
        // dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setContentView(view)
        dialog?.setOnShowListener {
            doDialogAnima()
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    dialog?.dismiss()
                }
                false
            }
        }

//        dialog?.show()
        if(context is Activity && !context.isFinishing && !context.isDestroyed){
            dialog?.show()
        } else {
            Log.i("HMSDialogUtils", "showHMSDialog context is not activity")
        }
    }

    /**
     * 查看已绑定电话医生的对话框
     * @param layoutId 布局必须包含 hms_dialog_message TextView positiveButton Button negativeButton Button
     * @param msg 显示消息
     * @param positiveBtnTitle 左边确认按钮文字
     * @param negativeBtnTitle 右边取消按钮文字
     */
    fun showHMSDoctorPhoneExitsDialog(
        context: Context,
        @LayoutRes layoutId: Int,
        msg: String,
        positiveBtnTitle: String,
        btnClickCallback: (isPositive: Boolean) -> Unit
    ) {
        // 如果对话框已经显示，先关闭它
        try {
            if ((dialog != null) && dialog!!.isShowing) {
                dialog!!.dismiss();
            }
        } catch (e: Exception) {
            e.printStackTrace();
        } finally {
            dialog = null;
        }

        // 创建新的对话框
        dialog = ImmersiveDialog(
            context,
            R.style.MyDialogStyle
        )
        var view: View = LayoutInflater.from(context).inflate(layoutId, null)
        val contentView: ViewGroup = view.findViewById(R.id.dialog_content);
        // 设置对话框标题
        val titleTextView: TextView = view.findViewById(R.id.hms_dialog_message)
        titleTextView.text = msg

        // 设置确认按钮
        val positiveButton: Button = view.findViewById(R.id.positiveButton)
        positiveButton.text = positiveBtnTitle
        positiveButton.setOnClickListener {
            btnClickCallback(true)
            dialog?.dismiss()
        }

        // 设置取消按钮
        val negativeButton: Button? = view.findViewById(R.id.negativeButton)
        negativeButton?.setOnClickListener {
            btnClickCallback(false)
            dialog?.dismiss()
        }

        dialog?.setContentView(view)
        dialog?.setOnShowListener {
            doDialogAnima()
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom))||(!(event.x.toInt() in contentView.left..contentView.right)))) {
                    dialog?.dismiss()
                }
                false
            }
        }
        // 使对话框全屏
        // dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT
        )

        dialog?.show()
    }

    /**
     * 显示电话医生绑定对话框
     * @param msg 显示消息
     * @param positiveBtnTitle 左边确认按钮文字
     * @param negativeBtnTitle 右边取消按钮文字
     */
    fun showHMSDialogWithTips(
        context: Context,
        @LayoutRes layoutId: Int,
        msg: String,
        tip: String,
        positiveBtnTitle: String,
        negativeBtnTitle: String,
        btnClickCallback: (isPositive: Boolean) -> Unit
    ) {
        // 如果对话框已经显示，先关闭它
        try {
            if ((dialog != null) && dialog!!.isShowing) {
                dialog!!.dismiss();
            }
        } catch (e: Exception) {
            e.printStackTrace();
        } finally {
            dialog = null;
        }

        // 创建新的对话框
        dialog = HmsDialog(context, R.style.MyDialogStyle)
        var view: View = LayoutInflater.from(context).inflate(layoutId, null)
        val contentView = view.findViewById<ViewGroup>(R.id.dialog_content)
        // 设置对话框标题
        val titleTextView: TextView = view.findViewById(R.id.hms_dialog_message)
        titleTextView.text = msg

        // 设置提示
        val tipTextView: TextView = view.findViewById(R.id.hms_dialog_tip)
        tipTextView.text = tip

        // 设置确认按钮
        val positiveButton: Button = view.findViewById(R.id.positiveButton)
        positiveButton.text = positiveBtnTitle
        positiveButton.setOnClickListener {
            btnClickCallback(true)
            dialog?.dismiss()
        }

        // 设置取消按钮
        val negativeButton: Button = view.findViewById(R.id.negativeButton)
        negativeButton.text = negativeBtnTitle
        negativeButton.setOnClickListener {
            btnClickCallback(false)
            dialog?.dismiss()
        }

        dialog?.setContentView(view)

        // 使对话框全屏
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setContentView(view)
        dialog?.setOnShowListener {
            doDialogAnima()
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    dialog?.dismiss()
                }
                false
            }
        }
        dialog?.show()
    }

    fun showHMSNotiDialog(
        context: Context,
        @LayoutRes layoutId: Int,
        title: String?,
        msg: String,
        positiveBtnTitle: String,
        btnClickCallback: (isPositive: Boolean) -> Unit
    ) {
        // 如果对话框已经显示，先关闭它
        try {
            if ((dialog != null) && dialog!!.isShowing) {
                dialog!!.dismiss();
            }
        } catch (e: Exception) {
            e.printStackTrace();
        } finally {
            dialog = null;
        }

        // 创建新的对话框
        dialog = ImmersiveDialog(
            context,
            R.style.MyDialogStyle
        )
        var view: View = LayoutInflater.from(context).inflate(layoutId, null)
        val contentView = view.findViewById<RelativeLayout>(R.id.dialog_content)
        // 设置对话框标题
        val msgTextView: TextView = view.findViewById(R.id.hms_dialog_message)
        msgTextView.text = msg

        if (!title.isNullOrEmpty()) {
            val titleTextView: TextView = view.findViewById(R.id.hms_dialog_title)
            titleTextView.text = title
        }

        // 设置确认按钮
        val positiveButton: Button = view.findViewById(R.id.positiveButton)
        positiveButton.text = positiveBtnTitle
        positiveButton.setOnClickListener {
            btnClickCallback(true)
            dialog?.dismiss()
        }
        dialog?.setContentView(view)
        dialog?.setOnShowListener {
            doDialogAnima()
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    dialog?.dismiss()
                }
                false
            }
        }
        // 使对话框全屏
        // dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setContentView(view)
        dialog?.show()
    }


    fun showHMSNotiDialogNew(
        context: Context,
        @LayoutRes layoutId: Int,
        title: String?,
        msg: String,
        positiveBtnTitle: String,
        btnClickCallback: (isPositive: Boolean) -> Unit
    ) {
        // 如果对话框已经显示，先关闭它
        try {
            if ((dialog != null) && dialog!!.isShowing) {
                dialog!!.dismiss();
            }
        } catch (e: Exception) {
            e.printStackTrace();
        } finally {
            dialog = null;
        }

        // 创建新的对话框
        dialog = ImmersiveDialog(
            context,
            R.style.MyDialogStyle
        )
        var view: View = LayoutInflater.from(context).inflate(layoutId, null)
        val contentView = view.findViewById<RelativeLayout>(R.id.dialog_content)
        var webScroll = view.findViewById<ScrollView2>(R.id.data_usage_container_scroll)
        val scrollbar = view.findViewById<StandaloneScrollBar>(R.id.scrollbar)
        scrollbar.attachTo(webScroll)
        scrollbar.customTrackDrawable = null
        // 设置对话框标题
        val msgTextView: TextView = view.findViewById(R.id.hms_dialog_message)
        msgTextView.text = msg

        if (!title.isNullOrEmpty()) {
            val titleTextView: TextView = view.findViewById(R.id.hms_dialog_title)
            titleTextView.text = title
        }

        // 设置确认按钮
        val positiveButton: Button = view.findViewById(R.id.positiveButton)
        positiveButton.text = positiveBtnTitle
        positiveButton.setOnClickListener {
            btnClickCallback(true)
            dialog?.dismiss()
        }
        dialog?.setContentView(view)
        dialog?.setOnShowListener {
            doDialogAnima()
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    dialog?.dismiss()
                }
                false
            }
        }
        // 使对话框全屏
        // dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setContentView(view)
        dialog?.show()
    }

    /**
     * 车机功能模式对话框
     */
    fun showHMSModeDialog(
        context: Context,
        isTowFun: Boolean,
        modeTitle: String,
        oneModeIconName: Int?,
        twoModeIconName: Int?,
        modeTips: String,
        positiveBtnTitle: String,
        negativeBtnTitle: String,
        btnClickCallback: (isPositive: Boolean) -> Unit
    ) {
        // 如果对话框已经显示，先关闭它
        try {
            if ((dialog != null) && dialog!!.isShowing) {
                dialog!!.dismiss();
            }
        } catch (e: Exception) {
            e.printStackTrace();
        } finally {
            dialog = null;
        }

        dialog = HmsDialog(context,R.style.MyDialogStyle)
        var view: View = LayoutInflater.from(context).inflate(R.layout.hms_dialog_open2mode, null)
        val contentView = view.findViewById<ViewGroup>(R.id.dialog_content)
        // 设置对话框标题
        val titleTextView: TextView = view.findViewById(R.id.mode_title)
        titleTextView.text = modeTitle

        view.findViewById<ImageView>(R.id.icon_right).visibility = if (isTowFun) VISIBLE else GONE
        // 是否为两个icon
        if (isTowFun) {
            view.findViewById<ImageView>(R.id.icon_left)
                .setImageResource(oneModeIconName!!.toInt())
            view.findViewById<ImageView>(R.id.icon_right)
                .setImageResource(twoModeIconName!!.toInt())
        } else {
            val iconName = oneModeIconName ?: twoModeIconName
            iconName?.let {
                view.findViewById<ImageView>(R.id.icon_left)
                    .setImageResource(it)
            }
        }


        // open tips
        val modeTipsTextView: TextView = view.findViewById(R.id.mode_tips)
        modeTipsTextView.text = modeTips

        // 设置确认按钮
        val positiveButton: Button = view.findViewById(R.id.positiveButton)
        positiveButton.text = positiveBtnTitle
        positiveButton.setOnClickListener {
            btnClickCallback(true)
            dialog?.dismiss()
        }

        // 设置取消按钮
        val negativeButton: Button = view.findViewById(R.id.negativeButton)
        negativeButton.text = negativeBtnTitle
        negativeButton.setOnClickListener {
            btnClickCallback(false)
            dialog?.dismiss()
        }

//        val layoutContainer = view.findViewById<FrameLayout>(R.id.fl_container)
//        layoutContainer.setOnClickListener {
//            dialog?.dismiss()
//        }

        // 使对话框全屏
        //dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setContentView(view)
//        dialog?.window?.setLayout(
//            WindowManager.LayoutParams.MATCH_PARENT,
//            WindowManager.LayoutParams.MATCH_PARENT
//        )
        dialog?.setOnShowListener {
            doDialogAnima()
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    btnClickCallback(false)
                    dialog?.dismiss()

                }
                false
            }
        }
        dialog?.setCanceledOnTouchOutside(true)
        dialog?.show()
    }

    /**
     * 车机功能模式对话框2 紧急救援
     */
    fun showHMSModeDialog2(
        context: Context,
        isTowFun: Boolean,
        modeTitle: String,
        oneModeIconName: Int,
        twoModeIconName: Int?,
        modeTips: String,
        positiveBtnTitle: String,
        negativeBtnTitle: String,
        btnClickCallback: (isPositive: Boolean) -> Unit
    ) {
        // 如果对话框已经显示，先关闭它
        try {
            if ((dialog != null) && dialog!!.isShowing) {
                dialog!!.dismiss();
            }
        } catch (e: Exception) {
            e.printStackTrace();
        } finally {
            dialog = null;
        }

        // 创建新的对话框
        dialog = HmsDialog(context,R.style.MyDialogStyle)
        var view: View =
            LayoutInflater.from(context).inflate(R.layout.hms_dialog_open2mode_1button, null)
        val contentView = view.findViewById<ViewGroup>(R.id.dialog_content)
        // 设置对话框标题
        val titleTextView: TextView = view.findViewById(R.id.mode_title)
        titleTextView.text = modeTitle

        // 是否为两个icon
        view.findViewById<ImageView>(R.id.icon_right).visibility =
            if (isTowFun) VISIBLE else GONE

        if (!isTowFun) {
            view.findViewById<ImageView>(R.id.icon_left).setImageResource(oneModeIconName.toInt())
        } else {
            if (twoModeIconName != null) {
                view.findViewById<ImageView>(R.id.icon_left)
                    .setImageResource(oneModeIconName.toInt())
                view.findViewById<ImageView>(R.id.icon_right)
                    .setImageResource(twoModeIconName!!.toInt())
            }
        }

        // open tips
        val modeTipsTextView: TextView = view.findViewById(R.id.mode_tips)
        modeTipsTextView.text = modeTips

        // 设置确认按钮
        val positiveButton: Button = view.findViewById(R.id.positiveButton)
        positiveButton.text = positiveBtnTitle
        positiveButton.setOnClickListener {
            btnClickCallback(true)
            dialog?.dismiss()
        }

        val layoutContainer = view.findViewById<FrameLayout>(R.id.fl_container)
        layoutContainer.setOnClickListener {
            dialog?.dismiss()
        }

        // 使对话框全屏
       // dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setContentView(view)
//        dialog?.window?.setLayout(
//            ViewGroup.LayoutParams.MATCH_PARENT,
//            ViewGroup.LayoutParams.MATCH_PARENT
//        )
        dialog?.setOnShowListener {
            doDialogAnima()
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    dialog?.dismiss()
                }
                false
            }
        }
        dialog?.show()
    }

    /**
     * 用户协议、隐私政策对话框
     */
    fun showPrivacyContentDialog(
        context: Context,
        title: String,
        url: String,
        dialog: ImmersiveDialog,
        mainDataModel: MainDataModel
    ) {
        // 创建新的对话框
//        dialog = ImmersiveDialog(
//            context,
//            R.style.MyDialogStyle
//        )
        dialog.setUpSystemBar()
        var view: View =
            LayoutInflater.from(context).inflate(R.layout.hms_dialog_privacy_content, null)
        var privacyContent = view.findViewById<RelativeLayout>(R.id.privacy_agree_content)
        var privacyWebLayout1 = view.findViewById<LinearLayout>(R.id.web_privacy1)
        var privacyScroll1 = view.findViewById<ScrollView2>(R.id.scroll_web_privacy1)
        val scrollbar = view.findViewById<StandaloneScrollBar>(R.id.scrollbar)
        scrollbar.attachTo(privacyScroll1)
        scrollbar.customTrackDrawable = null
        scrollbar.isAlwaysShown = false

        val mContainer = view.findViewById<FrameLayout>(R.id.privacy_agree_scroll)
        val inflater = LayoutInflater.from(context)
        val loadingView = inflater.inflate(R.layout.activity_detail_loading, mContainer, false)
        loadingView.setBackgroundColor(context.getColor(R.color.personal_setting_radio_trans))
        val netErrorView =
            inflater.inflate(R.layout.activity_detail_no_network_for_dialog, mContainer, false)
        netErrorView.setBackgroundColor(context.getColor(R.color.personal_setting_radio_trans))
        var hasErr = false
        var height1 = 0
        // 设置对话框标题
        val contentTitleTextView = view.findViewById<TextView>(R.id.content_dialog_title)
        contentTitleTextView.text = title

        if (netErrorView != null) {
            mContainer.removeView(netErrorView)
        }
        privacyScroll1.visibility = View.INVISIBLE
        showLoading(mContainer, context, loadingView)
        var mAgentWeb = AgentWeb
            .with(context as Activity)
            .setAgentWebParent(privacyWebLayout1, LinearLayout.LayoutParams(-1, -1))
            .closeIndicator()
            .setWebViewClient(WebViewClient())
            .setWebChromeClient(WebChromeClient()) //title重定向
            .createAgentWeb()
            .ready().go(url)
        mAgentWeb.webCreator.webView.setOnLongClickListener { true }
        mAgentWeb.agentWebSettings.webSettings.let {
            it.useWideViewPort = true
            it.loadWithOverviewMode = true // 缩放至屏幕的大小
            it.domStorageEnabled = true
            it.allowContentAccess = true //是否可访问Content Provider的资源，默认值 true
            it.allowFileAccess = true // 是否可访问本地文件，默认值 true
            it.javaScriptEnabled = true
        } //将图片调整到适合webview的大小
        val webView = mAgentWeb.webCreator.webView
        webView.setBackgroundColor(context.getColor(R.color.agent_web_bg_color))
        webView.isVerticalScrollBarEnabled = false
        webView.webViewClient = object : WebViewClient() {
            override fun shouldInterceptRequest(
                p0: WebView?,
                p1: WebResourceRequest?
            ): WebResourceResponse? {
                if (!HmsApplication.isNetworkConn()) {
                    // 返回自定义的错误响应，模拟网络错误
                    val errorResponse = WebResourceResponse(
                        "text/html", "utf-8", null
                    )
                    return errorResponse
                }
                return super.shouldInterceptRequest(p0, p1)
            }
            override fun onPageFinished(p0: WebView?, p1: String?) {
                if (!hasErr) {
                    privacyScroll1.postDelayed({
                        privacyScroll1.visibility = VISIBLE
                    },500)

                    privacyScroll1.postDelayed({
                        privacyScroll1.scrollTo(0,1)

                    },800)
                    p0?.postDelayed({
                        // 动态调整 WebView 的高度
                        p0.measure(
                            MeasureSpec.makeMeasureSpec(
                                MeasureSpec.UNSPECIFIED, MeasureSpec.UNSPECIFIED
                            ),
                            MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
                        )
                        if (height1 == 0)
                            height1 = p0.measuredHeight
                        val layoutParams = p0.layoutParams
                        layoutParams.height = height1
                        p0.layoutParams = layoutParams
                        if (loadingView != null) {
                            mContainer?.removeView(loadingView)
                        }
                        privacyScroll1.visibility = VISIBLE
                        val javascript =
                            changeWebTextColorScript(context.getColor(R.color.personal_setting_data_use_content))
                        p0.evaluateJavascript(javascript, null)
                    }, 500)

                }
            }

            override fun onReceivedError(
                p0: WebView?,
                p1: WebResourceRequest?,
                p2: WebResourceError?
            ) {
                super.onReceivedError(p0, p1, p2)
                if (loadingView != null) {
                    mContainer?.removeView(loadingView)
                }
                hasErr = true
                privacyScroll1.visibility = GONE
                showNetErrorOrSettingView(netErrorView, mContainer)
                netErrorView?.findViewById<TextView>(R.id.btn_no_net_refresh)?.setOnClickListener {
                    if (HmsApplication.isNetworkConn()){
                        mAgentWeb.urlLoader.loadUrl(url)
                        mContainer?.removeView(netErrorView)
                        showLoading(mContainer, context, loadingView)
                        hasErr = false
                    }
                }

            }

            override fun onReceivedHttpError(
                p0: WebView?,
                p1: WebResourceRequest?,
                p2: WebResourceResponse?
            ) {
                super.onReceivedHttpError(p0, p1, p2)
                if (loadingView != null) {
                    mContainer?.removeView(loadingView)
                }
                hasErr = true
                privacyScroll1.visibility = GONE
                showNetErrorOrSettingView(netErrorView, mContainer)
                netErrorView?.findViewById<TextView>(R.id.btn_no_net_refresh)?.setOnClickListener {
                    if (HmsApplication.isNetworkConn()){
                        mAgentWeb.urlLoader.loadUrl(url)
                        mContainer?.removeView(netErrorView)
                        showLoading(mContainer, context, loadingView)
                        hasErr = false
                    }
                }
            }

            // FIX ZNZCV4-105992
            override fun onRenderProcessGone(
                view: WebView?,
                detail: RenderProcessGoneDetail?
            ): Boolean {
                if (detail != null) {
                    if (detail.didCrash()) {
                        Log.i(TAG, "showPrivacyContentDialog Render process crashed!")
                    } else {
                        Log.i(TAG, "showPrivacyContentDialog Render process was killed to free up resources.")
                    }
                }
                return true
            }
        }
        privacyContent.visibility = VISIBLE
        val contentView = view.findViewById<ViewGroup>(R.id.privacy_agree_content)
//        val ff = view.findViewById<FrameLayout>(R.id.fl_container)
//        ff.setOnClickListener {
//            dialog?.dismiss()
//        }

        // 使对话框全屏
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT
        )
        dialog?.window?.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED)
        dialog?.setContentView(view)
        dialog?.setOnShowListener {
            doDialogAnima()
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    dialog?.dismiss()
                }
                false
            }
        }

        dialog?.setOnDismissListener {
            mainDataModel.privacyContentType = 0
            Log.d("MainActivity", "HMSDialogUtils privacy dialog dismiss")
        }

        context?.let {
            AutoSizeCompat.autoConvertDensityOfGlobal(context.resources)
        }
        dialog?.show()
    }




    fun showSmallInfoDialog(
        context: Context,
        str: String,
        positiveBtnTitle: String,
        btnClickCallback: (isPositive: Boolean) -> Unit
    ) {
        // 如果对话框已经显示，先关闭它
        try {
            if ((dialog != null) && dialog!!.isShowing) {
                dialog!!.dismiss();
            }
        } catch (e: Exception) {
            e.printStackTrace();
        } finally {
            dialog = null;
        }
        // 创建新的对话框
        dialog = HmsDialog(context,R.style.MyDialogStyle)
        var view: View = LayoutInflater.from(context).inflate(R.layout.hms_dialog_small_info, null)
        // 设置对话框标题
        val titleTextView: TextView = view.findViewById(R.id.hms_dialog_content)
        titleTextView.text = str
        // 设置确认按钮
        val positiveButton: Button = view.findViewById(R.id.positiveButton)
        positiveButton.text = positiveBtnTitle
        positiveButton.setOnClickListener {
            btnClickCallback(true)
            dialog?.dismiss()
        }

        // 使对话框全屏
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT
        )
        dialog?.setContentView(view)
        dialog?.show()
    }

    /**
     * 数据使用对话框
     */
    fun showDataUseIntroDialog(
        context: Context,
        title: String = "",
        positiveBtnTitle: String,
        btnClickCallback: (isPositive: Boolean) -> Unit
    ) {
        // 如果对话框已经显示，先关闭它
        try {
            if ((dialog != null) && dialog!!.isShowing) {
                dialog!!.dismiss();
            }
        } catch (e: Exception) {
            e.printStackTrace();
        } finally {
            dialog = null;
        }
        // 创建新的对话框
        dialog = ImmersiveDialog(
            context,
            R.style.MyDialogStyle
        )//Dialog(context, R.style.CustomDialogStyle)
        var view: View =
            LayoutInflater.from(context).inflate(R.layout.hms_dialog_data_use_intro, null)
        var webScroll = view.findViewById<ScrollView2>(R.id.data_usage_container_scroll)
        val scrollbar = view.findViewById<StandaloneScrollBar>(R.id.scrollbar)
        scrollbar.attachTo(webScroll)
        scrollbar.customTrackDrawable = null
        scrollbar.isAlwaysShown = false

        val mContainer = view.findViewById<LinearLayout>(R.id.lo_loading)
        val inflater = LayoutInflater.from(context)
        val loadingView = inflater.inflate(R.layout.activity_detail_loading, mContainer, false)
        val netErrorView =
            inflater.inflate(R.layout.activity_detail_no_network_for_dialog, mContainer, false)
        loadingView.setBackgroundColor(context.getColor(R.color.personal_setting_radio_trans))
        netErrorView.setBackgroundColor(context.getColor(R.color.personal_setting_radio_trans))
//        // 设置对话框标题
//        val titleTextView: TextView = view.findViewById(R.id.hms_dialog_message)
//        titleTextView.text = title
        val ll_container = view.findViewById<LinearLayout>(R.id.data_usage_container)
        var url = "https://pages.healthlinkiot.com/doc/Datadescription1.3.htm"
        var height = 0
        var hasErr = false
        mContainer.visibility = VISIBLE
        showLoading(mContainer, context, loadingView)
        var mAgentWeb = AgentWeb
            .with(context as Activity)
            .setAgentWebParent(ll_container, LinearLayout.LayoutParams(-1, -1))
            .closeIndicator()
            .setWebViewClient(WebViewClient())
            .setWebChromeClient(WebChromeClient()) //title重定向
            .createAgentWeb()
            .ready().go(url)

        mAgentWeb.agentWebSettings.webSettings.let {
            it.useWideViewPort = true
            it.loadWithOverviewMode = true // 缩放至屏幕的大小
            it.domStorageEnabled = true
            it.allowContentAccess = true //是否可访问Content Provider的资源，默认值 true
            it.allowFileAccess = true // 是否可访问本地文件，默认值 true
            it.javaScriptEnabled = true
        }
        val webView = mAgentWeb.webCreator.webView
        webView.setBackgroundColor(context.getColor(R.color.agent_web_bg_color))
        webView.isVerticalScrollBarEnabled = false
        webView.webViewClient = object : WebViewClient() {
            override fun shouldInterceptRequest(
                p0: WebView?,
                p1: WebResourceRequest?
            ): WebResourceResponse? {
                if (!HmsApplication.isNetworkConn()) {
                    // 返回自定义的错误响应，模拟网络错误
                    val errorResponse = WebResourceResponse(
                        "text/html", "utf-8", null
                    )
                    return errorResponse
                }
                return super.shouldInterceptRequest(p0, p1)
            }

            override fun onPageFinished(p0: WebView?, p1: String?) {
                if (!hasErr) {
//                    webScroll.visibility = VISIBLE
                    webScroll.postDelayed({
                        webScroll.visibility = VISIBLE
//                        webScroll.scrollTo(0,1)
                    },500)
                    
                    webScroll.postDelayed({
                        webScroll.scrollTo(0,1)

                    },800)
                    p0?.postDelayed({
                        // 动态调整 WebView 的高度
                        p0.measure(
                            MeasureSpec.makeMeasureSpec(
                                MeasureSpec.UNSPECIFIED, MeasureSpec.UNSPECIFIED
                            ),
                            MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
                        )
                        if (height == 0) height = p0.measuredHeight
                        val layoutParams = p0.layoutParams
                        layoutParams.height = height
                        Log.d("HmsDialogUtils", "height-> " + height)
                        p0.layoutParams = layoutParams
                        val javascript =
                            changeWebTextColorScript(context.getColor(R.color.personal_setting_data_use_content));
                        p0.evaluateJavascript(javascript, null)
                        if (loadingView != null) {
                            mContainer?.removeView(loadingView)
                        }
                        mContainer.visibility = GONE

                    }, 500)
                }
            }

            override fun onReceivedError(
                p0: WebView?,
                p1: WebResourceRequest?,
                p2: WebResourceError?
            ) {
                super.onReceivedError(p0, p1, p2)
                if (loadingView != null) {
                    mContainer?.removeView(loadingView)
                }
                hasErr = true
                webScroll.visibility = GONE
                showNetErrorOrSettingView(netErrorView, mContainer)
                netErrorView?.findViewById<TextView>(R.id.btn_no_net_refresh)?.setOnClickListener {
                    hasErr = retryClick(context, mAgentWeb, mContainer, netErrorView, loadingView, webScroll)
                }

            }

            override fun onReceivedHttpError(
                p0: WebView?,
                p1: WebResourceRequest?,
                p2: WebResourceResponse?
            ) {
                super.onReceivedHttpError(p0, p1, p2)
                if (loadingView != null) {
                    mContainer?.removeView(loadingView)
                }
                hasErr = true
                webScroll.visibility = GONE
                showNetErrorOrSettingView(netErrorView, mContainer)
                netErrorView?.findViewById<TextView>(R.id.btn_no_net_refresh)?.setOnClickListener {
                    hasErr = retryClick(context, mAgentWeb, mContainer, netErrorView, loadingView, webScroll)
                }
            }

            // FIX ZNZCV4-105992
            override fun onRenderProcessGone(
                view: WebView?,
                detail: RenderProcessGoneDetail?
            ): Boolean {
                if (detail != null) {
                    if (detail.didCrash()) {
                        Log.i(TAG, "showDataUseIntroDialog Render process crashed!")
                    } else {
                        Log.i(TAG, "showDataUseIntroDialog Render process was killed to free up resources.")
                    }
                }
                return true
            }

        }

        if (!HmsApplication.isNetworkConn()){
            if (loadingView != null) {
                mContainer?.removeView(loadingView)
            }
            hasErr = true
            webScroll.visibility = GONE
            showNetErrorOrSettingView(netErrorView, mContainer)
            netErrorView?.findViewById<TextView>(R.id.btn_no_net_refresh)?.setOnClickListener {
                hasErr = retryClick(context, mAgentWeb, mContainer, netErrorView, loadingView, webScroll)
            }
        }

        webView.setOnLongClickListener { true }
        //将图片调整到适合webview的大小
        //      val uiModeManager = context?.getSystemService(Context.UI_MODE_SERVICE) as UiModeManager
//        if(uiModeManager!=null&& uiModeManager.nightMode == UiModeManager.MODE_NIGHT_YES){
//            mAgentWeb.agentWebSettings.webSettings.let {
//                it.forceDark = WebSettings.FORCE_DARK_ON
//            }
//        } else if (uiModeManager!=null&& uiModeManager.nightMode == UiModeManager.MODE_NIGHT_AUTO){
//            mAgentWeb.agentWebSettings.webSettings.let {
//                it.forceDark = WebSettings.FORCE_DARK_AUTO
//            }
//        } else {
//            mAgentWeb.agentWebSettings.webSettings.let {
//                it.forceDark = WebSettings.FORCE_DARK_OFF
//            }
//        }
        val contentView = view.findViewById<ViewGroup>(R.id.content_container)
        val ff = view.findViewById<FrameLayout>(R.id.fl_container)
//        ff.setOnClickListener {
//            DataTrackUtil.dtExitPage(
//                "Health_Set_DatadescriptionPrompt_Close",
//                DataTrackUtil.userIDMap(MMKVUtil.getUserId() as String)
//            )
//           // dialog?.dismiss()
//        }

        dialog?.setContentView(view)
        dialog?.setOnShowListener {
            doDialogAnima()
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    DataTrackUtil.dtExitPage(
                        "Health_Set_DatadescriptionPrompt_Close",
                        DataTrackUtil.userIDMap(MMKVUtil.getUserId() as String)
                    )
                    mAgentWeb.destroy()
                    dialog?.dismiss()
                }
                false
            }
        }

        context?.let {
            AutoSizeCompat.autoConvertDensityOfGlobal(context.resources)
        }
        dialog?.show()
        DataTrackUtil.dtEnterPage(
            "Health_Set_DatadescriptionPrompt_PV",
            DataTrackUtil.userIDMap(MMKVUtil.getUserId() as String)
        )
    }

    private fun retryClick(context: Activity, mAgentWeb: AgentWeb, mContainer: LinearLayout, netErrorView: View, loadingView: View, webScroll: ScrollView2): Boolean {
        return if (HmsApplication.isNetworkConn()){
            mAgentWeb.urlLoader.reload()
            mContainer.removeView(netErrorView)
            showLoading(mContainer, context, loadingView)
            false
        } else {
            mContainer.removeView(loadingView)
            webScroll.visibility = GONE
            showNetErrorOrSettingView(netErrorView, mContainer)
            true
        }
    }

    /**
     * 显示华为授权相关对话框
     * @param layoutId 布局必须包含 hms_dialog_message TextView positiveButton Button
     * @param msg 显示消息
     * @param positiveBtnTitle 底部按钮文字
     */
    fun showHuaweiAuthHMSDialog(
        context: Context,
        @LayoutRes layoutId: Int,
        msg: String,
        positiveBtnTitle: String,
        btnClickCallback: (isPositive: Boolean) -> Unit
    ) {
        // 如果对话框已经显示，不再显示
        dialog?.let {
            if (it.isShowing) {
                return
            }
        }

        // 创建新的对话框
        dialog = ImmersiveDialog(
            context,
            R.style.MyDialogStyle
        )
        var view: View = LayoutInflater.from(context).inflate(layoutId, null)
        val contentView = view.findViewById<ViewGroup>(R.id.dialog_content)
        var layout = view.findViewById<FrameLayout>(R.id.fl_container)
        layout.setOnClickListener {
            // 用户点击空白区域
        }

        // 设置对话框标题
        val titleTextView: TextView = view.findViewById(R.id.hms_dialog_message)
        titleTextView.text = msg

        // 设置确认按钮
        val positiveButton: Button = view.findViewById(R.id.positiveButton)
        positiveButton.text = positiveBtnTitle
        positiveButton.setOnClickListener {
            btnClickCallback(true)
            dialog?.dismiss()
        }

        dialog?.setContentView(view)
        dialog?.setOnShowListener {
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    dialog?.dismiss()
                }
                false
            }
        }
        // 使对话框全屏
       // dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)

        dialog?.window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )

        dialog?.show()
    }

    fun showLoading(mContainer: ViewGroup, context: Context, loadingView: View) {
        // 创建 layoutparam 并设置位置
        val params = ConstraintLayout.LayoutParams(
            ConstraintLayout.LayoutParams.MATCH_PARENT,
            ConstraintLayout.LayoutParams.MATCH_PARENT
        )

        mContainer?.addView(loadingView, params)

        // 给ImageView设置动画
        val imageView = loadingView?.findViewById<ImageView>(R.id.iv_loading_amin)!!
        imageView.setImageResource(R.drawable.loading_80x80)
        val drawable = imageView.drawable as AnimatedVectorDrawable
        drawable.registerAnimationCallback(object : Animatable2.AnimationCallback() {
            override fun onAnimationEnd(drawable: Drawable) {
                super.onAnimationEnd(drawable)
                (drawable as AnimatedVectorDrawable).start()
            }
        })
        if (drawable != null) {
            drawable.start()
        }
    }

    fun changeWebTextColorScript(color: Int): String {
        val textColor = String.format("#%06X", (0xFFFFFF and color))
        val javascript = "function changeFontColor() {" +
                "  document.querySelectorAll('*').forEach(function(node) {" +
                "    if(node.style) {" +
                "      node.style.color = '" + textColor + "';" +
                "    }" +
                "  });" +
                "}" +
                "changeFontColor();" +
                "var observer = new MutationObserver(function(mutations) {" +
                "  mutations.forEach(function(mutation) {" +
                "    changeFontColor();" +
                "  });" +
                "});" +
                "observer.observe(document.body, { attributes: true, childList: true, subtree: true });"
        return javascript
    }

    fun showNetErrorOrSettingView(netErrorView: View, mContainer: ViewGroup) {
        // 创建 layoutparam 并设置位置
        val params = ConstraintLayout.LayoutParams(
            ConstraintLayout.LayoutParams.MATCH_PARENT,
            ConstraintLayout.LayoutParams.MATCH_PARENT
        )

        if (netErrorView.parent != null) {
            (netErrorView.parent as ViewGroup).removeView(netErrorView)  // 先移除
        }

        mContainer.addView(netErrorView, params)
    }

    private fun doDialogAnima(){
        if(dialog!=null) {
            doDialogAnimateEnter(dialog!!)
        }
    }

    /**
     * 对话框动效
     */
    public fun doDialogAnimateEnter(dialog: Dialog){
        if(dialog!=null) {
            // 进入动画
            var view = dialog!!.window?.decorView?.rootView?.findViewById<FrameLayout>(R.id.fl_container)
            view?.scaleX = 0f
            view?.scaleY = 0f
            var animate = view?.animate()
                ?.scaleX(1f)
                ?.scaleY(1f)
                ?.setDuration(300)
            animate?.interpolator = AccelerateDecelerateInterpolator()
            animate?.start()

        }
    }

    fun clearDialog(){
        if (dialog != null) {
            dialog!!.dismiss()
            dialog = null
        }
    }

}