package com.healthlink.hms.utils

import android.content.IntentFilter
import com.healthlink.hms.reciever.HMSAction

/**
 * 权限工具类
 */
object AuthorizationUtil {


    /**
     * 判断是否所有的主权限都已授权
     */
    fun isAllMainPrivillegesAuth():Bo<PERSON>an{
        return (MMKVUtil.getHeartRateAuthority()
                &&MMKVUtil.getSleepAuthority()
                &&MMKVUtil.getBloodOxygenAuthority()
                &&MMKVUtil.getStressAuthority()
                &&MMKVUtil.getTemperatureAuthority()
                &&MMKVUtil.getBloodPressureAuthority())
    }

    /**
     * 判断是否有主要的数据权限被授权
     */
    fun hasMainPrivillegesAuth():Boolean{
        return (MMKVUtil.getHeartRateAuthority()
                ||MMKVUtil.getSleepAuthority()
                ||MMKVUtil.getBloodOxygenAuthority()
                ||MMKVUtil.getStressAuthority()
                ||MMKVUtil.getTemperatureAuthority()
                ||MMKVUtil.getBloodPressureAuthority())
    }
}