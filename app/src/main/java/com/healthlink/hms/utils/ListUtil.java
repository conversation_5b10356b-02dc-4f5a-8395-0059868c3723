package com.healthlink.hms.utils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * listUtil
 *
 *
 */
public class ListUtil {

	/**
	 * 判断一个集合是否为空
	 *
	 * @param collection
	 * @return
	 */
	public static <T> boolean isEmpty(Collection<T> collection) {
		if (collection == null || collection.size() == 0) {
			return true;
		}
		return false;
	}

	/**
	 * 判断一个集合是否不为空
	 *
	 * @param collection
	 * @return
	 */
	public static <T> boolean isNotEmpty(Collection<T> collection) {
		return !isEmpty(collection);
	}

	/**
	 * 返回一个集合的size
	 *
	 * @return
	 */
	public static <T> int size(Collection<T> collection) {
		if (isEmpty(collection)) {
			return 0;
		}
		return collection.size();
	}

	/**
	 * @return
	 */
	public static List<ListEntity> mapToList(Map<String, String> map) {
		List<ListEntity> list = new ArrayList<>();
		for (String key : map.keySet()) {
			ListEntity entity = new ListEntity();
			entity.key = key;
			entity.value = map.get(key);
			list.add(entity);
		}
		return list;
	}

	/**
	 * 重写实体类的equals方法
	 * 比较两个list里的值是否一致
	 * @param a
	 * @param b
	 * @return
	 */
	public static boolean compare(List<?> a, List<?> b) {
		if (a == null || b == null) {
			return false;
		}
		if (a.size() != b.size()) {
			return false;
		}

		for (int i = 0; i < a.size(); i++) {
			if (!a.get(i).equals(b.get(i))) {
				return false;
			}
		}
		return true;
	}
//	public static <T extends Comparable<T>> boolean compare(List<T> a, List<T> b) {
//		if (a == null || b == null) {
//			return false;
//		}
//		if (a.size() != b.size()) {
//			return false;
//		}
//
//		Collections.sort(a);
//		Collections.sort(b);
//		for (int i = 0; i < a.size(); i++) {
//			if (!a.get(i).equals(b.get(i))) {
//				return false;
//			}
//		}
//		return true;
//	}
}
