package com.healthlink.hms.utils

import android.content.Context
import android.util.Log
import androidx.compose.ui.unit.Constraints
import com.blankj.utilcode.util.LogUtils
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.base.Constants
import com.tencent.mmkv.MMKV
import com.tencent.mmkv.MMKVLogLevel


/**
 * Created by imaginedays on 2024/7/15
 * MMKV通用存储工具类
 */
object MMKVUtil {
    private const val TAG = "MMKVUtil"
    private const val KEY_USER_ID_KEY = "user_id"
    private const val KEY_USER_TOKEN = "token"
    private const val KEY_LAST_P_TIME = "last_p_time"
    private const val KEY_IS_NOTIFICATION_OPEN = "isNotificationOpen"
    private const val KEY_DESTINATION_ELEVATION = "key_destination_elevation"
    private const val KEY_CUR_LOCATION_ELEVATION = "key_cur_location_elevation"
    private const val KEY_NAVIGATION_GUIDE_STATUS = "key_navigation_guide_status"
    private const val KEY_PHONE_DOCTOR_NUMBER = "key_phone_doctor_number"
    private const val KEY_VIN_CODE = "key_vin_code"

    // 开机首次启动App
    private const val KEY_FIRST_LAUNCH_APP_AFTER_BOOT = "key_first_launch_app_after_boot"
    private const val KEY_SCENE_REMAIN_RUN_COUNT = "key_scene_remain_run_count"
    private const val KEY_NAVIGATION_GUIDE_STATUS_TIMESTAMP =
        "key_navigation_guide_status_timestamp"
    private const val KEY_NAVIGATION_INFO = "key_navigation_info"
    private const val KEY_HEART_RATE_AUTHORITY = "key_heart_rate_authority"
    private const val KEY_SLEEP_AUTHORITY = "key_sleep_authority"
    private const val KEY_STRESS_AUTHORITY = "key_stress_authority"
    private const val KEY_BLOOD_OXYGEN_AUTHORITY = "key_blood_oxygen_authority"
    private const val KEY_BLOOD_PRESSURE_AUTHORITY = "key_blood_pressure_authority"
    private const val KEY_BODY_TEMPERATURE_AUTHORITY = "key_body_temperature_authority"
    private const val KEY_WELCOME_TIME = "key_welcomeTime"
    private const val KEY_BIND_DOCTOR_SERVICE = "key_bind_doctor_service"
    private const val KEY_IS_PHONE_DOCTOR_MEMBER = "key_is_phone_doctor_member"
    private const val KEY_HEALTH_REPORT_SCORE_CHANGE_IS_UP = "key_health_report_score_change_is_up"
    private const val KEY_HEALTH_TIPS = "key_health_tips"
    private const val KEY_POWER_MODE_2_START_TIME = "key_power_mode_2_start_time"
    private const val KEY_BANNER_TIPS = "key_banner_tips"
    private const val KEY_ALREADY_BANNER_TIPS = "key_already_banner_tips"
    private const val KEY_USERINFO_NICKNAME = "key_userinfo_nickname"
    private const val KEY_USERINFO_BIRTHYEAR = "key_userinfo_birthyear"
    private const val KEY_USERINFO_BIRTHMONTH = "key_userinfo_birthmonth"
    private const val KEY_USERINFO_HEIGHT = "key_userinfo_height"
    private const val KEY_USERINFO_WEIGHT = "key_userinfo_weight"
    private const val KEY_USERINFO_GENDER = "key_userinfo_gender"
    private const val KEY_HEALTH_REPORT_NOTIFY = "key_health_report_notify"

    // 最后一次档位状态及时间
    private const val KEY_LAST_GEAR_STATUS_INFO = "key_last_gear_status_info"
    // 是否提示过同意用户协议与隐私政策
    private const val KEY_USER_PRIVACY_AGREEMENT_TOAST = "key_user_privacy_agreement_toast"

    /**
     * 记录上一次存储的电源模式key
     */
    private const val KEY_LAST_POWER_MODE = "key_last_power_mode"
    private lateinit var mmkv: MMKV
//    private lateinit var mmkvNotEncry: MMKV

    /**
     * 主题切换
     */
    private const val KEY_LAST_THEME_MODE = "key_last_theme_mode"

    private const val KEY_LAST_BANNER_POSITION = "key_last_banner_position"

    /**
     * 隐私协议
     */
    private const val KEY_PRIVATE_POLICY = "key_private_policy"

    /**
     * 隐私协议
     */
    private const val KEY_LAST_DOCTOR_CALL_TIME = "KEY_LAST_DOCTOR_CALL_TIME"

    /**
     * 初始化 MMKV
     * @param context 应用上下文
     * @param cryptKey 加密密钥，可以为空
     */
    fun initialize(context: Context, cryptKey: String? = null) {
        val rootDir = MMKV.initialize(context)
        mmkv = if (cryptKey.isNullOrEmpty()) {
            MMKV.defaultMMKV()
        } else {
            MMKV.mmkvWithID("MyID", MMKV.MULTI_PROCESS_MODE, cryptKey)
        }
        checkMMKV(rootDir)
    }

    /**
     * 打印 MMKV 部分信息
     */
    fun checkMMKV(rootDir: String) {
        Log.i(TAG,"当前进程ID: ${android.os.Process.myPid()}")
        Log.i(TAG,"mmkv rootDir 存储地址: $rootDir")
        Log.i(TAG,"mmkv里存储了多少个值 mmkv.allKeys: ${mmkv.allKeys()?.size}")
        val userId = mmkv.decodeString(KEY_USER_ID_KEY, "UserId默认值")
        Log.i(TAG, "printMMKV key: $KEY_USER_ID_KEY, value: $userId")
        val userToken = HmsApplication.appContext.getSharedPreferences("USER_TOKEN", Context.MODE_PRIVATE)?.getString(KEY_USER_TOKEN, "UserToken默认值")
        Log.i(TAG, "printMMKV key: $KEY_USER_TOKEN, value: $userToken")
    }

    fun doSync() {
        mmkv.sync()
//        mmkvNotEncry.sync()
    }

    /**
     * 存储用户ID
     * @param userId 用户ID
     */
    fun storeUserId(userId: String) {
//        mmkv.encode(KEY_USER_ID_KEY, userId)
        HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_ID, Context.MODE_PRIVATE)?.edit()?.putString(KEY_USER_ID_KEY, userId)?.apply()
    }

    /**
     * 读取用户ID
     * @return 用户ID
     */
    fun getUserId(): String? {
//        return mmkv.decodeString(KEY_USER_ID_KEY, "")
        return HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_ID, Context.MODE_PRIVATE)?.getString(KEY_USER_ID_KEY, "")
    }

    /**
     * 清除用户ID
     */
    fun clearUserId() {
        mmkv.removeValueForKey(KEY_USER_ID_KEY)
    }

    /**
     * 存储用户token
     */
    fun storeUserToken(token: String) {
        Log.i(TAG, "storeUserToken: $token")
        HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_TOKEN, Context.MODE_PRIVATE)?.edit()?.putString(KEY_USER_TOKEN, token)?.apply()
    }

    /**
     * 获取用户token
     */
    fun getUserToken(): String? {
        var res: String? = null
        HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_TOKEN, Context.MODE_PRIVATE)?.let {
            res = it.getString(KEY_USER_TOKEN, "")
        }
//        Log.i(TAG, "getUserToken: $res")
        return res
    }

    /**
     * 清除所有数据 除
     * 同意隐私协议
     * 当天首次启动
     * 开机首次启动
     * @param isKeepAgreed 默认保留用户隐私协议，注销帐户时需要清除
     */
    fun clearAllExceptSome(isKeepAgreed: Boolean = true) {
        val isAgreed = getPrivacyPolicy()
        val isFirstLaunchAppAfterBoot = getIsFirstLaunchAppAfterBoot()
        val remainRunCountDTO = getSceneRemainRunCountInfo()
        // 电话医生号码
        val phoneDoctor = getPhoneDoctorNumber()
        // 当天播放开机迎宾时间
        val welcomeDate = getWelcomeTime()
        // 电话医生备案
        val isBindDoctorService = getBindDoctorService()
        // 记录开机弹窗设置
        val bannerTips = getBannerTips()
        val bannerAlreadyTips = getBannerAlreadyTips()
        // 记录上一次电源模式
        val lastPowerMode = getLastPowerMode()
//        mmkv.removeValueForKey() 可以清除指定key的数据
        // 记录Vin码
        val vinCode = getVinCode()
        // 记录是否是否执行了健康周报场景
        val notifyHealthReportStatus = isNotifyHealthWeekReport()

        // 记录当前主题
        val themeMode = getLastThemeMode()
        // 清除用户token
        clearUserTokenSharedPreferences()
        // 清除用户ID
        clearUserIdSharedPreferences()
        // 清除用户权限
        clearUserAuthoritySharedPreferences()
        mmkv.clearAll()
        Log.i("TAG_DIALOG_SHOW","=================isKeepAgreed is $isKeepAgreed")
        if (isKeepAgreed) {
            Log.i("TAG_DIALOG_SHOW","=================put 4")
            storePrivacyPolicy(isAgreed)
        } else {
            clearPrivacyPolicy()
        }
        storeIsFirstLaunchAppAfterBoot(isFirstLaunchAppAfterBoot)
        storeSceneRemainRunCountInfo(remainRunCountDTO!!)
        phoneDoctor?.let {
            storePhoneDoctorNumber(it)
        }
        // 存储播放欢迎场景的日期
        welcomeDate?.let {
            storeWelcomeTime(it)
        }
        // 存储备案电话医生结果
        setBindDoctorService(isBindDoctorService)
        // 记录开机弹窗设置
        bannerTips?.let {
            storeBannerTips(it)
        }
        bannerAlreadyTips?.let {
            storeAlreadyBannerTips(it)
        }
        // 记录上一次电源模式
        lastPowerMode?.let {
            storeLastPowerMode(it)
        }

        // 记录Vin码
        vinCode?.let {
            storeVinCode(it)
        }

        // 存储是否是否执行了健康周报场景
        storeIsNotifyHealthWeekReport(notifyHealthReportStatus)

        // 存储当前主题
        themeMode?.let {
            storeLastThemeMode(it)
        }

    }

    private fun clearUserIdSharedPreferences() {
        val userIdSp = HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_ID, Context.MODE_PRIVATE)
        userIdSp?.edit()?.apply{
            putString(KEY_USER_ID_KEY, "")
            apply()
        }
    }

    private fun clearUserTokenSharedPreferences() {
//        HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_TOKEN, Context.MODE_PRIVATE)?.edit()?.clear()?.apply()
        val tokenSp = HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_TOKEN, Context.MODE_PRIVATE)
        tokenSp.edit().putString(KEY_USER_TOKEN, "").apply()
    }

    private fun clearUserAuthoritySharedPreferences() {
        val authoritySp = HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_AUTHORITY, Context.MODE_PRIVATE)
        authoritySp?.edit()?.apply{
            putBoolean(KEY_HEART_RATE_AUTHORITY, false)
            putBoolean(KEY_SLEEP_AUTHORITY, false)
            putBoolean(KEY_BLOOD_OXYGEN_AUTHORITY, false)
            putBoolean(KEY_STRESS_AUTHORITY, false)
            putBoolean(KEY_BODY_TEMPERATURE_AUTHORITY, false)
            putBoolean(KEY_BLOOD_PRESSURE_AUTHORITY, false)
            apply()
        }
    }

    /**
     * 记录挂P档的时间
     */
    fun storeLastPTime(lastPTime: String) {
        mmkv.encode(KEY_LAST_P_TIME, lastPTime)
    }

    /**
     * 读取挂P档的时间
     */
    fun getLastPTime(): String? {
        return mmkv.decodeString(KEY_LAST_P_TIME, System.currentTimeMillis().toString())
    }

    /**
     * 存储经度
     * @param lon 经度
     */
    fun storeLon(lon: Double) {
        mmkv.encode("lon", lon)
    }

    /**
     * 获取经度
     */
    fun getLon(): Double {
        return mmkv.decodeDouble("lon", 0.0)
    }

    /**
     * 存储纬度
     * @param lat 纬度
     */
    fun storeLat(lat: Double) {
        mmkv.encode("lat", lat)
    }

    /**
     * 获取纬度
     */
    fun getLat(): Double {
        return mmkv.decodeDouble("lat", 0.0)
    }

    /**
     * 设置主动服务状态通知开关
     */
    fun storeNotificationOpen(isNotificationOpen: Boolean) {
        mmkv.encode(KEY_IS_NOTIFICATION_OPEN, isNotificationOpen)
    }

    /**
     * 获取主动服务状态通知开关
     */
    fun getNotificationOpen(): Boolean {
        return mmkv.decodeBool(KEY_IS_NOTIFICATION_OPEN, true)
    }

    /**
     * 是否是访客模式
     * 没有token就是访客模式
     * @return
     */
    fun isVisitorMode(): Boolean {
        val token = getUserToken()
        val userId = getUserId()
        var result = !(!userId.isNullOrEmpty() && !token.isNullOrEmpty())
        return result
    }

    /**
     * 目的地海拔
     */
    fun storeDestinationElevation(elevation: String?) {
        elevation?.let {
            mmkv.encode(KEY_DESTINATION_ELEVATION, it)
        }
    }

    /**
     * 通知显示内容获取目的地海拔
     */
    fun getDestinationElevation(): String? {
        return mmkv.decodeString(KEY_DESTINATION_ELEVATION, "")
    }

    /**
     * 存储当前位置海拔
     */
    fun storeCurLocationElevation(elevation: String?) {
        elevation?.let {
            mmkv.encode(KEY_CUR_LOCATION_ELEVATION, it)
        }
    }

    /**
     * 获取当前位置海拔
     */
    fun getCurLocationElevation(): String? {
        return mmkv.decodeString(KEY_CUR_LOCATION_ELEVATION, "")
    }

    /**
     * 存储导航状态
     * 0: 开始导航
     * 1: 结束导航
     */
    fun storeNavigationGuideStatus(navigationGuide: Boolean) {
        mmkv.encode(KEY_NAVIGATION_GUIDE_STATUS, navigationGuide)
    }

    /**
     * 获取导航状态
     * 0: 开始导航
     * 1: 结束导航
     */
    fun getNavigationGuideStatus(): Boolean {
        return mmkv.decodeBool(KEY_NAVIGATION_GUIDE_STATUS, false)
    }

    /**
     * 存储用户ID
     * @param userId 用户ID
     */
    fun storeData(key: String, value: String) {
        mmkv.encode(key, value)
    }

    /**
     * 读取用户ID
     * @return 用户ID
     */
    fun getData(key: String): String? {
        return mmkv.decodeString(key, "")
    }

    /** 存储电话医生号码
     */
    fun storePhoneDoctorNumber(doctorPhone: String) {
        mmkv.encode(KEY_PHONE_DOCTOR_NUMBER, doctorPhone)
    }

    /**
     * 获取电话医生号码
     */
    fun getPhoneDoctorNumber(): String? {
        return mmkv.decodeString(KEY_PHONE_DOCTOR_NUMBER, "")
    }

    /**
     * 获取vin
     */
    fun getVinCode(): String? {
        return "LGWN2TXLBGA983361"
        //return mmkv.decodeString(KEY_VIN_CODE, "")
    }

    /**
     * 存入vin
     */

    fun storeVinCode(vinCode: String) {
        vinCode?.let {
            mmkv.encode(KEY_VIN_CODE, it)
        }
    }

    fun storePrivacyPolicy(isAgreet: Boolean) {
        Log.i(TAG,"storePrivacyPolicy() is $isAgreet")
        Log.i("TAG_DIALOG_SHOW","=================put  is $isAgreet")
//        mmkv.encode(KEY_PRIVATE_POLICY, isAgreet)
//        doSync()
        HmsApplication.appContext.getSharedPreferences(Constants.USER_PRIVACY_AGREEMENT, Context.MODE_PRIVATE)?.edit()?.putBoolean(KEY_PRIVATE_POLICY, isAgreet)?.apply()
    }

    fun getPrivacyPolicy(): Boolean {
//        var privacyPolicyFlag = mmkv.decodeBool(KEY_PRIVATE_POLICY, false)
        var privacyPolicyFlag = HmsApplication.appContext.getSharedPreferences(Constants.USER_PRIVACY_AGREEMENT, Context.MODE_PRIVATE)?.getBoolean(KEY_PRIVATE_POLICY, false) ?: false
        Log.i(TAG,"getPrivacyPolicy() is $privacyPolicyFlag")
        return privacyPolicyFlag
    }

    fun clearPrivacyPolicy() {
        Log.i(TAG,"clearPrivacyPolicy()")
        HmsApplication.appContext.getSharedPreferences(Constants.USER_PRIVACY_AGREEMENT, Context.MODE_PRIVATE)?.edit()?.clear()?.apply()
    }

    // 1、开机首次启动App
    fun getIsFirstLaunchAppAfterBoot(): Boolean {
        Log.i(
            TAG,
            "getIsFirstLaunchAppAfterBoot: ${
                mmkv.decodeBool(
                    KEY_FIRST_LAUNCH_APP_AFTER_BOOT,
                    false
                )
            }"
        )
        return mmkv.decodeBool(KEY_FIRST_LAUNCH_APP_AFTER_BOOT, false)
    }

    /**
     * 开机首次启动App
     */
    fun storeIsFirstLaunchAppAfterBoot(firstLaunchAppAfterBoot: Boolean) {
        Log.i(TAG, "开机首次启动App: $firstLaunchAppAfterBoot")
        mmkv.encode(KEY_FIRST_LAUNCH_APP_AFTER_BOOT, firstLaunchAppAfterBoot)
    }

    /**
     * 存储行程内场景引擎通知次数
     */
    fun storeSceneRemainRunCountInfo(sceneRemainRunCountInfo: String) {
        mmkv.encode(KEY_SCENE_REMAIN_RUN_COUNT, sceneRemainRunCountInfo)
    }

    /**
     * 获取行程内场景引擎通知次数信息
     */
    fun getSceneRemainRunCountInfo(): String? {
        return mmkv.decodeString(KEY_SCENE_REMAIN_RUN_COUNT, "")
    }

    fun isOpenWarnMode(): Boolean {
        return mmkv.decodeBool("isOpenWarnMode", false)
    }

    fun setOpenWarnMode(isOpen: Boolean) {
        mmkv.encode("isOpenWarnMode", isOpen)
    }

    fun storeNavigationInfo(json: String) {
        mmkv.encode(KEY_NAVIGATION_INFO, json)
    }

    fun getNavigationInfo(): String? {
        return mmkv.decodeString(KEY_NAVIGATION_INFO, "")
    }

    /**
     * 获取心率授权信息
     */
    fun getHeartRateAuthority(): Boolean {
//        return mmkv.decodeBool(KEY_HEART_RATE_AUTHORITY, false)
        return HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_AUTHORITY, Context.MODE_PRIVATE)?.getBoolean(KEY_HEART_RATE_AUTHORITY, false) ?: false
    }

    /**
     * 存储心率授权信息
     */
    fun storeHeartRateAuthority(result: Boolean) {
//        mmkv.encode(KEY_HEART_RATE_AUTHORITY, result)
        HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_AUTHORITY, Context.MODE_PRIVATE)?.edit()?.putBoolean(KEY_HEART_RATE_AUTHORITY, result)?.apply()
    }

    /**
     * 获取睡眠授权信息
     */
    fun getSleepAuthority(): Boolean {
//        return mmkv.decodeBool(KEY_SLEEP_AUTHORITY, false)
        return HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_AUTHORITY, Context.MODE_PRIVATE)?.getBoolean(KEY_SLEEP_AUTHORITY, false) ?: false
    }

    /**
     * 存储睡眠授权信息
     */
    fun storeSleepAuthority(result: Boolean) {
//        mmkv.encode(KEY_SLEEP_AUTHORITY, result)
        HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_AUTHORITY, Context.MODE_PRIVATE)?.edit()?.putBoolean(KEY_SLEEP_AUTHORITY, result)?.apply()
    }

    /**
     * 获取压力授权信息
     */
    fun getStressAuthority(): Boolean {
//        return mmkv.decodeBool(KEY_STRESS_AUTHORITY, false)
        return HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_AUTHORITY, Context.MODE_PRIVATE)?.getBoolean(KEY_STRESS_AUTHORITY, false) ?: false
    }

    /**
     * 存储压力授权信息
     */
    fun storeStressAuthority(result: Boolean) {
//        mmkv.encode(KEY_STRESS_AUTHORITY, result)
        HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_AUTHORITY, Context.MODE_PRIVATE)?.edit()?.putBoolean(KEY_STRESS_AUTHORITY, result)?.apply()
    }

    /**
     * 获取血氧授权信息
     */
    fun getBloodOxygenAuthority(): Boolean {
//        return mmkv.decodeBool(KEY_BLOOD_OXYGEN_AUTHORITY, false)
        return HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_AUTHORITY, Context.MODE_PRIVATE)?.getBoolean(KEY_BLOOD_OXYGEN_AUTHORITY, false) ?: false
    }

    /**
     * 存储血氧授权信息
     */
    fun storeBloodOxygenAuthority(result: Boolean) {
//        mmkv.encode(KEY_BLOOD_OXYGEN_AUTHORITY, result)
        HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_AUTHORITY, Context.MODE_PRIVATE)?.edit()?.putBoolean(KEY_BLOOD_OXYGEN_AUTHORITY, result)?.apply()
    }

    /**
     * 获取体温授权信息
     */
    fun getTemperatureAuthority(): Boolean {
//        return mmkv.decodeBool(KEY_BODY_TEMPERATURE_AUTHORITY, false)
        return HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_AUTHORITY, Context.MODE_PRIVATE)?.getBoolean(KEY_BODY_TEMPERATURE_AUTHORITY, false) ?: false
    }

    /**
     * 存储体温授权信息
     */
    fun storeTempertureAuthority(result: Boolean) {
//        mmkv.encode(KEY_BODY_TEMPERATURE_AUTHORITY, result)
        HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_AUTHORITY, Context.MODE_PRIVATE)?.edit()?.putBoolean(KEY_BODY_TEMPERATURE_AUTHORITY, result)?.apply()
    }

    /**
     * 获取血压授权信息
     */
    fun getBloodPressureAuthority(): Boolean {
//        return mmkv.decodeBool(KEY_BLOOD_PRESSURE_AUTHORITY, false)
        return HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_AUTHORITY, Context.MODE_PRIVATE)?.getBoolean(KEY_BLOOD_PRESSURE_AUTHORITY, false) ?: false
    }

    /**
     * 存储血压授权信息
     */
    fun storeBloodPressureAuthority(result: Boolean) {
//        mmkv.encode(KEY_BLOOD_PRESSURE_AUTHORITY, result)
        HmsApplication.appContext.getSharedPreferences(Constants.SHARE_USER_AUTHORITY, Context.MODE_PRIVATE)?.edit()?.putBoolean(KEY_BLOOD_PRESSURE_AUTHORITY, result)?.apply()
    }

    /**
     * 存储播放欢迎场景的日期
     */
    fun storeWelcomeTime(welcomeDate: String) {
        mmkv.encode(KEY_WELCOME_TIME, welcomeDate)
    }

    /**
     * 获取播放欢迎场景的日期
     */
    fun getWelcomeTime(): String? {
        return mmkv.decodeString(KEY_WELCOME_TIME, "")
    }

    /**
     * 设置电话医生备案成功
     */
    fun setBindDoctorService(isBindDoctorService: Boolean) {
        mmkv.encode(KEY_BIND_DOCTOR_SERVICE, isBindDoctorService)
    }

    /**
     * 获取电话医生备案结果
     */
    fun getBindDoctorService(): Boolean {
        return mmkv.decodeBool(KEY_BIND_DOCTOR_SERVICE, false)
    }

    /**
     * 存储时间
     */
    fun storeTime(key: String, time: Long) {
        mmkv.encode(key, time)
    }

    /**
     * 存储时间
     */
    fun getTime(key: String): Long {
        return mmkv.decodeLong(key)
    }

    /**
     * 存储健康报告分数是否提高
     */
    fun storeHealthReportScoreChangeIsUp(scoreIsUp: Boolean) {
        mmkv.encode(KEY_HEALTH_REPORT_SCORE_CHANGE_IS_UP, scoreIsUp)
    }

    /**
     * 获取健康报告分数是否提高
     */
    fun getHealthReportScoreChangeIsUp(): Boolean {
        return mmkv.decodeBool(KEY_HEALTH_REPORT_SCORE_CHANGE_IS_UP, false)
    }

    /**
     * 存储健康tips分页数据
     */
    fun storeHealthTips(tipsArray: String?) {
        mmkv.encode(KEY_HEALTH_TIPS, tipsArray)
    }

    fun getHealthTips(): String? {
        return mmkv.decodeString(KEY_HEALTH_TIPS, "")
    }

    /**
     * 记录上电时间
     */
    fun storePowerMode2StartTime(timestamp: Long) {
        mmkv.encode(KEY_POWER_MODE_2_START_TIME, timestamp)
    }

    /**
     * 获取上电时间
     */
    fun getPowerMode2StartTime(): Long {
        return mmkv.decodeLong(KEY_POWER_MODE_2_START_TIME, 0)
    }

    /**
     * 记录开机后是否提醒过
     */
    fun storeAlreadyBannerTips(alreadyTips: Boolean) {
        Log.d(TAG, "storeAlreadyBannerTips = $alreadyTips")
        mmkv.encode(KEY_ALREADY_BANNER_TIPS, alreadyTips)
        doSync()
    }

    fun getBannerAlreadyTips(): Boolean {
        return mmkv.decodeBool(KEY_ALREADY_BANNER_TIPS, false)
    }

    /**
     * 记录不再提醒
     */
    fun storeBannerTips(tips: Boolean?) {
        mmkv.encode(KEY_BANNER_TIPS, true == tips)
        doSync()
    }

    fun getBannerTips(): Boolean {
        return mmkv.decodeBool(KEY_BANNER_TIPS, true)
    }


    /**
     * 记录上次开启的电源模式
     */
    fun storeLastPowerMode(powerMode: String) {
        mmkv.encode(KEY_LAST_POWER_MODE, powerMode)
    }

    /**
     * 获取上一次设置的电源模式
     */
    fun getLastPowerMode(): String? {
        return mmkv.decodeString(KEY_LAST_POWER_MODE, "")
    }

    /**
     * 记录个人信息昵称
     */
    fun storeUserinfoNickname(nickname: String) {
        mmkv.encode(KEY_USERINFO_NICKNAME, nickname)
    }

    /**
     * 记录个人信息生年
     */
    fun storeUserinfoBirthYear(birthYear: Int) {
        mmkv.encode(KEY_USERINFO_BIRTHYEAR, birthYear)
    }

    /**
     * 记录个人信息生月
     */
    fun storeUserinfoBirthMonth(birthMonth: Int) {
        mmkv.encode(KEY_USERINFO_BIRTHMONTH, birthMonth)
    }

    /**
     * 记录个人信息身高
     */
    fun storeUserinfoHeight(height: Int) {
        mmkv.encode(KEY_USERINFO_HEIGHT, height)
    }

    /**
     * 记录个人信息体重
     */
    fun storeUserinfoWeight(weight: Float) {
        mmkv.encode(KEY_USERINFO_WEIGHT, weight)
    }

    /**
     * 记录个人信息昵称/
     */
    fun storeUserinfoGender(gender: Int) {
        mmkv.encode(KEY_USERINFO_GENDER, gender)
    }

    /**
     * 记录个人信息
     */
    fun storeUserinfo(
        nickname: String,
        birthYear: Int,
        birthMonth: Int,
        height: Int,
        weight: Float,
        gender: Int
    ) {
        storeUserinfoNickname(nickname)
        storeUserinfoBirthYear(birthYear)
        storeUserinfoBirthMonth(birthMonth)
        storeUserinfoHeight(height)
        storeUserinfoWeight(weight)
        storeUserinfoGender(gender)
    }

    /**
     * 读取个人信息
     */
    fun getUserinfo(): HashMap<String, Any> {
        var personalMap = hashMapOf<String, Any>(
            Pair("nickName", mmkv.decodeString(KEY_USERINFO_NICKNAME, "") ?: ""),
            Pair("birthYear", mmkv.decodeInt(KEY_USERINFO_BIRTHYEAR, 0)),
            Pair("birthMonth", mmkv.decodeInt(KEY_USERINFO_BIRTHMONTH, 0)),
            Pair("height", mmkv.decodeInt(KEY_USERINFO_HEIGHT, 0)),
            Pair("weight", mmkv.decodeFloat(KEY_USERINFO_WEIGHT, 0f)),
            Pair("gender", mmkv.decodeInt(KEY_USERINFO_GENDER, 2))
        )
        return personalMap
    }

    /**
     * 存储是否执行了健康周报通知
     */
    fun storeIsNotifyHealthWeekReport(isNotifyHealthWeekTips: Boolean) {
        mmkv.encode(KEY_HEALTH_REPORT_NOTIFY, isNotifyHealthWeekTips)
    }

    /**
     * 获取是否执行了健康周报通知
     */
    fun isNotifyHealthWeekReport(): Boolean {
        return mmkv.decodeBool(KEY_HEALTH_REPORT_NOTIFY, false)
    }

    /**
     * 记录最后一次档位信息及时间
     */
    fun storeLastGearStatus(lastGearStatusInfoDTO : String) {
        mmkv.encode(KEY_LAST_GEAR_STATUS_INFO, lastGearStatusInfoDTO)
    }

    /**
     * 获取最后一次档位信息及时间
     */
    fun getLastGearStatus(): String? {
        return mmkv.decodeString(KEY_LAST_GEAR_STATUS_INFO, "")
    }

    /**
     * 0
     * 1
     * 2
     */
    fun storeLastThemeMode(themeMode: Int) {
        mmkv.encode(KEY_LAST_THEME_MODE, themeMode)
    }

    fun getLastThemeMode(): Int {
        return mmkv.decodeInt(KEY_LAST_THEME_MODE, -1)
    }

    fun storeLastBannerPosition(position: Int) {
        mmkv.encode(KEY_LAST_BANNER_POSITION, position)
    }

    fun getLastBannerPosition(): Int {
        return mmkv.decodeInt(KEY_LAST_BANNER_POSITION, 0)
    }

    /**
     * 记录最后一次电话医生通话时间
     */
    fun storeLastDoctorCallTime(lastDoctorCallTime: Long) {
        Log.d(TAG, "store KEY_LAST_DOCTOR_CALL_TIME is $lastDoctorCallTime")
        mmkv.encode(KEY_LAST_DOCTOR_CALL_TIME, lastDoctorCallTime)
    }

    /**
     * 获取最后一次电话医生通话时间
     */
    fun getLastDoctorCallTime():Long {
        val lastDoctorCallTime = mmkv.decodeLong(KEY_LAST_DOCTOR_CALL_TIME, 0)
        Log.d(TAG, "get KEY_LAST_DOCTOR_CALL_TIME is $lastDoctorCallTime")
        return lastDoctorCallTime
    }

}