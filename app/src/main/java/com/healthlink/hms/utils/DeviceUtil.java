package com.healthlink.hms.utils;

import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.content.Context;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.telephony.TelephonyManager;
import android.text.TextUtils;

import androidx.core.content.ContextCompat;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.LineNumberReader;
import java.io.RandomAccessFile;
import java.util.UUID;

/**
 * 手机设备相关工具类
 */
public class DeviceUtil {

    private static final String CHARSET = "UTF-8";

    /**
     * 在文件中读取UUID时RandomAccessFile的读取模式
     */
    private static final String ACCESS_MODE = "r";

    /**
     * 2.2手机上会经常出现此id
     */
    private static final String EMULATOR_ANDROID_ID = "9774d56d682e549c";

    /**
     * 无效的SerialId组合的集合
     */
    private static final String[] BAD_SERIAL_PATTERNS = {"1234567", "abcdef", "dead00beef"};

    /**
     * 内存中缓存的deviceToken
     */
    private static String sDeviceToken = null;

    private static final String DEVICEID_KEY = "DEVICEID_KEY";

    /**
     * 获取设备唯一标识符deviceId
     *
     * @param context
     * @return
     */
    @TargetApi(Build.VERSION_CODES.GINGERBREAD)
    public static String readDeviceId(Context context) {

        if (!TextUtils.isEmpty(sDeviceToken)) {
            // 内存中有，直接返回
            return sDeviceToken;
        }
        sDeviceToken = deviceId(context);
        return sDeviceToken;
//		String deviceId = null;
//		String androidSerialId = null;
//
//		if (Build.VERSION.SDK_INT > Build.VERSION_CODES.FROYO) {
//			try {
//				// 设备序列号，获取需要API>8
//				androidSerialId = Build.SERIAL;
//			} catch (NoSuchFieldError ignored) {
//			}
//		}
//
//		if (!TextUtils.isEmpty(androidSerialId)
//				&& !Build.UNKNOWN.equals(androidSerialId)
//				&& !isBadSerial(androidSerialId)) {
//			deviceId = androidSerialId;
//		} else {
//			// 获取Secure的android_id，恢复出厂设置之后有可能会变
//			String androidSecureId = Settings.Secure.getString(
//					context.getContentResolver(), Settings.Secure.ANDROID_ID);
//			if (!TextUtils.isEmpty(androidSecureId)
//					&& !EMULATOR_ANDROID_ID.equals(androidSecureId)
//					&& !isBadDeviceId(androidSecureId)
//					&& androidSecureId.length() == EMULATOR_ANDROID_ID.length()) {
//				deviceId = androidSecureId;
//			} else {
//				deviceId = SoftInstallationId.generateId(context);
//			}
//		}
//		sDeviceToken = UUID.nameUUIDFromBytes(deviceId.getBytes()).toString();
//		return sDeviceToken;
    }

    private static String deviceId(Context context) {
        String deviceId = StorageUtil.getInstance().getString(DEVICEID_KEY, "");
        if (TextUtils.isEmpty(deviceId)) {
            if (ContextCompat.checkSelfPermission(context, android.Manifest.permission.READ_PHONE_STATE) == 0) {
                try {
                    TelephonyManager phoneManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
                    deviceId = phoneManager.getDeviceId();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        if (isBadDeviceId(deviceId)) {
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.FROYO) {
                try {
                    // 设备序列号，获取需要API>8
                    deviceId = Build.SERIAL;
                } catch (NoSuchFieldError ignored) {
                }
            }
            if (TextUtils.isEmpty(deviceId)) {
                deviceId = UUID.randomUUID().toString();
            } else {
                deviceId = UUID.nameUUIDFromBytes(deviceId.getBytes()).toString();
            }
        }
        if (!TextUtils.isEmpty(deviceId)) {
            StorageUtil.getInstance().putString(DEVICEID_KEY, deviceId);
        }
        return deviceId;
    }

    /**
     * 判断该id是否是无效的deviceId
     *
     * @param id
     * @return
     */
    private static boolean isBadDeviceId(String id) {
        // 空串或者只包含0或者空格，视为无效的deviceId
        return TextUtils.isEmpty(id)
                || TextUtils.isEmpty(id.replace('0', ' ').replace('-', ' ')
                .trim());
    }

    /**
     * 判断该id是否是无效的SerialId
     *
     * @param id
     * @return
     */
    @SuppressLint("DefaultLocale")
    private static boolean isBadSerial(String id) {
        if (TextUtils.isEmpty(id)) {
            return true;
        }
        id = id.toLowerCase();
        for (String pattern : BAD_SERIAL_PATTERNS) {
            if (id.contains(pattern)) {
                return true;
            }
        }
        return false;
    }

    public static String getPhoneNumber(Context context) {
        if (ContextCompat.checkSelfPermission(context, android.Manifest.permission.READ_PHONE_STATE) == 0) {
            try {
                TelephonyManager phoneManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
                return phoneManager.getLine1Number();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    public static String getIMEI(Context context) {
        if (ContextCompat.checkSelfPermission(context, android.Manifest.permission.READ_PHONE_STATE) == 0) {
            try {
                TelephonyManager phoneManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
                return phoneManager.getDeviceId();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    public static String getIMSI(Context context) {
        if (ContextCompat.checkSelfPermission(context, android.Manifest.permission.READ_PHONE_STATE) == 0) {
            try {
                TelephonyManager phoneManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
                return phoneManager.getSubscriberId();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    /**
     * 获取手机MAC
     *
     * @return 设备唯一标识
     */
    public static String getMAC(Context context) {
        String macAddress = null;
        try {
            WifiManager wm = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            if (wm != null) {
                WifiInfo wi = wm.getConnectionInfo();
                if (wi != null) {
                    String mac = wi.getMacAddress();
                    if (mac != null) {
                        macAddress = mac.replace(":", "");
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(macAddress)) {
            String str = "";
            try {
                Process pp = Runtime.getRuntime().exec("cat /sys/class/net/wlan0/address ");
                InputStreamReader ir = new InputStreamReader(pp.getInputStream());
                LineNumberReader input = new LineNumberReader(ir);
                for (; null != str; ) {
                    str = input.readLine();
                    if (str != null) {
                        macAddress = str.trim();// 去空格
                        break;
                    }
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return macAddress;
    }

    /**
     * 该类的作用是针对当前安装的Application生成id
     */
    private static class SoftInstallationId {

        /**
         * 缓存UUID的文件名
         */
        private static final String ID_FILENAME = "install";

        /**
         * 内存中缓存的UUID
         */
        private static String sUUID = null;

        /**
         * 在安装路径下生成一个名为“ID_FILENAME”的文件，然后用Java的UUID生成，存入该文件
         *
         * @param context
         * @return
         * @throws IOException
         */
        public synchronized static String generateId(Context context) {
            if (TextUtils.isEmpty(sUUID)) {
                try {
                    File installFile = new File(context.getFilesDir(),
                            ID_FILENAME);
                    if (!installFile.exists()) {
                        // 如果该文件不存在，表明第一次访问或者先前访问都失败
                        installFile.createNewFile();
                        // 创建UUID，并添加到内存缓存中
                        sUUID = generateUUID();
                        // 先创建该文件，然后生成UUID写入到该文件中
                        writeInstallationFile(installFile, sUUID);
                    } else {
                        // 如果该文件存在，从文件中读取UUID
                        sUUID = readInstallationFile(installFile);
                    }
                } catch (Exception e) {
                }
            }

            if (TextUtils.isEmpty(sUUID)) {
                // 如果读取文件中缓存的uuid失败，重新生成一个uuid返回
                sUUID = generateUUID();
            }
            return sUUID;
        }

        /**
         * 从文件缓存中读取uuid
         *
         * @param installFile
         * @return
         * @throws IOException
         */
        private static String readInstallationFile(File installFile)
                throws IOException {
            RandomAccessFile raFile = null;
            try {
                raFile = new RandomAccessFile(installFile, ACCESS_MODE);
                byte[] bytes = new byte[(int) raFile.length()];
                raFile.readFully(bytes);
                return new String(bytes, CHARSET);
            } finally {
                if (raFile != null) {
                    raFile.close();
                }
            }
        }

        /**
         * 将uuid存入文件缓存
         *
         * @param installFile
         * @param uuid
         * @throws IOException
         */
        private static void writeInstallationFile(File installFile, String uuid)
                throws IOException {
            FileOutputStream out = null;
            try {
                out = new FileOutputStream(installFile);
                out.write(uuid.getBytes(CHARSET));
            } finally {
                if (out != null) {
                    out.close();
                }
            }
        }

    }

    /**
     * 生成UUID
     *
     * @return
     */
    public static String generateUUID() {
        return UUID.randomUUID().toString();
    }
}