package com.healthlink.hms.utils

import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.healthlink.hms.server.data.dto.LaunchAfterBootDTO

/**
 * Created by imaginedays on 2024/8/2
 *
 *
 */
object LaunchAfterBootManager {
    private const val KEY_STORE_LAUNCH_INFO_AFTER_BOOT = "key_store_launch_info_after_boot"
    private const val TAG = "LaunchAfterBootManager"


    /**
     * 存储开机启动信息
     */
    fun storeLaunchAfterBootInfo() {
        Log.i(TAG, "设备启动")
        var dto = getLaunchAfterBootDTO()
        if (dto == null) {

            dto = LaunchAfterBootDTO()
            dto.recordBootTimestamp()
            dto.resetAppLaunchCount()
        } else {
            dto.recordBootTimestamp()
            dto.resetAppLaunchCount()
        }
        Log.i(TAG, "存储开机信息:$dto")
        storeToMMKVDisk(dto)
    }

    /**
     * 存储App启动次数
     */
    fun storeAppLaunchCount() {
        var dto = getLaunchAfterBootDTO()
        if (dto == null) {
            dto = LaunchAfterBootDTO()
            dto.recordBootTimestamp()
            dto.increaseAppLaunchCount()
        } else {
            dto.increaseAppLaunchCount()
        }
        Log.i(TAG, "storeAppLaunchCount:$dto")
        storeToMMKVDisk(dto)
    }

    private fun storeToMMKVDisk(bootDTO: LaunchAfterBootDTO){
        MMKVUtil.storeData(KEY_STORE_LAUNCH_INFO_AFTER_BOOT, Gson().toJson(bootDTO))
    }

    private fun getLaunchAfterBootDTO(): LaunchAfterBootDTO? {
        val json = MMKVUtil.getData(KEY_STORE_LAUNCH_INFO_AFTER_BOOT)
        if (json.isNullOrEmpty()) {
            return null
        }
        return parseJson(json)
    }

    private fun parseJson(json: String): LaunchAfterBootDTO {
        val gson = Gson()
        val listType = object : TypeToken<LaunchAfterBootDTO>() {}.type
        return gson.fromJson(json, listType)
    }
}