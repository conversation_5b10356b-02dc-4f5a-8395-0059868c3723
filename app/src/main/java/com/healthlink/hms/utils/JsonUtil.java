package com.healthlink.hms.utils;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.lang.reflect.Type;

/**
 * 对象与json之间的转换
 *
 *
 */
public class JsonUtil {
    private static Gson sGson = new Gson();

    /**
     * 将对象转成json
     *
     * @param obj
     * @return
     */
    public static String objectToJson(Object obj) {
        if (obj != null) {
            try {
                return sGson.toJson(obj);
//                return JSON.toJSONString(obj);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    /**
     * 把json数据解析成对象
     *
     * @param json
     * @param type
     * @return
     */
    public static <T> T jsonToObject(String json, Type type) {
        if (!TextUtils.isEmpty(json)) {
            try {
                return sGson.fromJson(json, type);
//                return JSON.parseObject(json, clazz);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    private static Gson getsGson() {

        GsonBuilder builder = new GsonBuilder();
        builder.disableHtmlEscaping();
        Gson gson = builder.create();
        return gson;
    }

    public static String toDisableHtmlJson(Object o) {
        if (null == o) {
            return "";
        }
        return getsGson().toJson(o);
    }
}
