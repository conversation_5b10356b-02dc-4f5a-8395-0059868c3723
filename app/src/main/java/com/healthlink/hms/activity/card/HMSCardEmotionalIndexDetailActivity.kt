package com.healthlink.hms.activity.card

import android.os.Bundle
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.activity.HMSBaseCardDetailActivity
import com.healthlink.hms.fragment.EmotionalIndexFragment
import com.healthlink.hms.utils.DataTrackUtil

class HMSCardEmotionalIndexDetailActivity :  HMSBaseCardDetailActivity() , HMSCardFragmentInteractWithAcInterface{
    override fun initFragments() {
        fragmentList.add(EmotionalIndexFragment.newInstance(TimeCode.TIME_CODE_DAY, userId,this))
        fragmentList.add(EmotionalIndexFragment.newInstance(TimeCode.TIME_CODE_WEEK, userId,this))
        fragmentList.add(EmotionalIndexFragment.newInstance(TimeCode.TIME_CODE_MONTH, userId,this))
        fragmentList.add(EmotionalIndexFragment.newInstance(TimeCode.TIME_CODE_YEAR, userId,this))
    }

    override fun getCardAuthStatus(): Boolean? {
        return true
    }

    override fun setTabVisibilityforNetErrorOrSettingView(visibility: Int) {
        binding.tabLayout.visibility = visibility
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        DataTrackUtil.dtEnterPage("Health_Bodytemperaturereports_PV",DataTrackUtil.userIDMap(userId))
    }

    override fun onDestroy() {
        DataTrackUtil.dtExitPage("Health_Bodytemperaturereports_Close",DataTrackUtil.userIDMap(userId))
        super.onDestroy()

    }
    override fun backToMain() {
        DataTrackUtil.dtClick(
            "Health_Bodytemperaturereports_Return_Click",
            DataTrackUtil.userIDMap(userId)
        )
        super.backToMain()
    }
}