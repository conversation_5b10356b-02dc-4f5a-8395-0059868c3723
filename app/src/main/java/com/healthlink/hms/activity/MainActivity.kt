package com.healthlink.hms.activity

import android.annotation.SuppressLint
import android.app.Dialog
import android.app.Notification
import android.app.NotificationManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.IntentFilter
import android.content.SharedPreferences
import android.content.pm.PackageManager.PERMISSION_GRANTED
import android.content.res.Resources
import android.graphics.drawable.Animatable2
import android.graphics.drawable.AnimatedVectorDrawable
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.util.Log
import android.view.Gravity
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.ui.platform.ComposeView
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.gson.Gson
import com.gwm.tts.service.client.GwmTTSManager
import com.gwm.tts.service.request.StreamChannel
import com.healthlink.hms.BuildConfig
import com.healthlink.hms.R
import com.healthlink.hms.R.layout
import com.healthlink.hms.activity.viewmodel.MainDataModel
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.base.Constants
import com.healthlink.hms.business.doctorcall.DoctorCallManager
import com.healthlink.hms.business.medai.HealthSceneViewModel
import com.healthlink.hms.business.medai.view.HealthWarningAlert
import com.healthlink.hms.business.medai.view.MedAIChatView
import com.healthlink.hms.fragment.HMSHealthDetailFragment
import com.healthlink.hms.fragment.HMSHuaweiOAuthFragment
import com.healthlink.hms.fragment.HMSServiceListFragment
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.ktExt.setUpSystemBar
import com.healthlink.hms.medchart.viewmodel.LLMViewModel
import com.healthlink.hms.mvvm.model.request.HealthInfoRequestParam
import com.healthlink.hms.reciever.HMSAction
import com.healthlink.hms.utils.AlertDialogUtil
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.HMSDialogUtils
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.ToastUtil
import com.healthlink.hms.viewmodels.MainViewModel
import com.healthlink.hms.views.ImmersiveDialog
import com.healthlink.hms.views.dialog.HmsDialog
import com.petterp.floatingx.FloatingX
import com.petterp.floatingx.assist.FxDisplayMode
import com.petterp.floatingx.assist.FxGravity
import com.petterp.floatingx.assist.FxScopeType
import com.petterp.floatingx.util.FxScrollImpl
import com.petterp.floatingx.view.IFxInternalHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import me.jessyan.autosize.AutoSizeCompat
import me.jessyan.autosize.utils.AutoSizeUtils
import java.lang.ref.WeakReference
import kotlin.getValue


class MainActivity : AppCompatActivity(), HMSHealthDetailFragment.OpenAuthorizedInterface {

    // 触发事件来源
    val mTag = "MainActivity"
    private val TAG = "TAG_SPLASH_MAIN"  // 日志标签
    private var mContext = this

    // 是否数据已经加载。onResume时根据该状态判断是否需要加载数据。
    private var isDataLoaded = false;

    // GWM 的地图服务连接是否已经建立
    private var isMapServiceConnected = false
    private var isPrivacyMode = false

    private lateinit var mSharePreference: SharedPreferences
    private var healthDetailFragment: HMSHealthDetailFragment? = null
    private lateinit var serviceListFragment: HMSServiceListFragment
    private lateinit var oauthFragment: HMSHuaweiOAuthFragment

    private lateinit var mainViewModel: MainViewModel
    private lateinit var myReceiver: BroadcastReceiver

    // 用户协议 隐私政策 二级dialog
    private lateinit var privacyDialog: ImmersiveDialog

    //    private var privacyType = 0
//    private val userAgreementUrl = "https://pages.healthlinkiot.com/doc/UserAgreement1.3.htm"
//    private val privacyStatementUrl = "https://pages.healthlinkiot.com/doc/PrivacyStatement1.3.htm"

    private val handler = Handler(Looper.getMainLooper())

    private var forceUpgrade = false  //强制升级  是否

    private var upgradeTitle = ""  //升级弹窗标题

    private var upgradeContent = ""  //升级弹窗内容

    private var needUpgrade = false //判断是否需要更新  用于第一次onresume的标记位

    private var upgradeReady = false  //判断升级逻辑是不是走完  走完之后才可以数据刷新

    private var isBackEnabled = true // 如果不是强制更新 true 表示可以点击返回
    // 是否正在现实升级也页面
    private var isShowingUpgrade = false
    // 是否正在展示banner
    private var isShowingBanner = false

    // 通知相关
    //渠道Id
    private val channelId = "HMS_001"

    //渠道名
    private val channelName = "数字健康"

    //渠道重要级
    private val importance = NotificationManager.IMPORTANCE_HIGH

    //通知管理者
    private lateinit var notificationManager: NotificationManager

    //打开智能腰托系统消息通知
    private lateinit var notificationForSeatWaistDirection: Notification

    //上次刷新数据的时间戳
    private var lastTimestamp: Long = 0L

    var isCanRefresh = true

    // 记录用于恢复的数据
    private var mainDataModel = MainDataModel()

    // 记录用户协议内容弹窗是否在onPause前的状态
    private var privacyContentTypeBeforeOnPause = 0

    private var dialogUtil: AlertDialogUtil = AlertDialogUtil(this)

    //一分钟自刷新：更新桌面组件
    private var reciever = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (isCanRefresh(30)) {
                // sharePreference 添加标志位
                mSharePreference = mContext.getSharedPreferences(
                    Constants.SHARE_RECEIVE_WIDGET_DATA,
                    AppCompatActivity.MODE_PRIVATE
                )!!
                mSharePreference.edit().putBoolean(Constants.RECEIVE_WIDGET_DATA, true).apply()

//                Log.i("TAG_THEME_TOAST"," ------1")
                loadHealthInfo()

            }
            Log.i(mTag, "onReceive: 收到广播 $intent")
        }
    }


    fun isCanRefresh(time: Int): Boolean {
        //获取当前时间
        val currentTimeMillis = System.currentTimeMillis()
        // 计算时间差
        val timeDifferenceMillis = currentTimeMillis - lastTimestamp
        // 转换为秒、分钟或小时等
        val timeDifferenceSeconds = timeDifferenceMillis / 1000

        isCanRefresh = timeDifferenceSeconds >= time
        return isCanRefresh
    }

//    private var isInitFinished = false

//    private fun sendInitRequest() {
//        // 如果联网调用初始化接口
//        val isConnect = HmsApplication.isNetworkConn()
//        if(isConnect) {
//            HmsApplication.sendInitInfoReq { _ ->
//            }
//        }
//        isInitFinished = true
//    }

    override fun onCreate(savedInstanceState: Bundle?) {
        Log.d(mTag, "onCreate")
//        // 启动 Splash
//        val splashScreen = installSplashScreen()
//        // 设置一个条件来延迟启动动画消失
//        splashScreen.setKeepOnScreenCondition {
//            !isInitFinished
//        }
//
//        sendInitRequest()

        super.onCreate(savedInstanceState)
        Log.i(TAG, " -----:onCreate ")

        //plan B
//        selectTheme()

        if (savedInstanceState != null) {
            try {
                var mainModelContentString = savedInstanceState.getString("saved_data")
                if (mainModelContentString != null && mainModelContentString.isNotEmpty()) {
                    var gson = Gson()
                    this.mainDataModel =
                        gson.fromJson(mainModelContentString, MainDataModel::class.java)
                    this.upgradeReady = this.mainDataModel.upgradeRready
                    this.isShowingUpgrade = this.mainDataModel.isShowingUpgrade
                    this.isShowingBanner = this.mainDataModel.isShowingBanner
                }
            } catch (ex: Exception) {
                Log.d(mTag, "restore data from savedInstanceState fail. ${ex.message}")
            }
        }

        // 初始化viewmodel
        mainViewModel = ViewModelProvider(this).get(MainViewModel::class.java)

        val weakReference = WeakReference(this)
        Log.i(mTag, "oncreate: intent.action ${intent.action}")
        Log.i(mTag, "oncreate: mainViewModel.onThemeChanged =${mainDataModel.onThemeChanged}")
         if (intent.action == HMSAction.ACTION_OPEN_PHONE_DOCTOR_SERVICE && !mainDataModel.onThemeChanged) {
            // 2、是否通话中 直接返回
             if (!HmsApplication.isAgreePrivacy()) {
                 if (!HmsApplication.getIsShowUserPrivacyToast()) {
//                     ToastUtil.makeText(HmsApplication.appContext,
//                         getString(R.string.agree_privacy_toast_text),Toast.LENGTH_SHORT).show()
                     HmsApplication.storeShowUserPrivacyToast(true)
                 } else {
                     Log.i(mTag,"显示过 toast")
                 }

                 if(!DoctorCallManager.isInitialized){
                     DoctorCallManager.init(weakReference.get(), false)
                 }

                 // 如果初始化信息还未初始化，则初始化（桌面卡片进入时，会有此情况）
                 if(!HmsApplication.isInitSuccess){
                     HmsApplication.sendInitInfoReq { _ ->
                     }
                 }

             } else if (DoctorCallManager.inCallSession) {
                Log.i(mTag, "oncreate: 通话中不做任何操作")
            } else {
                DoctorCallManager.init(weakReference.get(), false) {
                    if (isFinishing || isDestroyed) {
                        return@init
                    }

                    // 1、隐私模式
                    // 2、无网络，重试
                    // 3、直接拨打

                    // 私密模式弹框 返回
                    if (HmsApplication.isPrivacyModeEnabled()) {
                        var message =
                            getString(R.string.dialog_service_tel_doc_content_privacy)
                        ToastUtil.makeText(HmsApplication.appContext, message, Toast.LENGTH_SHORT).show()
                        return@init
                    }

                    // 无网络
                    if (!HmsApplication.isNetworkConn()) {
                        retryCallDoctorCall()
                        return@init
                    }

//                 1、是否是通话中的判断
                    DoctorCallManager.call(weakReference.get())
                }
            }


        } else {
            if(!DoctorCallManager.isInitialized){
                DoctorCallManager.init(weakReference.get(), false)
            }
        }

        // 设置内容
        setContentView(layout.activity_main)

        // 可选：添加自定义动画
//        splashScreen.setOnExitAnimationListener { splashScreenViewProvider ->
//            splashScreenViewProvider.view.animate()
//                .alpha(0f)
//                .setDuration(300L)
//                .withEndAction {
//                    splashScreenViewProvider.remove()
//                }.start()
//        }

        // 设置系统状态条
        setUpSystemBar()
        // 初始化地图服务
//        initMapService(this)
        //监听隐私主动
        isPrivacyMode = HmsApplication.isPrivacyModeEnabled()
        //注册私密模式广播
        receiveBroadcast()

        // 监听下拉刷新 TODO 下来刷新的事件，直接调用loadHealthInfo是否可以？
        mainViewModel.userRefreshFlag.observe(this) { isRefresh: Boolean ->
            if (isRefresh) {
//                Log.i("TAG_THEME_TOAST"," ------2")
                loadHealthInfo()
                Log.d(mTag, "do loadHealthInfo() from mainViewModel.userRefreshFlag.observe")
            }
        }
        //版本信息获取
        registerVersionInfoObserver()
        //注册个人信息监听
        registerUserInfoObserver()
        // 注册widget广播接收器
        initWidgetBroadcastReciever()
        //初始化二级dialog
        privacyDialog = ImmersiveDialog(
            mContext,
            R.style.MyDialogStyle
        )
        //判断是否要显示banner组件
        isShowBanner()
        mainDataModel.onThemeChanged = false
    }

    private fun selectTheme() {
        if (BuildConfig.PLATFORM_CODE == "V4") {
            setTheme(R.style.Theme_HMS)
        } else {
            setTheme(R.style.SplashUITheme)
        }
    }

    private fun retryCallDoctorCall() {
        val msg = resources.getString(R.string.doctor_call_warning_on_no_network_msg)
        HMSDialogUtils.showHMSDoctorPhoneExitsDialog(
            mContext,
            R.layout.hms_dialog_call_no_network,
            msg,
            "重新拨打"
        ) { isPositive ->
            if (isPositive) {
                if (!HmsApplication.isNetworkConn()) {
                    Handler().postDelayed({retryCallDoctorCall()}, 100)
                } else {
                    when {
                        // 通话中
                        DoctorCallManager.inCallSession -> {
                            val msg = resources.getString(R.string.doctor_call_warning_on_calling_msg)
                            HMSDialogUtils.showHMSDoctorPhoneExitsDialog(
                                mContext,
                                R.layout.hms_dialog_doctor_phone_exist,
                                msg,
                                "知道了"
                            ) {}
                        }

                        else -> {
//                            if (isPlaying()) {
//                                mCurrentPlayTTSId?.let {
//                                    GwmTTSManager.getInstance().stopTTS(it)
//                                }
//                            }
                            GwmTTSManager.getInstance().stopAllTTS(StreamChannel.OTHER)
                            val weakContext = WeakReference(this@MainActivity)
                            DoctorCallManager.call(weakContext.get())
                        }
                    }
                }
            }
        }
    }

   // region 健康异常提示
   fun showGlobalHealthWarningAlert(title: String,
        onCallDoctor: () -> Unit,
        onAIConsultation: () -> Unit,
        onNavigateToHospital: () -> Unit
    ) {
        val container = findViewById<FrameLayout>(R.id.global_compose_alert_container)
        container.removeAllViews()
        val composeView = ComposeView(this)

        composeView.setContent {
            HealthWarningAlert(
                title,
                onDismiss = {
                    container.removeAllViews()
                    container.visibility = View.GONE
                },
                onCallDoctor = onCallDoctor,
                onAIConsultation = onAIConsultation,
                onNavigateToHospital = onNavigateToHospital
            )
        }
        container.addView(composeView)
        container.visibility = View.VISIBLE
    }
    // endregion


    // 未登录：第一次进入时 先弹banner ，banner关闭后 检查是否需要更新的逻辑
    private fun bannerDismiss() {
        // 如果升级信息未准备好，请求获取更新信息
        if(!upgradeReady){
            val packageInfo = this.packageManager.getPackageInfo(this.packageName, 0)
            val versionCode = packageInfo.longVersionCode
            mainViewModel.getVersionData(versionCode.toString())
        }
        // 如果请求更新信息已经获取好，则处理隐私弹窗
        else  if (!MMKVUtil.getPrivacyPolicy()) {
            lifecycleScope.launch {
                delay(500)
                while (!upgradeReady) {
                    delay(100) // 每隔100毫秒检查一次
                }
                if (needUpgrade) {
                    openUpgradeDialog(upgradeTitle, upgradeContent, forceUpgrade)
                } else {
                    delay(500)
                    Log.d("TAG_DIALOG_SHOW","doPrivacyDialog ---1")
                    doPrivacyDialog()
                }
            }
        }
    }

    /**
     * show之前需满足几个条件
     * 1. 未登录状态
     * 2. 每次冷启动 --> 车重新开机
     * 3. 未选择下次不再提醒
     * tip：优先级高于权限dialog
     */
    private fun isShowBanner() {

        Log.d(
            mTag, "!MMKVUtil.getBannerAlreadyTips()=${!MMKVUtil.getBannerAlreadyTips()} " +
                    "&& MMKVUtil.isVisitorMode()=${MMKVUtil.isVisitorMode()} " +
                    "&& MMKVUtil.getBannerTips()=${MMKVUtil.getBannerTips()} "
        )


        // 判断是否要弹出欢迎页
        if ((!MMKVUtil.getBannerAlreadyTips() && MMKVUtil.isVisitorMode() && MMKVUtil.getBannerTips())
            || this.isShowingBanner) {
//            val dialogUtil = AlertDialogUtil(this)
            dialogUtil.showBannerDialog {
                MMKVUtil.storeAlreadyBannerTips(true)
                bannerDismiss()
                this.isShowingBanner = false
            }
            this.isShowingBanner = true
        }
        // 如果不弹欢迎页，判断升级信息是否已经准备好，如果未获取，则请求升级信息
        else if(!upgradeReady){
            mainViewModel.getVersionData(BuildConfig.VERSION_CODE.toString())
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Log.i(TAG, " -----:onNewIntent ")
        Log.i(mTag, "onNewIntent: $intent")
        if (intent.action === HMSAction.ACTION_OPEN_PHONE_DOCTOR_SERVICE) {
            // 停止播放 TTS
            GwmTTSManager.getInstance().stopAllTTS(StreamChannel.OTHER)

            if (checkDoctorCallConditions()) {
                // 拨打电话
                val weakContext = WeakReference(this@MainActivity)
                DoctorCallManager.call(weakContext.get())
            }
        }
    }

    /**
     * 检测拨号前置条件
     * 1、是否同意用户政策与隐私协议 toast 提示
     * 2、是否在通话中 无 toast 提示
     * 3、是否在私密模式 toast 提示
     * 4、无网络 重试弹窗
     */
    private fun checkDoctorCallConditions() : Boolean {

        // 是否同意用户政策与隐私协议
        if (!HmsApplication.isAgreePrivacy()) {
            if (!HmsApplication.getIsShowUserPrivacyToast()) {
//                ToastUtil.makeText(HmsApplication.appContext,
//                    getString(R.string.agree_privacy_toast_text),Toast.LENGTH_SHORT).show()
                HmsApplication.storeShowUserPrivacyToast(true)
                return false
            }
        }

        // 是否在通话中
        if (DoctorCallManager.inCallSession) {
            return false
        }

        if (HmsApplication.isPrivacyModeEnabled()) {
            val message =
                getString(R.string.dialog_service_tel_doc_content_privacy)
            ToastUtil.makeText(HmsApplication.appContext, message, Toast.LENGTH_SHORT).show()
            return false
        }

        // 无网络
        if (!HmsApplication.isNetworkConn()) {
            retryCallDoctorCall()
            return false
        }

        return true
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if(grantResults.isEmpty()) return

        if (requestCode == DoctorCallManager.PERMISSION_REQUEST_CODE) {
            var isAllGranted = true
            grantResults.forEach {
                if (it != PERMISSION_GRANTED) {
                    isAllGranted = false
                }
            }
            if (isAllGranted) {
                DoctorCallManager.onRecordPermissionGranted(this)
            } else {
                if(BuildConfig.PLATFORM_CODE == "V35") {
                    // v35 单独适配这个弹窗显示，其余走原来逻辑
                    val toast = Toast.makeText(this, "录音权限未授予，无法拨打电话", Toast.LENGTH_SHORT)
                    toast.setGravity(Gravity.TOP or Gravity.CENTER_VERTICAL,0,100.dp.toInt())
                    toast.show()
                } else {
                    ToastUtil.makeText(this,"录音权限未授予，无法拨打电话",Toast.LENGTH_SHORT).show()
                }
            }
        }
    }


    /**
     * 显示隐私对话框
     */
    override fun onStop() {
        super.onStop()
        Log.i(TAG, " -----:onStop ")
        DataTrackUtil.dtEnterPage("Health_Homepage_Close")
    }

    private fun showPrivacyDialog() {
        Log.i("TAG_DIALOG_SHOW","--------- mPrivacyDialog?.isShowing   ${mPrivacyDialog?.isShowing}")
        if (true == mPrivacyDialog?.isShowing) {
            return
        }
        Log.d("TAG_DIALOG_SHOW", "showPrivacyDialog")
        showPrivacyDialog(
            this,
            resources.getString(R.string.dialog_privacy_agree_content),
            "同意",
            "取消"
        ) { isPositive ->
            if (isPositive) {
                // 同意
                Log.i("TAG_DIALOG_SHOW","=================put 3")
                MMKVUtil.storePrivacyPolicy(true)
                showHuaweiLogin()
            } else {
                finish()
            }
        }
    }

    private fun showHuaweiLogin() {
        // 打开华为授权登录界面
        startActivity(Intent(this, HuaweiOAuthActivity::class.java))
        overridePendingTransition(R.anim.activity_enter_dialog, R.anim.activity_stay)
    }

    // 加载fragment
    private fun initContentFragment(isForceRefreshDetailFragment: Boolean = false) {
        // 服务列表
        // 检查是否已经有 Fragment 被恢复或添加在布局容器中
        initServieListFragment()
        // 隐私模式
        isPrivacyMode = HmsApplication.isPrivacyModeEnabled()

        // 右侧内容页
        initHealthDetailFragment()

        // 来自语音开关私密模式
        if (isForceRefreshDetailFragment) {
//            Log.i("TAG_THEME_TOAST"," ------3")
            loadHealthInfo()
            return
        }

        lifecycleScope.launch {
            //TODO: 这里应该等待upgrade的结果 不允许自动刷新 当有结果了才允许刷新  但似乎没有效果 是不是在别的地方出发了刷新？
            while (!upgradeReady) {
                delay(100) // 每隔100毫秒检查一次
            }
            Log.i(
                mTag,
                "refreshContentFragment 是否执行了自动下拉刷新: ${mainViewModel.getIsAutoRefresh().value}"
            )
            if (mainViewModel.getIsAutoRefresh().value == true) {
                Log.i(mTag, "refreshContentFragment: 已经执行了自动下拉刷新")
                // 非私密模式才执行刷新
                if (!HmsApplication.isPrivacyModeEnabled()) {
                    Log.i(mTag, "refreshContentFragment: 非私密模式才执行刷新")
                    if (isCanRefresh(1)) {
//                        Log.i("TAG_THEME_TOAST"," ------4")
                        loadHealthInfo()
                    }
                }
            }
        }

        // 如果执行了右边卡片的自动下拉刷新这里才执行

    }

    /**
     * 初始化健康卡片的内容
     */
    private fun initHealthDetailFragment() {
        var fragment = supportFragmentManager.findFragmentById(R.id.center_fragment_container)
        if (fragment == null) {
//            //游客模式
//            if (MMKVUtil.isVisitorMode()) {
//                // TODO 游客模式，判断私密模式开启，开启的话，展示详情页面？游客模式应该不展示信息吧？
//                if (HmsApplication.isPrivacyModeEnabled()) {
//                    if (healthDetailFragment == null) {
//                        healthDetailFragment = HMSHealthDetailFragment()
//                        healthDetailFragment!!.setCallback(this)
//                        supportFragmentManager.beginTransaction()
//                            .add(R.id.center_fragment_container, healthDetailFragment!!)
//                            .commit()
//                    }
//                } else {
//                    if (healthDetailFragment == null) {
//                        healthDetailFragment = HMSHealthDetailFragment()
//                        healthDetailFragment!!.setCallback(this)
//                        supportFragmentManager.beginTransaction()
//                            .add(R.id.center_fragment_container, healthDetailFragment!!)
//                            .commit()
//                    }
//                }
//
//            } else {
//                if (healthDetailFragment == null) {
            fragment = HMSHealthDetailFragment()
            fragment!!.setCallback(this)
            supportFragmentManager.beginTransaction()
                .add(R.id.center_fragment_container, fragment!!)
                .commit()
            this.healthDetailFragment = fragment;
//                }
//            }
            //            loadHealthInfo("resume MainActivity")
        } else {
            // 更新
            this.healthDetailFragment = fragment as HMSHealthDetailFragment
            this.healthDetailFragment!!.onResume()
        }
    }

    private fun initServieListFragment() {
        val fragment = supportFragmentManager.findFragmentById(R.id.main_body_service_list)
        if (fragment == null) {
            // 如果没有，则创建并添加 Fragment
            serviceListFragment = HMSServiceListFragment()
            supportFragmentManager.beginTransaction()
                .add(R.id.main_body_service_list, serviceListFragment)
                .commit()
        } else {
            // 如果有，则从 FragmentManager 恢复 Fragment 实例
            serviceListFragment = fragment as HMSServiceListFragment
            serviceListFragment!!.onResume()
        }
        // 获取健康Tips 后进行播报
        //        mainViewModel.getHealthTips()
        if (serviceListFragment.savedUIDataMode == null) {
            mainViewModel.getHealthTipsWithVin(MMKVUtil.getVinCode()!!)
        } else {
            // 进行白天模式切换
            mainViewModel.getHealthTipsWithVin(MMKVUtil.getVinCode()!!)
        }
    }

    override fun onResume() {
        super.onResume()

        Log.d(mTag, "onResume")
        Log.i(TAG, " -----:onResume ")

        doResume()

        DataTrackUtil.dtEnterPageWithMode("Health_Homepage_PV")
    }

    fun resumeServiceListFragment() {
        supportFragmentManager.findFragmentById(R.id.main_body_service_list)?.onResume()
    }

    /**
     * 显示访客模式UI
     */
    fun showVisitorMode() {
        //服务列表页面
        val serviceFragment = supportFragmentManager.findFragmentById(R.id.main_body_service_list)
        if (serviceFragment != null) {
            // 如果有，则从 FragmentManager 恢复 Fragment 实例
            this.serviceListFragment = serviceFragment as HMSServiceListFragment
            this.serviceListFragment!!.initVisitorModeUI()
        }
        // 卡片页面
        var detailFragment = supportFragmentManager.findFragmentById(R.id.center_fragment_container)
        if (detailFragment != null) {
            this.healthDetailFragment = detailFragment as HMSHealthDetailFragment
            this.healthDetailFragment!!.showDefaultUI()
        }
    }


    fun doResume() {
        val sharedPrefs = getSharedPreferences(Constants.SHARE_IS_USER_RETURN_TO_MAIN, MODE_PRIVATE)
        if (sharedPrefs.getBoolean(Constants.BACK_TO_MAIN, false)) {
            // 禁用触摸事件
            window.setFlags(
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
            )

            // 设置延迟，等待动画结束后启用触摸事件
            window.decorView.postDelayed({
                window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
            }, 500) // 替换为实际动画时长

            sharedPrefs.edit().putBoolean(
                Constants.BACK_TO_MAIN, false
            ).apply()
        }

        // 弹出隐私协议.

        lifecycleScope.launch {
            Log.d(mTag,"upgradeReady -> ${upgradeReady}")
            while (!upgradeReady) {
//                Log.d(mTag,"upgradeReady -> ${upgradeReady}")
                delay(100) // 每隔100毫秒检查一次
            }
            // 未登录： 1、if 展示隐私弹窗 -> 进行是否升级的判断
            // 未登录：2、else 展示过隐私弹窗 —> 进行是否升级的判断
            Log.d("TAG_DIALOG_SHOW","doPrivacyDialog ---2")
            doPrivacyDialog()

        }

        // 数据已准备好，执行代码
//        Log.i("TAG_THEME_TOAST"," ------initContentFragment  1")
        initContentFragment()


    }

    fun doPrivacyDialog(){
        Log.d("TAG_DIALOG_SHOW","doPrivacyDialog --- ${MMKVUtil.getPrivacyPolicy()}")
        if (!MMKVUtil.getPrivacyPolicy()) {
            //
            //先跳转
            // 通过点击应用图标的时候，需要将临时缓存的弹窗状态记录下来，此处恢复
            if (privacyContentTypeBeforeOnPause > 0) {
                <EMAIL> = privacyContentTypeBeforeOnPause
            }

            // 如果引导页存在且展示中，则不展示隐私弹窗
            if(<EMAIL>!=null && <EMAIL>()){
                // Do Nothing
                Log.d("TAG_DIALOG_SHOW","banner is showing , don't show privacy dialog")
            }else if(<EMAIL>){
                // DO Nothing
                Log.d("TAG_DIALOG_SHOW","upgrade dialog is showing , don't show privacy dialog")
            }
            //如展示过banner，则不再展示就可以直接展示隐私协议
            // 或 如没有展示过banner，则判断是否点击过【不再提示了】则展示隐私协议
            else if (
                (MMKVUtil.getBannerAlreadyTips() || !MMKVUtil.getBannerTips()) || !dialogUtil.isDialogShowing) {
                Log.i("TAG_DIALOG_SHOW","getBannerAlreadyTips = true, show privacy dialog")

                Log.i("TAG_DIALOG_SHOW","--------- needUpgrade   $needUpgrade")
                if (needUpgrade) {
                    openUpgradeDialog(upgradeTitle, upgradeContent, forceUpgrade)
                } else {
                    showPrivacyDialog()
                    Log.i("TAG_DIALOG_SHOW","--------- privacyDialog?.isShowing   ${privacyDialog?.isShowing}")
                    if (privacyDialog?.isShowing == true){
                        return
                    }
                    isShowPrivacyContentDialog()
                }
            }
        }

//        else {
//            if (needUpgrade) {
//                openUpgradeDialog(upgradeTitle, upgradeContent, forceUpgrade)
//            }
//        }
    }

    override fun getResources(): Resources {
        GlobalScope.launch(Dispatchers.Main) {
            // 需要升级到 v1.1.2 及以上版本才能使用 AutoSizeCompat
            AutoSizeCompat.autoConvertDensityOfGlobal(super.getResources()) // 如果没有自定义需求用这个方法
        }
        return super.getResources()
    }

    /**
     * 加载健康数据
     */
    private fun loadHealthInfo() {
        if (MMKVUtil.isVisitorMode()) {
            return
        }

        if (MMKVUtil.getUserId() == null || MMKVUtil.getUserId()!!.isEmpty()) {
            return
        }

        if (!isDataLoaded) {
            isDataLoaded = true
        }

        if (!HmsApplication.isInitSuccess) {
            HmsApplication.sendInitInfoReq("MainActivity") { isSuccess ->
                Log.i(mTag, "首页调用初始化接口： $isSuccess")
            }
        }

        // 获取当前时间戳（毫秒）
        lastTimestamp = System.currentTimeMillis()

        // 发送实时健康状态请求
        sendLiveHealthStatus()

        isDataLoaded = false
    }

    /**
     * 发送实时健康状态请求
     * 1、非游客
     * 2、非私密模式
     */
    private fun sendLiveHealthStatus() {
        // 非私密模式
        if (HmsApplication.isPrivacyModeEnabled()) {
            Log.d(mTag, "privacy mode no need to refresh data.")
            return
        }
        // 如果无网络，提示网络异常。
        if (!HmsApplication.isNetworkConn()) {
            if (!HmsApplication.isDialogShow && !mainDataModel.onThemeChanged) {
//                Log.i("TAG_THEME_TOAST"," ------toast 1")
                ToastUtil.makeText(
                    this,
                    this.resources.getString(R.string.network_error_tips),
                    Toast.LENGTH_SHORT
                ).show()
            }
            // 关闭首页健康卡片下拉刷新
            mainViewModel.setUserRefreshFlag(false)
            return
        }
        // 获取健康总结及最新健康数据
        MMKVUtil.getUserId()?.let {
            var parameters = mapOf(
                "userId" to it
            )
            mainViewModel.sendLiveHealthStatusReq(parameters)

            val reqParam = HealthInfoRequestParam().apply {
                extenalId = "extenalId";//"DCETOHY3JJ0383608"
                channelId = "channelId";//"GMW-001"
                userId = it
            }
            Log.i(mTag, "loadHealthInfo reqParam: ${reqParam}")
            mainViewModel.getHomeNewHealthInfo(reqParam)
        }
    }

    private fun initWidgetBroadcastReciever() {
        val filter = IntentFilter()
        filter.addAction(HMSAction.ACTION_HMS_WIDGET_DATA_UPDATED)
        registerReceiver(reciever, filter)
    }

    /**
     * 初始化地图服务模块
     */
//    private fun initMapService(context: Context) {
//        MapServiceProxy.getInstance().init(context) { connected ->
//            isMapServiceConnected = connected
//        }
//    }


    private fun releaseDialog(dialog: Dialog?) {
        dialog?.let {
            if (it.isShowing){
                it.dismiss()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.i(TAG, " -----:onDestroy ")
        Log.d(mTag, "onDestroy")
        releaseDialog(privacyDialog)
        releaseDialog(mPrivacyDialog)
        releaseDialog(loadingDataDialog)
        try {
            /**停止当前播放TTS 从电话医生卡片进入后，播放 TTS ，之后侧边杀死应用，需要停止 TTS
            * 但白天黑夜模式切换时，不需要停止
             * 白天黑夜模式切换时，先走 onSaveInstanceState, 再走 onDestroy
            */
            if (!mainDataModel.onThemeChanged) {
                serviceListFragment?.stopCurrentTTS()
            }
            unRegisterBroadcast()
            reciever?.let {
                unregisterReceiver(it)
            }
            HMSDialogUtils.clearDialog()

            drawable?.unregisterAnimationCallback(animationCallback!!)
            drawable?.clearAnimationCallbacks()
            drawable = null
            animationCallback = null

            handler.removeCallbacksAndMessages(null)

        } catch (e: Exception) {
            Log.i(mTag, "${e.printStackTrace()}")
        }
    }

    override fun openAuthorizedFragment() {
        showHuaweiLogin()
    }

    /**
     * 动态注册广播接收器，监听私密模式开关
     */
    private fun receiveBroadcast() {
        myReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                val value = intent.getBooleanExtra("pm", false)
                if (value != isPrivacyMode) {
                    //刷新内容fragment
//                    Log.i("TAG_THEME_TOAST"," ------initContentFragment  2")
                    initContentFragment(true)

                    if (::serviceListFragment.isInitialized) {
                        serviceListFragment?.refreshHealthLiveCardUI()
                    }
                }
            }
        }
        val intentFilter = IntentFilter("com.healthlink.hms.privacymode")
        LocalBroadcastManager.getInstance(this).registerReceiver(myReceiver, intentFilter)
    }

    private fun unRegisterBroadcast() {
        LocalBroadcastManager.getInstance(this).unregisterReceiver(myReceiver)
    }


    private var mPrivacyDialog: HmsDialog? = null
    private fun showPrivacyDialog(
        context: Context,
        str: String,
        positiveBtnTitle: String,
        negativeBtnTitle: String,
        btnClickCallback: (isPositive: Boolean) -> Unit
    ) {
        // 如果对话框已经显示，先关闭它
//        try {
//            if ((mPrivacyDialog != null) && mPrivacyDialog!!.isShowing) {
//                mPrivacyDialog!!.dismiss()
//            }
//        } catch (e: Exception) {
//            e.printStackTrace()
//        } finally {
//            mPrivacyDialog = null
//        }
        if (null == mPrivacyDialog) {
            // 创建新的对话框
            mPrivacyDialog = HmsDialog(
                context,
                R.style.MyDialogStyle
            )
        } else {
            mPrivacyDialog!!.setUpSystemBar()
        }

        mPrivacyDialog!!.setOnShowListener {
            // 设置自定义的 OnKeyListener
            mPrivacyDialog!!.setOnKeyListener { dialogInterface, keyCode, event ->
                if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_DOWN) {
                    // 处理返回键事件
                    if (!MMKVUtil.getPrivacyPolicy()) {
                        finish()
                        return@setOnKeyListener false
                    }
                    true // 返回 true 表示事件被处理
                } else {
                    false // 返回 false 表示事件未处理，继续传递
                }
            }
        }

        var view: View = LayoutInflater.from(context).inflate(R.layout.hms_dialog_privacy, null)
        val titleTextView: TextView = view.findViewById(R.id.hms_dialog_title)
        titleTextView.text = context.getString(R.string.user_agreement_privacy_statement)
        var spanBuilder = SpannableStringBuilder(str)
        val debounceDelay: Long = 500 // 防抖延迟时间（毫秒）
        var lastClickTime: Long = 0

        val index1 = str.indexOf("《用户协议》")
        if (index1 != -1) {
            val clickableSpan: ClickableSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastClickTime > debounceDelay) {
                        lastClickTime = currentTime
                        HMSDialogUtils.showPrivacyContentDialog(
                            context,
                            context.getString(R.string.user_agreement),
                            Constants.USER_AGREEMENT_H5_URL,
                            privacyDialog,
                            mainDataModel
                        )
                        DataTrackUtil.dtClick(
                            "Health_Authorization_UserAgreement_Click",
                            DataTrackUtil.userIDMap()
                        )
                        mainDataModel.privacyContentType = 1
                    }
                }

                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.color = context.resources.getColor(R.color.health_report_highlight)
                    ds.isUnderlineText = false
                }
            }
            spanBuilder.setSpan(
                clickableSpan,
                index1,
                index1 + 6,
                Spannable.SPAN_INCLUSIVE_EXCLUSIVE
            )
        }
        val index2 = str.indexOf("《隐私声明》")
        if (index2 != -1) {
            val clickableSpan: ClickableSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastClickTime > debounceDelay) {
                        lastClickTime = currentTime
                        HMSDialogUtils.showPrivacyContentDialog(
                            context,
                            context.getString(R.string.privacy_statement),
                            Constants.PRIVACY_STATEMENT_H5_URL,
                            privacyDialog,
                            mainDataModel
                        )
                        DataTrackUtil.dtClick(
                            "Health_Authorization_PrivacyStatement_Click",
                            DataTrackUtil.userIDMap()
                        )
                        mainDataModel.privacyContentType = 2
                    }
                }

                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.color = context.resources.getColor(R.color.health_report_highlight)
                    ds.isUnderlineText = false
                }
            }
            spanBuilder.setSpan(
                clickableSpan,
                index2,
                index2 + 6,
                Spannable.SPAN_INCLUSIVE_EXCLUSIVE
            )
        }

        val msgTextView: TextView = view.findViewById(R.id.hms_dialog_message)
        msgTextView.movementMethod = LinkMovementMethod.getInstance()
        msgTextView.text = spanBuilder


        // 设置确认按钮
        val positiveButton: Button = view.findViewById(R.id.positiveButton)
        positiveButton.text = positiveBtnTitle
        positiveButton.setOnClickListener {
            btnClickCallback(true)
            DataTrackUtil.dtClick("Health_Authorization_Agree_Click", DataTrackUtil.userIDMap())
            DataTrackUtil.dtExitPage("Health_Authorization_Close", DataTrackUtil.userIDMap())
            mPrivacyDialog?.dismiss()
        }

        // 设置取消按钮
        val negativeButton: Button = view.findViewById(R.id.negativeButton)
        negativeButton.text = negativeBtnTitle
        negativeButton.setOnClickListener {
            btnClickCallback(false)
            DataTrackUtil.dtClick("Health_Authorization_Disagree_Click", DataTrackUtil.userIDMap())
            DataTrackUtil.dtExitPage("Health_Authorization_Close", DataTrackUtil.userIDMap())
            mPrivacyDialog?.dismiss()
        }

        mPrivacyDialog?.setContentView(view)

        // 使对话框全屏
        //dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        mPrivacyDialog?.window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT
        )
        mPrivacyDialog?.setContentView(view)
        context?.let {
            AutoSizeCompat.autoConvertDensityOfGlobal(context.resources)
        }

        mPrivacyDialog?.setOnDismissListener {
            Log.d(mTag, "mPrivacyDialog dismiss")
        }

        mPrivacyDialog?.show()
        DataTrackUtil.dtEnterPage("Health_Authorization_PV", DataTrackUtil.userIDMap())


    }

    private var loadingDataDialog: Dialog? = null

    private fun inflateLoadingContentView(): View {
        return LayoutInflater.from(this).inflate(R.layout.hms_dialog_loading_data, null)
    }

    // 网络请求加载框
    private fun showLoadingDataDialog() {

        // 如果有对话框展示，则不展示。
        if (HmsApplication.isDialogShow) {
            return;
        }

        // 如果网络请求已经完成，不再弹窗
        if (mainViewModel.networkLoadingFinished.value!!) {
            return
        }
        // 对网络请求完成进行监听
        mainViewModel.networkLoadingFinished.observe(this) {
            // 如果加载结束，让loading消失
            if (mainViewModel.networkLoadingFinished.value!!) {
                if ((loadingDataDialog != null) && loadingDataDialog!!.isShowing) {
                    loadingDataDialog!!.dismiss()
                }
            }
        }


        // 如果对话框已经显示，先关闭它
        try {
            if ((loadingDataDialog != null) && loadingDataDialog!!.isShowing) {
                loadingDataDialog!!.dismiss()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            loadingDataDialog = null
        }
        // 创建新的对话框
        loadingDataDialog = Dialog(
            this, R.style.TransparentDialog
        )

        val view = inflateLoadingContentView()
        showLoading(view)

        // 使对话框全屏
        loadingDataDialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        loadingDataDialog?.window?.setLayout(
            AutoSizeUtils.dp2px(this@MainActivity, 320f),
            AutoSizeUtils.dp2px(this@MainActivity, 320f),
        )

        loadingDataDialog?.setCancelable(true)

        loadingDataDialog?.setContentView(view)
        this@MainActivity?.let {
            AutoSizeCompat.autoConvertDensityOfGlobal(<EMAIL>)
        }
//        loadingDataDialog?.setOnDismissListener{
//            mainViewModel.networkLoadingFinishedType.value = "1"
//        }
        // 点击对话框外取消对话框 - 取消方式为1
//        view.setOnTouchListener { v: View?, event: MotionEvent ->
//            if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in view.top..view.bottom)) || (!(event.x.toInt() in view.left..view.right)))) {
//                mainViewModel.networkLoadingFinishedType.value = "1"
//                loadingDataDialog?.dismiss()
//            }
//            false
//        }
        // 允许点击对话框外部关闭对话框
        loadingDataDialog?.setCanceledOnTouchOutside(true)

        // 设置对话框取消时的监听器
        loadingDataDialog?.setOnCancelListener(DialogInterface.OnCancelListener { // 在这里处理点击对话框外部导致对话框取消的情况
            Log.d("Dialog", "Dialog was cancelled by clicking outside")
            mainViewModel.networkLoadingFinishedType.value = "1"
        })

        // 设置自定义的 OnKeyListener
        loadingDataDialog?.setOnKeyListener { dialogInterface, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_DOWN) {
                // 处理返回键事件
                mainViewModel.networkLoadingFinishedType.value = "1"
                loadingDataDialog?.dismiss()
                true // 返回 true 表示事件被处理
            } else {
                false // 返回 false 表示事件未处理，继续传递
            }
        }

        loadingDataDialog?.show()
    }

    private var drawable: AnimatedVectorDrawable? = null
    private var animationCallback: Animatable2.AnimationCallback? = null

    fun showLoading(loadingView: View) {
        val tv = loadingView.findViewById<TextView>(R.id.tv_loading_text)
        tv.text = "加载中..."
        // 给ImageView设置动画
        val imageView = loadingView.findViewById<ImageView>(R.id.iv_loading_amin)!!
        imageView.setImageResource(R.drawable.loading_80x80)

        drawable = imageView.drawable as AnimatedVectorDrawable
        animationCallback = object : Animatable2.AnimationCallback() {
            override fun onAnimationEnd(drawable: Drawable) {
                super.onAnimationEnd(drawable)
                (drawable as AnimatedVectorDrawable).start()
            }

            override fun onAnimationStart(drawable: Drawable?) {
                super.onAnimationStart(drawable)
                if (!HmsApplication.isNetworkConn()) {
                    showLoadingFailDialog()
                }
            }
        }
        drawable?.registerAnimationCallback(animationCallback!!)
        drawable?.start()
    }

    /**
     * 加载失败对话框
     */
    fun showLoadingFailLoading(loadingView: View) {
        drawable?.stop()
        animationCallback = null
        val tv = loadingView.findViewById<TextView>(R.id.tv_loading_text)!!
        tv.text = "加载失败"
        val imageView = loadingView.findViewById<ImageView>(R.id.iv_loading_amin)!!
        imageView.setImageResource(R.drawable.ic_loading_faild)
    }

    /**
     * 关闭加载对话框
     */
    fun dismissLoadingDataDialog() {
        try {
            if ((loadingDataDialog != null) && loadingDataDialog!!.isShowing) {
                loadingDataDialog!!.dismiss()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            loadingDataDialog = null
        }
    }

    /**
     * 显示加载失败对话框
     */
    fun showLoadingFailDialog() {
        if (loadingDataDialog != null && loadingDataDialog!!.isShowing) {
            val view = inflateLoadingContentView()
            showLoadingFailLoading(view)
            loadingDataDialog!!.setContentView(view)
            handler.postDelayed({
                dismissLoadingDataDialog()
            }, 3000)
        }
    }

    /**
     * 延迟加载
     */
    fun scheduleShowLoadingDialog() {

        handler.postDelayed({
            showLoadingDataDialog()
        }, 500)

    }

    override fun onPause() {
        super.onPause()
//        if (privacyDialog.isShowing)
//            privacyDialog.dismiss()
//        dismissLoadingDataDialog()
        Log.d(mTag, "onPause")
        Log.i(TAG, " -----:onPause ")

        privacyContentTypeBeforeOnPause = mainDataModel.privacyContentType
    }

    private fun registerUserInfoObserver() {
        mainViewModel.userInfoData.observe(this) {
            if (it.code == "0" && it.data != null) {
                val personInfo = it.data!!
                personInfo.nickName?.let { nickName ->
                    MMKVUtil.storeUserinfoNickname(nickName)
                }
                personInfo.gender?.let { gender ->
                    MMKVUtil.storeUserinfoGender(gender)
                }
                personInfo.birthYear?.let { birthYear ->
                    MMKVUtil.storeUserinfoBirthYear(birthYear)
                }
                personInfo.birthMonth?.let { birthMonth ->
                    MMKVUtil.storeUserinfoBirthMonth(birthMonth)
                }
                personInfo.height?.let { height ->
                    MMKVUtil.storeUserinfoHeight(height)
                }
                personInfo.weight?.let { weight ->
                    MMKVUtil.storeUserinfoWeight(weight)
                }
            }
        }
        requestUserInfo()
    }

    /**
     * 注册版本更新监听
     */
    private fun registerVersionInfoObserver() {
        val packageInfo = this.packageManager.getPackageInfo(this.packageName, 0)
        val versionCode = packageInfo.longVersionCode
        mainViewModel.upgradeVersion.observe(this) {
            if (it.code == "0" && it.data != null) {
                upgradeReady = false
                needUpgrade = false
                val versionInfoData = it.data
                if (versionInfoData?.upgradeFlag == 2) {
                    //强制
                    forceUpgrade = true
                    needUpgrade = true
                } else if (versionInfoData?.upgradeFlag == 1){
                    // 非强制更新
                    forceUpgrade = false
                    needUpgrade = true
                }else{
                    needUpgrade = false
                }
                upgradeTitle = versionInfoData?.upgradeTitle ?: ""
                upgradeContent = versionInfoData?.upgradeText ?: ""
                if (needUpgrade) {
                    upgradeReady = true
                    // 已登录情况：  主动触发  未登录模式  会在第一次的onResume中触发
                    openUpgradeDialog(upgradeTitle, upgradeContent, forceUpgrade)
                }else{
                    upgradeReady = true
                    Log.d("TAG_DIALOG_SHOW","doPrivacyDialog ---3")
                    doPrivacyDialog()
                }
            } else {
                upgradeReady = true
                Log.d("TAG_DIALOG_SHOW","doPrivacyDialog ---4")
                doPrivacyDialog()
            }
        }

    }

    private fun requestUserInfo() {
        if (!HmsApplication.isPrivacyModeEnabled() && !MMKVUtil.isVisitorMode()) {
            MMKVUtil.getUserId()?.let {
                mainViewModel.getUserInfoData(it)
            }
        }
    }

    /**
     * 通知停止下拉刷新的动画
     */
    public fun closeLoadingRefreshData() {
        mainViewModel.setUserRefreshFlag(false)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        Log.d(mTag, "onSaveInstanceState")

        var gson = Gson()
        mainDataModel.privacyContentType = privacyContentTypeBeforeOnPause
        mainDataModel.upgradeRready = this.upgradeReady
        mainDataModel.isShowingUpgrade = this.isShowingUpgrade
        mainDataModel.isShowingBanner = this.isShowingBanner
        privacyContentTypeBeforeOnPause = 0
        mainDataModel.onThemeChanged = true
        outState.putString("saved_data", gson.toJson(mainDataModel))
    }

    /**
     * 根据状态位确定是否展示隐私协议的内容对话框
     */
    private fun isShowPrivacyContentDialog() {
        Log.i("TAG_DIALOG_SHOW","--------- mainDataModel.privacyContentType   ${mainDataModel.privacyContentType}")
        when (mainDataModel.privacyContentType) {
            1 -> {
                privacyDialog?.let {
                    HMSDialogUtils.showPrivacyContentDialog(
                        mContext,
                        mContext.getString(R.string.user_agreement),
                        Constants.USER_AGREEMENT_H5_URL,
                        it,
                        mainDataModel
                    )
                }
                DataTrackUtil.dtClick(
                    "Health_Authorization_UserAgreement_Click",
                    DataTrackUtil.userIDMap()
                )
                mainDataModel.privacyContentType = 1
            }

            2 -> {
                privacyDialog?.let {
                    HMSDialogUtils.showPrivacyContentDialog(
                        mContext,
                        mContext.getString(R.string.privacy_statement),
                        Constants.PRIVACY_STATEMENT_H5_URL,
                        it,
                        mainDataModel
                    )
                }
                DataTrackUtil.dtClick(
                    "Health_Authorization_PrivacyStatement_Click",
                    DataTrackUtil.userIDMap()
                )
                mainDataModel.privacyContentType = 2
            }
        }
    }

//    fun resumeShowPrivicyDialog() {
//        showPrivacyDialog()
//        if (privacyDialog.isShowing) {
//            return
//        }
//        when (mainDataModel.privacyContentType) {
//            1 -> {
//                HMSDialogUtils.showPrivacyContentDialog(
//                    mContext,
//                    mContext.getString(R.string.user_agreement),
//                    userAgreementUrl,
//                    privacyDialog,
//                    mainDataModel
//                )
//                DataTrackUtil.dtClick(
//                    "Health_Authorization_UserAgreement_Click",
//                    DataTrackUtil.userIDMap()
//                )
//                mainDataModel.privacyContentType = 1
//            }
//
//            2 -> {
//                HMSDialogUtils.showPrivacyContentDialog(
//                    mContext,
//                    mContext.getString(R.string.privacy_statement),
//                    privacyStatementUrl,
//                    privacyDialog,
//                    mainDataModel
//                )
//                DataTrackUtil.dtClick(
//                    "Health_Authorization_PrivacyStatement_Click",
//                    DataTrackUtil.userIDMap()
//                )
//                mainDataModel.privacyContentType = 2
//            }
//        }
//    }

    fun openUpgradeDialog(title: String, content: String, force: Boolean) {
        isBackEnabled=false
        isShowingUpgrade = true
        val intent = Intent(this, HMSUpgradeDialogActivity::class.java)
        intent.putExtra("title", title)
        intent.putExtra("content", content)
        intent.putExtra("force", force)
        requestDataLauncher.launch(intent)
        overridePendingTransition(R.anim.activity_enter_dialog, R.anim.activity_stay)
    }

    /**
     * 显示 AI 健康小医悬浮窗
     * from: 来源 card 卡片 toast 弹窗
     */
    fun showMedAIChatView(from : String) {
        // 显示 AI 健康小医悬浮窗
        MedAIChatView.fromType = from
        val medAIChatView = MedAIChatView(this)
        val llmViewModel : LLMViewModel by viewModels()
        medAIChatView.setController(this,llmViewModel)
        val aiChatView = FloatingX.install {
            setContext(HmsApplication.appContext)
            setTag(MedAIChatView.TAG)
            setLayoutView(medAIChatView)
            setEnableAnimation(true)
//                setEnableScrollOutsideScreen(true)
            setScopeType(FxScopeType.APP)
            setGravity(FxGravity.LEFT_OR_CENTER)
            setEnableKeyBoardAdapt(true,listOf(R.id.et_message_input))
            setDisplayMode(FxDisplayMode.Normal)
            setTouchListener(object : FxScrollImpl() {
                @SuppressLint("ServiceCast")
                override fun onInterceptTouchEvent(
                    event: MotionEvent,
                    control: IFxInternalHelper?
                ): Boolean {
                    val isTouchChartHeader = control?.checkPointerDownTouch(R.id.ll_ai_chat_view_header, event)
                    Log.i(TAG, "isTouchChartHeader: $isTouchChartHeader")
                    // 收起系统键盘
                    // 当触摸事件开始时，检查是否点击在输入框之外的区域
                    if (event.action == MotionEvent.ACTION_DOWN) {
                        // 获取当前获得焦点的视图
                        val currentFocus = <EMAIL>
                        if (currentFocus != null && currentFocus is EditText) {
                            // 隐藏键盘
                            val imm = HmsApplication.appContext.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                            imm.hideSoftInputFromWindow(currentFocus.windowToken, 0)
                            // 清除输入框焦点
                            currentFocus.clearFocus()
                        }
                    }
                    return isTouchChartHeader ?: false
                }
            })
        }
        aiChatView.show()
    }


    //activityResult的结果 赋值是非强制升级  如果强制升级 会 finishAffinity()
    private val requestDataLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK || result.resultCode == RESULT_CANCELED) {
                needUpgrade = false
                upgradeReady = true
                isBackEnabled=true
                isShowingUpgrade = false
            }
        }


    override fun onBackPressed() {
        if (isBackEnabled) {
            super.onBackPressed() // 启用返回操作
        }
    }

    override fun onStart() {
        super.onStart()
        Log.i(TAG, " -----:onStart ")
    }

    override fun onRestart() {
        super.onRestart()
        Log.i(TAG, " -----:onRestart ")
    }

}

