package com.healthlink.hms.activity

import android.graphics.Canvas
import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.widget.EdgeEffect
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.util.UnstableApi
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.EdgeEffectFactory
import com.healthlink.hms.R
import com.healthlink.hms.adapter.GridSpaceItemDecoration
import com.healthlink.hms.adapter.PlaylistAdapter
import com.healthlink.hms.databinding.ActivityPlaylistBinding
import com.healthlink.hms.dialog.ApplyPracticeDialog
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.ktExt.setUpSystemBar
import com.healthlink.hms.viewmodel.PlaylistViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

@UnstableApi
@AndroidEntryPoint
class PlaylistActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPlaylistBinding
    private val viewModel: PlaylistViewModel by viewModels()
    private val playlistAdapter = PlaylistAdapter { videoItem ->
        // 跳转到视频播放页面
        VideoPlayerActivity.start(this, videoItem)
        overridePendingTransition(
            R.anim.activity_enter_slide_in_right,
            R.anim.activity_enter_slide_out_left
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 设置状态栏
        window.apply {
            statusBarColor = Color.TRANSPARENT
            decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                    View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
        }
        setUpSystemBar()
        binding = ActivityPlaylistBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        observeData()
    }

    private fun setupViews() {
        // 设置返回按钮
        binding.backAreaContainer.setOnClickListener {
            finish()
            overridePendingTransition(R.anim.activity_enter_slide_in_left,R.anim.activity_enter_slide_out_right)
        }

        // 设置申请按钮
        binding.btnApplyPractice.setOnClickListener {
            val dialog = ApplyPracticeDialog.newInstance()
            dialog.setOnSubmitListener(object : ApplyPracticeDialog.OnSubmitListener {
                override fun onSubmit(name: String, phone: String, city: String, expectedTime: String) {
                    // TODO: 处理提交的信息
                }
            })
            dialog.show(supportFragmentManager, ApplyPracticeDialog.TAG)
        }

        // 设置 RecyclerView
        val column = 4
        val gridLayoutMgr =
            GridLayoutManager(this, column, GridLayoutManager.VERTICAL, false)
        binding.rvPlaylist.apply {
            layoutManager = gridLayoutMgr
            adapter = playlistAdapter
            edgeEffectFactory = object : EdgeEffectFactory() {
                override fun createEdgeEffect( view: RecyclerView, direction: Int): EdgeEffect {
                    return object : EdgeEffect(view.context) {
                        override fun onPull(deltaDistance: Float, displacement: Float) {
                            // Do nothing, disable the stretch effect
                        }

                        override fun onRelease() {
                            // Do nothing
                        }

                        override fun onAbsorb(velocity: Int) {
                            // Do nothing
                        }

                        override fun draw(canvas: Canvas?): Boolean {
                            // Do nothing
                            return super.draw(canvas)
                        }
                    }
                }
            }
        }
        var columnSpacing = 40f.dp.toInt()
        var rowSpacing = 40f.dp.toInt()
        binding.rvPlaylist.addItemDecoration(GridSpaceItemDecoration(column, rowSpacing, columnSpacing))

    }

    private fun observeData() {
        lifecycleScope.launch {
            viewModel.videos.collectLatest { videos ->
                playlistAdapter.submitList(videos)
            }
        }
    }
} 