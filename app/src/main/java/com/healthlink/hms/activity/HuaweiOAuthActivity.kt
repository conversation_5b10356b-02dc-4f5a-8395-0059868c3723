package com.healthlink.hms.activity

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.app.UiModeManager
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.Animatable2
import android.graphics.drawable.AnimatedVectorDrawable
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.util.Log
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.Animation
import android.view.animation.ScaleAnimation
import android.webkit.JavascriptInterface
import android.webkit.JsPromptResult
import android.webkit.JsResult
import android.webkit.RenderProcessGoneDetail
import android.webkit.ValueCallback
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebSettings
import android.webkit.WebView
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import com.google.gson.Gson
import com.gwm.widget.GwmButton
import com.healthlink.hms.BuildConfig
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.application.HmsApplication.Companion.getCurrentActivity
import com.healthlink.hms.databinding.ActivityHuaweiOauthBinding
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.ktExt.setUpStatusBar
import com.healthlink.hms.ktExt.setUpSystemBar
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.ToastUtil
import com.healthlink.hms.utils.isDarkModeEnabled
import com.healthlink.hms.views.AnyViewOutlineProvider
import com.hieupt.android.standalonescrollbar.attachTo
import com.just.agentweb.AgentWeb
import com.just.agentweb.AgentWebConfig
import com.just.agentweb.WebChromeClient
import com.just.agentweb.WebViewClient
import me.jessyan.autosize.utils.AutoSizeUtils
import java.lang.ref.WeakReference


/**
 * TODO : 登陆的信息应从后台获取。具体为：
 * 1、后台提供接口，获取登录基本信息（基本URL）
 * 2、前台打开URL，进行登录申请
 * 3、后台提供授权申请的地址（根据配置的使用的数据权限进行动态权限调整，以符合华为政策）
 *
 */
class HuaweiOAuthActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "HuaweiOAuthActivity"
    }

    private var mAgentWeb: AgentWeb? = null
    var bridgeInterface: AndroidInterface? = null
    private val binding: ActivityHuaweiOauthBinding by lazy {
        ActivityHuaweiOauthBinding.inflate(layoutInflater)
    }

    private val baseDomainName = BuildConfig.BASE_DOMAIN_NAME
    private val appId = BuildConfig.HUAWEI_APP_ID//111693185

    // 登录地址
    private var huaweiOAuthURL = "https://oauth-login.cloud.huawei.com/oauth2/v3/authorize?" +
            "response_type=code&state=login&client_id=${appId}" +
            "&redirect_uri=https%3A%2F%2F${baseDomainName}%2Fapp-api%2Fgwm-cockpit%2Fhms%2Ftoken2" +
            "&access_type=offline&display=touch"

    private val defaultTitle = "帐号登录"

    // 网页名称信息缓存列表
    private var webTitles = arrayListOf<String>(
        defaultTitle,
    )

    // Loading 组件
    private var loadingView: View? = null

    // 网络异常组件
    private var netErrorView: View? = null

    var weakReference : WeakReference<HuaweiOAuthActivity>?  = WeakReference(this)

    @RequiresApi(Build.VERSION_CODES.Q)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        setUpStatusBar()
        initLoading()
        initOAuthUI()
        binding.apply {
            container.setOnClickListener {
                finish()
            }
            loginDialogContainer.setOnClickListener {
                // TODO 这里要做什么？点击登录界面时不消失对话框
            }
        }
    }


    private fun initOAuthUI() {
        //根据白天黑夜模式，设置华为登录样式
        var huaweiOAuthURLWithTheme = huaweiOAuthURL
        val uiModeManager = this.getSystemService(Context.UI_MODE_SERVICE) as UiModeManager
        if (isDarkModeEnabled(this)) {
            huaweiOAuthURLWithTheme = "$huaweiOAuthURL&themeName=dark"
        }
        // 引导信息（不可见）
        binding.rlIntro.visibility = View.INVISIBLE
        // 后退按钮
        binding.hwOauthWebviewBack.setOnClickListener {
            var webView = mAgentWeb?.webCreator?.webView
            if (webView != null) {
                var url = webView.url
                if (url != null) {
                    if (isLoginPage(url)) {
                        // do noting
                        binding.tvWebviewTitle.text = "账号登录"
                    } else if (isAuthPage(url)) {
                        mAgentWeb?.clearWebCache()
                        webView.loadUrl(huaweiOAuthURL)
                        binding.tvWebviewTitle.text = "账号授权"
                    } else {
                        mAgentWeb?.back()
                        if (webTitles.size > 0) {
                            var lastTitle = webTitles.last()
                            Log.i(TAG, "lastTitle: $lastTitle")
                            webTitles.removeLast()
                            if (webTitles.size > 0) {
                                var title = webTitles.last()
                                Log.i(TAG, "current title: $title")
                                binding.tvWebviewTitle.text = title
                                if (title == defaultTitle || title.contains("数字健康")) {
                                    binding.hwOauthWebviewBack.visibility = View.GONE
                                } else {
                                    binding.hwOauthWebviewBack.visibility = View.VISIBLE
                                }
                            }
                        }
                    }
                }
            }
        }
        // 设置默认标题 TODO 这里webTitles必然有数据吗？不可能未空列表？
        binding.tvWebviewTitle.text = webTitles[0]

        binding.wvHuaweiOauthParent.visibility = View.INVISIBLE

//        binding.scrollbar.attachTo(binding.scrollWebPrivacy1)
//        binding.scrollbar.customTrackDrawable = null
//        binding.scrollbar.isAlwaysShown = false
        //初始化webView
        mAgentWeb = AgentWeb
            .with(weakReference?.get())
            .setAgentWebParent(binding.wvHuaweiOauthParent, LinearLayout.LayoutParams(-1, -1))
            .closeIndicator()
            .setWebViewClient(HuaweiOAuthWebViewClient())
            .setWebChromeClient(webChromeClient) //title重定向
            .createAgentWeb()
            .ready()
            .go(huaweiOAuthURLWithTheme)

        mAgentWeb!!.webCreator.webParentLayout.setBackgroundColor(resources.getColor(android.R.color.transparent))

        mAgentWeb!!.agentWebSettings.webSettings.let {
            it.useWideViewPort = true
            it.loadWithOverviewMode = true // 缩放至屏幕的大小
            it.domStorageEnabled = true
            it.allowContentAccess = true //是否可访问Content Provider的资源，默认值 true
            it.allowFileAccess = true // 是否可访问本地文件，默认值 true
            it.javaScriptEnabled = true
            it.userAgentString="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        } //将图片调整到适合webview的大小

//        if(Build. >= Build.VERSION_CODES.Q) {
//            mAgentWeb.agentWebSettings.webSettings.setAlgorithmicDarkeningAllowed(true)
//        }

        if (isDarkModeEnabled(this)) {
            mAgentWeb!!.agentWebSettings.webSettings.let {
                it.forceDark = WebSettings.FORCE_DARK_ON
            }
        } else {
            mAgentWeb!!.agentWebSettings.webSettings.let {
                it.forceDark = WebSettings.FORCE_DARK_OFF
            }
        }

        //支持缩放
        mAgentWeb!!.webCreator.webView.settings.let {
//            it.cacheMode = WebSettings.LOAD_NO_CACHE
            it.builtInZoomControls = true
            it.setSupportZoom(true)
            //隐藏缩放图标
            it.displayZoomControls = false
            it.userAgentString="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            //允许webview对文件的操作
            it.setAllowFileAccessFromFileURLs(true)
            it.setAllowUniversalAccessFromFileURLs(true)
            it.userAgentString =
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        // 不是用硬件加速
        mAgentWeb!!.webCreator.webView.setLayerType(View.LAYER_TYPE_NONE,null)

        // webview 长按无反应
        mAgentWeb!!.webCreator.webView.setOnLongClickListener {
            true
        }

        //清除缓存，以实现重新登录
        mAgentWeb!!.clearWebCache()

        // 前端调android 方法
        bridgeInterface = AndroidInterface(mAgentWeb!!, weakReference?.get()!!)
        mAgentWeb!!.jsInterfaceHolder.addJavaObject(
            "hlHmsBridge", bridgeInterface
        )

//        mAgentWeb.webCreator.webParentLayout.outlineProvider = AnyViewOutlineProvider(24F)
//        mAgentWeb.webCreator.webParentLayout.setClipToOutline(true)


//        binding.root.outlineProvider = AnyViewOutlineProvider(24F)
//        binding.root.setClipToOutline(true)

        binding.loginDialogContainer.outlineProvider = AnyViewOutlineProvider(24F.dp)
        binding.loginDialogContainer.setClipToOutline(true)

        // val contentView = binding.loginDialogContainer
//contentView.visibility=View.VISIBLE
        //  enterAnimie(contentView)

    }

    private fun initLoading() {
        // loadingView
        if (!HmsApplication.isNetworkConn()) {
            showNetErrorOrSettingView()
        } else {
            if (loadingView != null) {
                return
            }
            val inflater = LayoutInflater.from(weakReference?.get())
            loadingView =
                inflater.inflate(R.layout.activity_detail_loading, binding.loginDialogContainer, false)
            val linearLayout = loadingView!!.findViewById<LinearLayout>(R.id.loadingView_container)
            linearLayout.setBackgroundColor(getColor(R.color.bg_login_color))

            showLoading(binding.loginDialogContainer, loadingView!!)
        }
//        mAgentWeb.urlLoader.reload()
    }

    private var drawable: AnimatedVectorDrawable? = null
    private var animationCallback: Animatable2.AnimationCallback? = null

    fun showLoading(mContainer: ViewGroup, loadingView: View) {
        // 创建 layoutparam 并设置位置
        val params = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT
        )
        params.setMargins(0, AutoSizeUtils.dp2px(weakReference?.get(), 96f), 0, 0)
        mContainer.addView(loadingView, params)

        // 给ImageView设置动画
        val imageView = loadingView.findViewById<ImageView>(R.id.iv_loading_amin)!!
        imageView.setImageResource(R.drawable.loading_80x80)
        drawable = imageView.drawable as AnimatedVectorDrawable
        animationCallback = object : Animatable2.AnimationCallback() {
            override fun onAnimationEnd(drawable: Drawable) {
                super.onAnimationEnd(drawable)
                (drawable as AnimatedVectorDrawable).start()
            }

            override fun onAnimationStart(drawable: Drawable?) {
                super.onAnimationStart(drawable)
                //并不会监听到后立刻生效
//                if (!HmsApplication.isNetworkConn()) {
//                    showNetErrorOrSettingView()
//                }
            }
        }
        drawable?.registerAnimationCallback(animationCallback!!)
        drawable?.start()
    }

    fun hideLoading() {
        try {
            if (loadingView != null) {
                loadingView!!.visibility = View.GONE
//                binding.loginDialogContainer.removeView(loadingView)
//                loadingView = null
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onResume() {
        mAgentWeb?.webLifeCycle?.onResume()
        super.onResume()
        if (HmsApplication.isNetworkConn()) {
            initLoading()
        }
//        HmsApplication.setStatusBarColor(false)

    }

//    private fun setUpStatusBar() {
//        window?.apply {
//            val resources = context.resources
//            val lightStatusBar = resources.getBoolean(R.bool.lightStatusBar)
//            val lightNavigationBar = resources.getBoolean(R.bool.lightNavigationBar)
//
//            WindowCompat.getInsetsController(this,this.decorView).apply {
//                //设置状态栏图标深浅色
//                isAppearanceLightStatusBars = false
//                //设置Dock栏图标深浅色
//                isAppearanceLightNavigationBars = lightNavigationBar
//            }
//
////            //设置状态栏背景色
//            statusBarColor = ContextCompat.getColor(context, R.color.statusBarColor)
////
////            //设置Dock栏背景色
//            navigationBarColor = ContextCompat.getColor(context, R.color.navigationBarColor)
//
//            // 确保没有设置半透明状态栏标志
//            clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
//            // 允许绘制系统栏背景
//            addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
//            // 设置状态栏颜色为透明
//            statusBarColor = Color.TRANSPARENT
//        }
//    }

    override fun onDestroy() {
        mAgentWeb?.webLifeCycle?.onDestroy()
        if (mAgentWeb != null) {
            mAgentWeb!!.webCreator.webParentLayout?.removeAllViews()
            mAgentWeb!!.webCreator.webParentLayout?.parent?.let {
                (it as ViewGroup).removeView(it)
            }
            mAgentWeb!!.webCreator.webView.webChromeClient = null
//            mAgentWeb!!.webCreator.webView.stopLoading()
            mAgentWeb!!.webCreator.webView?.settings?.javaScriptEnabled = false
//            mAgentWeb!!.webCreator.webView?.clearHistory()
//            mAgentWeb!!.webCreator.webView?.clearCache(true)
            mAgentWeb!!.webCreator.webView?.removeAllViewsInLayout()
            mAgentWeb!!.webCreator.webView?.removeAllViews()
//            mAgentWeb!!.webCreator.webView?.destroy()

            mAgentWeb!!.webCreator.webView.parent?.let {
                (it as ViewGroup).removeView(mAgentWeb!!.webCreator.webView)
            }

            //清除缓存，以实现重新登录
            AgentWebConfig.clearDiskCache(weakReference?.get())
//            mAgentWeb?.destroy()
            mAgentWeb = null
        }
        bridgeInterface = null

        // 移除loading动画回调
        drawable?.let {
            it.stop()
            it.unregisterAnimationCallback(animationCallback!!)
            it.clearAnimationCallbacks()
        }
        drawable = null
        animationCallback = null

        if (netErrorView != null) {
            try {
//                binding.loginDialogContainer.removeView(netErrorView)
                netErrorView = null
            } catch (e: Exception) {
                Log.i(TAG, "移除netErrorView失败")
            }
        }

        if (loadingView != null) {
            try {
//                binding.loginDialogContainer.removeView(loadingView)
                loadingView = null
            } catch (e: Exception) {
                Log.i(TAG, "loadingView")
            }
        }
//        (window.decorView as? ViewGroup)?.removeAllViews()
        weakReference = null
        super.onDestroy()
    }

    override fun onPause() {
        mAgentWeb?.webLifeCycle?.onPause()
        super.onPause()

//        if(loadingView?.visibility == View.VISIBLE){
//            hideLoading()
//        }
    }

    //region 无网络设置
    fun showNetErrorOrSettingView() {
        if (netErrorView != null) {
            netErrorView!!.visibility = View.VISIBLE
            return
        }

        hideLoading()

        var mContainer = binding.loginDialogContainer
        // 动态加载自定义布局文件
        val inflater = LayoutInflater.from(weakReference?.get())
        netErrorView = inflater.inflate(R.layout.activity_detail_no_network, mContainer, false)
        val noNetworkContainer = netErrorView!!.findViewById<FrameLayout>(R.id.no_network_container)
        noNetworkContainer.setBackgroundColor(getColor(R.color.bg_login_color))

        // 创建 layoutparam 并设置位置
        val params = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT
        )
        params.setMargins(0, AutoSizeUtils.dp2px(weakReference?.get(), 96f), 0, 0)

        mContainer?.addView(netErrorView, params)

        // 设置netErrorView的点击事件
        // 刷新重试点击事件
        netErrorView?.findViewById<GwmButton>(R.id.btn_no_net_refresh)?.setOnClickListener {
            weakReference?.get()?.let {
                if (HmsApplication.isNetworkConn()){
                    hideNetErrorOrSettingView()
                    initLoading()
                    it.mAgentWeb?.urlLoader?.reload()
                }
            }
//            return@setOnClickListener
        }
    }

    private fun hideNetErrorOrSettingView() {
        if (netErrorView != null) {
            netErrorView!!.visibility = View.GONE
//            binding.loginDialogContainer?.removeView(netErrorView)
//            netErrorView = null;
        }
    }

    //endregion


    /**
     *  自定义 WebViewClient 辅助WebView设置处理关于页面跳转，页面请求等操作【处理tel协议和视频通讯请求url的拦截转发】
     */
    private var totalRequests = 0
    private var completedRequests = 0

    inner class HuaweiOAuthWebViewClient() : WebViewClient() {
        private var isLoadPage = false
        override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
            Log.i(TAG, "shouldOverrideUrlLoading: $url")
            // 使用 JavaScript 修改页面样式
            return true
        }

        override fun onPageStarted(webView: WebView?, url: String?, favicon: Bitmap?) {
            Log.d(TAG, "start to load page of url : $url")

            totalRequests = 0;
            completedRequests = 0;

//            if(loadingView!=null){
//                loadingView!!.visibility = View.VISIBLE
//            }

            super.onPageStarted(webView, url, favicon)
        }

        override fun shouldInterceptRequest(
            view: WebView,
            request: WebResourceRequest
        ): WebResourceResponse? {
            Log.d(TAG, "load new resource of: ${request.url}")

            totalRequests++;

            if (request == null) {
                return null
            }
            if (isLoginPage(request.url.toString())) {
                weakReference?.get()?.runOnUiThread {
                    binding.rlIntro.visibility = View.GONE
                    // 登录界面
                    webTitles.removeIf { it != defaultTitle }
                    binding.tvWebviewTitle.text = defaultTitle
                }

            } else if (isAuthPage(request.url.toString())) {
                weakReference?.get()?.runOnUiThread {
                    binding.rlIntro.visibility = View.VISIBLE
                }
            }

            request.requestHeaders["Sec-Ch-Ua-Platform"] = "Windows"
            request.requestHeaders["Sec-Ch-Ua"] =
                "\"Chromium\";v=\"124\", \"Google Chrome\";v=\"124\", \"Not-A.Brand\";v=\"99\""
            request.requestHeaders["Accept-Language"] = "zh-CN,zh;q=0.9"
            request.requestHeaders["User-Agent"] =
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36"

            return try {
                super.shouldInterceptRequest(view, request)
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }

        override fun shouldOverrideKeyEvent(view: WebView, event: KeyEvent): Boolean {
            return super.shouldOverrideKeyEvent(view, event)
        }

        override fun onReceivedError(p0: WebView?, p1: WebResourceRequest?, p2: WebResourceError?) {
            super.onReceivedError(p0, p1, p2)
            completedRequests++;
            checkAllRequestsCompleted();

            // -2 net::ERR_ADDRESS_UNREACHABLE
            Log.i(TAG, "onReceivedError ${p0?.originalUrl} ${p2?.errorCode} ${p2?.description}")
//            if (p2 != null && p2.errorCode == -2) {
//                p0?.reload()
            // 显示无网络界面
            hiddenDefaultErrorView()
            showNetErrorOrSettingView()
//            }
        }

        override fun onReceivedHttpError(
            p0: WebView?,
            p1: WebResourceRequest?,
            p2: WebResourceResponse?
        ) {
            super.onReceivedHttpError(p0, p1, p2)
            completedRequests++;
            checkAllRequestsCompleted();

            Log.i(
                TAG,
                "onReceivedHttpError ${p0?.originalUrl} ${p1?.url} ${p2?.statusCode} ${p2?.reasonPhrase}"
            )
            hiddenDefaultErrorView()
            showNetErrorOrSettingView()

        }

        /**
         * 隐藏 mAgentWeb 请求出错时默认的错误view
         */
        private fun hiddenDefaultErrorView() {
            val childCount = mAgentWeb?.webCreator?.webParentLayout?.childCount
            if (childCount != null) {
                if (childCount >= 2) {
        //                mAgentWeb.webCreator.webParentLayout.getChildAt(1).visibility = View.GONE
                    mAgentWeb?.webCreator?.webParentLayout?.removeViewAt(1)
                }
            }
        }

        // 暂时只处理页面触发两次加载完成的事件
        override fun onPageFinished(p0: WebView?, p1: String?) {
            super.onPageFinished(p0, p1)
            Log.d(TAG, "onPageFinished $p1")

            completedRequests++;
            checkAllRequestsCompleted();

            // 第一次加载Login
            if (!isLoadPage && p1 != null && isLoginPage(p1)) {
                isLoadPage = true
                return
            }

            // 第二次加载Login
            if (isLoadPage && p1 != null && isLoginPage(p1)) {
                isLoadPage = false
                p0?.postDelayed({
                    loadingView?.visibility = View.GONE
                    binding.wvHuaweiOauthParent.visibility = View.VISIBLE
                }, 2000)
            }
        }

        private fun checkAllRequestsCompleted() {
            Log.d(TAG, "loading resource : （${completedRequests}/${totalRequests}）")
            if (completedRequests >= totalRequests) {
                // 所有请求完成
            }
        }

        // FIX ZNZCV4-105992
        override fun onRenderProcessGone(
            view: WebView?,
            detail: RenderProcessGoneDetail?
        ): Boolean {
            if (detail != null) {
                if (detail.didCrash()) {
                    Log.i(TAG, "Render process crashed!")
                } else {
                    Log.i(TAG, "Render process was killed to free up resources.")
                }
            }
            return true
        }
    }


    /**
     * title重定向获取网页的标题
     */
    private val webChromeClient: WebChromeClient = object : WebChromeClient() {

        override fun onReceivedTitle(view: WebView, title: String) {
            Log.i(TAG, "onReceivedTitle: $title ${view.originalUrl}")
            super.onReceivedTitle(view, title)
            var textWithoutZwsp = title.replace("\u200B", "")
            if (textWithoutZwsp.length > 1 && textWithoutZwsp.isNotBlank() && textWithoutZwsp.isNotEmpty()) {
                if (webTitles.contains(textWithoutZwsp)) {
                    return
                }

                // 首页无网络情况 或者以 http、https开头的都不展示
                if ((title == "网页无法打开" || title == "Webpage not available") && view.originalUrl != null && isLoginPage(
                        view.originalUrl!!
                    ) || title.startsWith("http") || title.startsWith("https")
                ) {
                    return
                }

                webTitles.add(textWithoutZwsp)
                if (view.originalUrl != null && isLoginPage(view.originalUrl!!)) {
                    textWithoutZwsp = "帐号登录"
                } else if (view.originalUrl != null && isAuthPage(view.originalUrl!!)) {
                    textWithoutZwsp = "帐号授权"
                } else if (textWithoutZwsp == "长城健康座驾") {
                    textWithoutZwsp = "帐号授权"
                }
                Log.i(TAG, "添加了: $textWithoutZwsp $webTitles")
                Log.i(TAG, "添加了: ${view.url}")
                binding.tvWebviewTitle.text = textWithoutZwsp
                if (title == defaultTitle || title.contains("数字健康")) {
                    binding.hwOauthWebviewBack.visibility = View.GONE
                } else {
                    binding.hwOauthWebviewBack.visibility = View.VISIBLE
                }
            }
        }

        override fun onJsTimeout(): Boolean {
            Log.i(TAG, "onJsTimeout")
            return super.onJsTimeout()
        }

        override fun onJsAlert(p0: WebView?, p1: String?, p2: String?, p3: JsResult?): Boolean {
            Log.i(TAG, "onJsAlert")
            return super.onJsAlert(p0, p1, p2, p3)
        }

        override fun onJsConfirm(p0: WebView?, p1: String?, p2: String?, p3: JsResult?): Boolean {
            Log.i(TAG, "onJsAlert")
            return super.onJsConfirm(p0, p1, p2, p3)
        }

        override fun onJsPrompt(
            p0: WebView?,
            p1: String?,
            p2: String?,
            p3: String?,
            p4: JsPromptResult?
        ): Boolean {
            Log.i(TAG, "onJsAlert")
            return super.onJsPrompt(p0, p1, p2, p3, p4)
        }

        override fun onShowFileChooser(
            webView: WebView, filePathCallback: ValueCallback<Array<Uri>>,
            fileChooserParams: FileChooserParams
        ): Boolean {
//            mUploadCallbackAboveL = filePathCallback
//            if (videoFlag) {
////                recordVideo();
//            } else {
//                //url中包含saveId=0 代表化验单模块,不包含saveId=0 代表其他,可以调起拍照与相册的方法
////                takePhoto();
//            }
            return true
        }

        override fun onProgressChanged(webView: WebView?, newProgress: Int) {
            super.onProgressChanged(webView, newProgress)
        }


    }


    /**
     * H5调android方法接口
     */
    inner class AndroidInterface(mAgentWeb: AgentWeb, mContext: Context?) {

        @JavascriptInterface
        fun hlLoginSuccess(jsonData: String) {
            Log.i(TAG, "登录成功！${jsonData}")
//            this@HMSHuaweiOAuthFragment?.doLogin(jsonData)
            //打开授权页面
//            binding.rlIntro.visibility = View.VISIBLE
//            var huaweiOAuthURLForAuthorizeWidthTheme = huaweiOAuthURLForAuthorize
//            if (isDarkModeEnabled(this@HuaweiOAuthActivity)) {
//                huaweiOAuthURLForAuthorizeWidthTheme = "$huaweiOAuthURLForAuthorize&themeName=dark"
//            }
//            mAgentWeb.urlLoader.loadUrl(huaweiOAuthURLForAuthorizeWidthTheme)
        }

        @JavascriptInterface
        fun hlAuthSuccess(jsonData: String) {
            Log.i("HuaweiOAuthActivity", "授权成功！${jsonData}")
            weakReference?.get()?.doLogin(jsonData)
            //打开授权页面
//            mAgentWeb.urlLoader.loadUrl(huaweiOAuthURLForAuthorize)
        }
    }

    private fun doLogin(jsonData: String) {
        var loginUserInfo = Gson().fromJson(jsonData, LoginUserInfo::class.java)
        val userId = loginUserInfo.userId
        val userToken = loginUserInfo.token
        if (userId != null && !userId.isNullOrBlank()
            && userToken != null && !userToken.isNullOrBlank()
        ) {
            // 存储用户信息
            MMKVUtil.storeUserId(userId)
            MMKVUtil.storeUserToken(userToken)
            //存储授权信息
            //weightRead,temperatureRead,hearthealthRead,bloodglucoseRead,reproductiveRead,stressRead,bloodpressureRead,historyYear,historyWeek,spo2Read,sleepRead,distanceRead,heartRateRead
            loginUserInfo.userScope?.let {
                //心率
                if (it.contains("heartRateRead")) MMKVUtil.storeHeartRateAuthority(true)
                else MMKVUtil.storeHeartRateAuthority(false)
                //睡眠
                if (it.contains("sleepRead")) MMKVUtil.storeSleepAuthority(true)
                else MMKVUtil.storeSleepAuthority(false)
                //血氧
                if (it.contains("spo2Read")) MMKVUtil.storeBloodOxygenAuthority(true)
                else MMKVUtil.storeBloodOxygenAuthority(false)
                //压力
                if (it.contains("stressRead")) MMKVUtil.storeStressAuthority(true)
                else MMKVUtil.storeStressAuthority(false)
                //体温
                if (it.contains("temperatureRead")) MMKVUtil.storeTempertureAuthority(true)
                else MMKVUtil.storeTempertureAuthority(false)
                //血压
                if (it.contains("bloodpressureRead")) MMKVUtil.storeBloodPressureAuthority(true)
                else MMKVUtil.storeBloodPressureAuthority(false)
            }
            // 调用初始化接口
            HmsApplication.sendInitInfoReq("HuaweiOAuthActivity") { isSuccess ->

            }
            // 通知刷新主界面
//            mainViewModel.notifyRefreshMainUI.postValue(true)
            // 发送广播 用户状态已经改变
//            val intentToApp = Intent(HMSWidgetUpdateDataReceiver.ACTION_HMS_USER_STATUS_UPDATE)
//            intentToApp.setPackage(this.packageName)
//            this.sendBroadcast(intentToApp)
            finish()
        } else {
            ToastUtil.makeText(this, "未获取到用户信息", Toast.LENGTH_LONG).show()
        }
    }

    /**
     * {"userId":"hl20240530183538-807704956","userScope":"weightRead,temperatureRead,hearthealthRead,bloodglucoseRead,reproductiveRead,stressRead,historyYear,historyWeek,spo2Read,sleepRead,distanceRead,heartRateRead","token":"token1234567890","expireIn":3600}
     */
    data class LoginUserInfo(
        var userId: String? = "",
        var userScope: String? = "", // "weightRead,temperatureRead,hearthealthRead,bloodglucoseRead,reproductiveRead,stressRead,historyYear,historyWeek,spo2Read,sleepRead,distanceRead,heartRateRead"
        var token: String? = "", //
        var expireIn: Long? = 0, // 单位s 3600s
        var code: String? = null,
        var message: String? = null
    ) {

    }

    /**
     * 判断当前页面是否是登录页面
     */
    private fun isLoginPage(url: String): Boolean {
        return url.contains("state=login", false)
                && url.contains("oauth2/v3/authorize")
    }

    /**
     * 判断当前页面是否是授权页面
     */
    private fun isAuthPage(url: String): Boolean {
        return url.contains("state=auth", false)
                && url.contains("oauth2/v3/authorize")
    }

    override fun finish() {
        super.finish()
//        HmsApplication.setStatusBarColor(false)
        overridePendingTransition(R.anim.activity_stay, R.anim.activity_exit_dialog)
    }

}

