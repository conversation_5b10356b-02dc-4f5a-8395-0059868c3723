package com.healthlink.hms.activity

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.gwm.tts.service.client.GwmTTSManager
import com.gwm.tts.service.request.StreamChannel
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.journey.JourneyManager
import com.healthlink.hms.ktExt.setUpSystemBar
import com.healthlink.hms.utils.LaunchAfterBootManager
import com.healthlink.hms.widget.HMSWidgetManager
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Locale

class SplashActivity : AppCompatActivity() {
    private val mTag = "SplashActivity"
    private val TAG = "TAG_SPLASH_MAIN_SPLASH"  // 日志标签
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.i(TAG, " -----:onCreate ")
        //Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT 含义做法参考:https://www.jianshu.com/p/b202690b7d96 这篇文章
        if ((intent.flags and Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT) > 0){
           //为了防止重复启动多个闪屏页面
           Log.i(mTag, "onCreate: FLAG_ACTIVITY_BROUGHT_TO_FRONT so finish")
            finish()
            return
        }
        setContentView(R.layout.activity_splash)
        updateBackgroundColor(resources.configuration)
        setUpSystemBar()
//        // 存储启动App次数
        LaunchAfterBootManager.storeAppLaunchCount()
        // 启动定时任务（如果任务已经存在，系统会默认覆盖，只会触发一个）
        HMSWidgetManager.scheduleTask(HmsApplication.appContext, "SplashActivity")

        //启动行程记录程序
        JourneyManager().startJourneyCollectService(HmsApplication.appContext,"SplashActivity")
        JourneyManager().startJourneyCollectProtectService(HmsApplication.appContext,"SplashActivity")

        window.decorView.setOnApplyWindowInsetsListener { view, insets ->
            view.setPadding(0, 0, 0, 0)
            insets.consumeSystemWindowInsets()
        }

        // 如果联网调用初始化接口
        val isConnect = HmsApplication.isNetworkConn()
        if(isConnect) {
            HmsApplication.sendInitInfoReq { _ ->
//                    dealInitInfo()
            }
        } else {
            // 没联网直接进入App
//                dealInitInfo()
        }
        dealInitInfo()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        val currentLocale = Locale.getDefault()
        val newLocale = newConfig.locales.get(0)
        Log.d("LocaleChange", "Locale changed: $currentLocale -> $newLocale")
        if(currentLocale != newLocale) {
            return
        }

        // 不处理 layoutDirection 变化
        if (newConfig.layoutDirection != resources.configuration.layoutDirection) {
            return
        }
        // 只处理 uiMode 变化
        // 根据新的配置更新背景颜色
        updateBackgroundColor(newConfig)
    }

    private fun updateBackgroundColor(config: Configuration) {
        val rootView = window.decorView.rootView
        val backgroundColorRes = when (config.uiMode and Configuration.UI_MODE_NIGHT_MASK) {
            Configuration.UI_MODE_NIGHT_YES -> R.color.view_color_bg // 夜间模式颜色
            Configuration.UI_MODE_NIGHT_NO -> R.color.view_color_bg     // 白天模式颜色
            else -> R.color.view_color_bg
        }

        rootView.setBackgroundColor(ContextCompat.getColor(this, backgroundColorRes))
    }

    /**
     * 初始化地图，并进入主页面
     */
    private fun dealInitInfo() {
        lifecycleScope.launch {
            delay(100)
            val toActivityIntent = Intent(this@SplashActivity, MainActivity::class.java)
            <EMAIL>(toActivityIntent, 10000)
//        finish()
            overridePendingTransition(R.anim.activity_enter_fade_in,R.anim.activity_exit_fade_out)
        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        Log.i(mTag, "onActivityResult")
        super.onActivityResult(requestCode, resultCode, data)
        // 当 MainActivity 返回时，立即结束 SplashActivity
        if (requestCode == 10000) {
            // 应用从主界面返回时，停止 TTS播放
            GwmTTSManager.getInstance().stopAllTTS(StreamChannel.OTHER)
            Log.d(mTag, "onActivityResult: so finish")
            finish();
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.i(TAG, " -----:onDestroy ")
        Log.i(mTag, "onDestroy")
    }

    override fun onStart() {
        super.onStart()
        Log.i(TAG, " -----:onStart ")
    }

    override fun onResume() {
        super.onResume()
        Log.i(TAG, " -----:onResume ")
    }

    override fun onPause() {
        super.onPause()
        Log.i(TAG, " -----:onPause ")
    }

    override fun onStop() {
        super.onStop()
        Log.i(TAG, " -----:onStop ")
    }

    override fun onRestart() {
        super.onRestart()
        Log.i(TAG, " -----:onRestart ")
    }
}