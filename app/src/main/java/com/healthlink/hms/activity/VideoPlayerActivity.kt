package com.healthlink.hms.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.exoplayer.source.MediaSource
import com.healthlink.hms.databinding.ActivityVideoPlayerBinding
import com.healthlink.hms.model.VideoItem
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.common.PlaybackException
import android.util.Log
import android.net.Uri
import androidx.media3.datasource.AssetDataSource
import androidx.media3.datasource.DataSource
import com.healthlink.hms.R

@UnstableApi
class VideoPlayerActivity : AppCompatActivity() {

    private lateinit var binding: ActivityVideoPlayerBinding
    private var player: ExoPlayer? = null
    private var playWhenReady = true
    private var mediaItem: MediaItem? = null
    private var playbackPosition = 0L

    companion object {
        private const val EXTRA_VIDEO = "extra_video"
        private const val TAG = "VideoPlayerActivity"

        fun start(context: Context, videoItem: VideoItem) {
            val intent = Intent(context, VideoPlayerActivity::class.java).apply {
                putExtra(EXTRA_VIDEO, videoItem)
            }
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityVideoPlayerBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 获取传入的视频信息
        val videoItem = intent.getParcelableExtra<VideoItem>(EXTRA_VIDEO)
            ?: throw IllegalArgumentException("VideoItem must not be null")

        // 设置标题
        binding.tvTitle.text = videoItem.title

        // 设置返回按钮
        binding.titleBar.setOnClickListener {
            finish()
            overridePendingTransition(R.anim.activity_enter_slide_in_left,R.anim.activity_enter_slide_out_right)
        }

        // 初始化播放器
        initializePlayer(videoItem)
    }

    private fun initializePlayer(videoItem: VideoItem) {
        // 创建资源工厂
        val dataSourceFactory = if (videoItem.videoUrl.startsWith("file://android_asset/")) {
            // 使用AssetDataSource.Factory来处理assets目录下的文件
            DataSource.Factory {
                AssetDataSource(this)
            }
        } else {
            // 使用默认的HTTP数据源工厂处理其他类型的URL
            DefaultHttpDataSource.Factory()
                .setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                .setAllowCrossProtocolRedirects(true)
        }

        // 创建媒体源工厂
        val mediaSourceFactory = DefaultMediaSourceFactory(this)
            .setDataSourceFactory(dataSourceFactory)

        // 初始化播放器
        player = ExoPlayer.Builder(this)
            .setMediaSourceFactory(mediaSourceFactory)
            .build()
            .also { exoPlayer ->
                binding.playerView.player = exoPlayer
                
                // 根据视频URL类型创建不同的MediaItem
                mediaItem = when {
                    // assets目录下的文件
                    videoItem.videoUrl.startsWith("file://android_asset/") -> {
                        val assetPath = videoItem.videoUrl.replace("file://android_asset/", "")
                        MediaItem.fromUri(assetPath)
                    }
                    // 本地文件路径
                    videoItem.videoUrl.startsWith("file://") -> {
                        MediaItem.fromUri(Uri.parse(videoItem.videoUrl))
                    }
                    // 应用内资源
                    videoItem.videoUrl.startsWith("android.resource://") -> {
                        MediaItem.fromUri(Uri.parse(videoItem.videoUrl))
                    }
                    // Content URI
                    videoItem.videoUrl.startsWith("content://") -> {
                        MediaItem.fromUri(Uri.parse(videoItem.videoUrl))
                    }
                    // 网络URL
                    else -> {
                        MediaItem.fromUri(videoItem.videoUrl)
                    }
                }
                
                exoPlayer.setMediaItem(mediaItem!!)
                exoPlayer.playWhenReady = playWhenReady
                exoPlayer.seekTo(playbackPosition)
                exoPlayer.addListener(playbackStateListener)
                exoPlayer.prepare()
            }
    }

    private val playbackStateListener = object : Player.Listener {
        override fun onPlaybackStateChanged(playbackState: Int) {
            val progressBar = binding.progressBar
            Log.e(TAG, "onPlaybackStateChanged: $playbackState")
            // 暂时隐藏加载进度
            when (playbackState) {
//                Player.STATE_BUFFERING -> {
//                    progressBar.visibility = View.VISIBLE
//                }
                Player.STATE_READY -> {
//                    progressBar.visibility = View.GONE
                    binding.playerView.hideController()
                }
                Player.STATE_ENDED -> {
//                    progressBar.visibility = View.GONE
                    binding.playerView.showController()
                }
//                Player.STATE_IDLE -> {
//                    progressBar.visibility = View.GONE
//                }
            }
        }

        override fun onPlayerError(error: PlaybackException) {
            Log.e(TAG, "Player error: ${error.message}")
            val errorMessage = when (error.errorCode) {
                PlaybackException.ERROR_CODE_IO_BAD_HTTP_STATUS,
                PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_FAILED -> 
                    "网络连接错误，请检查网络设置"
                PlaybackException.ERROR_CODE_IO_INVALID_HTTP_CONTENT_TYPE ->
                    "不支持的视频格式"
                PlaybackException.ERROR_CODE_PARSING_CONTAINER_MALFORMED ->
                    "视频文件格式错误"
                else -> "播放出错，请稍后重试"
            }
            Toast.makeText(this@VideoPlayerActivity, errorMessage, Toast.LENGTH_LONG).show()
        }
    }

    override fun onResume() {
        super.onResume()
        hideSystemUi()
        if (player == null) {
            val videoItem = intent.getParcelableExtra<VideoItem>(EXTRA_VIDEO)
            videoItem?.let {
                initializePlayer(it)
            }
        }
    }

    override fun onPause() {
        super.onPause()
        releasePlayer()
    }

    override fun onStop() {
        super.onStop()
        releasePlayer()
    }

    private fun releasePlayer() {
        player?.let { exoPlayer ->
            playbackPosition = exoPlayer.currentPosition
            playWhenReady = exoPlayer.playWhenReady
            exoPlayer.removeListener(playbackStateListener)
            exoPlayer.release()
        }
        player = null
    }

    private fun hideSystemUi() {
        WindowCompat.setDecorFitsSystemWindows(window, false)
        WindowInsetsControllerCompat(window, binding.root).let { controller ->
            controller.hide(WindowInsetsCompat.Type.systemBars())
            controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
    }
} 