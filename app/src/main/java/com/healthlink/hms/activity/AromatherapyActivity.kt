package com.healthlink.hms.activity

import android.graphics.Color
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.healthlink.hms.R
import com.healthlink.hms.databinding.ActivityAromatherapyBinding
import com.healthlink.hms.ktExt.setUpSystemBar

class AromatherapyActivity : AppCompatActivity() {
    private lateinit var binding: ActivityAromatherapyBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAromatherapyBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 设置状态栏
        window.apply {
            statusBarColor = Color.TRANSPARENT
            decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                                         View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                                         View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
        }
        setUpSystemBar()
        initViews()
        setupListeners()
    }

    private fun initViews() {
        // 设置图片资源
        binding.ivAromatherapy.setImageResource(R.drawable.img_aromatherapy)
    }

    private fun setupListeners() {
        // 返回按钮点击事件
        binding.backAreaContainer.setOnClickListener {
            finish()
            overridePendingTransition(R.anim.activity_enter_slide_in_left,R.anim.activity_enter_slide_out_right)
        }

        // 设置按钮点击事件
        binding.btnSettings.setOnClickListener {
            // TODO: 实现设置功能
        }
    }
}