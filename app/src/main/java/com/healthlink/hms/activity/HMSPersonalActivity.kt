package com.healthlink.hms.activity

import android.appwidget.AppWidgetManager
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.ColorStateList
import android.net.ConnectivityManager
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.LinearLayout
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.blankj.utilcode.util.ClickUtils
import com.healthlink.hms.BuildConfig
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.base.Constants
import com.healthlink.hms.databinding.ActivityPersonalBinding
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.ktExt.maskPhoneNumber
import com.healthlink.hms.mvvm.model.BaseResponse
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.HMSDialogUtils
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.ToastUtil
import com.healthlink.hms.viewmodels.SettingsViewModel
import com.healthlink.hms.widget.HMSWidgetProvider
import java.lang.ref.WeakReference


class HMSPersonalActivity : BaseVBVMActivity<ActivityPersonalBinding, SettingsViewModel>() {

    private var mTag = "HMSPersonalActivity"
    private var isLoggin = true
    private lateinit var mainViewModel: SettingsViewModel
    private var nickNameShow = ""
    private var birthYearShow = 0
    private var birthMonthShow = 0
    private var heightShow = 0
    private var weightShow = 0f
    private var genderShow = 2
    private var isNoti = false
    private var personalMap = hashMapOf<String, Any>(
        Pair("userId", MMKVUtil.getUserId() as String),
        Pair("nickName", ""),
        Pair("birthYear", 0),
        Pair("birthMonth", 0),
        Pair("height", 0),
        Pair("weight", 0f),
        Pair("gender", 2)
    )
    private var bundle: Bundle = Bundle()

    private var weakActivity: WeakReference<HMSPersonalActivity>? = WeakReference(this)

    override fun getLayoutId(): Int {
        return R.layout.activity_personal
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(mTag, "onCreate ${this}")
        // 埋点
        DataTrackUtil.dtEnterPage(
            "Health_Set_PV",
            DataTrackUtil.userIDMap(MMKVUtil.getUserId() as String)
        )
        // 初始化默认用户昵称
        initDefaultUserName(MMKVUtil.getUserId() as String)
        // 获取状态栏的高度
        val topHeight = getStatusBarHeight() + 8f.dp
        // 左侧布局页面高度动态调整
        Log.d("TOPBAR", "topHeight -> " + topHeight)
        val leftView = findViewById<View>(R.id.left_view)
        val leftLayoutParams = leftView.layoutParams
        leftLayoutParams.height = topHeight.toInt()
        leftView.layoutParams = leftLayoutParams
        // 右侧布局页面高度动态调整
        val rightView = findViewById<View>(R.id.right_view)
        val rightLayoutParams = rightView.layoutParams
        rightLayoutParams.height = topHeight.toInt()
        rightView.layoutParams = rightLayoutParams
        // 左侧内容区域高度动态调整
//        val params = binding.rlLeftContent.layoutParams
//        params.height = (984F.dp - topHeight.toInt()).toInt()
//        binding.rlLeftContent.layoutParams = params
        // TODO 补充代码说明
        val root = findViewById<LinearLayout>(R.id.lo_setting)
        root.systemUiVisibility =
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
        // 初始化MVVM的模型
        mainViewModel = ViewModelProvider(this).get(SettingsViewModel::class.java)
        // 生命周期观察对象
        binding.lifecycleOwner = this
        // 初始化UI
        initUI()
        // 初始化MVVM的监听动作
        initLiveDataObserve()
        //receiveBroadcast()
    }

    override fun createViewModel(): SettingsViewModel {
        return ViewModelProvider(this)[SettingsViewModel::class.java]
    }

    override fun onStop() {
        DataTrackUtil.dtExitPage(
            "Health_Set_Close",
            DataTrackUtil.userIDMap(MMKVUtil.getUserId() as String)
        )
        //清除数据
//        this.mainViewModel?.deleteuserResult?.value = null

        super.onStop()
    }

    override fun onDestroy() {
        super.onDestroy()
//        mainViewModel.deleteuserResult.removeObserver(deleteUserResultObserver)
        if(weakActivity?.get() != null){
            weakActivity = null
        }
        Log.d(mTag, "onDestroy $this")
    }

    /**
     * 初始化设置页面的UI
     */
    fun initUI() {
        // TODO 这行代码的意义？
        // 初始化系统消息是否推送设置的值
        isNoti = MMKVUtil.getNotificationOpen()
        // 返回按钮的事件设置
        binding.settingBack.setOnClickListener {
            val activity = weakActivity?.get()
            if (activity != null && !activity.isFinishing) {
                if (isLoggin) {
                    DataTrackUtil.dtClick(
                        "Health_Set_Return_Click",
                        DataTrackUtil.userIDMap(MMKVUtil.getUserId() as String)
                    )
                    backToMain()
                } else {
                    backToMain()
                }
            }
        }
        ClickUtils.applySingleDebouncing(binding.settingPersonalInfo, 1000) {
            // 个人设置按钮的单击事件设置
            it.isEnabled = false
            DataTrackUtil.dtClick(
                "Health_Set_Personalinformation_Click",
                DataTrackUtil.userIDMap(MMKVUtil.getUserId() as String)
            )
            val intent = Intent(this, DialogTransparentActivity::class.java)
            bundle.putFloat("weight", weightShow)
            bundle.putInt("height", heightShow)
            bundle.putInt("birthMonth", birthMonthShow)
            bundle.putInt("birthYear", birthYearShow)
            bundle.putInt("gender", genderShow)
            bundle.putString("nickName", nickNameShow)
            bundle.putString("userId", MMKVUtil.getUserId() as String)
            intent.putExtras(bundle)
            requestDataLauncher.launch(intent)
            overridePendingTransition(R.anim.activity_enter_dialog, R.anim.activity_stay)
            it.postDelayed({ it.isEnabled = true }, 500)
        }
        // 退出登录按钮的单击事件设置
        binding.settingLogout.setOnClickListener {
            val msg = "确定退出当前账号？"
            DataTrackUtil.dtClick(
                "Health_Set_Logoff_Click",
                DataTrackUtil.userIDMap(MMKVUtil.getUserId() as String)
            )
            val activity = weakActivity?.get()
            if (activity != null && !activity.isFinishing) {
                HMSDialogUtils.showHMSDialog(
                    activity,
                    R.layout.hms_dialog_confirm_auto_align,
                    msg,
                    "确定",
                    "取消"
                ) { isPositive ->
                    if (isPositive) {
                        Log.i(mTag, "confirm to logout.")
                        doLogout()
                        backToMain()
                    }
                }
            }
        }
        // 注销按钮的单击事件设置
        binding.settingLogoffAccount.setOnClickListener {
            //注销
            DataTrackUtil.dtClick(
                "Health_Set_Logout_Click",
                DataTrackUtil.userIDMap(MMKVUtil.getUserId() as String)
            )
            if (isLoggin) {
                val msg = "确定注销健康账户？注销后将解绑华为账户和删除您的健康历史数据"
                DataTrackUtil.dtEnterPage(
                    "Health_Set_LogoutPrompt_PV",
                    DataTrackUtil.userIDMap(MMKVUtil.getUserId() as String)
                )
                val activity = weakActivity?.get()
                if (activity != null && !activity.isFinishing) {
                    HMSDialogUtils.showHMSDialog(
                        activity,
                        R.layout.hms_dialog_confirm_logoff,
                        msg,
                        "确定",
                        "取消"
                    ) { isPositive ->
                        if (isPositive) {
                            Log.i(mTag, "confirm to delete user.")
                            MMKVUtil.getUserId()?.let {
                                DataTrackUtil.dtClick(
                                    "Health_Set_LogoutPrompt_Agree_Click",
                                    DataTrackUtil.userIDMap(it)
                                )
                                viewModel.getDeleteUserData(it)
                            }
                            binding.tvDoctorNumber.text = ""
                        } else {
                            DataTrackUtil.dtClick(
                                "Health_Set_LogoutPrompt_Disagree_Click",
                                DataTrackUtil.userIDMap()
                            )
                        }
                        DataTrackUtil.dtExitPage(
                            "Health_Set_LogoutPrompt_Close",
                            DataTrackUtil.userIDMap()
                        )
                    }
                }
            }
        }
        // 解绑华为账号按钮的单击事件设置
        binding.settingUnbindAccount.setOnClickListener {
            //关联账户解绑
            DataTrackUtil.dtClick(
                "Health_Set_Unbind_Click",
                DataTrackUtil.userIDMap(MMKVUtil.getUserId() as String)
            )
            if (isLoggin) {
                DataTrackUtil.dtEnterPage(
                    "Health_Set_UnbindPrompt_PV",
                    DataTrackUtil.userIDMap(MMKVUtil.getUserId() as String)
                )
                val msg = resources.getString(R.string.unbind_huawei_account)
                val activity = weakActivity?.get()
                if (activity != null && !activity.isFinishing) {
                    HMSDialogUtils.showHMSDialog(
                        activity,
                        R.layout.hms_dialog_confirm_auto_align,
                        msg,
                        "确定",
                        "取消"
                    ) { isPositive ->
                        if (isPositive) {
                            MMKVUtil.getUserId()?.let {
                                DataTrackUtil.dtClick(
                                    "Health_Set_UnbindPrompt_Agree_Click",
                                    DataTrackUtil.userIDMap(it)
                                )
                                viewModel.getUnbindUserData(it)
                            }

                        } else {
                            DataTrackUtil.dtClick(
                                "Health_Set_UnbindPrompt_Disagree_Click",
                                DataTrackUtil.userIDMap(MMKVUtil.getUserId() as String)
                            )
                        }
                        DataTrackUtil.dtExitPage(
                            "Health_Set_UnbindPrompt_Close",
                            DataTrackUtil.userIDMap()
                        )
                    }
                }
            }

        }


        // 主动关怀场景消息推送开关
        if (isNoti) {
            binding.settingNotiSwitcher.isChecked = true
            binding.settingNotiSwitcher.backColor = getNotificationBgColor(true)
        } else {
            binding.settingNotiSwitcher.isChecked = false
            binding.settingNotiSwitcher.backColor = getNotificationBgColor(false)
        }

        binding.settingNotiSwitcher.setOnCheckedChangeListener() { buttonView, isChecked ->
            if (isChecked) {
                binding.settingNotiSwitcher.backColor = getNotificationBgColor(true)
            } else {
                binding.settingNotiSwitcher.backColor = getNotificationBgColor(false)
            }

            isNoti = isChecked
            MMKVUtil.storeNotificationOpen(isNoti)
            DataTrackUtil.dtClick(
                "Health_Set_Message_Click",
                DataTrackUtil.userIDAnNotifyMap(
                    MMKVUtil.getUserId() as String,
                    if (isNoti) "1" else "2"
                )
            )
        }

        // 数据使用说明的单击事件设置
        binding.rlDataUseIntro.setOnClickListener {
            DataTrackUtil.dtClick(
                "Health_Set_Datadescription_Click",
                DataTrackUtil.userIDMap(MMKVUtil.getUserId() as String)
            )
            showPrivacyDialog()
        }
        // 软件版本的设置
        var versionInfo = "软件版本V" + BuildConfig.VERSION_NAME+"." + BuildConfig.BUILD_TIME + " "
        if (!BuildConfig.BUILD_TYPE.equals("release")) {
            versionInfo += "-" + BuildConfig.PLATFORM_CODE
            versionInfo += "-" + BuildConfig.BUILD_TYPE
//            versionInfo += "-" + BuildConfig.BUILD_TIME + " "
        }else{

        }
        binding.version.text = versionInfo
    }

    private fun getNotificationBgColor(isOn: Boolean): ColorStateList {
        if (isOn) {
            return ColorStateList.valueOf(
                ContextCompat.getColor(
                    baseContext,
                    R.color.notification_switch_on_bg
                )
            )
        } else {
            return ColorStateList.valueOf(
                ContextCompat.getColor(
                    baseContext,
                    R.color.notification_switch_off_bg
                )
            )
        }
    }

    /**
     * 初始化MVVM的监听
     */
    private fun initLiveDataObserve() {
        // 监听解绑成功数据
        mainViewModel.unbindAccountResult.observe(this) {
            if (it.code == "0" && it.data != null) {
                if (it.data!!) {
                    ToastUtil.makeText(this, "关联账户解绑成功", Toast.LENGTH_SHORT).show()
                    doLogout(true)
                    Log.i("TAG_DIALOG_SHOW","=================put 1")
                    MMKVUtil.storePrivacyPolicy(true)
                    backToMain()
                } else
                    ToastUtil.makeText(this, "关联账户解绑失败", Toast.LENGTH_SHORT).show()
            } else {
                ToastUtil.makeText(this, "关联账户解绑失败", Toast.LENGTH_SHORT).show()
            }
        }


        // 监听注销用户数据
//        mainViewModel.deleteuserResult.observe(this, deleteUserResultObserver)
        mainViewModel.deleteuserResult.observe(this) {
            if (it.code == "0" && it.data != null) {
                if (it.data!! != null) {
                    Log.i(mTag, "delete user success ")
                    ToastUtil.makeText(this, "注销账户成功", Toast.LENGTH_SHORT).show()
                    doLogout(true)
                    Log.i("TAG_DIALOG_SHOW","=================put 2")
                    MMKVUtil.storePrivacyPolicy(true)
                    backToMain()
                } else{
                    Log.i(mTag, "delete user success fail because no data")
                    ToastUtil.makeText(this, "注销账户失败", Toast.LENGTH_SHORT).show()
                }
            } else {
                Log.i(mTag, "delete user success fail ${it.msg}")
                ToastUtil.makeText(this, "注销账户失败", Toast.LENGTH_SHORT).show()
            }
        }

        // 监听用户信息修改事件
        mainViewModel.userInfoData.observe(this) {
            if (it.code == "0" && it.data != null) {
                val personInfo = it.data!!
                personInfo.nickName?.let { nickName ->
                    personalMap.put("nickName", nickName)
                    MMKVUtil.storeData(KEY_NIKE_NAME, nickName)
                    this.nickNameShow = nickName
                    MMKVUtil.storeUserinfoNickname(nickName)
                }
                personInfo.gender?.let { gender ->
                    personalMap.put("gender", gender)
                    this.genderShow = gender
                    MMKVUtil.storeUserinfoGender(gender)
                }
                personInfo.birthYear?.let { birthYear ->
                    personalMap.put("birthYear", birthYear)
                    this.birthYearShow = birthYear
                    MMKVUtil.storeUserinfoBirthYear(birthYear)
                }
                personInfo.birthMonth?.let { birthMonth ->
                    personalMap.put("birthMonth", birthMonth)
                    MMKVUtil.storeUserinfoBirthMonth(birthMonth)
                    this.birthMonthShow = birthMonth
                }
                personInfo.height?.let { height ->
                    personalMap.put("height", height)
                    MMKVUtil.storeUserinfoHeight(height)
                    this.heightShow = height
                }
                personInfo.weight?.let { weight ->
                    personalMap.put("weight", weight)
                    MMKVUtil.storeUserinfoWeight(weight)
                    this.weightShow = weight
                }

            }
            binding.settingUsername.text = personalMap.get("nickName") as String

        }
        // 监听用户信息保存成功事件
        mainViewModel.saveUserInfoResult.observe(this) {
            if (it.code == "0" && it.data != null) {
                if (it.data!!) {
                    //更新用户暂存信息
                    personalMap.put("nickName", nickNameShow)
                    personalMap.put("birthYear", birthYearShow)
                    personalMap.put("birthMonth", birthMonthShow)
                    personalMap.put("height", heightShow)
                    personalMap.put("weight", weightShow)
                    personalMap.put("gender", genderShow)

                    personalMap.get("nickName")?.let { nickName ->
                        this.nickNameShow = nickName.toString()
                        binding.settingUsername.text = nickName.toString()
                        MMKVUtil.storeUserinfoNickname(nickName.toString())
                    }
                    personalMap.get("gender")?.let { gender ->
                        gender.toIntNumber()?.let {
                            this.genderShow = it
                            MMKVUtil.storeUserinfoGender(it)
                        }

                    }
                    personalMap.get("birthYear")?.let { birthYear ->
                        birthYear.toIntNumber()?.let {
                            this.birthYearShow = it
                            MMKVUtil.storeUserinfoBirthYear(it)
                        }

                    }
                    personalMap.get("birthMonth")?.let { birthMonth ->
                        birthMonth.toIntNumber()?.let {
                            this.birthMonthShow = it
                            MMKVUtil.storeUserinfoBirthMonth(it)
                        }

                    }
                    personalMap.get("height")?.let { height ->
                        height.toIntNumber()?.let {
                            this.heightShow = it
                            MMKVUtil.storeUserinfoHeight(it)
                        }

                    }
                    personalMap.get("weight")?.let { weight ->
                        this.weightShow = weight.toString().toFloat()
                        MMKVUtil.storeUserinfoWeight(weight.toString().toFloat())
                    }
                } else {
                    rollbackInfo()
                }
            } else {
                rollbackInfo()
            }
        }
        initUserFromMMKV()
        if (HmsApplication.isNetworkConn()) {
            MMKVUtil.getUserId()?.let {
                mainViewModel.getUserInfoData(it)
            }
        }
        // 数据使用说明
        mainViewModel.getDataUsageLiveData().observe(this) {
            if (it.code == "0" && it.data != null) {
                val activity = weakActivity?.get()
                if (activity != null && !activity.isFinishing) {
                    HMSDialogUtils.showDataUseIntroDialog(activity, it.data.toString(), "知道了") {
                    }
                }
            }
        }
    }

    private fun rollbackInfo() {
        nickNameShow = personalMap.get("nickName") as String
        birthYearShow = personalMap.get("birthYear") as Int
        birthMonthShow = personalMap.get("birthMonth") as Int
        heightShow = personalMap.get("height") as Int
        weightShow = personalMap.get("weight") as Float
        genderShow = personalMap.get("gender") as Int
    }

    private fun showPrivacyDialog() {
        val activity = weakActivity?.get()
        if (activity != null && !activity.isFinishing) {
            HMSDialogUtils.showDataUseIntroDialog(activity, "数据使用说明", "知道了") {
            }
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d(mTag, "onResume ${this}")
//        sendQueryDoctorService()
    }

    private fun doLogout(isKeepAgreed: Boolean = true) {
        isLoggin = false
        // 清除用户缓存数据
        MMKVUtil.clearAllExceptSome(isKeepAgreed)
        //更新桌面卡片状态
        var hmsWidgetProvider = HMSWidgetProvider()
        val activity = weakActivity?.get()
        if (activity != null && !activity.isFinishing) {
//            hmsWidgetProvider.updateHmsWidgetForNoLogin(activity)
            // 获取 AppWidgetManager 实例
            val appWidgetManager = AppWidgetManager.getInstance(applicationContext)
            // 获取当前小部件的所有 AppWidgetId
            val appWidgetIds = appWidgetManager.getAppWidgetIds(
                ComponentName(applicationContext, HMSWidgetProvider::class.java)
            )
            hmsWidgetProvider.updateHmsWidget(applicationContext,appWidgetIds)
        }
    }

    private fun saveData(map: Map<*, *>) {
        mainViewModel.saveUserData(map)
    }

    private val requestDataLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            var map = hashMapOf<String, Any>()
            map.put("userId", MMKVUtil.getUserId() as String)
            if (result.resultCode == RESULT_OK || result.resultCode == RESULT_CANCELED) {
                result.data?.let {
                    it.getStringExtra("nickName")?.let { nName ->
                        map.put("nickName", nName)
                        nickNameShow = nName
                        binding.settingUsername.text = nName
                    }
                    if (it.getIntExtra("birthYear", 0) != 0) {
                        map.put("birthYear", it.getIntExtra("birthYear", 0))
                        birthYearShow = it.getIntExtra("birthYear", 0)
                    }
                    if (it.getIntExtra("birthMonth", 0) != 0) {
                        map.put("birthMonth", it.getIntExtra("birthMonth", 0))
                        birthMonthShow = it.getIntExtra("birthMonth", 0)
                    }
                    if (it.getIntExtra("height", 0) != 0) {
                        map.put("height", it.getIntExtra("height", 0))
                        heightShow = it.getIntExtra("height", 0)
                    }
                    if (it.getFloatExtra("weight", 0f) != 0f) {
                        map.put("weight", it.getFloatExtra("weight", 0f))
                        weightShow = it.getFloatExtra("weight", 0f)
                    }
                    map.put("gender", it.getIntExtra("gender", 2))
                    genderShow = it.getIntExtra("gender", 2)
                }
                if (result.resultCode == RESULT_OK) saveData(map.toMap())
            }
        }

    fun Any.toOneDecimalStr(): String? {
        //显示身高一位小数
        if (this is Float) return String.format("%.1f", this)
        if (this is String) {
            try {
                return String.format("%.1f", this.toFloat())
            } catch (e: NumberFormatException) {
                return null
            }
        }
        return null
    }

    fun Any.toIntNumber(): Int? {
        if (this is Int) return this
        if (this is Float) return this.toInt()
        if (this is String) {
            try {
                return this.toFloat().toInt()
            } catch (e: NumberFormatException) {
                return null
            }
        }
        return null
    }

    private fun initDefaultUserName(userID: String?) {
        // 默认使用系统缓存；如无系统缓存，则使用默认规则生成昵称
        val nikeName = MMKVUtil.getData(KEY_NIKE_NAME)
        if (nikeName != null && !nikeName.isNullOrEmpty()) {
            nickNameShow = nikeName
        } else {
            userID?.let {
                if (userID.length > 6) {
                    nickNameShow = "CH${it.substring(it.length - 6)}"
                }
            }
        }
        personalMap.put("nickName", nickNameShow)
    }

    private fun getStatusBarHeight(): Int {
        var statusBarHeight = 0
        val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            statusBarHeight = resources.getDimensionPixelSize(resourceId)
        }
        return statusBarHeight
    }

    companion object {
        // 用户昵称缓存KEY
        const val KEY_NIKE_NAME = "NIKE_NAME"
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putString("nickNameShowSave", nickNameShow)
        outState.putInt("birthYearShowSave", birthYearShow)
        outState.putInt("birthMonthShowSave", birthMonthShow)
        outState.putInt("heightShowSave", heightShow)
        outState.putFloat("weightShowSave", weightShow)
        outState.putInt("genderShowSave", genderShow)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        nickNameShow = savedInstanceState.getString("nickNameShowSave", nickNameShow)
        birthYearShow = savedInstanceState.getInt("birthYearShowSave", birthYearShow)
        birthMonthShow = savedInstanceState.getInt("birthMonthShowSave", birthMonthShow)
        heightShow = savedInstanceState.getInt("heightShowSave", heightShow)
        weightShow = savedInstanceState.getFloat("weightShowSave", weightShow)
        genderShow = savedInstanceState.getInt("genderShowSave", genderShow)
    }

    private fun initUserFromMMKV() {
        val piMap = MMKVUtil.getUserinfo()
        val defaultName =
            "CH${MMKVUtil.getUserId()!!.substring(MMKVUtil.getUserId()!!.length - 6)}"
        if (piMap.get("nickName") == "") {
            personalMap.put("nickName", defaultName)
            MMKVUtil.storeUserinfoNickname(defaultName)
            this.nickNameShow = defaultName
            binding.settingUsername.text = defaultName
        } else {
            personalMap.put("nickName", piMap.get("nickName") ?: defaultName)
            this.nickNameShow = piMap.get("nickName") as String
            binding.settingUsername.text = nickNameShow
        }
        personalMap.put("gender", piMap.get("gender") ?: 2)
        this.genderShow = piMap.get("gender") as Int

        personalMap.put("birthYear", piMap.get("birthYear") ?: 0)
        this.birthYearShow = piMap.get("birthYear") as Int

        personalMap.put("birthMonth", piMap.get("birthMonth") ?: 0)
        this.birthMonthShow = piMap.get("birthMonth") as Int

        personalMap.put("height", piMap.get("height") ?: 0)
        this.heightShow = piMap.get("height") as Int

        personalMap.put("weight", piMap.get("weight") ?: 0f)
        this.weightShow = piMap.get("weight") as Float
    }

    /**
     * 注销用户结果的观察者
     */
//    var deleteUserResultObserver = Observer<BaseResponse<Boolean>>{
//        if (it.code == "0" && it.data != null) {
//            if (it.data!! != null) {
//                Log.i(mTag, "delete user success ")
//                ToastUtil.makeText(this, "注销账户成功", Toast.LENGTH_SHORT).show()
//                doLogout(false)
//                finish()
//                overridePendingTransition(
//                    R.anim.activity_enter_slide_in_left,
//                    R.anim.activity_enter_slide_out_right
//                )
//            } else{
//                Log.i(mTag, "delete user success fail because no data")
//                ToastUtil.makeText(this, "注销账户失败", Toast.LENGTH_SHORT).show()
//            }
//        } else {
//            Log.i(mTag, "delete user success fail ${it.msg}")
//            ToastUtil.makeText(this, "注销账户失败", Toast.LENGTH_SHORT).show()
//        }
//    }

    private fun backToMain() {
        getSharedPreferences(Constants.SHARE_IS_USER_RETURN_TO_MAIN, MODE_PRIVATE).edit().putBoolean(
            Constants.BACK_TO_MAIN, true).apply()
        finish()
        overridePendingTransition(
            R.anim.activity_enter_slide_in_left,
            R.anim.activity_enter_slide_out_right
        )
    }

}