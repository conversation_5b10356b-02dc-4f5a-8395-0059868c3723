package com.healthlink.hms.activity

import android.animation.Animator
import android.animation.Animator.AnimatorListener
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.app.Dialog
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import android.util.Log
import android.view.ActionMode
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.ClickUtils
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.databinding.ActivityDialogTransparentBinding
import com.healthlink.hms.ktExt.observeKeyboardChange
import com.healthlink.hms.ktExt.setUpStatusBar
import com.healthlink.hms.ktExt.setUpSystemBar
import com.healthlink.hms.mvvm.network.RetrofitClient
import com.healthlink.hms.mvvm.network.utils.DateUtil
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.ToastUtil
import com.healthlink.hms.viewmodels.MainViewModel
import com.healthlink.hms.views.dialog.HmsDialog
import com.shawnlin.numberpicker.NumberPicker
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference
import java.util.Calendar


class DialogTransparentActivity :
    BaseVBVMActivity<ActivityDialogTransparentBinding, MainViewModel>() {
//    private lateinit var mainViewModel: MainViewModel
    private var personalMap = hashMapOf<String, Any>()
    private lateinit var netErrToastStr: String
    private var nickName = ""
    private var birthYear = 0
    private var birthMonth = 0
    private var height = 0
    private var weight = 0f
    private var gender = 2
    private var firstOpen = true
    private lateinit var personalUsername: TextView
    private lateinit var personalBirthday: TextView
    private lateinit var personalHeight: TextView
    private lateinit var personalWeight: TextView
    private lateinit var loUsername: LinearLayout
    private lateinit var loBirthday: LinearLayout
    private lateinit var loHeight: LinearLayout
    private lateinit var loWeight: LinearLayout
    private lateinit var genderGroup: RadioGroup
    private lateinit var genderMale: RadioButton
    private lateinit var genderFemale: RadioButton
    private lateinit var genderPrivacy: RadioButton
    private lateinit var personalInfoContent: RelativeLayout
    private lateinit var container: FrameLayout
    private var weakActivity: WeakReference<DialogTransparentActivity>? = WeakReference(this)

    override fun getLayoutId(): Int {
        return R.layout.activity_dialog_transparent
    }

    override fun createViewModel(): MainViewModel {
        return ViewModelProvider(this)[MainViewModel::class.java]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
//        mainViewModel = ViewModelProvider(this).get(MainViewModel::class.java)
        binding.lifecycleOwner = this
        // 设置系统状态条
        setUpStatusBar()
        initView()
        DataTrackUtil.dtEnterPage(
            "Health_Set_PersonalinformationPrompt_PV",
            DataTrackUtil.userIDMap(MMKVUtil.getUserId() as String)
        )
        if (savedInstanceState != null) {
            initDataFromInstance(savedInstanceState!!)
        } else {
            initData(intent)
        }
        showPersonalInfoDialog(personalMap)
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onStop() {
        DataTrackUtil.dtExitPage(
            "Health_Set_PersonalinformationPrompt_Close",
            DataTrackUtil.userIDMap(MMKVUtil.getUserId() as String)
        )
        super.onStop()
    }


    fun initView() {
        personalUsername = binding.tvSettingPersonalInfoUsername
        personalBirthday = binding.tvSettingPersonalInfoBirth
        personalHeight = binding.tvSettingPersonalInfoHeight
        personalWeight = binding.tvSettingPersonalInfoWeight
        loUsername = binding.loPersonalInfoUsername
        loBirthday = binding.loPersonalInfoBirth
        loHeight = binding.loPersonalInfoHeight
        loWeight = binding.loPersonalInfoWeight
        genderGroup = binding.rgGenderGroup
        genderMale = binding.rbGenderMale
        genderFemale = binding.rbGenderFemale
        genderPrivacy = binding.rbGenderPrivacy
        personalInfoContent = binding.personalDialogContent
        container = binding.dialogPersonalContainer
    }

    fun initData(intent: Intent) {
        netErrToastStr = getString(R.string.edit_userinfo_fail_toast)
        intent.getStringExtra("nickName")?.let {
            nickName = it
        }
        intent.getIntExtra("birthYear", 0)
        nickName = intent.getStringExtra("nickName").toString()
        birthYear = intent.getIntExtra("birthYear", 0)
        birthMonth = intent.getIntExtra("birthMonth", 0)
        height = intent.getIntExtra("height", 0)
        weight = intent.getFloatExtra("weight", 0f)
        gender = intent.getIntExtra("gender", 2)
        personalMap.put("nickName", nickName)
        personalMap.put("birthYear", birthYear)
        personalMap.put("birthMonth", birthMonth)
        personalMap.put("height", height)
        personalMap.put("weight", weight)
        personalMap.put("gender", gender)
    }

    fun initDataFromInstance(bundle: Bundle) {
        netErrToastStr = getString(R.string.edit_userinfo_fail_toast)
        bundle.getString("nickName")?.let {
            nickName = it
        }
        bundle.getInt("birthYear", 0)
        nickName = bundle.getString("nickName").toString()
        birthYear = bundle.getInt("birthYear", 0)
        birthMonth = bundle.getInt("birthMonth", 0)
        height = bundle.getInt("height", 0)
        weight = bundle.getFloat("weight", 0f)
        gender = bundle.getInt("gender", 2)
        personalMap.put("nickName", nickName)
        personalMap.put("birthYear", birthYear)
        personalMap.put("birthMonth", birthMonth)
        personalMap.put("height", height)
        personalMap.put("weight", weight)
        personalMap.put("gender", gender)
    }

    private fun showPersonalInfoDialog(map: MutableMap<*, *>) {
        when (map.get("gender")) {
            0 -> {
                genderFemale.isChecked = true
            }

            1 -> {
                genderMale.isChecked = true
            }

            2 -> {
                genderPrivacy.isChecked = true
            }
        }
        personalUsername.text = map.get("nickName") as String

        if (birthMonth != 0) {
            personalBirthday.setTextColor(resources.getColor(R.color.text_color_fc_100))
            personalBirthday.text =
                map.get("birthYear").toString() + "年" + map.get("birthMonth").toString() + "月"
        } else {
            personalBirthday.setTextColor(resources.getColor(R.color.text_color_fc_30))
            personalBirthday.text = resources.getString(R.string.userinfo_birth_hint)
        }
        if (height != 0) {
            personalHeight.setTextColor(resources.getColor(R.color.text_color_fc_100))
            personalHeight.text = map.get("height")!!.toIntNumber().toString() + "cm"
        } else {
            personalHeight.setTextColor(resources.getColor(R.color.text_color_fc_30))
            personalHeight.text = resources.getString(R.string.userinfo_height_hint)
        }
        if (weight != 0f) {
            personalWeight.setTextColor(resources.getColor(R.color.text_color_fc_100))
            personalWeight.text = map.get("weight")!!.toOneDecimalStr() + "kg"
        } else {
            personalWeight.setTextColor(resources.getColor(R.color.text_color_fc_30))
            personalWeight.text = resources.getString(R.string.userinfo_weight_hint)
        }
        genderGroup.setOnCheckedChangeListener { _, radioButtonid ->
            if (!HmsApplication.isNetworkConn()) {
                ToastUtil.makeText(this, netErrToastStr, Toast.LENGTH_SHORT).show()
                return@setOnCheckedChangeListener
            }
            when (radioButtonid) {
                R.id.rb_gender_male -> {
                    personalMap.put("gender", 1)
                }

                R.id.rb_gender_female -> {
                    personalMap.put("gender", 0)
                }

                R.id.rb_gender_privacy -> {
                    personalMap.put("gender", 2)
                }
            }
            lifecycleScope.launch {
                try {
                    var map = hashMapOf<String, Any>()
                    map.put("userId", MMKVUtil.getUserId() as String)
                    // 确保性别有值，则进行更新
                    var gender = personalMap.get("gender")
                    if (gender != null) {
                        map.put("gender", gender.toString())
                        var result = withContext(Dispatchers.IO) {
                            saveUserInfo(map)
                        }
                        if (result) {
                            <EMAIL> = map.get("gender") as Int
                            MMKVUtil.storeUserinfoGender(gender as Int)
                        } else {
                            ToastUtil.makeText(
                                this@DialogTransparentActivity,
                                netErrToastStr,
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    Log.i("DialogTransparentActivity", "save gender error , msg is ${e.message}")
                }
            }
        }
        loUsername.setOnClickListener {
            personalInfoContent.visibility = View.GONE
            showEditUsernameDialog(personalUsername.text.toString())

        }
        loBirthday.setOnClickListener {
            personalInfoContent.visibility = View.GONE
            if (birthYear == 0)
                showEditBirthdayDialog(
                    "1999",
                    "6"
                )
            else
                showEditBirthdayDialog(
                    personalMap.get("birthYear").toString(),
                    personalMap.get("birthMonth").toString()
                )

        }
        loHeight.setOnClickListener {
            personalInfoContent.visibility = View.GONE
            if (height == 0)
                showEditHeightDialog(180)
            else
                showEditHeightDialog(personalMap.get("height").toString().toFloat().toInt())
        }
        loWeight.setOnClickListener {
            personalInfoContent.visibility = View.GONE
            var weightStr = "70.0"
            if (weight == 0f) {
                weightStr = "70.0"
            } else {
                if ((weight * 10) % 1 == 0f) {
                    personalMap.get("weight")!!.toOneDecimalStr()?.let {
                        weightStr = it
                    }
                } else {
                    try {
                        if (weight + 0.1f > weight.toInt()) {
                            weightStr = (weight.toInt().toFloat() + 0.1f).toOneDecimalStr()!!
                        } else {
                            weightStr = (weight.toInt().toFloat()).toOneDecimalStr()!!
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
            showEditWeightDialog(weightStr)
        }
        val positiveButton: Button = findViewById(R.id.positiveButton)
        positiveButton.text = "完成"
        positiveButton.setOnClickListener {
            DataTrackUtil.dtClick(
                "Health_Set_PersonalinformationPrompt_Submit_Click",
                DataTrackUtil.userIDMap()
            )
            intent.putExtra("userId", MMKVUtil.getUserId() as String)
            intent.putExtra("nickName", personalMap.get("nickName") as String)
            intent.putExtra("birthYear", personalMap.get("birthYear") as Int)
            intent.putExtra("birthMonth", personalMap.get("birthMonth") as Int)
            intent.putExtra("height", personalMap.get("height") as Int)
            intent.putExtra("weight", personalMap.get("weight") as Float)
            intent.putExtra("gender", personalMap.get("gender") as Int)
            setResult(RESULT_OK, intent)
            //dialog?.dismiss()
            finish()
            overridePendingTransition(R.anim.activity_stay, R.anim.activity_exit_dialog)
        }
        container.setOnTouchListener { view, event ->
            if (!container.isClickable) return@setOnTouchListener true
            if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in personalInfoContent.top..personalInfoContent.bottom)) || (!(event.x.toInt() in personalInfoContent.left..personalInfoContent.right)))) {
                intent.putExtra("userId", MMKVUtil.getUserId() as String)
                intent.putExtra("nickName", personalMap.get("nickName") as String)
                intent.putExtra("birthYear", personalMap.get("birthYear") as Int)
                intent.putExtra("birthMonth", personalMap.get("birthMonth") as Int)
                intent.putExtra("height", personalMap.get("height") as Int)
                intent.putExtra("weight", personalMap.get("weight") as Float)
                intent.putExtra("gender", personalMap.get("gender") as Int)
                setResult(RESULT_CANCELED, intent)
                finish()
                overridePendingTransition(R.anim.activity_stay, R.anim.dialog_activity_exit_anim)
                true
            }
            false
        }

        personalInfoContent.visibility = View.VISIBLE
        if (firstOpen) {
            enterAnimie(personalInfoContent, object : AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                    container.isClickable = false
                }

                override fun onAnimationEnd(animation: Animator) {
                    container.isClickable = true
                }

                override fun onAnimationCancel(animation: Animator) {
                }

                override fun onAnimationRepeat(animation: Animator) {
                }
            })
            firstOpen = false
        }
    }

    fun doDialogAnimateEnter(dialog: Dialog, animateListener: AnimatorListener) {
        if (dialog != null) {
            // 进入动画
            var view =
                dialog!!.window?.decorView?.rootView?.findViewById<FrameLayout>(R.id.fl_container)
            view?.scaleX = 0f
            view?.scaleY = 0f
            var animate = view?.animate()
                ?.scaleX(1f)
                ?.scaleY(1f)
                ?.setDuration(300)
            animate?.interpolator = AccelerateDecelerateInterpolator()
            animate?.setListener(animateListener)
            animate?.start()

        }
    }


    fun showEditUsernameDialog(nickName: String) {
        val activity = weakActivity?.get()
        if(activity === null || activity.isFinishing) {
            return
        }
        var dialog = HmsDialog(activity, R.style.MyDialogStyle)
        var view: View = LayoutInflater.from(activity).inflate(R.layout.hms_dialog_edit_username, null)
        val contentView = view.findViewById<ViewGroup>(R.id.dialog_content)
        val editText = view.findViewById<EditText>(R.id.et_username)
        val clearTextImg = view.findViewById<ImageView>(R.id.img_clear_all_text)
        val positiveButton: Button = view.findViewById(R.id.positiveButton)
        clearTextImg.setOnClickListener {
            editText.setText("")
        }
        editText.setText(nickName)
        editText.requestFocus()
//        editText.postDelayed({
//            // 显示软键盘
//            val imm = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
//            imm.showSoftInput(editText, 0)
//            editText.viewTreeObserver.addOnGlobalLayoutListener {
//                val rect = Rect()
//                // 获取屏幕可见区域
//                editText.getWindowVisibleDisplayFrame(rect)
//                val screenHeight = editText.rootView.height
//                val keypadHeight = screenHeight - rect.bottom
//                // 输入法隐藏时
//                if (keypadHeight <= screenHeight * 0.15) {
//                    editText.clearFocus()
//                } else {
//                    editText.requestFocus()
//                }
//            }
//        }, 200)

        editText.postDelayed({
            val locationOnScreen = IntArray(2)
            editText.getLocationOnScreen(locationOnScreen)
            // 模拟按下事件
            simulateTouchEvent(
                editText,
                MotionEvent.ACTION_DOWN,
                locationOnScreen[0].toFloat(),
                locationOnScreen[1].toFloat()
            )

            // 模拟抬起事件
            simulateTouchEvent(
                editText,
                MotionEvent.ACTION_UP,
                locationOnScreen[0].toFloat(),
                locationOnScreen[1].toFloat()
            )

        }, 200)

        editText.filters = arrayOf(InputFilter { source, _, _, _, _, _ ->
            val input = source.toString()
            if (input.contains(" ") || input.contains("\n")) {
                return@InputFilter input.replace(" ", "").replace("\n", "")
            }
            null
        })

        observeKeyboardChange({
            if(!it) editText.clearFocus()
        },200)

        editText.customSelectionActionModeCallback = object : ActionMode.Callback {
            override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                return false // 禁用菜单创建
            }

            override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                return false // 禁用菜单准备
            }

            override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                return false // 禁用菜单项点击
            }

            override fun onDestroyActionMode(mode: ActionMode?) {
                // 不需要做任何处理
            }
        }

        editText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
                // 在文本改变之前调用
            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                if (s.isEmpty()) {
                    clearTextImg.visibility = View.GONE
                    positiveButton.setTextColor(getColor(R.color.nickname_confirm_gray))
                    positiveButton.isEnabled = false
                } else {
                    clearTextImg.visibility = View.VISIBLE
                    positiveButton.setTextColor(getColor(R.color.btn_left_text_color))
                    positiveButton.isEnabled = true
                }
                //20位字符设置
                var length = 0
                s?.forEach { char ->
                    if (char in '\u4E00'..'\u9FA5') {
                        length += 2
                    } else {
                        length += 1
                    }
                }
                if (length > 20) {
                    val newString = StringBuilder()
                    length = 0
                    for (char in s) {
                        if (char in '\u4E00'..'\u9FA5') {
                            length += 2
                        } else {
                            length += 1
                        }
                        if (length > 20) {
                            break
                        }
                        newString.append(char)
                    }
                    editText.setText(newString)
                    editText.setSelection(newString.length)
                }

            }

            override fun afterTextChanged(s: Editable) {
                // 在文本改变之后调用
            }
        })
        ClickUtils.applySingleDebouncing(positiveButton, 1000) {
            if (!HmsApplication.isNetworkConn()) {
                ToastUtil.makeText(this, netErrToastStr, Toast.LENGTH_SHORT).show()
                return@applySingleDebouncing
            }

            if (!editText.text.isNullOrEmpty())
                personalMap.put("nickName", editText.text.toString())
            var map = hashMapOf<String, Any>()
            map.put("userId", MMKVUtil.getUserId() as String)
            map.put("nickName", editText.text.toString())
            lifecycleScope.launch {
                try {
                    var result = withContext(Dispatchers.IO) {
                        saveUserInfo(map)
                    }
                    if (result) {
                        <EMAIL> = map.get("nickName") as String
                        personalMap.put("nickName", map.get("nickName") as String)
                        MMKVUtil.storeUserinfoNickname(map.get("nickName") as String)
//                    ToastUtil.makeText(
//                        this@DialogTransparentActivity,
//                        "昵称保存成功",
//                        Toast.LENGTH_SHORT
//                    ).show()
                    } else {
                        ToastUtil.makeText(
                            this@DialogTransparentActivity,
                            netErrToastStr,
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                    dialog?.dismiss()
                    showPersonalInfoDialog(personalMap)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        // 设置取消按钮
        val negativeButton: Button = view.findViewById(R.id.negativeButton)
        negativeButton.setOnClickListener {
            dialog?.dismiss()
            showPersonalInfoDialog(personalMap)
        }

        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setOnShowListener { dialogInterface ->
            val window = (dialogInterface as Dialog).window
            window?.setSoftInputMode(
                WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE or
                        WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE
            )
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    dialog?.dismiss()
                    showPersonalInfoDialog(personalMap)
                }
                false
            }
        }
        dialog?.window?.setDimAmount(0f)
        dialog?.window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT
        )
        dialog?.setContentView(view)
        if (personalInfoContent.visibility != View.GONE) personalInfoContent.visibility = View.GONE
        dialog?.show()
    }

    private fun simulateTouchEvent(view: View, action: Int, x: Float, y: Float) {
        val downTime = System.currentTimeMillis()
        val eventTime = System.currentTimeMillis()
        val motionEvent = MotionEvent.obtain(
            downTime,
            eventTime,
            action,
            x,
            y,
            0
        )
        val handled: Boolean = view.dispatchTouchEvent(motionEvent)
        Log.d("DialogTrans", "Touch Event Handled: $handled")
    }

    fun showEditBirthdayDialog(defaultYear: String, defaultMonth: String) {
        val activity = weakActivity?.get()
        if(activity === null || activity.isFinishing) {
            return
        }
        var dialog = HmsDialog(activity, R.style.MyDialogStyle)
        var view: View = LayoutInflater.from(activity).inflate(R.layout.hms_dialog_edit_birthday, null)
        val contentView = view.findViewById<ViewGroup>(R.id.privacy_agree_home)
        var yearPicker: NumberPicker = view.findViewById(R.id.birthday_year_piker)
        var monthPicker: NumberPicker = view.findViewById(R.id.birthday_month_piker)
        val calendar: Calendar = Calendar.getInstance()
        val year: Int = calendar.get(Calendar.YEAR)
        val count = year - 1900 + 1 // 需要初始化的字符串数量
        val yearArray = List(count) { index ->
            "" + (1900 + index)
        }.toTypedArray()
        val monthArray = arrayOf("1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12")
        monthPicker.minValue = 1
        monthPicker.maxValue = 12
        monthPicker.displayedValues = monthArray
        var index = monthArray.indexOf(defaultMonth)
        if (index != -1) {
            monthPicker.value = index + 1
        }
        yearPicker.minValue = 1
        yearPicker.maxValue = yearArray.size
        yearPicker.displayedValues = yearArray
        index = yearArray.indexOf(defaultYear)
        if (index != -1) {
            yearPicker.value = index + 1
        }
        val positiveButton: Button = view.findViewById(R.id.positiveButton)
        ClickUtils.applySingleDebouncing(positiveButton, 1000) {
            if (!HmsApplication.isNetworkConn()) {
                ToastUtil.makeText(this, netErrToastStr, Toast.LENGTH_SHORT).show()
                return@applySingleDebouncing
            }

            var map = hashMapOf<String, Any>()
            var birthYear = yearArray[yearPicker.value - 1].toInt()
            var birthMonth = monthArray[monthPicker.value - 1].toInt()
            map.put("userId", MMKVUtil.getUserId() as String)
            map.put("birthYear", birthYear)
            map.put("birthMonth", birthMonth)

            // 校验出生年月的合法性
            var currentYear = DateUtil.getYear().toInt()
            var currentMonth = DateUtil.getMonth().toInt()
            if (birthYear == currentYear && birthMonth > currentMonth) {
                var errMsg = getString(R.string.user_birthdate_must_not_great_then_current)
                ToastUtil.makeText(this, errMsg, Toast.LENGTH_SHORT).show()
                return@applySingleDebouncing
            }

            lifecycleScope.launch {
                try {
                    var result = withContext(Dispatchers.IO) {
                        saveUserInfo(map)
                    }
                    Log.i("MTA", "result $result")
                    if (result) {
                        <EMAIL> = map.get("birthYear") as Int
                        <EMAIL> = map.get("birthMonth") as Int
                        personalMap.put("birthYear", map.get("birthYear") as Int)
                        personalMap.put("birthMonth", map.get("birthMonth") as Int)
                        MMKVUtil.storeUserinfoBirthYear(map.get("birthYear") as Int)
                        MMKVUtil.storeUserinfoBirthMonth(map.get("birthMonth") as Int)

                    } else {
                        ToastUtil.makeText(
                            this@DialogTransparentActivity,
                            netErrToastStr,
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                    dialog?.dismiss()
                    showPersonalInfoDialog(personalMap)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        // 设置取消按钮
        val negativeButton: Button = view.findViewById(R.id.negativeButton)
        negativeButton.setOnClickListener {
            dialog?.dismiss()
            showPersonalInfoDialog(personalMap)

        }
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.window?.setDimAmount(0f)
        dialog.setOnCancelListener {
            dialog?.dismiss()
            showPersonalInfoDialog(personalMap)
        }
        dialog?.setContentView(view)
        dialog?.setOnShowListener {
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    dialog?.dismiss()
                    showPersonalInfoDialog(personalMap)
                }
                false
            }
        }
        if (personalInfoContent.visibility != View.GONE) personalInfoContent.visibility = View.GONE
        dialog?.show()
    }

    fun showEditHeightDialog(height: Int?) {
        val activity = weakActivity?.get()
        if(activity === null || activity.isFinishing) {
            return
        }
        var dialog = HmsDialog(activity, R.style.MyDialogStyle)
        var view: View =
            LayoutInflater.from(activity).inflate(R.layout.hms_dialog_edit_height_weight, null)
        val contentView = view.findViewById<ViewGroup>(R.id.privacy_agree_home)
        var dataPicker: NumberPicker = view.findViewById(R.id.body_data_piker)
        var title: TextView = view.findViewById(R.id.hms_dialog_title)
        var dataUnit: TextView = view.findViewById(R.id.body_data_unit)
        title.text = "身高"
        dataUnit.text = "cm "
        val dataArray = List(201) { index ->
            "" + (50 + index)
        }.toTypedArray()

        dataPicker.minValue = 1
        dataPicker.maxValue = dataArray.size
        dataPicker.displayedValues = dataArray
        height?.let {
            var index = dataArray.indexOf(it.toString())
            if (index != -1) {
                dataPicker.value = index + 1
            }
        }
        val positiveButton: Button = view.findViewById(R.id.positiveButton)
        ClickUtils.applySingleDebouncing(positiveButton, 1000) {
            if (!HmsApplication.isNetworkConn()) {
                ToastUtil.makeText(this, netErrToastStr, Toast.LENGTH_SHORT).show()
                return@applySingleDebouncing
            }

            var map = hashMapOf<String, Any>()
            map.put("userId", MMKVUtil.getUserId() as String)
            map.put("height", dataArray[dataPicker.value - 1].toInt())
            lifecycleScope.launch {
                try {
                    var result = withContext(Dispatchers.IO) {
                        saveUserInfo(map)
                    }
                    if (result) {
                        <EMAIL> = map.get("height") as Int
                        personalMap.put("height", map.get("height") as Int)
                        MMKVUtil.storeUserinfoHeight(map.get("height") as Int)
//                    ToastUtil.makeText(
//                        this@DialogTransparentActivity,
//                        "身高保存成功",
//                        Toast.LENGTH_SHORT
//                    ).show()
                    } else {
                        ToastUtil.makeText(
                            this@DialogTransparentActivity,
                            netErrToastStr,
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                    dialog?.dismiss()
                    showPersonalInfoDialog(personalMap)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
        // 设置取消按钮
        val negativeButton: Button = view.findViewById(R.id.negativeButton)
        negativeButton.setOnClickListener {
            dialog?.dismiss()
            showPersonalInfoDialog(personalMap)

        }
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.window?.setDimAmount(0f)
        dialog?.setCancelable(true)
        dialog.setOnCancelListener {
            dialog?.dismiss()
            showPersonalInfoDialog(personalMap)
        }
        dialog?.window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT
        )
        dialog?.setContentView(view)
        dialog?.setOnShowListener {
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    dialog?.dismiss()
                    showPersonalInfoDialog(personalMap)
                }
                false
            }
        }
        if (personalInfoContent.visibility != View.GONE) personalInfoContent.visibility = View.GONE
        dialog?.show()
    }

    fun showEditWeightDialog(weight: String) {
        val activity = weakActivity?.get()
        if(activity === null || activity.isFinishing) {
            return
        }
        var dialog = HmsDialog(activity, R.style.MyDialogStyle)
        var view: View =
            LayoutInflater.from(activity).inflate(R.layout.hms_dialog_edit_height_weight, null)
        val contentView = view.findViewById<ViewGroup>(R.id.privacy_agree_home)
        var dataPicker: NumberPicker = view.findViewById(R.id.body_data_piker)
        var title: TextView = view.findViewById(R.id.hms_dialog_title)
        var dataUnit: TextView = view.findViewById(R.id.body_data_unit)
        title.text = "体重"
        dataUnit.text = "kg"
        val dataArray = List(2400 + 1) { index ->
            String.format("%.1f", 10f + index * 0.1f)
        }.toTypedArray()

        dataPicker.minValue = 1
        dataPicker.maxValue = dataArray.size
        dataPicker.displayedValues = dataArray
        var index = dataArray.indexOf(weight)
        if (index != -1) {
            dataPicker.value = index + 1
        }
        val positiveButton: Button = view.findViewById(R.id.positiveButton)
        ClickUtils.applySingleDebouncing(positiveButton, 1000) {
            if (!HmsApplication.isNetworkConn()) {
                ToastUtil.makeText(this, netErrToastStr, Toast.LENGTH_SHORT).show()
                return@applySingleDebouncing
            }

            var map = hashMapOf<String, Any>()
            map.put("userId", MMKVUtil.getUserId() as String)
            map.put("weight", dataArray[dataPicker.value - 1].toFloat())

            lifecycleScope.launch {
                try {
                    var result = withContext(Dispatchers.IO) {
                        saveUserInfo(map)
                    }
                    if (result) {
                        <EMAIL> = map.get("weight") as Float
                        personalMap.put("weight", map.get("weight") as Float)
                        MMKVUtil.storeUserinfoWeight(map.get("weight") as Float)
//                    ToastUtil.makeText(
//                        this@DialogTransparentActivity,
//                        "体重保存成功",
//                        Toast.LENGTH_SHORT
//                    ).show()
                    } else {
                        ToastUtil.makeText(
                            this@DialogTransparentActivity,
                            netErrToastStr,
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                    dialog?.dismiss()
                    showPersonalInfoDialog(personalMap)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        // 设置取消按钮
        val negativeButton: Button = view.findViewById(R.id.negativeButton)
        negativeButton.setOnClickListener {
            dialog?.dismiss()
            showPersonalInfoDialog(personalMap)
        }
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.window?.setDimAmount(0f)

        dialog?.window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT
        )
        dialog?.setContentView(view)
        dialog?.setOnShowListener {
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    dialog?.dismiss()
                    showPersonalInfoDialog(personalMap)
                }
                false
            }
        }
        if (personalInfoContent.visibility != View.GONE) personalInfoContent.visibility = View.GONE
        dialog?.show()
    }

    fun Any.toOneDecimalStr(): String? {
        //显示身高一位小数
        if (this is Float) return String.format("%.1f", this)
        if (this is String) {
            try {
                return String.format("%.1f", this.toFloat())
            } catch (e: NumberFormatException) {
                return null
            }
        }
        return null
    }

    fun Any.toIntNumber(): Int? {
        if (this is Int) return this
        if (this is Float) return this.toInt()
        if (this is String) {
            try {
                return this.toFloat().toInt()
            } catch (e: NumberFormatException) {
                return null
            }
        }
        return null
    }

    suspend fun saveUserInfo(map: Map<String, Any>): Boolean {
        RetrofitClient.apiService.saveUserInfo(map).let { dto ->
            if (dto.data != null && dto.data as Boolean) {
                return true
            } else {
                return false
            }
        }
    }

    fun enterAnimie(view: ViewGroup, animateListener: AnimatorListener) {
        view.scaleX = 0f
        view.scaleY = 0f

        val animatorSet = AnimatorSet()
        animatorSet.playTogether(
            ObjectAnimator.ofFloat(view, "scaleX", 0f, 1f),
            ObjectAnimator.ofFloat(view, "scaleY", 0f, 1f)
        )
        animatorSet.setDuration(250)
        animatorSet.interpolator = AccelerateDecelerateInterpolator()
        animatorSet.addListener(animateListener)
        animatorSet.start()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putString("nickName",personalMap.get("nickName") as String)
        outState.putInt("birthYear", personalMap.get("birthYear") as Int)
        outState.putInt("birthMonth",personalMap.get("birthMonth") as Int)
        outState.putInt("height", personalMap.get("height") as Int)
        outState.putFloat("weight", personalMap.get("weight") as Float)
        outState.putInt("gender", personalMap.get("gender") as Int)
    }
}