package com.healthlink.hms.activity

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import com.blankj.utilcode.util.ClickUtils
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.databinding.HmsDialogMutiPhoneBinding
import com.healthlink.hms.ktExt.maskPhoneNumber
import com.healthlink.hms.ktExt.setUpSystemBar
import com.healthlink.hms.sdks.gwmadapter.GwmAdapterManagerKotCoroutines
import com.healthlink.hms.utils.HMSDialogUtils
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.ToastUtil
import com.healthlink.hms.viewmodels.MainViewModel

/**
 * Created by imaginedays on 2024/7/30
 * 绑定电话医生Activity
 */
class BindPhoneCallDoctorActivity : HMSBaseActivity() {
    private var TAG = "BindPhoneCallDoctorActivity"
    private lateinit var binding: HmsDialogMutiPhoneBinding
    private lateinit var mainViewModel: MainViewModel
    private var phones : ArrayList<String> = arrayListOf()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this,R.layout.hms_dialog_muti_phone)
        mainViewModel = ViewModelProvider(this)[MainViewModel::class.java]

        // 设置系统状态条
        setUpSystemBar()

        initData()
        initLiveDataObserve()
    }

    override fun onResume() {
        super.onResume()
        HmsApplication.isDialogShow = true
        HmsApplication.setStatusBarColor(false)
    }

    override fun onPause() {
        super.onPause()
        HmsApplication.isDialogShow = false
    }

    private fun initLiveDataObserve() {
        // 绑定电话医生成功
        mainViewModel.getDoctorServiceUpdateLiveData().observe(this) {
            if (it.code == "0" && it.data == true) {
                showBindSuccessDialog()
                phones.clear()
            } else {
                ToastUtil.makeText(this, "绑定失败", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun showBindSuccessDialog() {
        binding.rlPhoneBindContainer.visibility = View.GONE
        var tip = ""
        if (phones!!.isNotEmpty()) {
            var phones = phones!!.joinToString(separator = "、").maskPhoneNumber()
            tip = "已绑定手机号：$phones"
        }
        val msg = "手机号绑定成功，现在您可以呼叫资深全科医生团队的优质电话问诊服务。"
        HMSDialogUtils.showHMSDialogWithTips(this,R.layout.hms_dialog_doctor_phone,msg,tip,"确定","取消") { isPositive ->
            if (isPositive) {
//                dialPhone(MMKVUtil.getPhoneDoctorNumber() ?: getString(R.string.phone_doctor_default))
                finish()
            }
        }
    }

    /**
     * 拨打电话
     */
    private fun dialPhone(phoneNumber: String) {
        val dialIntent = Intent(Intent.ACTION_DIAL, Uri.parse("tel:$phoneNumber"))
        if (dialIntent.resolveActivity(this.packageManager) != null) {
            startActivity(dialIntent)
        } else {
            Log.i(TAG, "No activity found to handle the intent")
        }
    }


    private fun initData() {
        binding.flContainer.setOnClickListener{
            finish()
        }

        binding.phone1.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun afterTextChanged(s: Editable?) {
            }
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                changePositiveButtonState()
            }
        })

        binding.phone2.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun afterTextChanged(s: Editable?) {
            }
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                changePositiveButtonState()
            }
        })

        binding.phone3.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun afterTextChanged(s: Editable?) {
            }
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                changePositiveButtonState()
            }
        })

        ClickUtils.applySingleDebouncing(binding.positiveButton){
            val phone1Text = binding.phone1.text.toString()
            val phone2Text = binding.phone2.text.toString()
            val phone3Text = binding.phone3.text.toString()

            if(phone1Text.isNotEmpty()) {
                if (phone1Text.length != 11) {
                    ToastUtil.makeText(this, "手机号1长度不正确", Toast.LENGTH_SHORT).show()
                    return@applySingleDebouncing
                }
                if (!isPhoneNumberValid(phone1Text)) {
                    ToastUtil.makeText(this, "手机号1格式不正确", Toast.LENGTH_SHORT).show()
                    return@applySingleDebouncing
                }
                phones.add(phone1Text.trim())
            }

            if(phone2Text.isNotEmpty()) {
                if (phone2Text.length != 11) {
                    ToastUtil.makeText(this, "手机号2长度不正确", Toast.LENGTH_SHORT).show()
                    return@applySingleDebouncing
                }
                if (!isPhoneNumberValid(phone2Text)) {
                    ToastUtil.makeText(this, "手机号2格式不正确", Toast.LENGTH_SHORT).show()
                    return@applySingleDebouncing
                }
                phones.add(phone2Text.trim())
            }

            if(phone3Text.isNotEmpty()) {
                if (phone3Text.length != 11) {
                    ToastUtil.makeText(this, "手机号3长度不正确", Toast.LENGTH_SHORT).show()
                    return@applySingleDebouncing
                }
                if (!isPhoneNumberValid(phone3Text)) {
                    ToastUtil.makeText(this, "手机号3格式不正确", Toast.LENGTH_SHORT).show()
                    return@applySingleDebouncing
                }
                phones.add(phone3Text.trim())
            }

            val vin= GwmAdapterManagerKotCoroutines.getVechileVIN(this)
            if (vin != null && vin.length > 0) {
                var params: MutableMap<String, Any> = mutableMapOf()
                params["phoneGroup"] = phones
                params["vin"] = vin
                mainViewModel.sendDoctorServiceUpdateDataReq(params)
            }
        }

        binding.negativeButton.setOnClickListener {
            finish()
        }
    }

    private fun changePositiveButtonState() {
        val isEnabled = binding.phone1.text.toString().isNotEmpty() || binding.phone2.text.toString().isNotEmpty() || binding.phone3.text.toString().isNotEmpty()
        binding.positiveButton.isEnabled = isEnabled
        if (isEnabled) {
            // 设置按钮颜色 R.color.btn_left_text_color
            binding.positiveButton.setTextColor(ContextCompat.getColor(this, R.color.btn_left_text_color))
        } else {
            binding.positiveButton.setTextColor(ContextCompat.getColor(this, R.color.btn_left_text_unEnabled_color))
        }
    }

    private val phoneNumberRegex = Regex("^1[3-9]\\d{9}$")

    private fun isPhoneNumberValid(phoneNumber: String): Boolean {
        return phoneNumberRegex.matches(phoneNumber)
    }

    override fun finish() {
        super.finish()
        HmsApplication.setStatusBarColor(true)
        overridePendingTransition(R.anim.activity_stay, R.anim.activity_exit_dialog)
    }
}