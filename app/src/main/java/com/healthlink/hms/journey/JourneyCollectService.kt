package com.healthlink.hms.journey

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.work.OneTimeWorkRequest
import androidx.work.WorkManager
import com.google.gson.Gson
import com.gwm.android.adapter.client.DataChangeListener
import com.gwm.android.adapter.client.GwmAdapterClient
import com.gwm.android.adapter.client.ServiceStateListener
import com.healthlink.hms.HmsSettings
import com.healthlink.hms.R
import com.healthlink.hms.activity.MainActivity
import com.healthlink.hms.sceneEngine.dto.SceneGearStatusInfoDTO
import com.healthlink.hms.sceneEngine.scenes.SceneLongDriveCare2Impl
import com.healthlink.hms.sdks.gwmadapter.GwmAdapterManager
import com.healthlink.hms.sdks.gwmadapter.GwmAdapterManagerKotCoroutines
import com.healthlink.hms.service.WidgetHmsDataWorker
import com.healthlink.hms.utils.MMKVUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class JourneyCollectService : Service() {
    private var isServiceRunning = false
    private var handler: Handler? = null

    private var mGwmAdapterClient: GwmAdapterClient =  GwmAdapterClient.getInstance()
    private var mServiceConnected = false
    private var mContext = this
    private var mServiceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    override fun onCreate() {
        super.onCreate()
        handler = Handler()
        createNotificationChannel()
        // 内部已经 createNotification
        updateNotification()
        bindService(Intent(this, JourneyCollectProtectService::class.java), serviceConnection, BIND_AUTO_CREATE)

        //判断GwmAdapter是否已连接，如果威连接，则先进行连接，然后注册监听；否则直接注册监听
        // 被上面的代码引起阻塞 协程
//        GlobalScope.launch {
            doGwmAdapterConnect()
//        }
        Log.d(mTag, "onCreate")
    }

    /**
     * 判断GwmAdapter是否已连接，如果威连接，则先进行连接，然后注册监听；否则直接注册监听
     */
    private fun doGwmAdapterConnect(){
        mServiceScope.launch {
            delay(30000)
        // 监听电源状态，如果下电则读取里程
//        JourneyManager().doPowerStatusMonitor(this)
            val servers = GwmAdapterManagerKotCoroutines.providerGwmAdapterServiceIds()//arrayListOf("gwm_adapter_weather","gwm_adapter_media","gwm_adapter")
            if(!mGwmAdapterClient.isServiceConnected) {
                Log.i(mTag, "GwmAdapterClient is not connected , do connecting first")
                mGwmAdapterClient.init(this@JourneyCollectService, servers.toTypedArray(), object : ServiceStateListener {
                    override fun onServiceConnected() {
                        Log.i(mTag, "GwmAdapterClient is connected now, to monitor...")
                        mServiceConnected = true
                        comparePowerModeAndRecord()
                        doMonitor()
                    }

                    /**
                     * 断开连接时，成功新链接
                     */
                    override fun onServiceDisconnected() {
                        super.onServiceDisconnected()
                        doGwmAdapterConnect()
                    }
                })
            }else{
                Log.i(mTag, "GwmAdapterClient is already connected , do monitor directly")
                doMonitor()
            }
        }
    }

    private fun comparePowerModeAndRecord() {
        // 启动服务后，获取当前电源状态与上次存储的电源状态进行比较
        if (mGwmAdapterClient.isServiceConnected) {
            val res = mGwmAdapterClient.getData(GwmAdapterManager.POWER_MODE)
            val okRes = (res != null) && res == POWER_MODE_2 && MMKVUtil.getLastPowerMode() != POWER_MODE_2
            if (okRes) {
                Log.i(mTag, "上次状态不为大电，补充上大电事件")
                recordLastPowerMode(POWER_MODE_2)
                val mileageStr = mGwmAdapterClient.getData(TOTAL_ODOMETER)
                if (mileageStr != null && mileageStr.isNotEmpty()) {
                    startJobForPowerMode2(mileageStr)
                }
            }
        }
    }

    private fun recordLastPowerMode(powerMode : String) {
        MMKVUtil.storeLastPowerMode(powerMode)
    }


    /**
     * 当电源模式为2时，启动任务
     */
    private fun startJobForPowerMode2(mileageStr: String) {
        JourneyManager().onJourneyStart(mileageStr.toFloat())
        MMKVUtil.storePowerMode2StartTime(System.currentTimeMillis())
        Log.i(mTag, "on journey started, mileage is = $mileageStr")

        // 重置场景引擎信号
//        SceneManager.resetJourneySceneTriggerCount()
        // 记录热启动时间
//        LaunchAfterBootManager.storeLaunchAfterBootInfo()

        // 上电时，运行一次Woker
//                doWorker(this@JourneyCollectService)
    }



    /**
     * 车机信号监听
     */
    private fun doMonitor() {
        // 监听电源、档位数据变化
        val dataIds = arrayOf(GwmAdapterManager.POWER_MODE,GwmAdapterManager.GEAR_STATUS)
        mGwmAdapterClient.registerDataChangeListener(dataIds,dataChangeListener)
    }

    /**
     * 车机监听器
     */
    private var dataChangeListener = DataChangeListener { dataId, dataValue ->

        Log.i(mTag, "GwmAdapter data changed: key=$dataId, value=$dataValue")

        var mileageStr = mGwmAdapterClient.getData(TOTAL_ODOMETER)
        if (mileageStr != null && mileageStr.isNotEmpty()) {

            // 记录电源状态
            if(dataId == GwmAdapterManager.POWER_MODE && dataValue.isNotEmpty()) {
                recordLastPowerMode(dataValue)
            }
            // 记录档位状态
            else if(dataId == GwmAdapterManager.GEAR_STATUS && dataValue.isNotEmpty()){
                processGearStatusChanged(dataValue)
            }

            // 上电信号
            if (dataId == GwmAdapterManager.POWER_MODE && dataValue.isNotEmpty() && dataValue.equals(
                    POWER_MODE_2
                )
            ) {
                startJobForPowerMode2(mileageStr)
            }
            // 下电信号
            else if (dataId == GwmAdapterManager.POWER_MODE && dataValue.isNotEmpty() && dataValue.equals(
                    POWER_MODE_0
                )
            ) {
                var mileage = mileageStr.toFloat()
                JourneyManager().onJourneyEnd(mileage)
                Log.i(mTag, "on journey ended, mileage is = $mileage")
            }
        }
    }

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {

        doGwmAdapterConnect()

        if(intent!=null) {
            if (!isServiceRunning) {
                isServiceRunning = true
                startForeground(NOTIFICATION_ID, createNotification())
                handler!!.post(updateNotificationRunnable)
            }

            Log.d(mTag, "onStartCommand")
        }
        return START_STICKY_COMPATIBILITY
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name: CharSequence = "Foreground Service Channel"
            val description = "Channel for foreground service"
            val importance = NotificationManager.IMPORTANCE_DEFAULT
            val channel = NotificationChannel(CHANNEL_ID, name, importance)
            channel.description = description
            val notificationManager = getSystemService(
                NotificationManager::class.java
            )
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        val notificationIntent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this,
            0, notificationIntent, PendingIntent.FLAG_IMMUTABLE
        )
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(resources.getString(R.string.app_name))
            .setContentText("正在记录行程信息，进行健康分析...")
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(pendingIntent)
            .build()
    }

    private val updateNotificationRunnable: Runnable = object : Runnable {
        override fun run() {
            updateNotification()
//            handler!!.postDelayed(this, 5000) // Update every second
        }
    }

    private fun updateNotification() {
        val notification = createNotification()
        startForeground(NOTIFICATION_ID, notification)
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            mServiceScope.cancel()
        } catch (e: Exception) {
            Log.i(mTag, "mServiceScope cancel error ${e.message}")
        }
        isServiceRunning = false
        handler!!.removeCallbacks(updateNotificationRunnable)
        stopForeground(true)

        unbindService(serviceConnection)

        Log.d(mTag, "onDestroy")
    }

    override fun onBind(intent: Intent): IBinder? {
        return null
    }

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Log.d(mTag, "JourneyCollectProtectService connected")
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Log.d(mTag, "JourneyCollectProtectService disconnected. Restarting JourneyCollectProtectService...")
            // 重新启动 Service2
//            startService(Intent(this@JourneyCollectService, JourneyCollectProtectService::class.java))

            JourneyManager().startJourneyCollectProtectService(this@JourneyCollectService,"JourneyCollectService")
            bindService(Intent(this@JourneyCollectService, JourneyCollectProtectService::class.java), this, BIND_AUTO_CREATE)
        }
    }

    /**
     * 通过Worker去获取最新健康数据
     */
    public fun doWorker(context: Context){
//        MMKVUtil.storeTime(WidgetHmsDataWorker.WORKER_TASK_SCENE_ENGINE_RUN_SUCCESS_TIME,0)
        MMKVUtil.storeTime(WidgetHmsDataWorker.WORKER_TASK_WIDGET_RUN_SUCCESS_TIME,0)
        //定义回调广播
        val workManager = WorkManager.getInstance(context!!)
        // 创建WorkRequest
        val hmsHealthDataWorker = OneTimeWorkRequest.Builder(WidgetHmsDataWorker::class.java).build()
        // 调度WorkRequest
        workManager.enqueue(hmsHealthDataWorker)

//        GlobalScope.launch(Dispatchers.IO) {
//            var worker = HmsDataWorker(context)
//            worker.doWork()
//        }
    }

    companion object {
        private var mTag = "JourneyCollectService"
        private const val NOTIFICATION_ID = 1
        private const val CHANNEL_ID = "ForegroundServiceChannel"

        // 汽车电源状态
//        const val POWER_MODE = "car.basic.power_mode";
        // 仪表盘总里程数据ID
        const val TOTAL_ODOMETER = "car.basic.total_odometer"


        /**
         * 上大电 0 > 3 > 2
         */
        const val POWER_MODE_2 = "2"
        /**
         * 上小电 0 > 3 > 2
         */
        const val POWER_MODE_3 = "3"
        /**
         * 下电 0 > 3 > 2
         */
        const val POWER_MODE_0 = "0"

        /**
         * 档位发生变化是进行处理
         */
        fun processGearStatusChanged(status : String) {
            Log.d(mTag, "starting to process gear status changed. gear_status= ${status}")
            try {
                val gson = Gson();
                // 当前档位信息
                val currentGearStatusInfoDTO  = SceneGearStatusInfoDTO()
                currentGearStatusInfoDTO.status = status
                currentGearStatusInfoDTO.timestamp = System.currentTimeMillis()
                // 最近一次档位信息
                val lastGearStatusInfoStr = MMKVUtil.getLastGearStatus()
                if(lastGearStatusInfoStr!=null && !lastGearStatusInfoStr.isNullOrEmpty()){
                    val lastGearStatusInfoDTO = gson.fromJson(lastGearStatusInfoStr, SceneGearStatusInfoDTO::class.java)
                    Log.d(mTag, "last gear status is ${lastGearStatusInfoDTO.status}")
                    // 最近档位是P或R或N档，当前档位不是P、R、N档
                    if((lastGearStatusInfoDTO.status == "0"
                                ||lastGearStatusInfoDTO.status == "3"
                                ||lastGearStatusInfoDTO.status == "4")
                        &&
                        (currentGearStatusInfoDTO.status != "0"
                                && currentGearStatusInfoDTO.status != "3"
                                &&currentGearStatusInfoDTO.status != "4")){
                        Log.d(mTag, "pre gear status is ${lastGearStatusInfoDTO.status}, current gear status is ${currentGearStatusInfoDTO.status}")
                        // 档位发生变化：
                        Log.d(mTag, "pre gear status times is ${lastGearStatusInfoDTO.timestamp}")
                        if(lastGearStatusInfoDTO.timestamp > 0){

                            var timeTotal = currentGearStatusInfoDTO.timestamp - lastGearStatusInfoDTO.timestamp
                            if(timeTotal > HmsSettings.SCENE_LONG_DRIVE_2_TOTAL_REST_TIME){
                                // TODO 停车超过20分钟，是否要重置
                                Log.d(mTag, "rest time great than 20 mins , reset scene_long_driving_2 ")
                                SceneLongDriveCare2Impl.resetJourney()
                            }else {
                                var totalRestTimeInJourney = lastGearStatusInfoDTO.totalRestTimeInJourney
                                totalRestTimeInJourney += timeTotal
                                currentGearStatusInfoDTO.totalRestTimeInJourney = totalRestTimeInJourney
                                Log.d(
                                    mTag,
                                    "rest time increase from ${lastGearStatusInfoDTO.totalRestTimeInJourney} to $totalRestTimeInJourney "
                                )
                                // 保存当前档位信息
                                MMKVUtil.storeLastGearStatus(gson.toJson(currentGearStatusInfoDTO))
                            }
                        }else{
                            // 最近档位无效，不做处理
                            MMKVUtil.storeLastGearStatus(gson.toJson(currentGearStatusInfoDTO))
                            Log.i(mTag, "last gear status is invalid${currentGearStatusInfoDTO.status}。")
                        }
                    }else{
                        // 没有从休息档位变为移动档位，不做处理
                        currentGearStatusInfoDTO.totalRestTimeInJourney = lastGearStatusInfoDTO.totalRestTimeInJourney
                        MMKVUtil.storeLastGearStatus(gson.toJson(currentGearStatusInfoDTO))
                        Log.i(mTag, "没有从休息档位变为移动档位，不做处理${currentGearStatusInfoDTO.status}。")
                    }
                }else{
                    // 没有最近档位信息，保存当前档位信息。
                    MMKVUtil.storeLastGearStatus(gson.toJson(currentGearStatusInfoDTO))
                    Log.i(mTag, "没有最近档位信息，保存当前档位信息${currentGearStatusInfoDTO.status}。")
                }
            } catch (e: Exception) {
                Log.i(mTag, "processGearStatusChanged error", e)
            }

        }

    }

}
