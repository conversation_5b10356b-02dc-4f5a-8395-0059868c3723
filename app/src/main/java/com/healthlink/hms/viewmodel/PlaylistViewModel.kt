package com.healthlink.hms.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.healthlink.hms.R
import com.healthlink.hms.model.VideoItem
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class PlaylistViewModel @Inject constructor() : ViewModel() {
    
    private val _videos = MutableStateFlow<List<VideoItem>>(emptyList())
    val videos: StateFlow<List<VideoItem>> = _videos

    init {
        loadVideos()
    }

    private fun loadVideos() {
        viewModelScope.launch {
            // 这里模拟加载视频列表数据
            // 实际应用中应该从网络或本地数据库加载
            val videoList = listOf(
                VideoItem(
                    id = "1",
                    title = "急救医生贾大成：必备急救知识科普",
                    thumbnailUrl = R.drawable.icon_video_default,
                    duration = 594, // 9:54
                    videoUrl = "file://android_asset/video1.mp4"
                ),
                VideoItem(
                    id = "2",
                    title = "意外骨折不要慌",
                    thumbnailUrl = R.drawable.icon_video_default_02,
                    duration = 56, // 8:00
                    videoUrl = "file://android_asset/video4.mp4"
                )
                // 添加更多视频项...
            )
            _videos.value = videoList
        }
    }
} 