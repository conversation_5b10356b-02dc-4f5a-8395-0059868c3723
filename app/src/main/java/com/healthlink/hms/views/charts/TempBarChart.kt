package com.healthlink.hms.views.charts

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Point
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.graphics.Typeface
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.View
import androidx.core.graphics.blue
import androidx.core.graphics.green
import androidx.core.graphics.red
import com.healthlink.hms.BuildConfig
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.R
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.ktExt.sp
import com.healthlink.hms.server.data.dto.TempItemDTO
import com.healthlink.hms.utils.ChartTouchEventDelegate
import com.healthlink.hms.utils.TimeUtils
import com.healthlink.hms.utils.ViewDrawUtils
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.math.abs
import kotlin.math.min

/**
 *@Author: 付仁秀
 *@Description：
 **/
class TempBarChart(context: Context, attrs: AttributeSet?) : View(context, attrs) {
    private val mTag = "TempBarChart"

    //屏幕宽高
    private var scrWidth = 0f
    private var scrHeight = 0f
    private var perDp = 0f //1dp所占的值
    private var xData = arrayListOf("00:00", "06:00", "12:00", "18:00", "24:00")
    private var yDataRight: ArrayList<String> =
        arrayListOf("45", "43", "40", "37", "34")
    private var type: Int = 0 //六种纵坐标类型
    private var xCount = 30 //三种横坐标类型
    private var tempData = emptyList<TempItemDTO>()
    private lateinit var paintLine: Paint // y轴线
    private lateinit var paintGradientLine: Paint //指示渐变线
    private lateinit var paintXText: Paint // x轴坐标
    private lateinit var paintYText: Paint // y轴坐标
    private lateinit var paintPolyline: Paint // 海拔折线
    private lateinit var paintPolyShadow: Paint // 海拔折线阴影
    private lateinit var paintPillar: Paint //柱子
    private lateinit var paintRound: Paint  // 指示滑块圆
    private lateinit var paintBessel: Paint  // 滑块底部
    private var xSlider = 0f //滑块的x轴位置
    private var mLinePath: Path  //折线路径
    private val curveCircleRadius = 22f.dp
    private var mPath: Path  //滑块贝塞尔
    private var isDayType = true
    private var wmyTypeStr = TimeCode.TIME_CODE_DAY.timeCode
    private var w = 0
    private var h = 0
    private var oldw = 0
    private var oldh = 0

    //第一条曲线的坐标
    private val mFirstCurveStartPoint = Point()
    private val mFirstCurveEndPoint = Point()
    private val mFirstCurveControlPoint1 = Point()
    private val mFirstCurveControlPoint2 = Point()

    //第二条曲线的坐标
    private var mSecondCurveStartPoint = Point()
    private val mSecondCurveEndPoint = Point()
    private val mSecondCurveControlPoint1 = Point()
    private val mSecondCurveControlPoint2 = Point()


    init {
        setLayerType(LAYER_TYPE_SOFTWARE, null)
        mLinePath = Path()
        mPath = Path()
        initPaint()
    }

    /**
     * 初始化画笔
     */
    private fun initPaint() {
        // Y轴刻度线
        paintLine = Paint()
        paintLine.style = Paint.Style.STROKE
        paintLine.strokeWidth = 1f
        paintLine.color = resources.getColor(R.color.color_y_line)

        // 指示渐变线
        paintGradientLine = Paint()
        paintGradientLine.style = Paint.Style.STROKE
        paintGradientLine.strokeWidth = 2f

        // 折现阴影
        paintPolyShadow = Paint()
        paintPolyShadow.style = Paint.Style.FILL

        // X轴上文字
        paintXText = Paint()
        paintXText.isAntiAlias = true
        paintXText.strokeWidth = 1f
        paintXText.textSize = 22f.sp
        paintXText.textAlign = Paint.Align.CENTER
        paintXText.color =
            resources.getColor(R.color.text_color_fc_40);//context.colorCompat(R.color.color_on_surface)

        // Y轴上文字
        paintYText = Paint()
        paintYText.isAntiAlias = true
        paintYText.textSize = 22f.sp
        paintYText.strokeWidth = 1f
        paintYText.textAlign = Paint.Align.RIGHT
        paintYText.color =
            resources.getColor(R.color.text_color_fc_40)//resources.getColor(R.color.c_666666_808080);//context.colorCompat(R.color.secondary_666666_808080)

        // 柱状图
        paintPillar = Paint()
        paintPillar.style = Paint.Style.FILL
        paintPillar.isAntiAlias = true
        paintPillar.color = resources.getColor(R.color.heart_month_poly_line)

        // 海拔折线
        paintPolyline = Paint()
        paintPolyline.style = Paint.Style.FILL
        paintPolyline.strokeWidth = 4f
        paintPolyline.isAntiAlias = true
        paintPolyline.color =
            resources.getColor(R.color.heart_poly_line);//context.colorCompat(R.color.fc355c_fc3159)

        // 指示滑块圆
        paintRound = Paint()
        paintRound.style = Paint.Style.FILL
        paintRound.isAntiAlias = true
        paintRound.color =
            resources.getColor(R.color.slider_round);//context.colorCompat(R.color.ffffff_6e6e6e)

        // 滑块底部 通过shader设置渐变色
        paintBessel = Paint()
        paintBessel.style = Paint.Style.FILL
        paintBessel.isAntiAlias = true
        paintBessel.color =
            resources.getColor(R.color.slider_round_bessel_bg)
    }


    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        this.w = w
        this.h = h
        this.oldh = oldh
        this.oldw = oldw
        scrWidth = width.toFloat()
        scrHeight = height.toFloat()
        ySpacing = scrHeight / 8f //y轴分8份
        perDp = scrHeight / 450

        // 底部圆滑块可以滑动的范围
        if (wmyTypeStr == TimeCode.TIME_CODE_WEEK.timeCode) {
            xWithStart = margin + paintXText.measureText("00-00") / 2
        }
        else if (wmyTypeStr == TimeCode.TIME_CODE_MONTH.timeCode){
            xWithStart = margin + paintXText.measureText("00-00") / 2
        }
        else if (wmyTypeStr == TimeCode.TIME_CODE_YEAR.timeCode){
            xWithStart = margin + paintXText.measureText("00-00") / 2
        }
        else {
            xWithStart = margin + paintXText.measureText(xData[0]) / 2
        }
        xWithEnd = scrWidth - xWithStart - paintYText.measureText(yDataRight[0]) * 1.5f
        if (wmyTypeStr == TimeCode.TIME_CODE_DAY.timeCode) {
            xSpacing = (xWithEnd - xWithStart) / 24
        } else {
            xSpacing = (xWithEnd - xWithStart) / xCount
        }
        if (wmyTypeStr == TimeCode.TIME_CODE_DAY.timeCode) {
            xTextSpacing = (xWithEnd - xWithStart) / 4
        } else {
            xTextSpacing = (xWithEnd - xWithStart) / xCount
        }
        if (!this.tempData.isNullOrEmpty()) {
            var lastIndex = 0
            for (i in tempData.size - 1 downTo 0) {
                if (tempData[i].max != 0.0f) {
                    lastIndex = i
                    break
                }
            }
            if (lastIndex > 0 || tempData[0].max != 0f) {
                xSlider = xWithStart + (xSpacing * lastIndex)
            } else {
                xSlider = scrWidth / 2
            }
        } else {
            xSlider = scrWidth / 2
        }

    }

    private val touchEventDelegate by lazy {
        ChartTouchEventDelegate(
            setSliderX = {
                xSlider = it
                invalidate()
            }
        )
    }
    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        return touchEventDelegate.onTouchEvent(
            event,
            this,
            xSlider,
            xSpacing,
            ySpacing,
            xWithStart,
            xWithEnd,
            tempData.size,
        )
    }

    private val margin = 50f.dp//20f.dp //左右两边距离
    private var xWithStart = 0f //x轴的起始点
    private var xWithEnd = 0f  //x轴结束点
    private var ySpacing = 0f //高度分割份数后间距
    private var xSpacing = 0f //x轴柱子分割份数后间距
    private var xTextSpacing = 0f //x轴文字分割份数后间距

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // X轴背景渐变
        drawGradientBackgroundUnderXText(canvas)
        //画y轴方向横线与文字
        drawY(canvas)

        drawPillar(canvas)

        //垂直渐变线
        drawGradientLine(canvas)
        //底部
        drawBessel(canvas)

        //画x轴方向文字
        drawX(canvas)
    }

    private fun drawGradientBackgroundUnderXText(canvas: Canvas) {
        // 画渐变颜色
        ViewDrawUtils.drawGradientBackgroundUnderXText(canvas,context,scrWidth,ySpacing,7)
    }

    private fun drawX(canvas: Canvas) {
        xData.forEachIndexed { index, s ->
            val x = xWithStart + xTextSpacing * index
            val dis = Math.abs(x - xSlider)
            var y = ySpacing * 7 - 45f
            if (dis < xTextSpacing / 2) {
                paintXText.typeface = Typeface.DEFAULT_BOLD
//                y -= 40f * (1 - dis / xTextSpacing)
                if (wmyTypeStr == TimeCode.TIME_CODE_YEAR.timeCode && index == 0) {
                    y -= ((30f.dp - 22.sp) * (1 - dis / xTextSpacing))
                } else {
                    y -= 30f.dp * (1 - dis / xTextSpacing)
                }
                paintXText.color = resources.getColor(R.color.text_color_fc_100)
                onXTextSelectListener?.invoke(index, s)
            } else {
                paintXText.typeface = Typeface.DEFAULT
                paintXText.color = resources.getColor(R.color.text_color_fc_40)

            }
            if (wmyTypeStr == TimeCode.TIME_CODE_YEAR.timeCode) {
                if (index == 0) {
                    val tp = TextPaint()
                    tp.color = paintXText.color
                    tp.style = paintXText.style
                    tp.textSize = paintXText.textSize
                    tp.typeface = paintXText.typeface
                    tp.isAntiAlias = true
                    tp.strokeWidth = 1f
                    val point = Point(x.toInt(), (y.toInt() - 20.dp).toInt())
                    textCenter(
                        s,
                        tp,
                        canvas,
                        point,
                        100,
                        Layout.Alignment.ALIGN_NORMAL,
                        0.8f,
                        0f,
                        false
                    )
                } else {
                    canvas.drawText(s, x, y, paintXText)
                }
            } else {
                canvas.drawText(s, x, y, paintXText)
            }
        }
    }
    private fun textCenter(
        string: String, textPaint: TextPaint, canvas: Canvas, point: Point, width: Int,
        align: Layout.Alignment, spacingmult: Float, spacingadd: Float, includepad: Boolean
    ) {
        val staticLayout =
            StaticLayout(string, textPaint, width, align, spacingmult, spacingadd, includepad)
        canvas.save()
        canvas.translate(
            (-staticLayout.width / 2 + point.x).toFloat(),
            (-staticLayout.height / 2 + point.y).toFloat()
        )
        staticLayout.draw(canvas)
        canvas.restore()
    }

    private fun drawY(canvas: Canvas) {
        for (i in 0..4) {
            canvas.drawLine(
                margin, ySpacing * (i + 1), scrWidth - margin,
                ySpacing * (i + 1), paintLine
            )
            paintYText.color =
                resources.getColor(R.color.c_666666_808080);//context.colorCompat(R.color.secondary_666666_808080)

            canvas.drawText(
                yDataRight[i], scrWidth - margin, ySpacing * (i + 1) - 10f, paintYText
            )

        }

    }

    // 绘制滑块底部
    private fun drawBessel(canvas: Canvas) {
        // 第一条曲线开始点
        mFirstCurveStartPoint[(xSlider - curveCircleRadius * 3).toInt()] = (ySpacing * 7).toInt()
        // 第一条曲线结束点
        mFirstCurveEndPoint[xSlider.toInt()] =
            (ySpacing * 7 - curveCircleRadius - curveCircleRadius / 4).toInt()
        // 第二条开始点
        mSecondCurveStartPoint = mFirstCurveEndPoint
        mSecondCurveEndPoint[(xSlider + curveCircleRadius * 3).toInt()] = (ySpacing * 7).toInt()

        // 第一条控制点
        mFirstCurveControlPoint1[(mFirstCurveStartPoint.x + curveCircleRadius + curveCircleRadius / 4).toInt()] =
            mFirstCurveStartPoint.y
        mFirstCurveControlPoint2[(mFirstCurveEndPoint.x - curveCircleRadius * 2 + curveCircleRadius).toInt()] =
            mFirstCurveEndPoint.y
        // 第二条控制点
        mSecondCurveControlPoint1[(mSecondCurveStartPoint.x + curveCircleRadius * 2 - curveCircleRadius).toInt()] =
            mSecondCurveStartPoint.y
        mSecondCurveControlPoint2[(mSecondCurveEndPoint.x - curveCircleRadius - curveCircleRadius / 4).toInt()] =
            mSecondCurveEndPoint.y
        mPath.reset()
        mPath.moveTo(0f, ySpacing * 7)
        mPath.lineTo(mFirstCurveStartPoint.x.toFloat(), mFirstCurveStartPoint.y.toFloat())
        mPath.cubicTo(
            mFirstCurveControlPoint1.x.toFloat(), mFirstCurveControlPoint1.y.toFloat(),
            mFirstCurveControlPoint2.x.toFloat(), mFirstCurveControlPoint2.y.toFloat(),
            mFirstCurveEndPoint.x.toFloat(), mFirstCurveEndPoint.y.toFloat()
        )
        mPath.cubicTo(
            mSecondCurveControlPoint1.x.toFloat(), mSecondCurveControlPoint1.y.toFloat(),
            mSecondCurveControlPoint2.x.toFloat(), mSecondCurveControlPoint2.y.toFloat(),
            mSecondCurveEndPoint.x.toFloat(), mSecondCurveEndPoint.y.toFloat()
        )
        mPath.lineTo(scrWidth, ySpacing * 7)
        mPath.lineTo(scrWidth, scrHeight)
        mPath.lineTo(0f, scrHeight)
        mPath.close()

        canvas.drawPath(mPath, paintBessel)
        //底部滑块
        canvas.drawCircle(xSlider, ySpacing * 7 + 5f, curveCircleRadius, paintRound)
    }
    companion object {
        private const val TAG = "TempBarChart"
        // 定义一个整型常量的枚举类型
        enum class BarChartType(val value: Int) {
            // 仅高温
            ONLY_HIGH_TEMP(1),
            // 高中温
            HIGH_MIDDLE_TEMP(2),
            // 高中低温
            ALL_RANGE_TEMP(3),
            // 仅中温
            ONLY_MIDDLE_TEMP(4),
            // 中正常温
            MIDDLE_NORMAL_TEMP(5),
            // 正常温
            NORMAL_TEMP(6),
        }
    }

    // Temperature thresholds
    private val highFeverThreshold = 38.1f
    private val lowFeverThreshold = 37.3f
    private val maxTempThreshold = 45f
    private val minTempThreshold = 34f
    private val barCornerRadius = 10f.dp // 柱子圆角半径
    private var minBarHeight = 10f.dp // 最小柱子高度
    private val defaultTempColorArray = intArrayOf(
    resources.getColor(R.color.bodytemp_high_ferver),
    resources.getColor(R.color.bodytemp_low_grade_fever),
    resources.getColor(R.color.bodytemp_normal)
    )
    // 画柱子
    private fun drawPillar(canvas: Canvas) {
        if (tempData.isNullOrEmpty())
            return

        var forCount = xCount
        if (this.wmyTypeStr == TimeCode.TIME_CODE_YEAR.timeCode) {
            forCount = 12
        }

        for (index in 0..< forCount) {
//            val x = xWithStart + xTextSpacing * index
//            val dis = Math.abs(x - xSlider)
            var isSelected =
                //  dis < xTextSpacing / 2
                xSlider < xWithStart + xSpacing * index + xSpacing / 2 && xSlider > xWithStart + xSpacing * index - xSpacing / 2
            if (index < tempData.size) {
                val item = tempData[index]
                if (item.avg != null && item.avg != 0f) {
                    var p1Top = 0f
                    var p1Left = xWithStart + xSpacing * index - 10f.dp
                    var p1Right = xWithStart + xSpacing * index + 10f.dp
                    var p1Bottom = 0f
                    var p1Radii: FloatArray = floatArrayOf(0f, 0f, 0f, 0f, 0f, 0f, 0f, 0f)
                    var p2Top = 0f
                    var p2Left = p1Left
                    var p2Right = p1Right
                    var p2Bottom = 0f
                    var p2Radii: FloatArray = floatArrayOf(0f, 0f, 0f, 0f, 0f, 0f, 0f, 0f)
                    var p3Top = 0f
                    var p3Left = p1Left
                    var p3Right = p1Right
                    var p3Bottom = 0f
                    var p3Radii: FloatArray = floatArrayOf(0f, 0f, 0f, 0f, 0f, 0f, 0f, 0f)
                    var barType = 0
                    var imax = item.max
                    var imin = item.min
                    var per = 0f
                    var pillerCurrLen = 0f
                    //最值修正 如果最值超过范围  则修正至35f-45f
                    if (imax > maxTempThreshold) imax = maxTempThreshold
                    if (imin < minTempThreshold) imin = minTempThreshold
                    if (imax in 38.1f..maxTempThreshold) {
                        if (imin in 38.1f..maxTempThreshold) barType = 1 // 仅高温
                        else if (imin >= 37.3f && imin < 38.1f) barType = 2 // 仅高中温
                        else barType = 3 // 高中正常温区域
                    } else if (imax >= 37.3f && imax < 38.1f) { // 最大值在 中温部分
                        if (imin >= 37.3f && imin < 38.1f) barType = 4 // 仅中温
                        else barType = 5 // 中正常温区域
                    } else
                        barType = 6 // 仅正常温度
                    var step = 1f
                    var top = 39f
                    if (type == 2) {
                        top = 46f
                        step = 3f
                    }
                    per = ySpacing / step //每一点的实际长度
                    if (barType == 1) { // 仅高温
                        pillerCurrLen = per * (imax - 38.1f)
                        p1Top = ySpacing + (top - imax) * per
                        if (imin == imax || pillerCurrLen < minBarHeight) pillerCurrLen = minBarHeight
                        p1Bottom = p1Top + pillerCurrLen
                        if(imin == imax) {
                            p1Top -= minBarHeight / 2
                            p1Bottom -= minBarHeight /2
                        }
                        val rectF = RectF(p1Left,p1Top,p1Right,p1Bottom)
                        val tempeProportions = calculateTemperatureProportions(item)
                        drawPerPillerBar(rectF,canvas,tempeProportions,paintPillar,isSelected)
                    } else if (barType == 2) { // 仅高中温
                        p1Top = ySpacing + (top - imax) * per
                        pillerCurrLen = per * (imax - 38.1f)
                        p1Bottom = p1Top + pillerCurrLen
                        pillerCurrLen = per * (38.1f - imin)
                        p2Top = p1Bottom
                        if (pillerCurrLen < minBarHeight) pillerCurrLen = minBarHeight
                        p2Bottom = p2Top + pillerCurrLen
                        val rectF = RectF(p1Left,p1Top,p1Right,p2Bottom)
                        val tempeProportions = calculateTemperatureProportions(item)
                        drawPerPillerBar(rectF,canvas,tempeProportions,paintPillar,isSelected)
                    } else if (barType == 3) { // 高中正常温度
                        pillerCurrLen = per * (imax - 38.1f)
                        p1Top = ySpacing + (top - imax) * per
                        p1Bottom = p1Top + pillerCurrLen
                        p2Top = p1Bottom
                        pillerCurrLen = per * (38.1f - 37.3f)
                        p2Bottom = p2Top + pillerCurrLen
                        p3Top = p2Bottom
                        pillerCurrLen = per * (37.3f - imin)
                        p3Bottom = p3Top + pillerCurrLen
                        val rectF = RectF(p1Left,p1Top,p1Right,p3Bottom)
                        val tempeProportions = calculateTemperatureProportions(item)
                        drawPerPillerBar(rectF,canvas,tempeProportions,paintPillar,isSelected)
                    } else if (barType == 4) { // 仅中温
                        pillerCurrLen = per * (imax - imin)
                        p2Top = ySpacing + per * (top - imax)
                        if (imin == imax || pillerCurrLen < minBarHeight) pillerCurrLen = minBarHeight
                        p2Bottom = p2Top + pillerCurrLen
                        if(imin == imax) {
                            p2Top -= minBarHeight / 2
                            p2Bottom -= minBarHeight /2
                        }
                        val rectF = RectF(p2Left,p2Top,p2Right,p2Bottom)
                        val tempeProportions = calculateTemperatureProportions(item)
                        drawPerPillerBar(rectF,canvas,tempeProportions,paintPillar,isSelected)
                    } else if (barType == 5) { // 中温正常温度
                        p2Top = ySpacing + per * (top - imax)
                        pillerCurrLen = per * (imax - 37.3f)
                        if (pillerCurrLen < minBarHeight) pillerCurrLen = minBarHeight
                        p2Bottom = p2Top + pillerCurrLen
                        p3Top = p2Bottom
                        pillerCurrLen = per * (37.3f - imin)
                        p3Bottom = p3Top + pillerCurrLen
                        val rectF = RectF(p2Left,p2Top,p2Right,p3Bottom)
                        val tempeProportions = calculateTemperatureProportions(item)
                        drawPerPillerBar(rectF,canvas,tempeProportions,paintPillar,isSelected)
                    } else if (barType == 6) { // 仅正常温度
                        pillerCurrLen = per * (imax - imin)
                        p3Top = ySpacing + (top - imax) * per
                        if (imin == imax || pillerCurrLen < minBarHeight) pillerCurrLen = minBarHeight
                        p3Bottom = p3Top + pillerCurrLen
                        if(imin == imax) {
                            p3Top -= minBarHeight / 2
                            p3Bottom -= minBarHeight /2
                        }
                        val rectF = RectF(p3Left,p3Top,p3Right,p3Bottom)
                        val tempeProportions = calculateTemperatureProportions(item)
                        drawPerPillerBar(rectF,canvas,tempeProportions,paintPillar,isSelected)
                    }
                    if (isSelected)
                        onDaySelectListener?.invoke(index,tempData[index])
                } else {
                    if (isSelected)
                        onDaySelectListener?.invoke(index, null)
                }
            } else {
                if (isSelected)
                    onDaySelectListener?.invoke(index, null)
            }

        }
    }

    private fun drawPerPillerBar(rectF: RectF, canvas: Canvas, proportions: FloatArray,paintPillar: Paint,isSelected: Boolean,colors: IntArray = defaultTempColorArray,) {
        // Create the rounded rectangle path
        val path = Path().apply {
            addRoundRect(
                rectF,
                floatArrayOf(
                    barCornerRadius, barCornerRadius, // Top-left corner
                    barCornerRadius, barCornerRadius, // Top-right corner
                    barCornerRadius, barCornerRadius, // Bottom-right corner
                    barCornerRadius, barCornerRadius  // Bottom-left corner
                ),
                Path.Direction.CW
            )
        }

        // Clip to the rounded rectangle path
        canvas.save()
        canvas.clipPath(path)

        // 分段绘制颜色逻辑
        var currentTop = rectF.top
        val totalHeight = maxOf(rectF.height() , minBarHeight)
        for (i in proportions.indices) {
            val sectionHeight = totalHeight * proportions[i]
            val sectionBottom = currentTop + sectionHeight

            paintPillar.color = colors!![i]
            if (!isSelected) {
                paintPillar.color = adjustAlpha(paintPillar.color)
            }
            canvas.drawRect(rectF.left, currentTop, rectF.right, sectionBottom, paintPillar)
            currentTop = sectionBottom
        }

        canvas.restore()
    }

    /**
     * Calculate temperature proportions for a given temperature item
     * @return FloatArray with proportions for high fever, low fever, and normal temperature
     */
    private fun calculateTemperatureProportions(item: TempItemDTO): FloatArray {

        var imax = item.max
        var imin = item.min

        // Limit temperature values to 34-45 range
        if (imax > maxTempThreshold) imax = maxTempThreshold
        if (imin < minTempThreshold) imin = minTempThreshold

        // Calculate proportions for each temperature range
        val totalRange = imax - imin

        // Default proportions (all normal temperature)
        var highFeverProp = 0f
        var lowFeverProp = 0f
        var normalProp = 0f

        if (totalRange > 0) {
            if (imax >= highFeverThreshold) {
                // 这里将高温区间的起点稍微往下扩展 epsilon（比如0.1度）
                if (imax - highFeverThreshold <= 0.3f) {
                    val effectiveThreshold = highFeverThreshold - 0.1f
                    highFeverProp = (imax - maxOf(imin, effectiveThreshold)) / totalRange
                } else {
                    highFeverProp = (imax - maxOf(imin, highFeverThreshold)) / totalRange
                }
            }

            if ((imax >= lowFeverThreshold && imax <= highFeverThreshold) ||
                (imin <= highFeverThreshold && imin >= lowFeverThreshold) ||
                (imin < lowFeverThreshold && imax > highFeverThreshold)) {

                val lowFeverMax = minOf(imax, highFeverThreshold)
                val lowFeverMin = maxOf(imin, lowFeverThreshold)

                if (lowFeverMax > lowFeverMin) {
                    lowFeverProp = (lowFeverMax - lowFeverMin) / totalRange
                }
            }

            if (imin < lowFeverThreshold) {
                normalProp = (minOf(imax, lowFeverThreshold) - imin) / totalRange
            }
        } else {
            // If total range is 0, all proportions are 0
            return when {
                imax >= highFeverThreshold -> floatArrayOf(1f, 0f, 0f)
                imax in lowFeverThreshold..(highFeverThreshold - 0.1f) -> floatArrayOf(0f, 1f, 0f)
                else -> floatArrayOf(0f, 0f, 1f)
            }
        }
        return floatArrayOf(highFeverProp, lowFeverProp, normalProp)
    }

    fun setXData(dateList: ArrayList<String>?, wMYType: String) {
        this.wmyTypeStr = wMYType
        when (wMYType) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                xCount = 24
                xData = arrayListOf("00:00", "06:00", "12:00", "18:00", "24:00")
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                dateList?.let {
                    xCount = it.size - 1
                    xData = it
                }
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                dateList?.let {
                    xCount = it.size - 1
                    xData = TimeUtils.getXDataWithStep(it, 7)
                }
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                this.isDayType = false
                xCount = 11
                xData = arrayListOf(
                    "${TimeUtils.getCurrentYearStr()}\n01",
                    "02",
                    "03",
                    "04",
                    "05",
                    "06",
                    "07",
                    "08",
                    "09",
                    "10",
                    "11",
                    "12"
                )
            }
        }
        onSizeChanged(this.w, this.h, this.oldw, this.oldh)
        postInvalidate()
    }

    fun setValue(
        value: MutableList<TempItemDTO>?,
        dateList: ArrayList<String>?,
        barChartType: Int,
        wMYType: String
    ): TempBarChart {
        if (value != null) this.tempData = value as ArrayList<TempItemDTO>
        this.wmyTypeStr = wMYType

        if (value.isNullOrEmpty()) {
            yDataRight = arrayListOf("42", "40", "38", "36", "34")
        }else {
            if (barChartType == 1) {
                type = 1
                yDataRight = arrayListOf("39", "38", "37", "36", "35")
            } else {
                type = 2
            }
        }
        when (wMYType) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                this.isDayType = true
                this.xCount = 24
                xData = arrayListOf("00:00", "06:00", "12:00", "18:00", "24:00")
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                this.isDayType = false
                dateList?.let {
                    this.xCount = it.size - 1
                    xData = it
                }
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                this.isDayType = false
                dateList?.let {
                    this.xCount = it.size - 1
                    xData = TimeUtils.getXDataWithStep(it, 7)
                }
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                this.isDayType = false
                this.xCount = 11
                xData = arrayListOf(
                    "${TimeUtils.getCurrentYearStr()}\n01",
                    "02",
                    "03",
                    "04",
                    "05",
                    "06",
                    "07",
                    "08",
                    "09",
                    "10",
                    "11",
                    "12"
                )
            }
        }
        onSizeChanged(this.w, this.h, this.oldw, this.oldh)
        postInvalidate()
        return this
    }


    private fun drawGradientLine(canvas: Canvas) {
        var mLinearGradient = LinearGradient(
            xSlider, ySpacing, xSlider, ySpacing * 6,
            intArrayOf(
                resources.getColor(R.color.indicator_line_start),
                resources.getColor(R.color.indicator_line_center),
                resources.getColor(R.color.indicator_line_start)
            ), null, Shader.TileMode.MIRROR
        )
        if (tempData.isNullOrEmpty()) {
            mLinearGradient = LinearGradient(
                xSlider, ySpacing, xSlider, ySpacing * 6,
                intArrayOf(
                    resources.getColor(R.color.indicator_grey_line_start),
                    resources.getColor(R.color.indicator_grey_line_center),
                    resources.getColor(R.color.indicator_grey_line_start)
                ), null, Shader.TileMode.MIRROR
            )
        }
        paintGradientLine.shader = mLinearGradient

        if (ySpacing > 0) {
            canvas.drawLine(xSlider, ySpacing, xSlider, ySpacing * 6, paintGradientLine)
        }
    }

    private var onDaySelectListener: ((index: Int, temp: TempItemDTO?) -> Unit)? = null

    fun setOnDaySelectListener(l: ((index: Int, temp: TempItemDTO?) -> Unit)): TempBarChart {
        this.onDaySelectListener = l
        return this
    }

    private var onXTextSelectListener: ((index: Int, xText: String) -> Unit)? = null

    fun setOnXTextSelectListener(l: ((index: Int, xText: String) -> Unit)): TempBarChart {
        this.onXTextSelectListener = l
        return this
    }


    fun adjustAlpha(color: Int, alpha: Int = 153): Int {
        return Color.argb(alpha, color.red, color.green, color.blue)
    }

    fun getXData(list: ArrayList<TempItemDTO>, step: Int): ArrayList<String> {
        var dataSet = ArrayList<String>()
        list.forEachIndexed() { index, item ->
            if ((index + 2) % step == 0) {
                dataSet.add(getDate(item.startTime))
            } else
                dataSet.add("")
        }
        return dataSet
    }

    fun getDate(ori: String): String {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        val dateTime = LocalDateTime.parse(ori, formatter)
        return String.format("%02d", dateTime.dayOfMonth)
    }

    fun calYData(ori: Triple<Float, Float, Float>): ArrayList<String> {
        //first->maxTemp  second->minTemp third->step
        var result = arrayListOf(String.format("%.1f", ori.first) + "℃")
        var t = ori.first
        for (i in 0..2) {
            t -= ori.third
            result.add(String.format("%.1f", t) + "℃")
        }
        result.add(String.format("%.1f", ori.second) + "℃")
        return result
    }

}