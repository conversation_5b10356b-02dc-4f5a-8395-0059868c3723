package com.healthlink.hms.views.charts

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Point
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.graphics.Typeface
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.core.graphics.blue
import androidx.core.graphics.green
import androidx.core.graphics.red
import com.healthlink.hms.R
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.ktExt.sp
import com.healthlink.hms.server.data.dto.SpO2ItemDTO
import com.healthlink.hms.utils.ChartTouchEventDelegate
import com.healthlink.hms.utils.TimeUtils
import com.healthlink.hms.utils.ViewDrawUtils
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.math.abs

/**
 *@Author: 付仁秀
 *@Description：
 **/
class SpO2BarChart(context: Context, attrs: AttributeSet?) : View(context, attrs) {

    //屏幕宽高
    private var scrWidth = 0f
    private var scrHeight = 0f
    private var perDp = 0f //1dp所占的值
    private var xData = arrayListOf("01", "02", "03", "04", "05", "06", "07")
    private var yDataRight: Array<String> = arrayOf("100%", "90%", "80%", "70%", "0%")
    private var type: Int = 0 //六种纵坐标类型
    private var xCount = 30 //三种横坐标类型
    private var spo2Data = emptyList<SpO2ItemDTO>()
    private lateinit var paintLine: Paint // y轴线
    private lateinit var paintGradientLine: Paint //指示渐变线
    private lateinit var paintXText: Paint // x轴坐标
    private lateinit var paintYText: Paint // y轴坐标
    private lateinit var paintPolyline: Paint // 海拔折线
    private lateinit var paintPolyShadow: Paint // 海拔折线阴影
    private lateinit var paintPillar: Paint //柱子
    private lateinit var paintRound: Paint  // 指示滑块圆
    private lateinit var paintBessel: Paint  // 滑块底部
    private var xSlider = 0f //滑块的x轴位置
    private var mLinePath: Path  //折线路径
    private val curveCircleRadius = 22f.dp
    private var mPath: Path  //滑块贝塞尔
    private var wmyTypeStr = TimeCode.TIME_CODE_DAY.timeCode
    private var w = 0
    private var h = 0
    private var oldw = 0
    private var oldh = 0


    //第一条曲线的坐标
    private val mFirstCurveStartPoint = Point()
    private val mFirstCurveEndPoint = Point()
    private val mFirstCurveControlPoint1 = Point()
    private val mFirstCurveControlPoint2 = Point()

    //第二条曲线的坐标
    private var mSecondCurveStartPoint = Point()
    private val mSecondCurveEndPoint = Point()
    private val mSecondCurveControlPoint1 = Point()
    private val mSecondCurveControlPoint2 = Point()


    init {
        setLayerType(LAYER_TYPE_SOFTWARE, null)
        mLinePath = Path()
        mPath = Path()
        initPaint()
    }

    /**
     * 初始化画笔
     */
    private fun initPaint() {
        // Y轴刻度线
        paintLine = Paint()
        paintLine.style = Paint.Style.STROKE
        paintLine.strokeWidth = 1f
        paintLine.color = resources.getColor(R.color.color_y_line)

        // 指示渐变线
        paintGradientLine = Paint()
        paintGradientLine.style = Paint.Style.STROKE
        paintGradientLine.strokeWidth = 2f

        // 折现阴影
        paintPolyShadow = Paint()
        paintPolyShadow.style = Paint.Style.FILL

        // X轴上文字
        paintXText = Paint()
        paintXText.isAntiAlias = true
        paintXText.strokeWidth = 1f
        paintXText.textSize = 22f.sp
        paintXText.textAlign = Paint.Align.CENTER
        paintXText.color =
            resources.getColor(R.color.text_color_fc_40);//context.colorCompat(R.color.color_on_surface)

        // Y轴上文字
        paintYText = Paint()
        paintYText.isAntiAlias = true
        paintYText.textSize = 22f.sp
        paintYText.strokeWidth = 1f
        paintYText.textAlign = Paint.Align.RIGHT
        paintYText.color =
            resources.getColor(R.color.text_color_fc_40)//resources.getColor(R.color.c_666666_808080);//context.colorCompat(R.color.secondary_666666_808080)

        // 柱状图
        paintPillar = Paint()
        paintPillar.style = Paint.Style.FILL
        paintPillar.isAntiAlias = true
        paintPillar.color = resources.getColor(R.color.heart_month_poly_line)

        // 海拔折线
        paintPolyline = Paint()
        paintPolyline.style = Paint.Style.FILL
        paintPolyline.strokeWidth = 4f
        paintPolyline.isAntiAlias = true
        paintPolyline.color =
            resources.getColor(R.color.heart_poly_line);//context.colorCompat(R.color.fc355c_fc3159)

        // 指示滑块圆
        paintRound = Paint()
        paintRound.style = Paint.Style.FILL
        paintRound.isAntiAlias = true
        paintRound.color =
            resources.getColor(R.color.slider_round);//context.colorCompat(R.color.ffffff_6e6e6e)

        // 滑块底部 通过shader设置渐变色
        paintBessel = Paint()
        paintBessel.style = Paint.Style.FILL
        paintBessel.isAntiAlias = true
        paintBessel.color =
            resources.getColor(R.color.slider_round_bessel_bg)
    }


    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        this.w = w
        this.h = h
        this.oldh = oldh
        this.oldw = oldw
        scrWidth = width.toFloat()
        scrHeight = height.toFloat()
        ySpacing = scrHeight / 8f //y轴分8份
        perDp = scrHeight / 450
        // 底部圆滑块可以滑动的范围
        if (wmyTypeStr == TimeCode.TIME_CODE_WEEK.timeCode)
            xWithStart = margin + paintXText.measureText("00-00") / 2
        else
            xWithStart = margin + paintXText.measureText(xData[0]) / 2
        xWithEnd = scrWidth - margin - paintYText.measureText(yDataRight[0]) * 1.5f
        xSpacing = (xWithEnd - xWithStart) / xCount
        xTextSpacing = (xWithEnd - xWithStart) / xCount
        if (spo2Data.isNullOrEmpty()) {
            xSlider = scrWidth / 2
        } else {
            var lastIndex = 0
            for (i in spo2Data.size - 1 downTo 0) {
                if (spo2Data[i].max != 0) {
                    lastIndex = i
                    break;
                }

            }
            if (lastIndex > 0 || spo2Data[0].avg != 0) {
                xSlider = xWithStart + (xSpacing * lastIndex)
            } else {
                xSlider = scrWidth / 2
            }
        }
    }

    private val touchEventDelegate by lazy {
        ChartTouchEventDelegate(
            setSliderX = {
                xSlider = it
                invalidate()
            }
        )
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        return touchEventDelegate.onTouchEvent(
            event,
            this,
            xSlider,
            xSpacing,
            ySpacing,
            xWithStart,
            xWithEnd,
            spo2Data.size
        )
    }

    private val margin = 50f.dp//20f.dp //左右两边距离
    private var xWithStart = 0f //x轴的起始点
    private var xWithEnd = 0f  //x轴结束点
    private var ySpacing = 0f //高度分割份数后间距
    private var xSpacing = 0f //x轴柱子分割份数后间距
    private var xTextSpacing = 0f //x轴文字分割份数后间距

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // X轴背景渐变
        drawGradientBackgroundUnderXText(canvas)
        //画y轴方向横线与文字
        drawY(canvas)

        drawPillar(canvas)

        //垂直渐变线
        drawGradientLine(canvas)
        //底部
        drawBessel(canvas)

        //画x轴方向文字
        drawX(canvas)
    }

    private fun drawGradientBackgroundUnderXText(canvas: Canvas) {
        // 画渐变颜色
        ViewDrawUtils.drawGradientBackgroundUnderXText(canvas,context,scrWidth,ySpacing,7)
    }

    private fun drawX(canvas: Canvas) {

        xData.forEachIndexed { index, s ->
            val x = xWithStart + xTextSpacing * index
            val dis = Math.abs(x - xSlider)
            var y = ySpacing * 7 - 45f
            if (dis < xTextSpacing / 2) {
                paintXText.typeface = Typeface.DEFAULT_BOLD
//                y -= 40f * (1 - dis / xTextSpacing)
                if (wmyTypeStr == TimeCode.TIME_CODE_YEAR.timeCode && index == 0) {
                    y -= ((30f.dp - 22.sp) * (1 - dis / xTextSpacing))
                } else {
                    y -= 30f.dp * (1 - dis / xTextSpacing)
                }
                paintXText.color = resources.getColor(R.color.text_color_fc_100)
                onXTextSelectListener?.invoke(index, s)
            } else {
                paintXText.typeface = Typeface.DEFAULT
                paintXText.color = resources.getColor(R.color.text_color_fc_40)

            }

            if (wmyTypeStr == TimeCode.TIME_CODE_YEAR.timeCode) {
                if (index == 0) {
                    val tp = TextPaint()
                    tp.color = paintXText.color
                    tp.style = paintXText.style
                    tp.textSize = paintXText.textSize
                    tp.typeface = paintXText.typeface
                    tp.isAntiAlias = true
                    tp.strokeWidth = 1f
                    val point = Point(x.toInt(), (y.toInt() - 20.dp).toInt())
                    textCenter(
                        s,
                        tp,
                        canvas,
                        point,
                        100,
                        Layout.Alignment.ALIGN_NORMAL,
                        0.8f,
                        0f,
                        false
                    )
                } else {
                    canvas.drawText(s, x, y, paintXText)
                }
            } else {
                canvas.drawText(s, x, y, paintXText)
            }
        }
    }

    private fun textCenter(
        string: String, textPaint: TextPaint, canvas: Canvas, point: Point, width: Int,
        align: Layout.Alignment, spacingmult: Float, spacingadd: Float, includepad: Boolean
    ) {
        val staticLayout =
            StaticLayout(string, textPaint, width, align, spacingmult, spacingadd, includepad)
        canvas.save()
        canvas.translate(
            (-staticLayout.width / 2 + point.x).toFloat(),
            (-staticLayout.height / 2 + point.y).toFloat()
        )
        staticLayout.draw(canvas)
        canvas.restore()
    }

    private fun drawY(canvas: Canvas) {
        for (i in 0..4) {
            canvas.drawLine(
                margin, ySpacing * (i + 1), scrWidth - margin,
                ySpacing * (i + 1), paintLine
            )
            paintYText.color =
                resources.getColor(R.color.c_666666_808080);//context.colorCompat(R.color.secondary_666666_808080)

            canvas.drawText(
                yDataRight[i], scrWidth - margin, ySpacing * (i + 1) - 10f, paintYText
            )

        }

    }

    // 绘制滑块底部
    private fun drawBessel(canvas: Canvas) {
        // 第一条曲线开始点
        mFirstCurveStartPoint[(xSlider - curveCircleRadius * 3).toInt()] = (ySpacing * 7).toInt()
        // 第一条曲线结束点
        mFirstCurveEndPoint[xSlider.toInt()] =
            (ySpacing * 7 - curveCircleRadius - curveCircleRadius / 4).toInt()
        // 第二条开始点
        mSecondCurveStartPoint = mFirstCurveEndPoint
        mSecondCurveEndPoint[(xSlider + curveCircleRadius * 3).toInt()] = (ySpacing * 7).toInt()

        // 第一条控制点
        mFirstCurveControlPoint1[(mFirstCurveStartPoint.x + curveCircleRadius + curveCircleRadius / 4).toInt()] =
            mFirstCurveStartPoint.y
        mFirstCurveControlPoint2[(mFirstCurveEndPoint.x - curveCircleRadius * 2 + curveCircleRadius).toInt()] =
            mFirstCurveEndPoint.y
        // 第二条控制点
        mSecondCurveControlPoint1[(mSecondCurveStartPoint.x + curveCircleRadius * 2 - curveCircleRadius).toInt()] =
            mSecondCurveStartPoint.y
        mSecondCurveControlPoint2[(mSecondCurveEndPoint.x - curveCircleRadius - curveCircleRadius / 4).toInt()] =
            mSecondCurveEndPoint.y
        mPath.reset()
        mPath.moveTo(0f, ySpacing * 7)
        mPath.lineTo(mFirstCurveStartPoint.x.toFloat(), mFirstCurveStartPoint.y.toFloat())
        mPath.cubicTo(
            mFirstCurveControlPoint1.x.toFloat(), mFirstCurveControlPoint1.y.toFloat(),
            mFirstCurveControlPoint2.x.toFloat(), mFirstCurveControlPoint2.y.toFloat(),
            mFirstCurveEndPoint.x.toFloat(), mFirstCurveEndPoint.y.toFloat()
        )
        mPath.cubicTo(
            mSecondCurveControlPoint1.x.toFloat(), mSecondCurveControlPoint1.y.toFloat(),
            mSecondCurveControlPoint2.x.toFloat(), mSecondCurveControlPoint2.y.toFloat(),
            mSecondCurveEndPoint.x.toFloat(), mSecondCurveEndPoint.y.toFloat()
        )
        mPath.lineTo(scrWidth, ySpacing * 7)
        mPath.lineTo(scrWidth, scrHeight)
        mPath.lineTo(0f, scrHeight)
        mPath.close()

        canvas.drawPath(mPath, paintBessel)
        //底部滑块
        canvas.drawCircle(xSlider, ySpacing * 7 + 5f, curveCircleRadius, paintRound)
    }

    private fun drawPillar(canvas: Canvas) {
        //画柱子
        val radius = 10f.dp // 圆角半径
        if (spo2Data.isNullOrEmpty())
            return
        for (index in 0..xCount) {
            val x = xWithStart + xTextSpacing * index
            val dis = Math.abs(x - xSlider)
            var isSelected =
                dis < xTextSpacing / 2
            if (index < spo2Data.size && spo2Data[index].avg != 0) {
                val item = spo2Data[index]
                var p1Top = 0f
                var p1Left = xWithStart + xSpacing * index - 10f.dp
                var p1Right = xWithStart + xSpacing * index + 10f.dp
                var p1Bottom = 0f
                var p1Radii: FloatArray = floatArrayOf(0f, 0f, 0f, 0f, 0f, 0f, 0f, 0f)
                var p2Top = 0f
                var p2Left = p1Left
                var p2Right = p1Right
                var p2Bottom = 0f
                var p2Radii: FloatArray = floatArrayOf(0f, 0f, 0f, 0f, 0f, 0f, 0f, 0f)
                var p3Top = 0f
                var p3Left = p1Left
                var p3Right = p1Right
                var p3Bottom = 0f
                var p3Radii: FloatArray = floatArrayOf(0f, 0f, 0f, 0f, 0f, 0f, 0f, 0f)
                var barType = 0
                var imax = item.max
                var imin = item.min
                var per = 0f
                var pillerCurrLen = 0f
                if (imax in 90..100) {
                    if (imin in 90..100) barType = 1 //存在画圆
                    else if (imin in 70 until 90) barType = 2
                    else barType = 3
                } else if (imax in 70 until 90) {
                    if (imin in 70 until 90) barType = 4 //存在画圆
                    else barType = 5
                } else
                    barType = 6 //存在画圆
                when (type) {
                    1 -> {
                        //纵坐标为 90..100
                        per = (ySpacing * 4) / 12 //每一点的实际长度
                        pillerCurrLen = per * (imax - imin)
                        p1Top = ySpacing + (100 - imax) * per
                        if (imin == imax || pillerCurrLen < 10f.dp) pillerCurrLen = 10f.dp
                        p1Bottom = p1Top + pillerCurrLen
                        p1Radii = floatArrayOf(
                            radius, radius, // 左上角
                            radius, radius, // 右上角
                            radius, radius, // 右下角
                            radius, radius  // 左下角
                        )


                    }

                    2 -> {
                        //纵坐标 70..100 三种情况
                        per = (ySpacing * 4) / 32 //每一点的实际长度
                        pillerCurrLen = per * (imax - 90)
                        p1Left = xWithStart + xSpacing * index - 10f.dp
                        p1Top = ySpacing + (100 - imax) * per
                        p1Right = xWithStart + xSpacing * index + 10f.dp
                        if (imin == imax || pillerCurrLen < 10f.dp) pillerCurrLen = 10f.dp
                        p1Bottom = p1Top + pillerCurrLen
                        if (barType == 1) {
                            p1Top = ySpacing + (100 - imax) * per
                            if (imin == imax || pillerCurrLen < 10f.dp) pillerCurrLen = 10f.dp
                            else pillerCurrLen = (imax - imin) * per
                            p1Bottom = p1Top + pillerCurrLen
                            p1Radii = floatArrayOf(
                                radius, radius, // 左上角
                                radius, radius, // 右上角
                                radius, radius, // 右下角
                                radius, radius // 左下角
                            )
                        } else if (barType == 2) {
                            p1Radii = floatArrayOf(
                                radius, radius, // 左上角
                                radius, radius, // 右上角
                                0f, 0f, // 右下角
                                0f, 0f // 左下角
                            )
                            pillerCurrLen = per * (90 - imin)
                            p2Top = p1Bottom
                            if (pillerCurrLen < 10f.dp) pillerCurrLen = 10f.dp
                            p2Bottom = p2Top + pillerCurrLen
                            p2Radii = floatArrayOf(
                                0f, 0f, //  左上角
                                0f, 0f, // 右上角
                                radius, radius, // 左下角
                                radius, radius, // 右下角
                            )
                        } else if (barType == 4) {
                            pillerCurrLen = per * (imax - imin)
                            p2Top = ySpacing + (100 - imax) * per
                            if (imin == imax || pillerCurrLen < 10f.dp) pillerCurrLen = 10f.dp
                            p2Bottom = p2Top + pillerCurrLen
                            p2Radii = floatArrayOf(
                                radius, radius, //  左上角
                                radius, radius, // 右上角
                                radius, radius, // 左下角
                                radius, radius, // 右下角
                            )
                            p1Top = 0f
                        }

                    }

                    3 -> {
                        //纵坐标 0..100 6种情况
                        per = (ySpacing - radius) / 10 //每一点的实际长度
                        if (barType == 1) {
                            pillerCurrLen = per * (imax - 90)
                            p1Top = ySpacing + (100 - imax) * per
                            if (imin == imax || pillerCurrLen < 10f.dp) pillerCurrLen = 10f.dp
                            p1Bottom = p1Top + pillerCurrLen
                            p1Radii = floatArrayOf(
                                radius, radius, // 左上角
                                radius, radius, // 右上角
                                radius, radius, // 右下角
                                radius, radius // 左下角
                            )

                        } else if (barType == 2) {
                            p1Top = ySpacing + (100 - imax) * per
                            p1Bottom = ySpacing * 2f
                            p1Radii = floatArrayOf(
                                radius, radius, // 左上角
                                radius, radius, // 右上角
                                0f, 0f, // 右下角
                                0f, 0f // 左下角
                            )
                            pillerCurrLen = per * (90 - imin)
                            p2Top = p1Bottom
                            if (pillerCurrLen < 10f.dp) pillerCurrLen = 10f.dp
                            p2Bottom = p2Top + pillerCurrLen
                            p2Radii = floatArrayOf(
                                0f, 0f, //  左上角
                                0f, 0f, // 右上角
                                radius, radius, // 左下角
                                radius, radius, // 右下角
                            )
                        } else if (barType == 3) {
                            p1Top = ySpacing + (100 - imax) * per
                            p1Bottom = ySpacing * 2f
                            p1Radii = floatArrayOf(
                                radius, radius, // 左上角
                                radius, radius, // 右上角
                                0f, 0f, // 右下角
                                0f, 0f // 左下角
                            )
                            p2Top = p1Bottom
                            p2Bottom = ySpacing * 4f
                            p2Radii = floatArrayOf(
                                0f, 0f, //  左上角
                                0f, 0f, // 右上角
                                0f, 0f, // 左下角
                                0f, 0f, // 右下角
                            )
                            p3Top = p2Bottom
                            p3Bottom = ySpacing * 4.3f
                            p3Radii = floatArrayOf(
                                0f, 0f, //  左上角
                                0f, 0f, // 右上角
                                radius, radius, // 左下角
                                radius, radius, // 右下角
                            )


                        } else if (barType == 4) {
                            pillerCurrLen = per * (imax - imin)
                            p2Top = ySpacing * 2f + per * (90 - imax)
                            if (imin == imax || pillerCurrLen < 10f.dp) pillerCurrLen = 10f.dp
                            p2Bottom = p2Top + pillerCurrLen
                            p2Radii = floatArrayOf(
                                radius, radius, //  左上角
                                radius, radius, // 右上角
                                radius, radius, // 左下角
                                radius, radius, // 右下角
                            )
                        } else if (barType == 5) {
                            p2Top = ySpacing * 2f + per * (90 - imax)
                            p2Bottom = ySpacing * 4
                            p2Radii = floatArrayOf(
                                radius, radius, //  左上角
                                radius, radius, // 右上角
                                0f, 0f, // 左下角
                                0f, 0f, // 右下角
                            )
                            p3Top = p2Bottom
                            p3Bottom = ySpacing * 4.3f
                            p3Radii = floatArrayOf(
                                0f, 0f, //  左上角
                                0f, 0f, // 右上角
                                radius, radius, // 左下角
                                radius, radius, // 右下角
                            )

                        } else if (barType == 6) {
                            per = ySpacing / 70
                            pillerCurrLen = per * (imax - imin)
                            p3Top = ySpacing * 4 + (70 - imax) * per
                            if (pillerCurrLen >= 10f.dp)
                                p3Bottom = p3Top + pillerCurrLen
                            else p3Bottom = p3Top + 10f.dp
                            p3Radii = floatArrayOf(
                                radius, radius, //  左上角
                                radius, radius, // 右上角
                                radius, radius, // 左下角
                                radius, radius, // 右下角
                            )
                        }
                    }

                    4 -> {
                        // 70..90 一种情况
                        per = (ySpacing * 4 - radius) / 20 //每一点的实际长度
                        pillerCurrLen = per * (imax - imin)
                        p2Top = ySpacing + radius + (90 - imax) * per
                        if (imin == imax || pillerCurrLen < 10f.dp) pillerCurrLen = 10f.dp
                        p2Bottom = p2Top + pillerCurrLen
                        p2Radii = floatArrayOf(
                            radius, radius, // 左上角
                            radius, radius, // 右上角
                            radius, radius, // 右下角
                            radius, radius  // 左下角
                        )
                    }

                    5 -> {
                        //0..90 三种情况
                        if (barType == 4) {
                            per = (ySpacing * 3) / 30 //每一点的实际长度
                            pillerCurrLen = per * (imax - imin)
                            p2Top = ySpacing + (95 - imax) * per
                            if (imin == imax || pillerCurrLen < 10f.dp) pillerCurrLen = 10f.dp
                            p2Bottom = p2Top + pillerCurrLen
                            p2Radii = floatArrayOf(
                                radius, radius, // 左上角
                                radius, radius, // 右上角
                                radius, radius, // 右下角
                                radius, radius  // 左下角
                            )

                        } else if (barType == 5) {
                            per = (ySpacing * 3) / 30 //每一点的实际长度
                            p2Top = ySpacing + (95 - imax) * per
                            p2Bottom = ySpacing * 3.5f
                            p2Radii = floatArrayOf(
                                radius, radius, // 左上角
                                radius, radius, // 右上角
                                0f, 0f, // 右下角
                                0f, 0f  // 左下角
                            )
                            p3Top = p2Bottom
                            if (imin >= 65) p3Bottom = p3Top + 10f.dp
                            else p3Bottom = ySpacing * 4.3f
                            p3Radii = floatArrayOf(
                                0f, 0f,
                                0f, 0f,
                                radius, radius,
                                radius, radius
                            )
                        } else if (barType == 6) {
                            per = (ySpacing * 3) / 30 //每一点的实际长度
                            var over65Len = per * (imax - 65)
                            var over0Len = ySpacing / 50f * (65 - imin)
                            if (imax > 65) {
                                p3Top = ySpacing * 4 - over65Len
                                if (imin > 65) over0Len = 0f
                                pillerCurrLen = over65Len + over0Len
                            } else {
                                p3Top = ySpacing * 4 + ySpacing / 50f * (65 - imax)
                                pillerCurrLen = ySpacing / 50f * (imax - imin)
                            }
                            if (imin == imax || pillerCurrLen < 10f.dp) pillerCurrLen = 10f.dp
                            p3Bottom = p3Top + pillerCurrLen
                            p3Radii = floatArrayOf(
                                radius, radius, // 左上角
                                radius, radius, // 右上角
                                radius, radius, // 右下角
                                radius, radius  // 左下角
                            )
                        }
                    }

                    6 -> {
                        //0..70 一种情况
                        per = (ySpacing * 3) / 30 //每一点的实际长度
                        var over45Len = per * (imax - 40)
                        var over0Len = (40 - imin) * ySpacing / 40
                        if (imax > 40) {
                            p3Top = ySpacing + (70 - imax) * per
                            if (imin > 40) over0Len = 0f
                            pillerCurrLen = over45Len + over0Len
                        } else {
                            p3Top = ySpacing + (40 - imax) * ySpacing / 40
                            pillerCurrLen = (imax - imin) * ySpacing / 40
                        }
                        if (imin == imax || pillerCurrLen < 10f.dp) pillerCurrLen = 10f.dp
                        p3Bottom = p3Top + pillerCurrLen
                        p3Radii = floatArrayOf(
                            radius, radius, // 左上角
                            radius, radius, // 右上角
                            radius, radius, // 右下角
                            radius, radius  // 左下角
                        )
                    }

                }
                // 是否选中
                if (isSelected) {
                    onDaySelectListener?.invoke(index, item)
                }
                if (p1Top != 0f) {
                    paintPillar.color = resources.getColor(R.color.card_spo2_high)
                    if (!isSelected)
                        paintPillar.color = adjustAlpha(paintPillar.color)
                    drawPerPiller(paintPillar, p1Left, p1Top, p1Right, p1Bottom, p1Radii, canvas)
                }
                if (p2Top != 0f) {
                    paintPillar.color = resources.getColor(R.color.card_spo2_middle)
                    if (!isSelected)
                        paintPillar.color = adjustAlpha(paintPillar.color)
                    drawPerPiller(paintPillar, p2Left, p2Top, p2Right, p2Bottom, p2Radii, canvas)
                }
                if (p3Top != 0f) {
                    paintPillar.color = resources.getColor(R.color.card_spo2_low)
                    if (!isSelected)
                        paintPillar.color = adjustAlpha(paintPillar.color)
                    drawPerPiller(paintPillar, p3Left, p3Top, p3Right, p3Bottom, p3Radii, canvas)
                }
            } else {
                if (isSelected) {
                    onDaySelectListener?.invoke(index, null)
                }
            }
        }
    }

    fun drawPerPiller(
        paint: Paint,
        left: Float,
        top: Float,
        right: Float,
        bottom: Float,
        radii: FloatArray,
        canvas: Canvas
    ) {
        val rect = RectF(
            left,
            top,
            right,
            bottom
        )
        val path = Path().apply {
            addRoundRect(rect, radii, android.graphics.Path.Direction.CW)
        }
        canvas.drawPath(path, paint)
    }


    fun setXData(dateList: ArrayList<String>?, wMYType: String) {
        this.wmyTypeStr = wMYType
        when (wMYType) {
            TimeCode.TIME_CODE_WEEK.timeCode -> {
                dateList?.let {
                    xCount = it.size - 1
                    xData = it
                }
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                dateList?.let {
                    xCount = it.size - 1
                    xData = TimeUtils.getXDataWithStep(it, 7)
                }
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                xCount = 11
                xData = arrayListOf(
                    "${TimeUtils.getCurrentYearStr()}\n01",
                    "02",
                    "03",
                    "04",
                    "05",
                    "06",
                    "07",
                    "08",
                    "09",
                    "10",
                    "11",
                    "12"
                )
            }
        }
        onSizeChanged(this.w, this.h, this.oldw, this.oldh)
        postInvalidate()
    }


    fun setValue(
        value: MutableList<SpO2ItemDTO>?,
        dateList: ArrayList<String>?,
        barChartType: Int,
        wMYType: String
    ): SpO2BarChart {
        //分六种类型
        // 90 .. 100     type=1
        // 70 .. 100     type=2
        //  0 ..100      type=3
        // 70 until 90   type=4
        //  0 until 90   type=5
        //  0 until 70   type=6

        when (barChartType) {
            1 -> {
                this.type = 1
                yDataRight = arrayOf("100%", "97%", "94%", "91%", "88%")
            }

            2 -> {
                this.type = 2
                yDataRight = arrayOf("100%", "92%", "84%", "76%", "68%")
            }

            3 -> {
                this.type = 3
                yDataRight = arrayOf("100%", "90%", "80%", "70%", "0%")
            }

            4 -> {
                this.type = 4
                yDataRight = arrayOf("90%", "85%", "80%", "75%", "70%")
            }

            5 -> {
                this.type = 5
                yDataRight = arrayOf("95%", "85%", "75%", "65%", "0%")
            }

            6 -> {
                this.type = 6
                yDataRight = arrayOf("70%", "60%", "50%", "40%", "0%")
            }
        }

        if (value != null) this.spo2Data = value as ArrayList<SpO2ItemDTO>
        when (wMYType) {
            TimeCode.TIME_CODE_WEEK.timeCode -> {
                dateList?.let {
                    xCount = it.size - 1
                    xData = it
                }


            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                dateList?.let {
                    xCount = it.size - 1
                    xData = TimeUtils.getXDataWithStep(it, 7)
                }
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                this.xCount = 11
                xData = arrayListOf(
                    "${TimeUtils.getCurrentYearStr()}\n01",
                    "02",
                    "03",
                    "04",
                    "05",
                    "06",
                    "07",
                    "08",
                    "09",
                    "10",
                    "11",
                    "12"
                )
            }
        }
        onSizeChanged(w, h, oldw, oldh)
        postInvalidate()
        return this
    }


    private fun drawGradientLine(canvas: Canvas) {
        var mLinearGradient = LinearGradient(
            xSlider, ySpacing, xSlider, ySpacing * 6,
            intArrayOf(
                resources.getColor(R.color.indicator_line_start),
                resources.getColor(R.color.indicator_line_center),
                resources.getColor(R.color.indicator_line_start)
            ), null, Shader.TileMode.MIRROR
        )
        if (spo2Data.isNullOrEmpty())
            mLinearGradient = LinearGradient(
                xSlider, ySpacing, xSlider, ySpacing * 6,
                intArrayOf(
                    resources.getColor(R.color.indicator_grey_line_start),
                    resources.getColor(R.color.indicator_grey_line_center),
                    resources.getColor(R.color.indicator_grey_line_start)
                ), null, Shader.TileMode.MIRROR
            )
        paintGradientLine.shader = mLinearGradient

        if (ySpacing > 0) {
            canvas.drawLine(xSlider, ySpacing, xSlider, ySpacing * 6, paintGradientLine)
        }
    }

    private var onDaySelectListener: ((index: Int, spo2: SpO2ItemDTO?) -> Unit)? = null

    fun setOnDaySelectListener(l: ((index: Int, spo2: SpO2ItemDTO?) -> Unit)): SpO2BarChart {
        this.onDaySelectListener = l
        return this
    }

    private var onXTextSelectListener: ((index: Int, xText: String) -> Unit)? = null

    fun setOnXTextSelectListener(l: ((index: Int, xText: String) -> Unit)): SpO2BarChart {
        this.onXTextSelectListener = l
        return this
    }


    fun adjustAlpha(color: Int, alpha: Int = 153): Int {
        return Color.argb(alpha, color.red, color.green, color.blue)
    }

    fun getXData(list: ArrayList<SpO2ItemDTO>, step: Int): ArrayList<String> {
        var dataSet = ArrayList<String>()
        list.forEachIndexed() { index, item ->
            if (index % step == 0) {
                dataSet.add(getDate(item.startTime))
            } else
                dataSet.add("")
        }
        return dataSet
    }

    fun getDate(ori: String): String {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        val dateTime = LocalDateTime.parse(ori, formatter)
        return String.format("%02d", dateTime.dayOfMonth)
    }

}