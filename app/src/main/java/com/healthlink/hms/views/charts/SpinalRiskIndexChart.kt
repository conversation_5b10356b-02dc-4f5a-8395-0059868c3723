package com.healthlink.hms.views.charts

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.healthlink.hms.R
import kotlin.math.abs

/**
 * 脊柱风险指数数据点
 * @param week 周数 (1-7)
 * @param riskIndex 风险指数 (0-100)
 * @param hasSpecialEvent 是否有特殊事件（如久坐）
 * @param eventLabel 事件标签
 */
data class SpinalRiskDataPoint(
    val week: Int,
    val riskIndex: Float,
    val hasSpecialEvent: Boolean = false,
    val eventLabel: String = ""
)

/**
 * 脊柱风险指数图表配置
 * @param showGrid 是否显示网格
 * @param showFill 是否显示填充区域
 * @param enableInteraction 是否启用交互
 */
data class SpinalRiskChartConfig(
    val showGrid: Boolean = true,
    val showFill: Boolean = true,
    val enableInteraction: Boolean = true
)

/**
 * 脊柱风险指数图表组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SpinalRiskIndexChart(
    modifier: Modifier = Modifier,
    dataPoints: List<SpinalRiskDataPoint> = generateSampleRiskData(),
    config: SpinalRiskChartConfig = SpinalRiskChartConfig(),
    onWeekClick: ((Int) -> Unit)? = null
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF8F9FA)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(20.dp)
        ) {
            // 标题栏
            SpinalRiskTitleSection()
            
            Spacer(modifier = Modifier.height(20.dp))
            
            // 图表主体
            SpinalRiskChartArea(
                dataPoints = dataPoints,
                config = config,
                onWeekClick = onWeekClick,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(280.dp)
            )
        }
    }
}

@Composable
private fun SpinalRiskTitleSection() {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标（绿色图标）
            Icon(
                painter = painterResource(id = R.drawable.icon_seat_chest), // 使用现有图标
                contentDescription = "脊柱风险",
                tint = Color(0xFF4CAF50),
                modifier = Modifier.size(28.dp)
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Text(
                text = "脊柱风险指数",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF333333)
            )
        }
        
        Text(
            text = "2025年",
            fontSize = 20.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF666666)
        )
    }
    
    // 单位标识
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Start
    ) {
        Spacer(modifier = Modifier.width(40.dp)) // 对齐图标位置
        Text(
            text = "单位：分",
            fontSize = 16.sp,
            color = Color(0xFF888888)
        )
    }
}

@Composable
private fun SpinalRiskChartArea(
    dataPoints: List<SpinalRiskDataPoint>,
    config: SpinalRiskChartConfig,
    onWeekClick: ((Int) -> Unit)?,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
    ) {
        // 图表区域
        Column(
            modifier = Modifier.weight(1f)
        ) {
            // 使用Box叠加图表和特殊事件标签
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
            ) {
                Canvas(
                    modifier = Modifier
                        .fillMaxSize()
                        .clickable(enabled = config.enableInteraction) {
                            // 处理点击事件
                        }
                ) {
                    drawSpinalRiskChart(dataPoints, config, size.width)
                }
                
                // 特殊事件标签叠加层
                SpinalRiskEventLabels(
                    dataPoints = dataPoints,
                    modifier = Modifier.fillMaxSize()
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // X轴标签 - 与数据点精确对齐
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                dataPoints.forEach { dataPoint ->
                    val week = dataPoint.week
                    val hasPlayButton = week == 4 // 第四周有播放按钮
                    
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.clickable(enabled = config.enableInteraction) {
                            onWeekClick?.invoke(week)
                        }
                    ) {
                        Text(
                            text = "第${week}周",
                            fontSize = 14.sp,
                            color = Color(0xFF666666),
                            textAlign = TextAlign.Center
                        )
                        
                        if (hasPlayButton) {
                            Spacer(modifier = Modifier.height(4.dp))
                            // 播放按钮
                            Box(
                                modifier = Modifier
                                    .size(24.dp)
                                    .clip(RoundedCornerShape(12.dp))
                                    .background(Color(0xFF4CAF50))
                                    .clickable {
                                        onWeekClick?.invoke(week)
                                    },
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    painter = painterResource(id = android.R.drawable.ic_media_play),
                                    contentDescription = "播放",
                                    tint = Color.White,
                                    modifier = Modifier.size(12.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
        
        // Y轴标签现在绘制在图表区域内，无需额外占位空间
    }
}

private fun DrawScope.drawSpinalRiskChart(
    dataPoints: List<SpinalRiskDataPoint>,
    config: SpinalRiskChartConfig,
    totalWidth: Float
) {
    val chartWidth = size.width
    val chartHeight = size.height
    
    // 网格线和Y轴标签
    if (config.showGrid) {
        drawGridLinesWithYAxisLabels(chartWidth, chartHeight, totalWidth)
    }
    
    // 数据处理
    val maxValue = 100f
    val minValue = 20f
    val valueRange = maxValue - minValue
    
    // 计算数据点坐标
    val points = dataPoints.mapIndexed { index, dataPoint ->
        val x = chartWidth * (index.toFloat() / (dataPoints.size - 1).toFloat())
        val normalizedValue = (dataPoint.riskIndex - minValue) / valueRange
        val y = chartHeight * (1f - normalizedValue)
        Offset(x, y)
    }
    
    if (points.isNotEmpty()) {
        // 绘制填充区域
        if (config.showFill) {
            drawFillArea(points, chartHeight)
        }
        
        // 绘制折线
        drawLineChart(points)
        
        // 绘制数据点
        drawDataPoints(points, dataPoints)
        
        // 特殊事件标注现在在Compose组件中绘制
    }
}

/**
 * 创建带箭头的气泡形状路径
 */
private fun createBubbleWithArrowPath(
    width: Float,
    height: Float,
    arrowWidth: Float,
    arrowHeight: Float,
    cornerRadius: Float
): Path {
    val path = Path()
    
    // 计算箭头的中心位置
    val arrowCenterX = width / 2f
    val bubbleHeight = height - arrowHeight
    
    // 从左上角开始绘制气泡主体（顺时针）
    path.moveTo(cornerRadius, 0f)
    
    // 上边
    path.lineTo(width - cornerRadius, 0f)
    path.arcTo(
        rect = androidx.compose.ui.geometry.Rect(
            width - cornerRadius * 2,
            0f,
            width,
            cornerRadius * 2
        ),
        startAngleDegrees = 270f,
        sweepAngleDegrees = 90f,
        forceMoveTo = false
    )
    
    // 右边
    path.lineTo(width, bubbleHeight - cornerRadius)
    path.arcTo(
        rect = androidx.compose.ui.geometry.Rect(
            width - cornerRadius * 2,
            bubbleHeight - cornerRadius * 2,
            width,
            bubbleHeight
        ),
        startAngleDegrees = 0f,
        sweepAngleDegrees = 90f,
        forceMoveTo = false
    )
    
    // 下边右侧（到箭头起始点）
    path.lineTo(arrowCenterX + arrowWidth / 2f, bubbleHeight)
    
    // 绘制向下的箭头
    path.lineTo(arrowCenterX, bubbleHeight + arrowHeight)
    path.lineTo(arrowCenterX - arrowWidth / 2f, bubbleHeight)
    
    // 下边左侧（从箭头结束点）
    path.lineTo(cornerRadius, bubbleHeight)
    path.arcTo(
        rect = androidx.compose.ui.geometry.Rect(
            0f,
            bubbleHeight - cornerRadius * 2,
            cornerRadius * 2,
            bubbleHeight
        ),
        startAngleDegrees = 90f,
        sweepAngleDegrees = 90f,
        forceMoveTo = false
    )
    
    // 左边
    path.lineTo(0f, cornerRadius)
    path.arcTo(
        rect = androidx.compose.ui.geometry.Rect(
            0f,
            0f,
            cornerRadius * 2,
            cornerRadius * 2
        ),
        startAngleDegrees = 180f,
        sweepAngleDegrees = 90f,
        forceMoveTo = false
    )
    
    path.close()
    return path
}

@Composable
private fun SpinalRiskEventLabels(
    dataPoints: List<SpinalRiskDataPoint>,
    modifier: Modifier = Modifier
) {
    BoxWithConstraints(modifier = modifier) {
        val chartWidth = maxWidth.value
        val chartHeight = maxHeight.value
        
        // 计算数据点位置（与Canvas中的计算保持一致）
        val maxValue = 100f
        val minValue = 20f
        val valueRange = maxValue - minValue
        
        dataPoints.forEachIndexed { index, dataPoint ->
            if (dataPoint.hasSpecialEvent && dataPoint.eventLabel.isNotEmpty()) {
                // 计算数据点在屏幕上的位置
                val x = chartWidth * (index.toFloat() / (dataPoints.size - 1).toFloat())
                val normalizedValue = (dataPoint.riskIndex - minValue) / valueRange
                val y = chartHeight * (1f - normalizedValue)
                
                // 气泡标签的尺寸
                val labelWidth = 50.dp
                val bubbleHeight = 28.dp
                val arrowHeight = 8.dp
                val totalHeight = bubbleHeight + arrowHeight
                val offsetY = totalHeight + 10.dp
                
                // 使用Canvas绘制整体的气泡形状
                Canvas(
                    modifier = Modifier
                        .offset(
                            x = x.dp - labelWidth / 2,
                            y = y.dp - offsetY
                        )
                        .size(labelWidth, totalHeight)
                ) {
                    val bubblePath = createBubbleWithArrowPath(
                        width = size.width,
                        height = size.height,
                        arrowWidth = 12.dp.toPx(),
                        arrowHeight = arrowHeight.toPx(),
                        cornerRadius = 8.dp.toPx()
                    )
                    
                    // 绘制白色填充
                    drawPath(
                        path = bubblePath,
                        color = Color.White
                    )
                    
                    // 绘制红色边框
                    drawPath(
                        path = bubblePath,
                        color = Color(0xFFFF4444),
                        style = Stroke(width = 1.5.dp.toPx())
                    )
                }
                
                // 在气泡上方绘制文本
                Box(
                    modifier = Modifier
                        .offset(
                            x = x.dp - labelWidth / 2,
                            y = y.dp - offsetY
                        )
                        .size(labelWidth, bubbleHeight),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = dataPoint.eventLabel,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFFFF4444),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}

private fun DrawScope.drawGridLinesWithYAxisLabels(chartWidth: Float, chartHeight: Float, totalWidth: Float) {
    val gridColor = Color(0xFFE0E0E0)
    val strokeWidth = 1.dp.toPx()
    val labelColor = Color(0xFF888888)
    val labelFontSize = 14.sp
    
    // Y轴刻度值 (从上到下: 100, 80, 60, 40, 20)
    val yAxisValues = listOf(100, 80, 60, 40, 20)
    
    // 绘制水平网格线和对应的Y轴标签
    for (i in 0..4) {
        val y = chartHeight * (i.toFloat() / 4f)
        
        // 绘制网格线
        drawLine(
            color = gridColor,
            start = Offset(0f, y),
            end = Offset(chartWidth, y),
            strokeWidth = strokeWidth
        )
        
        // 绘制Y轴标签 (在网格线上方且右对齐)
        val labelValue = yAxisValues[i]
        
        // 创建文本绘制配置
        drawContext.canvas.nativeCanvas.apply {
            val paint = android.graphics.Paint().apply {
                color = labelColor.toArgb()
                textSize = labelFontSize.toPx()
                isAntiAlias = true
                textAlign = android.graphics.Paint.Align.RIGHT // 设置右对齐
            }
            
            // 计算标签位置：图表区域右边缘（右对齐到网格线右端点）
            val labelX = chartWidth
            
            // 获取文本的基线偏移，使文本位于网格线上方
            val fontMetrics = paint.fontMetrics
            val textOffsetAbove = -fontMetrics.descent - 4.dp.toPx() // 网格线上方4dp
            
            // 绘制文本，右对齐到网格线右端点，位于网格线上方
            drawText(
                labelValue.toString(),
                labelX,
                y + textOffsetAbove,
                paint
            )
        }
    }
}

private fun DrawScope.drawFillArea(points: List<Offset>, chartHeight: Float) {
    if (points.isEmpty()) return
    
    // 创建渐变色画刷
    val gradientBrush = Brush.linearGradient(
        colors = listOf(
            Color(0x3307C160), // 顶部颜色 #3307C160
            Color(0x0007C160)  // 底部颜色 #0007C160
        ),
        start = Offset(0f, 0f),        // 渐变起始点（顶部）
        end = Offset(0f, chartHeight)   // 渐变结束点（底部）
    )
    
    // 创建填充路径，使用与折线相同的平滑曲线
    val fillPath = createSmoothFillPath(points, chartHeight)
    
    drawPath(
        path = fillPath,
        brush = gradientBrush
    )
}

/**
 * 创建平滑填充路径
 */
private fun createSmoothFillPath(points: List<Offset>, chartHeight: Float): Path {
    val path = Path()
    
    if (points.isEmpty()) return path
    
    // 起始点 - 左下角
    path.moveTo(points.first().x, chartHeight)
    
    // 连接到第一个数据点
    path.lineTo(points.first().x, points.first().y)
    
    // 绘制平滑曲线（与折线相同的算法）
    if (points.size == 1) {
        // 只有一个点，无需额外处理
    } else if (points.size == 2) {
        // 两个点，直线连接
        path.lineTo(points[1].x, points[1].y)
    } else {
        // 多个点，使用平滑曲线
        val smoothing = 0.25f
        
        for (i in 0 until points.size - 1) {
            val current = points[i]
            val next = points[i + 1]
            
            // 计算控制点（与createSmoothPath相同的逻辑）
            val controlPoint1: Offset
            val controlPoint2: Offset
            
            when (i) {
                0 -> {
                    val nextNext = if (i + 2 < points.size) points[i + 2] else next
                    controlPoint1 = Offset(
                        current.x + (next.x - current.x) * smoothing,
                        current.y + (next.y - current.y) * smoothing
                    )
                    controlPoint2 = Offset(
                        next.x - (nextNext.x - current.x) * smoothing,
                        next.y - (nextNext.y - current.y) * smoothing
                    )
                }
                points.size - 2 -> {
                    val prev = points[i - 1]
                    controlPoint1 = Offset(
                        current.x + (next.x - prev.x) * smoothing,
                        current.y + (next.y - prev.y) * smoothing
                    )
                    controlPoint2 = Offset(
                        next.x - (next.x - current.x) * smoothing,
                        next.y - (next.y - current.y) * smoothing
                    )
                }
                else -> {
                    val prev = points[i - 1]
                    val nextNext = points[i + 2]
                    controlPoint1 = Offset(
                        current.x + (next.x - prev.x) * smoothing,
                        current.y + (next.y - prev.y) * smoothing
                    )
                    controlPoint2 = Offset(
                        next.x - (nextNext.x - current.x) * smoothing,
                        next.y - (nextNext.y - current.y) * smoothing
                    )
                }
            }
            
            // 绘制三次贝塞尔曲线
            path.cubicTo(
                controlPoint1.x, controlPoint1.y,
                controlPoint2.x, controlPoint2.y,
                next.x, next.y
            )
        }
    }
    
    // 从最后一个数据点连接到右下角
    path.lineTo(points.last().x, chartHeight)
    
    // 闭合路径
    path.close()
    
    return path
}

/**
 * 创建平滑曲线路径 - 使用更好的贝塞尔曲线算法
 */
private fun createSmoothPath(points: List<Offset>): Path {
    val path = Path()
    
    if (points.isEmpty()) return path
    if (points.size == 1) {
        path.moveTo(points[0].x, points[0].y)
        return path
    }
    
    // 移动到第一个点
    path.moveTo(points[0].x, points[0].y)
    
    if (points.size == 2) {
        // 只有两个点，直接连线
        path.lineTo(points[1].x, points[1].y)
        return path
    }
    
    // 使用三次贝塞尔曲线创建更平滑的连接
    // 计算控制点
    val smoothing = 0.25f // 平滑因子，控制曲线的弯曲程度
    
    for (i in 0 until points.size - 1) {
        val current = points[i]
        val next = points[i + 1]
        
        // 计算控制点
        val controlPoint1: Offset
        val controlPoint2: Offset
        
        when (i) {
            0 -> {
                // 第一段
                val nextNext = if (i + 2 < points.size) points[i + 2] else next
                controlPoint1 = Offset(
                    current.x + (next.x - current.x) * smoothing,
                    current.y + (next.y - current.y) * smoothing
                )
                controlPoint2 = Offset(
                    next.x - (nextNext.x - current.x) * smoothing,
                    next.y - (nextNext.y - current.y) * smoothing
                )
            }
            points.size - 2 -> {
                // 最后一段
                val prev = points[i - 1]
                controlPoint1 = Offset(
                    current.x + (next.x - prev.x) * smoothing,
                    current.y + (next.y - prev.y) * smoothing
                )
                controlPoint2 = Offset(
                    next.x - (next.x - current.x) * smoothing,
                    next.y - (next.y - current.y) * smoothing
                )
            }
            else -> {
                // 中间段
                val prev = points[i - 1]
                val nextNext = points[i + 2]
                controlPoint1 = Offset(
                    current.x + (next.x - prev.x) * smoothing,
                    current.y + (next.y - prev.y) * smoothing
                )
                controlPoint2 = Offset(
                    next.x - (nextNext.x - current.x) * smoothing,
                    next.y - (nextNext.y - current.y) * smoothing
                )
            }
        }
        
        // 绘制三次贝塞尔曲线
        path.cubicTo(
            controlPoint1.x, controlPoint1.y,
            controlPoint2.x, controlPoint2.y,
            next.x, next.y
        )
    }
    
    return path
}

private fun DrawScope.drawLineChart(points: List<Offset>) {
    val lineColor = Color(0xFF4CAF50)
    val strokeWidth = 3.dp.toPx()
    
    if (points.size < 2) return
    
    // 创建平滑曲线路径
    val smoothPath = createSmoothPath(points)
    
    // 绘制平滑曲线
    drawPath(
        path = smoothPath,
        color = lineColor,
        style = Stroke(
            width = strokeWidth,
            cap = StrokeCap.Round,
            join = StrokeJoin.Round
        )
    )
}

private fun DrawScope.drawDataPoints(points: List<Offset>, dataPoints: List<SpinalRiskDataPoint>) {
    val greenColor = Color(0xFF4CAF50)  // 绿色外环
    val whiteColor = Color.White        // 白色内环
    val outerRadius = 8.dp.toPx()       // 外环半径
    val innerRadius = 5.dp.toPx()       // 内环半径
    
    points.forEachIndexed { index, point ->
        // 先绘制绿色外环
        drawCircle(
            color = greenColor,
            radius = outerRadius,
            center = point
        )
        
        // 再绘制白色内环
        drawCircle(
            color = whiteColor,
            radius = innerRadius,
            center = point
        )
        
        // 特殊点不需要额外的圆环标记，样式统一
    }
}



/**
 * 生成示例脊柱风险数据
 */
fun generateSampleRiskData(): List<SpinalRiskDataPoint> {
    return listOf(
        SpinalRiskDataPoint(week = 1, riskIndex = 60f),
        SpinalRiskDataPoint(week = 2, riskIndex = 50f),
        SpinalRiskDataPoint(week = 3, riskIndex = 80f, hasSpecialEvent = true, eventLabel = "久坐"),
        SpinalRiskDataPoint(week = 4, riskIndex = 50f),
        SpinalRiskDataPoint(week = 5, riskIndex = 70f, hasSpecialEvent = true, eventLabel = "需复查"),
        SpinalRiskDataPoint(week = 6, riskIndex = 45f),
        SpinalRiskDataPoint(week = 7, riskIndex = 75f)
    )
}

@Preview(showBackground = true, name = "脊柱风险指数图表")
@Composable
fun SpinalRiskIndexChartPreview() {
    SpinalRiskIndexChart(
        modifier = Modifier
            .fillMaxWidth()
            .height(400.dp),
        onWeekClick = { week ->
            println("点击了第${week}周")
        }
    )
}

@Preview(showBackground = true, name = "无交互图表")
@Composable
fun SpinalRiskIndexChartNoInteractionPreview() {
    SpinalRiskIndexChart(
        modifier = Modifier
            .fillMaxWidth()
            .height(400.dp),
        config = SpinalRiskChartConfig(enableInteraction = false)
    )
}

@Preview(showBackground = true, name = "渐变填充效果预览")
@Composable
fun SpinalRiskIndexChartGradientPreview() {
    Column(
        modifier = Modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "带渐变填充的脊柱风险指数图表",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold
        )
        
        SpinalRiskIndexChart(
            modifier = Modifier
                .fillMaxWidth()
                .height(350.dp),
            dataPoints = generateSampleRiskData(),
            config = SpinalRiskChartConfig(
                showGrid = true,
                showFill = true,
                enableInteraction = true
            )
        )
        
        Text(
            text = "渐变色说明：从顶部的较深绿色(#3307C160)渐变到底部的较浅绿色(#0007C160)",
            fontSize = 12.sp,
            color = Color(0xFF666666)
        )
    }
}

@Preview(showBackground = true, name = "精确对齐预览")
@Composable
fun SpinalRiskIndexChartAlignmentPreview() {
    Column(
        modifier = Modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "X轴标签与数据点精确垂直对齐",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF333333)
        )
        
        SpinalRiskIndexChart(
            modifier = Modifier
                .fillMaxWidth()
                .height(380.dp),
            dataPoints = generateSampleRiskData(),
            config = SpinalRiskChartConfig(
                showGrid = true,
                showFill = true,
                enableInteraction = true
            )
        )
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFF0F8FF))
        ) {
            Column(modifier = Modifier.padding(12.dp)) {
                Text(
                    text = "对齐说明",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333)
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "• X轴标签现在与对应的数据点完全垂直对齐\n• 垂直网格线也与数据点位置一致\n• 布局使用Arrangement.SpaceBetween确保精确分布",
                    fontSize = 12.sp,
                    color = Color(0xFF666666),
                    lineHeight = 16.sp
                )
            }
        }
    }
} 