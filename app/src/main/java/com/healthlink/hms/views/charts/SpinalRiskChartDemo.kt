package com.healthlink.hms.views.charts

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 脊柱风险指数图表演示页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SpinalRiskChartDemo(
    modifier: Modifier = Modifier
) {
    var selectedWeek by remember { mutableStateOf<Int?>(null) }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        
        // 页面标题
        Text(
            text = "脊柱风险指数图表演示",
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF333333)
        )
        
        // 主图表
        SpinalRiskIndexChart(
            modifier = Modifier
                .fillMaxWidth()
                .height(400.dp),
            dataPoints = generateSampleRiskData(),
            onWeekClick = { week ->
                selectedWeek = week
            }
        )
        
        // 选中周的信息显示
        selectedWeek?.let { week ->
            val dataPoint = generateSampleRiskData().find { it.week == week }
            dataPoint?.let { point ->
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFF0F8FF)
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "第${week}周详情",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF333333)
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = "风险指数: ${point.riskIndex.toInt()}分",
                            fontSize = 16.sp,
                            color = Color(0xFF666666)
                        )
                        
                        if (point.hasSpecialEvent) {
                            Text(
                                text = "特殊事件: ${point.eventLabel}",
                                fontSize = 16.sp,
                                color = Color(0xFFFF9800)
                            )
                        }
                        
                        Text(
                            text = "风险等级: ${getRiskLevel(point.riskIndex)}",
                            fontSize = 16.sp,
                            color = getRiskColor(point.riskIndex)
                        )
                    }
                }
            }
        }
        
        // 不同配置的图表展示
        Text(
            text = "不同配置演示",
            fontSize = 20.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF333333)
        )
        
        // 无网格线的图表
        SpinalRiskIndexChart(
            modifier = Modifier
                .fillMaxWidth()
                .height(300.dp),
            config = SpinalRiskChartConfig(
                showGrid = false,
                showFill = true,
                enableInteraction = false
            )
        )
        
        // 无填充的图表
        SpinalRiskIndexChart(
            modifier = Modifier
                .fillMaxWidth()
                .height(300.dp),
            config = SpinalRiskChartConfig(
                showGrid = true,
                showFill = false,
                enableInteraction = false
            )
        )
        
        // 使用说明
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "使用说明",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                val usageText = """
                    1. 点击数据点或X轴标签可查看详细信息
                    2. 第四周有播放按钮，支持特殊交互
                    3. 橙色标注表示特殊事件（如久坐）
                    4. 绿色区域表示风险指数变化趋势
                    5. 支持自定义配置：网格线、填充、交互等
                """.trimIndent()
                
                Text(
                    text = usageText,
                    fontSize = 14.sp,
                    color = Color(0xFF666666),
                    lineHeight = 20.sp
                )
            }
        }
    }
}

/**
 * 获取风险等级描述
 */
private fun getRiskLevel(riskIndex: Float): String {
    return when {
        riskIndex >= 80 -> "高风险"
        riskIndex >= 60 -> "中风险"
        riskIndex >= 40 -> "低风险"
        else -> "正常"
    }
}

/**
 * 获取风险等级颜色
 */
private fun getRiskColor(riskIndex: Float): Color {
    return when {
        riskIndex >= 80 -> Color(0xFFFF3333)
        riskIndex >= 60 -> Color(0xFFFF9800)
        riskIndex >= 40 -> Color(0xFFFFC107)
        else -> Color(0xFF4CAF50)
    }
}

@Preview(showSystemUi = true)
@Composable
fun SpinalRiskChartDemoPreview() {
    SpinalRiskChartDemo()
} 