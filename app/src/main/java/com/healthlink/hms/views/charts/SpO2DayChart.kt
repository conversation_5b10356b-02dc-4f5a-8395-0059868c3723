package com.healthlink.hms.views.charts

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PathMeasure
import android.graphics.Point
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.graphics.Typeface
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import androidx.core.graphics.blue
import androidx.core.graphics.green
import androidx.core.graphics.red
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.R
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.ktExt.sp
import com.healthlink.hms.server.data.dto.SpO2ItemDTO
import com.healthlink.hms.utils.ChartTouchEventDelegate
import com.healthlink.hms.utils.ViewDrawUtils
import kotlin.math.abs

/**
 *@Author: 付仁秀
 *@Description：血氧日表图
 **/
class SpO2DayChart(context: Context, attrs: AttributeSet?) : View(context, attrs) {


    //屏幕宽高
    private var scrWidth = 0f
    private var scrHeight = 0f
    private var perDp = 0f //1dp所占的值
    private var xData: Array<String> = arrayOf("00:00", "06:00", "12:00", "18:00", "24:00")
    private var yDataLeft: Array<String> = arrayOf("100米", "50米", "0米")
    private var yDataRight: Array<String> = arrayOf("100%", "85%", "70%")
    private var altiValue: ArrayList<Int>? = arrayListOf()
    private var alMax = 0 //海拔最大值
    private var alYc = 0 //每个yspace海拔数
    private var yType = 0
    private var w = 0
    private var h = 0
    private var oldw = 0
    private var oldh = 0

    private var spo2Data = mutableListOf<SpO2ItemDTO>()
    private lateinit var paintLine: Paint // y轴线
    private lateinit var paintGradientLine: Paint //指示渐变线
    private lateinit var paintXText: Paint // x轴坐标
    private lateinit var paintYText: Paint // y轴坐标
    private lateinit var paintPolyline: Paint // 海拔折线
    private lateinit var paintPolyShadow: Paint // 海拔折线阴影
    private lateinit var paintPillar: Paint //柱子
    private lateinit var paintRound: Paint  // 指示滑块圆
    private lateinit var paintBessel: Paint  // 滑块底部
    private lateinit var paintAlLine: Paint  // 滑块底部
    private lateinit var paintAlCircle: Paint

    private var xSlider = 0f //滑块的x轴位置
    private var mLinePath: Path  //折线路径
    private val curveCircleRadius = 22f.dp
    private var mPath: Path  //滑块贝塞尔

    //第一条曲线的坐标
    private val mFirstCurveStartPoint = Point()
    private val mFirstCurveEndPoint = Point()
    private val mFirstCurveControlPoint1 = Point()
    private val mFirstCurveControlPoint2 = Point()

    //第二条曲线的坐标
    private var mSecondCurveStartPoint = Point()
    private val mSecondCurveEndPoint = Point()
    private val mSecondCurveControlPoint1 = Point()
    private val mSecondCurveControlPoint2 = Point()

    private var restHeart = 0 //静息值

    private val alInnerRadius = 6f.dp //海拔内圆半径
    private val alouterRadius = 10f.dp //海拔外圆半径
    private val alInnerColor = resources.getColor(R.color.al_inner_circle) //海拔内圆颜色
    private val alouterColor = resources.getColor(R.color.al_outer_circle) //海拔外圆颜色
    init {
        setLayerType(LAYER_TYPE_SOFTWARE, null)
        mLinePath = Path()
        mPath = Path()
        initPaint()
    }

    /**
     * 初始化画笔
     */
    private fun initPaint() {
        // Y轴刻度线
        paintLine = Paint()
        paintLine.style = Paint.Style.STROKE
        paintLine.strokeWidth = 1f
        paintLine.color = resources.getColor(R.color.color_y_line)

        paintAlLine = Paint()
        paintAlLine.style = Paint.Style.STROKE
        paintAlLine.strokeWidth = 3f.dp
        paintAlLine.strokeJoin = Paint.Join.ROUND
        paintAlLine.color = resources.getColor(R.color.card_altitude_line)

        // 指示渐变线
        paintGradientLine = Paint()
        paintGradientLine.style = Paint.Style.STROKE
        paintGradientLine.strokeWidth = 2f

        // 折现阴影
        paintPolyShadow = Paint()
        paintPolyShadow.style = Paint.Style.FILL

        // X轴上文字
        paintXText = Paint()
        paintXText.isAntiAlias = true
        paintXText.strokeWidth = 1f
        paintXText.textSize = 22f.sp
        paintXText.textAlign = Paint.Align.CENTER
        paintXText.color =
            resources.getColor(R.color.text_color_fc_40);//context.colorCompat(R.color.color_on_surface)

        // Y轴上文字
        paintYText = Paint()
        paintYText.isAntiAlias = true
        paintYText.textSize = 22f.sp
        paintYText.strokeWidth = 1f
        paintYText.textAlign = Paint.Align.RIGHT
        paintYText.color =
            resources.getColor(R.color.text_color_fc_40)//resources.getColor(R.color.c_666666_808080);//context.colorCompat(R.color.secondary_666666_808080)

        // 柱状图
        paintPillar = Paint()
        paintPillar.style = Paint.Style.FILL
        paintPillar.isAntiAlias = true
        paintPillar.color = resources.getColor(R.color.heart_month_poly_line)

        // 海拔折线
        paintPolyline = Paint()
        paintPolyline.style = Paint.Style.FILL
        paintPolyline.strokeWidth = 4f
        paintPolyline.isAntiAlias = true
        paintPolyline.color =
            resources.getColor(R.color.heart_poly_line);//context.colorCompat(R.color.fc355c_fc3159)

        // 指示滑块圆
        paintRound = Paint()
        paintRound.style = Paint.Style.FILL
        paintRound.isAntiAlias = true
        paintRound.color =
            resources.getColor(R.color.slider_round);//context.colorCompat(R.color.ffffff_6e6e6e)

        // 指示滑块圆
        paintAlCircle = Paint()
        paintAlCircle.style = Paint.Style.FILL
        paintAlCircle.isAntiAlias = true

        // 滑块底部 通过shader设置渐变色
        paintBessel = Paint()
        paintBessel.style = Paint.Style.FILL
        paintBessel.isAntiAlias = true
        paintBessel.color =
            resources.getColor(R.color.slider_round_bessel_bg)
    }


    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        scrWidth = width.toFloat()
        scrHeight = height.toFloat()
        ySpacing = scrHeight / 8f //y轴分8份
        perDp = scrHeight / 450
        // 底部圆滑块可以滑动的范围
        xWithStart =
            margin + paintXText.measureText(xData[0]) / 2 + paintXText.measureText(yDataLeft[0])
        xWithEnd = scrWidth - margin - paintYText.measureText(yDataRight[0]) * 1.5f
        xTextSpacing = (xWithEnd - xWithStart) / (xData.size - 1)
        xSpacing = (xWithEnd - xWithStart) / 48
        //最后一条数据不为0的
        if (!spo2Data.isNullOrEmpty()) {
            var lastIndex = 0
            for (i in spo2Data.size - 1 downTo 0) {
                if (spo2Data[i].avg != 0) {
                    lastIndex = i
                    break;
                }

            }
            if (lastIndex > 0 || spo2Data[0].avg != 0) {
                xSlider = xWithStart + (xSpacing * lastIndex)
            }else{
                xSlider = scrWidth / 2
            }
        } else {
            xSlider = scrWidth / 2
        }

    }

    private val touchEventDelegate by lazy {
        ChartTouchEventDelegate(
            setSliderX = {
                xSlider = it
                invalidate()
            }
        )
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        return touchEventDelegate.onTouchEvent(
            event,
            this,
            xSlider,
            xSpacing,
            ySpacing,
            xWithStart,
            xWithEnd,
            spo2Data.size
        )
    }

    private val margin = 50f.dp//20f.dp //左右两边距离
    private var xWithStart = 0f //x轴的起始点
    private var xWithEnd = 0f  //x轴结束点
    private var ySpacing = 0f //高度分割份数后间距
    private var xSpacing = 0f //x轴柱子分割份数后间距
    private var xTextSpacing = 0f //x轴文字分割份数后间距

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // X轴背景渐变
        drawGradientBackgroundUnderXText(canvas)
        //画y轴方向横线与文字
        drawY(canvas)
        //折线
        drawPolyline(canvas)
        // 画血氧柱子
        drawPillar(canvas)
        //垂直渐变线
        drawGradientLine(canvas)
        //底部
        drawBessel(canvas)
        //画x轴方向文字
        drawX(canvas)
    }

    private fun drawGradientBackgroundUnderXText(canvas: Canvas) {
        // 画渐变颜色
        ViewDrawUtils.drawGradientBackgroundUnderXText(canvas,context,scrWidth,ySpacing,7)
    }

    private fun drawX(canvas: Canvas) {
        xData.forEachIndexed { index, s ->
            val x = xWithStart + xTextSpacing * index
            val dis = Math.abs(x - xSlider)
            var y = ySpacing * 7 - 45f
            if (dis < xTextSpacing / 2) {
                paintXText.typeface = Typeface.DEFAULT_BOLD
                y -= 30f * (1 - dis / xTextSpacing)
                paintXText.color = resources.getColor(R.color.text_color_fc_100)
                onXTextMoveListener?.invoke(index, s)
            } else {
                paintXText.typeface = Typeface.DEFAULT
                paintXText.color = resources.getColor(R.color.text_color_fc_40)

            }
            canvas.drawText(s, x, y, paintXText)
        }
    }

    private fun drawPolyline(canvas: Canvas) {
        paintAlLine.color = resources.getColor(R.color.card_altitude_line)
        paintAlLine.strokeWidth=3f.dp
        val lg = LinearGradient(
            scrWidth / 2,
            ySpacing,
            scrWidth / 2,
            ySpacing * 3,
            intArrayOf(
                resources.getColor(R.color.light_blue_60),
                resources.getColor(R.color.light_blue_0)
            ),
            null,
            Shader.TileMode.CLAMP
        )
        paintPolyShadow.shader = lg
        paintPolyShadow.style = Paint.Style.FILL
        if (altiValue.isNullOrEmpty()) return
        var path = Path()  //阴影所用path
        var linePath = Path()  //圆点所用
        var xS = xWithStart - 10f.dp
        var per = ySpacing / alYc
        var x = xWithStart - 10f.dp
        var y = 0f
        for (i in 0..altiValue!!.size - 1) {
            if (altiValue!![i] != null) {
                y = (alMax - altiValue!![i]!!) * per + ySpacing
                break
            }
        }
        var perPoint = Pair(x, y)
        path.moveTo(x, y)
        linePath.moveTo(x, y)
        if (altiValue!!.size == 1) {
            path.lineTo(x + 10f.dp, y)
            linePath.lineTo(x + 10f.dp, y)
            canvas.drawLine(x, y, x + 10f.dp, y, paintLine)
            x += 10f.dp
        } else {
            altiValue!!.forEachIndexed { index, item ->
                if (index == 0) return@forEachIndexed
                x = xWithStart + xSpacing * index
                if (item == null) {
                    y = perPoint.second
                } else {
                    y = (alMax - item) * per + ySpacing
                }
                path.lineTo(x, y)
                linePath.lineTo(x, y)
                canvas.drawLine(perPoint.first, perPoint.second, x, y, paintAlLine)
                perPoint = Pair(x, y)
            }
        }
        path.lineTo(x, ySpacing * 3)
        path.lineTo(xS, ySpacing * 3)
        path.lineTo(xS, (alMax - altiValue!![0]) * per + ySpacing)
        canvas.drawPath(path, paintPolyShadow)
        //判断xslider位置是否处于path横坐标集合
        if(xSlider in xS..x){
            val cX=xSlider
            val cY=getYForGivenX(linePath,cX,5f)  //precision越小 精度越高
            paintAlCircle.color=alouterColor
            canvas.drawCircle(cX,cY,alouterRadius,paintAlCircle)
            paintAlCircle.color=alInnerColor
            canvas.drawCircle(cX,cY,alInnerRadius,paintAlCircle)
        }
    }

    private fun drawY(canvas: Canvas) {
        val y = ySpacing * 5f - ySpacing * ((restHeart - 40) / 45f)
        for (i in 0..5) {
            if (i < 5)
                canvas.drawLine(
                    margin, ySpacing * (i + 1), scrWidth - margin,
                    ySpacing * (i + 1), paintLine
                )
            paintYText.color =
                resources.getColor(R.color.c_666666_808080);//context.colorCompat(R.color.secondary_666666_808080)

            if (i < 3) {
                canvas.drawText(
                    yDataLeft[i],
                    margin + paintYText.measureText(yDataLeft[i]),
                    ySpacing * (i + 1) - 10f,
                    paintYText
                )
            } else {
                canvas.drawText(
                    yDataRight[i - 3], scrWidth - margin, ySpacing * (i) - 10f, paintYText
                )
            }

        }

    }

    // 绘制滑块底部
    private fun drawBessel(canvas: Canvas) {
        // 第一条曲线开始点
        mFirstCurveStartPoint[(xSlider - curveCircleRadius * 3).toInt()] = (ySpacing * 7).toInt()
        // 第一条曲线结束点
        mFirstCurveEndPoint[xSlider.toInt()] =
            (ySpacing * 7 - curveCircleRadius - curveCircleRadius / 4).toInt()
        // 第二条开始点
        mSecondCurveStartPoint = mFirstCurveEndPoint
        mSecondCurveEndPoint[(xSlider + curveCircleRadius * 3).toInt()] = (ySpacing * 7).toInt()

        // 第一条控制点
        mFirstCurveControlPoint1[(mFirstCurveStartPoint.x + curveCircleRadius + curveCircleRadius / 4).toInt()] =
            mFirstCurveStartPoint.y
        mFirstCurveControlPoint2[(mFirstCurveEndPoint.x - curveCircleRadius * 2 + curveCircleRadius).toInt()] =
            mFirstCurveEndPoint.y
        // 第二条控制点
        mSecondCurveControlPoint1[(mSecondCurveStartPoint.x + curveCircleRadius * 2 - curveCircleRadius).toInt()] =
            mSecondCurveStartPoint.y
        mSecondCurveControlPoint2[(mSecondCurveEndPoint.x - curveCircleRadius - curveCircleRadius / 4).toInt()] =
            mSecondCurveEndPoint.y
        mPath.reset()
        mPath.moveTo(0f, ySpacing * 7)
        mPath.lineTo(mFirstCurveStartPoint.x.toFloat(), mFirstCurveStartPoint.y.toFloat())
        mPath.cubicTo(
            mFirstCurveControlPoint1.x.toFloat(), mFirstCurveControlPoint1.y.toFloat(),
            mFirstCurveControlPoint2.x.toFloat(), mFirstCurveControlPoint2.y.toFloat(),
            mFirstCurveEndPoint.x.toFloat(), mFirstCurveEndPoint.y.toFloat()
        )
        mPath.cubicTo(
            mSecondCurveControlPoint1.x.toFloat(), mSecondCurveControlPoint1.y.toFloat(),
            mSecondCurveControlPoint2.x.toFloat(), mSecondCurveControlPoint2.y.toFloat(),
            mSecondCurveEndPoint.x.toFloat(), mSecondCurveEndPoint.y.toFloat()
        )
        mPath.lineTo(scrWidth, ySpacing * 7)
        mPath.lineTo(scrWidth, scrHeight)
        mPath.lineTo(0f, scrHeight)
        mPath.close()

        canvas.drawPath(mPath, paintBessel)
        //底部滑块
        canvas.drawCircle(xSlider, ySpacing * 7 + 5f, curveCircleRadius, paintRound)
    }

    private fun drawPillar(canvas: Canvas) {
        //画柱子
        for (index in 0..47) {
            val isSelected =
                xSlider < xWithStart + xSpacing * index + xSpacing / 2 && xSlider > xWithStart + xSpacing * index - xSpacing / 2
            if (index < spo2Data.size && spo2Data[index].avg != 0) {
                val item = spo2Data[index]
                var per = 0f
                if (item.avg in 0 until 70) {
                    paintPillar.color = resources.getColor(R.color.card_spo2_low)
                    per = ySpacing / 70f
                } else if (item.avg in 70 until 90) {
                    per = ySpacing / 30f
                    paintPillar.color = resources.getColor(R.color.card_spo2_middle)
                } else {
                    per = ySpacing / 30f
                    paintPillar.color = resources.getColor(R.color.card_spo2_high)
                }
                // val data = i.split("-")
                // 是否选中
                if (isSelected) {
                    onDaySelectListener?.invoke(index, item.avg)
                } else {
                    paintPillar.color = adjustAlpha(paintPillar.color)
                }
                var rect = RectF()
                val radius = 10f.dp // 圆角半径
                if (yType == 0) {
                    val pillerCurrLen = (ySpacing * 2) * item.avg / 100
                    rect = RectF(
                        xWithStart + xSpacing * index - 10f.dp,
                        ySpacing * 3f + (ySpacing * 2 - pillerCurrLen),
                        xWithStart + xSpacing * index + 10f.dp,
                        ySpacing * 5f
                    )
                } else {
                    if (item.avg in 1 until 70) {
                        rect = RectF(
                            xWithStart + xSpacing * index - 10f.dp,
                            ySpacing * 4f + (70 - item.avg) * per,
                            xWithStart + xSpacing * index + 10f.dp,
                            ySpacing * 5f
                        )
                    } else {
                        rect = RectF(
                            xWithStart + xSpacing * index - 10f.dp,
                            ySpacing * 3f + (100 - item.avg) * per,
                            xWithStart + xSpacing * index + 10f.dp,
                            ySpacing * 5f
                        )
                    }
                }
                val radii = floatArrayOf(
                    radius, radius, // 左上角
                    radius, radius, // 右上角
                    0f, 0f,         // 右下角
                    0f, 0f          // 左下角
                )
                val path = Path().apply {
                    addRoundRect(rect, radii, android.graphics.Path.Direction.CW)
                }
                canvas.drawPath(path, paintPillar)
            } else {
                if (isSelected)
                    onDaySelectListener?.invoke(index, null)
            }
        }


    }
    fun setValue(
        value: MutableList<SpO2ItemDTO>?,
        altiList: ArrayList<Int>?,
        yl: Pair<Int, Int>,
        type: Int
    ): SpO2DayChart {
        if (value!=null) spo2Data = value.toMutableList()
        altiValue = altiList
        alMax = yl.first
        yDataLeft = dealYLeftListStr(yl)
        yType = type
        if (type == 1)
            yDataRight = arrayOf("100%", "70%", "0%")
        if (type == -1)
            yDataRight = arrayOf("100%", "90%", "80%")
        onSizeChanged(w, h, oldw, oldh)
        postInvalidate()
        return this
    }


    private fun drawGradientLine(canvas: Canvas) {
        var mLinearGradient = LinearGradient(
            xSlider, ySpacing, xSlider, ySpacing * 6,
            intArrayOf(
                resources.getColor(R.color.indicator_line_start),
                resources.getColor(R.color.indicator_line_center),
                resources.getColor(R.color.indicator_line_start)
            ), null, Shader.TileMode.MIRROR
        )
        if (spo2Data.isNullOrEmpty())
            mLinearGradient = LinearGradient(
                xSlider, ySpacing, xSlider, ySpacing * 6,
                intArrayOf(
                    resources.getColor(R.color.indicator_grey_line_start),
                    resources.getColor(R.color.indicator_grey_line_center),
                    resources.getColor(R.color.indicator_grey_line_start)
                ), null, Shader.TileMode.MIRROR
            )
        paintGradientLine.shader = mLinearGradient

        if (ySpacing > 0) {
            canvas.drawLine(xSlider, ySpacing, xSlider, ySpacing * 6, paintGradientLine)
        }
    }

    private var onDaySelectListener: ((index: Int, heart: Int?) -> Unit)? = null

    fun setOnDaySelectListener(l: ((index: Int, heart: Int?) -> Unit)): SpO2DayChart {
        this.onDaySelectListener = l
        return this
    }

    fun setOnxTextSelectListener(l: ((index: Int, xText: String) -> Unit)): SpO2DayChart {
        this.onXTextMoveListener = l
        return this
    }

    private var onXTextMoveListener: ((index: Int, xText: String) -> Unit)? = null

    fun adjustAlpha(color: Int, alpha: Int = 127): Int {
        return Color.argb(alpha, color.red, color.green, color.blue)
    }

    fun dealYLeftListStr(pair: Pair<Int, Int>): Array<String> {
        this.alMax = pair.first
        if (pair.first == 0 && pair.second == 0) {
            return arrayOf("100米", "50米", "0米")
        } else {
            val middle = (pair.first + pair.second) / 2
            this.alYc = pair.first - middle
            return arrayOf("${pair.first}米", "${middle}米", "${pair.second}米")
        }

    }

    fun getYForGivenX(path: Path, targetX: Float, precision: Float): Float {
        val pathMeasure = PathMeasure(path, false)
        val length = pathMeasure.length

        val position = FloatArray(2)
        var low = 0f
        var high = length

        while (high - low > precision) {
            val mid = (low + high) / 2
            pathMeasure.getPosTan(mid, position, null)

            if (position[0] < targetX) {
                low = mid
            } else {
                high = mid
            }
        }

        pathMeasure.getPosTan(low, position, null)
        return position[1]
    }
}