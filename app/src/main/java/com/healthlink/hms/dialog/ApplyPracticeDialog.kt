package com.healthlink.hms.dialog

import android.content.res.Resources
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import com.healthlink.hms.databinding.DialogApplyPracticeBinding
import kotlin.math.min

class ApplyPracticeDialog : DialogFragment() {
    
    private var _binding: DialogApplyPracticeBinding? = null
    private val binding get() = _binding!!
    
    interface OnSubmitListener {
        fun onSubmit(name: String, phone: String, city: String, expectedTime: String)
    }
    
    private var onSubmitListener: OnSubmitListener? = null
    
    fun setOnSubmitListener(listener: OnSubmitListener) {
        onSubmitListener = listener
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 设置点击外部不关闭
//        isCancelable = false
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogApplyPracticeBinding.inflate(inflater, container, false)
        // 设置点击外部不关闭
//        dialog?.setCanceledOnTouchOutside(false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        binding.btnSubmit.setOnClickListener {
            val name = binding.etName.text.toString()
            val phone = binding.etPhone.text.toString()
            val city = binding.etCity.text.toString()
            val expectedTime = binding.etExpectedTime.text.toString()
            
            onSubmitListener?.onSubmit(name, phone, city, expectedTime)
            dismiss()
        }
    }
    
    override fun onStart() {
        super.onStart()
        dialog?.window?.let { window ->
            // 设置对话框宽高（单位：dp）
            val widthDp = 1280
            val heightDp = 640
            
//            // 获取屏幕宽度
//            val screenWidth = Resources.getSystem().displayMetrics.widthPixels
//            val screenHeight = Resources.getSystem().displayMetrics.heightPixels
//
//            // 将dp转换为px
//            val density = Resources.getSystem().displayMetrics.density
//            var widthPx = (widthDp * density).toInt()
//            var heightPx = (heightDp * density).toInt()
//
//            // 确保对话框不会超出屏幕
//            widthPx = min(widthPx, screenWidth)
//            heightPx = min(heightPx, screenHeight)
//
//            window.setLayout(widthPx, heightPx)
            window.setLayout(widthDp, heightDp)
            window.setBackgroundDrawableResource(android.R.color.transparent)
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
    
    companion object {
        const val TAG = "ApplyPracticeDialog"
        
        fun newInstance(): ApplyPracticeDialog {
            return ApplyPracticeDialog()
        }
    }
} 