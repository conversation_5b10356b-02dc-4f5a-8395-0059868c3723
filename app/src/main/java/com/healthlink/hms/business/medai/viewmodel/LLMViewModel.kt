package com.healthlink.hms.medchart.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.healthlink.hms.business.medai.viewmodel.MedAIChatController
import com.healthlink.hms.medchart.api.Message
import com.healthlink.hms.medchart.repository.LLMRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

class LLMViewModel (application: Application) : AndroidViewModel(application), MedAIChatController {
    private val llmRepository = LLMRepository(application.applicationContext)
    
    private val _responseText = MutableLiveData<String>()
    val responseText: LiveData<String> = _responseText
    
    private val _isLoading = MutableLiveData<Boolean>()
    override val isLoading: LiveData<Boolean> = _isLoading
    
    private val _errorMessage = MutableLiveData<String?>()
    override val errorMessage: LiveData<String?> = _errorMessage
    
    private val _loginStatus = MutableLiveData<Boolean>(false)
    override val loginStatus: LiveData<Boolean> = _loginStatus
    
    // 标记是否使用流式输出
    private val _isStreaming = MutableLiveData<Boolean>(false)
    override val isStreaming: LiveData<Boolean> = _isStreaming
    
    // 当前流式回复的内容
    private val _streamingContent = MutableLiveData<String>()
    val streamingContent: LiveData<String> = _streamingContent
    
    // 存储对话历史
    private val _chatHistory = MutableLiveData<List<ChatMessage>>(emptyList())
    override val chatHistory: LiveData<List<ChatMessage>> = _chatHistory
    
    // 转换成API所需的消息格式
    private val apiMessages = mutableListOf<Message>()
    
    // 登录函数，在应用启动时自动调用
    override fun login() {
        _isLoading.value = true
        _errorMessage.value = null
        
        viewModelScope.launch {
            llmRepository.login()
                .onSuccess { token ->
                    _loginStatus.value = true
                    _isLoading.value = false
                    
                    // 登录成功后添加欢迎消息
//                    addMessageToHistory(ChatMessage(
//                        role = "assistant",
//                        content = "你好！我是一个医疗AI助手，可以帮您解答医疗相关问题，请注意我的建议仅供参考，重要的医疗决策请咨询专业医生。有什么我可以帮助您的吗？",
//                        timestamp = System.currentTimeMillis()
//                    ))
                }
                .onFailure { exception ->
                    _errorMessage.value = "登录失败: ${exception.message ?: "未知错误"}"
                    _isLoading.value = false
                }
        }
    }

    override fun sendMessage(userMessage: String, useStreamingAPI: Boolean) {
        if (userMessage.isBlank() || _loginStatus.value != true) return
        
        // 添加用户消息到聊天历史
        addMessageToHistory(ChatMessage("user", userMessage))
        
        // 添加到API消息列表
        apiMessages.add(Message("user", userMessage))
        
        _isLoading.value = true
        _errorMessage.value = null
        
        if (useStreamingAPI) {
            sendStreamingMessage()
        } else {
            sendRegularMessage()
        }
    }
    
    private fun sendRegularMessage() {
        viewModelScope.launch {
            llmRepository.getChatCompletion(apiMessages.first().content)
                .onSuccess { response ->
                    _responseText.value = response
                    
                    // 添加AI响应到聊天历史和API消息列表
                    addMessageToHistory(ChatMessage("assistant", response))
                    apiMessages.add(Message("assistant", response))
                    
                    _isLoading.value = false
                }
                .onFailure { exception ->
                    _errorMessage.value = exception.message ?: "未知错误"
                    _isLoading.value = false
                }
        }
    }
    
    private fun sendStreamingMessage() {
        // 设置流式模式标记
        _isStreaming.value = true
        _isLoading.value = true
        
        // 创建一个空的助手回复，将在流式响应中不断更新
        val assistantMessage = ChatMessage("assistant", "")
        addMessageToHistory(assistantMessage)
        
        // 清空流式内容
        _streamingContent.value = ""
        
        // 收集流式响应
        llmRepository.getStreamingChatCompletion(apiMessages)
            .onEach { content ->
                // 更新当前的流式内容
                val currentContent = _streamingContent.value ?: ""
                val newContent = currentContent + content
                _streamingContent.value = newContent
                
                // 更新聊天历史中的消息
                updateLastAssistantMessage(newContent)
            }
            .onCompletion { cause -> 
                // 流式响应完成
                _isLoading.value = false
                _isStreaming.value = false
                
                // 完成后，将最终内容添加到API消息列表
                val finalContent = _streamingContent.value ?: ""
                if (finalContent.isNotEmpty()) {
                    apiMessages.add(Message("assistant", finalContent))
                } else if (cause != null) {
                    // 如果是由于异常导致的完成，且没有内容，则显示错误提示
                    updateLastAssistantMessage("抱歉，无法获取回复。请重试。")
                }
            }
            .catch { exception ->
                val errorMsg = "流式响应错误: ${exception.message ?: "未知错误"}"
                _errorMessage.value = errorMsg
                _isLoading.value = false
                _isStreaming.value = false
                
                // 在聊天记录中显示错误
                updateLastAssistantMessage("抱歉，出现错误: ${exception.message}")
            }
            .launchIn(viewModelScope)
    }
    
    private fun updateLastAssistantMessage(content: String) {
        val currentList = _chatHistory.value ?: return
        if (currentList.isEmpty()) return
        
        // 找到最后一个助手消息
        val lastIndex = currentList.lastIndex
        if (lastIndex >= 0 && currentList[lastIndex].role == "assistant") {
            val updatedList = currentList.toMutableList()
            updatedList[lastIndex] = currentList[lastIndex].copy(content = content)
            _chatHistory.value = updatedList
        }
    }
    
    private fun addMessageToHistory(message: ChatMessage) {
        val currentList = _chatHistory.value ?: emptyList()
        _chatHistory.value = currentList + message
    }
    
    fun clearChat() {
        _chatHistory.value = emptyList()
        apiMessages.clear()
    }
}

data class ChatMessage(
    val role: String, // "user" 或 "assistant"
    val content: String,
    val timestamp: Long = System.currentTimeMillis()
)