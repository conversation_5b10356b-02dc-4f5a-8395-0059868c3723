package com.healthlink.hms.business.medai.view

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.Toast
import androidx.appcompat.widget.PopupMenu
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.healthlink.hms.R
import com.healthlink.hms.activity.MainActivity
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.base.Constants
import com.healthlink.hms.business.medai.adapter.MedAIChatAdapter
import com.healthlink.hms.business.medai.viewmodel.MedAIChatController
import com.healthlink.hms.medchart.viewmodel.LLMViewModel
import com.petterp.floatingx.FloatingX
import com.healthlink.hms.business.medai.utils.SimulationPerformanceMonitor
import java.util.UUID

// 消息类型枚举
enum class MedAIChatViewType {
    WELCOME_AND_OPTIONS, // 欢迎消息和功能选项
    DIVIDER,             // 分隔线
    AI_MESSAGE,          // AI消息
    USER_MESSAGE,        // 新增：用户消息
    HEALTH_DATA_CARD,    // 健康数据卡片
    QUICK_OPTIONS        // 快捷选项
}

// 消息数据模型
sealed class MedAIChatItem {
    // 欢迎消息和功能选项
    data class WelcomeAndOptions(
        val welcomeMessage: String,
        val introMessage: String,
        val options: List<Option>
    ) : MedAIChatItem() {
        data class Option(val title: String, val iconResId: Int?)
    }

    object Divider : MedAIChatItem()

    data class AIMessage(val message: String) : MedAIChatItem()

    // 新增：用户消息
    data class UserMessage(val message: String) : MedAIChatItem()

    data class HealthDataCard(
        val title: String,
        val healthItems: List<HealthItem>
    ) : MedAIChatItem() {
        data class HealthItem(
            val iconResId: Int,
            val name: String,
            val value: String,
            val unit: String,
            val status: String,
            val isAbnormal: Boolean
        )
    }

    data class QuickOptions(val options: List<String>) : MedAIChatItem()
}

// 模拟对话消息数据结构
data class SimulatedMessage(
    val id: String,
    val role: String, // "user" 或 "assistant"
    val content: String,
    val delay: Long = 2000L, // 消息显示延迟时间（毫秒）
    val typingSpeed: Long = 50L // 打字机效果速度（毫秒/字符）
)

class MedAIChatView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    companion object {
        const val TAG = "MedAIChatView"
        var fromType = Constants.FROM_TYPE_CARD_OPEN_CHAT // 默认是卡片 toast 弹窗
    }

    // 适配器
    private val chatAdapter = MedAIChatAdapter()
    private var controller: MedAIChatController? = null
//    private lateinit var llmChatViewModel: LLMViewModel

    // RecyclerView引用
    private lateinit var recyclerView: RecyclerView
    private lateinit var inputEditText: EditText
    private lateinit var sendButton: ImageView
    private lateinit var moreOptionsButton: ImageView

    // 模拟对话状态管理
    private var isSimulationRunning = false
    private var currentMessageIndex = 0
    private var simulationHandler: Handler? = null
    private var typingHandler: Handler? = null
    private var isUserScrolling = false
    private var simulationPaused = false
    private var autoScrollPaused = false  // 自动滚动是否被暂停
    
    // 错误处理和性能优化相关
    private var simulationStartTime: Long = 0
    private var lastErrorTime: Long = 0
    private var errorCount: Int = 0
    private val maxRetryCount = 3
    private val errorCooldownTime = 5000L // 5秒错误冷却时间
    private var isDestroyed = false
    private var realChatBackup: List<MedAIChatItem>? = null
    
    // 性能监控器
    private val performanceMonitor = SimulationPerformanceMonitor()
    private var currentSessionId: String? = null
    
    // 预设模拟对话数据 - 按照需求文档中的扁桃体发炎咨询场景
    private val simulatedConversation = listOf(
        SimulatedMessage(
            id = "sim_1",
            role = "user",
            content = "Hi，小医",
            delay = 1000L
        ),
        SimulatedMessage(
            id = "sim_2",
            role = "assistant",
            content = "我在，请问有什么可以帮您的？",
            delay = 1000L
        ),
        SimulatedMessage(
            id = "sim_3",
            role = "user",
            content = "扁桃体发炎了怎么办？",
            delay = 1000L
        ),
        SimulatedMessage(
            id = "sim_4",
            role = "assistant",
            content = "嗯，扁桃体发炎确实会让人很不舒服。请问您喉咙痛得厉害吗？有没有发烧或者其他症状？这样我们可以更好地判断情况。",
            delay = 1000L
        ),
        SimulatedMessage(
            id = "sim_5",
            role = "user",
            content = "有点发烧",
            delay = 1000L
        ),
        SimulatedMessage(
            id = "sim_6",
            role = "assistant",
            content = "明白了，发烧确实会让您更难受。请问体温大概多少度？发烧持续多长时间了？同时建议您多喝温水，可以先用温盐水漱口缓解喉咙不适。",
            delay = 1000L
        ),
        SimulatedMessage(
            id = "sim_7",
            role = "user",
            content = "39度",
            delay = 1000L
        ),
        SimulatedMessage(
            id = "sim_8",
            role = "assistant",
            content = "39度属于高热了，需要特别注意。建议您立即采取以下措施：用温水擦拭额头、腋下等部位。 可以服用布洛芬或对乙酰氨基酚，但一定要按照说明书剂量服用。 建议您尽快到附近医院就诊，可能需要血常规检查判断感染类型。如果出现呼吸困难、意识模糊等情况，请立即前往急诊。您现在感觉怎么样？需要我帮您导航附近医院么？",
            delay = 1000L
        ),
        SimulatedMessage(
            id = "sim_9",
            role = "user",
            content = "我扁桃体发炎吃什么药啊",
            delay = 1000L
        ),
        SimulatedMessage(
            id = "sim_10",
            role = "assistant",
            content = "您的情况需要根据具体症状来用药。建议您：可以服用布洛芬或对乙酰氨基酚，既能退烧又能缓解喉咙疼痛。如果是细菌感染引起的扁桃体炎，可能需要抗生素治疗，但必须由医生开具处方。含片或喷雾剂可以暂时缓解症状，比如西瓜霜含片或碘甘油喷雾。 重要提醒：**39度高热建议尽快就医，医生会根据检查结果开具最适合的药物。您现在能去医院吗？需要我帮您导航附近医院么？",
            delay = 1000L
        ),
        SimulatedMessage(
            id = "sim_11",
            role = "user",
            content = "帮我导航到附近的医院吧",
            delay = 1000L
        ),
        SimulatedMessage(
            id = "sim_12",
            role = "assistant",
            content = "好的，已经帮您导航到附近的医院，祝您早日康复，如果有什么问题随时叫我，我一直都在。",
            delay = 1000L
        )
    )

    init {
        LayoutInflater.from(context).inflate(R.layout.view_ai_dialog, this, true)

        // 初始化Handler
        simulationHandler = Handler(Looper.getMainLooper())
        typingHandler = Handler(Looper.getMainLooper())

        // 先初始化UI
        initUI()

    }

    fun setController(lifecycleOwner: LifecycleOwner, controller: MedAIChatController) {
        this.controller = controller

        if (controller.loginStatus.value == false) {
            controller.login()
        }

        controller.chatHistory.observe(lifecycleOwner) { messages ->
            // 兼容性检查：只在非模拟状态下更新真实对话
            if (!isSimulationRunning) {
                if (chatAdapter.getItems().size > 1 && messages.isNotEmpty()) {
                    chatAdapter.submitList(messages)
                }

                if (messages.isNotEmpty()) {
                    recyclerView.smoothScrollToPosition(chatAdapter.itemCount - 1)
                }
            } else {
                // 模拟状态下，备份真实对话数据
                if (messages.isNotEmpty()) {
                    Log.d(TAG, "模拟状态下收到真实对话数据，已备份")
                }
            }
        }

        controller.isLoading.observe(lifecycleOwner) { isLoading ->
//            binding.progressIndicator.visibility = if (isLoading) View.VISIBLE else View.GONE
            sendButton.isEnabled = !isLoading
        }

        controller.errorMessage.observe(lifecycleOwner) { errorMessage ->
            errorMessage?.let {
                Toast.makeText(HmsApplication.appContext, it, Toast.LENGTH_LONG).show()
            }
        }

        controller.loginStatus.observe(lifecycleOwner) { isLoggedIn ->
            sendButton.isEnabled = isLoggedIn && controller.isLoading.value != true
            if (isLoggedIn) {
//                Toast.makeText(HmsApplication.appContext, "登录成功", Toast.LENGTH_SHORT).show()
            }
        }

        controller.isStreaming.observe(lifecycleOwner) { isStreaming ->
            // 当流式状态改变时可以更新UI
            if (isStreaming) {
                // 正在使用流式API
//                binding.progressIndicator.visibility = View.VISIBLE
            }
        }

    }

    private fun initUI() {
        val ivClose = findViewById<ImageView>(R.id.iv_close)
        ivClose.setOnClickListener {
            if (!FloatingX.isInstalled(TAG)) {
                return@setOnClickListener
            }
            FloatingX.control(TAG).cancel()
        }

        // 初始化RecyclerView
        initRecyclerView()

        // 设置输入框视图
        setupInputViews()

        // 加载初始消息
        loadInitialMessages()

        // 设置事件监听
        setupEventListeners()
    }

    private fun initRecyclerView() {
        // 替换NestedScrollView为RecyclerView
        val contentContainer = findViewById<FrameLayout>(R.id.ll_ai_chat_view_content)
        contentContainer.removeAllViews()

        recyclerView = RecyclerView(context).apply {
            layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
            layoutManager = LinearLayoutManager(context)
            adapter = chatAdapter
        }

        // 添加智能滚动监听器
        setupScrollListener()

        contentContainer.addView(recyclerView)
    }

    /**
     * 设置智能滚动监听器
     * 实现用户滚动检测和自动滚动暂停/恢复机制
     */
    private fun setupScrollListener() {
        recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            private var scrollStateChangeHandler = Handler(Looper.getMainLooper())
            private var scrollStateChangeRunnable: Runnable? = null
            
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                
                when (newState) {
                    RecyclerView.SCROLL_STATE_DRAGGING -> {
                        // 用户开始拖拽滚动
                        isUserScrolling = true
                        
                        // 如果正在模拟对话且用户向上滚动，暂停自动滚动
                        if (isSimulationRunning && !simulationPaused) {
                            // 检查是否向上滚动（不在底部）
                            if (!isAtBottom()) {
                                pauseAutoScroll()
                            }
                        }
                    }
                    
                    RecyclerView.SCROLL_STATE_SETTLING -> {
                        // 滚动正在减速停止
                        // 延迟检查最终位置
                        scheduleScrollStateCheck()
                    }
                    
                    RecyclerView.SCROLL_STATE_IDLE -> {
                        // 滚动完全停止
                        scheduleScrollStateCheck()
                    }
                }
            }
            
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                
                // 如果用户滚动到底部，恢复自动滚动
                if (isUserScrolling && isAtBottom() && isSimulationRunning && autoScrollPaused) {
                    resumeAutoScroll()
                }
            }
            
            /**
             * 延迟检查滚动状态，避免频繁触发
             */
            private fun scheduleScrollStateCheck() {
                scrollStateChangeRunnable?.let { scrollStateChangeHandler.removeCallbacks(it) }
                
                scrollStateChangeRunnable = Runnable {
                    // 检查是否在底部
                    if (isAtBottom()) {
                        // 在底部，恢复自动滚动
                        if (isSimulationRunning && autoScrollPaused) {
                            resumeAutoScroll()
                        }
                        isUserScrolling = false
                    } else {
                        // 不在底部，保持用户滚动状态
                        if (isSimulationRunning && !autoScrollPaused) {
                            pauseAutoScroll()
                        }
                    }
                }
                
                scrollStateChangeHandler.postDelayed(scrollStateChangeRunnable!!, 500)
            }
        })
    }

    private fun setupInputViews() {
    // 获取视图引用
        inputEditText = findViewById(R.id.et_message_input)
        sendButton = findViewById(R.id.btn_send)
        moreOptionsButton = findViewById(R.id.btn_more_options)

        // 设置发送按钮点击事件
        sendButton.setOnClickListener {
            val message = inputEditText.text.toString().trim()
            if (message.isNotEmpty()) {
                controller?.sendMessage(message)
                inputEditText.text.clear()
            } else {
                // 显示提示
                Toast.makeText(HmsApplication.appContext, "请输入问题内容", Toast.LENGTH_SHORT).show()
            }
        }

        // 设置更多选项按钮点击事件
        moreOptionsButton.setOnClickListener {
            showMoreOptions()
        }
    }

    private fun showMoreOptions() {
        // 创建弹出菜单
        val popupMenu = PopupMenu(context, moreOptionsButton)
        popupMenu.menuInflater.inflate(R.menu.menu_chat_options, popupMenu.menu)
        
        // 根据模拟状态动态显示菜单项
         val startItem = popupMenu.menu.findItem(R.id.option_simulation_start)
         val pauseItem = popupMenu.menu.findItem(R.id.option_simulation_pause)
         val resumeItem = popupMenu.menu.findItem(R.id.option_simulation_resume)
         val resetItem = popupMenu.menu.findItem(R.id.option_simulation_reset)
         
         when {
             !isSimulationActive() -> {
                 // 模拟未启动时，显示开始选项，隐藏其他模拟控制选项
                 startItem?.isVisible = true
                 pauseItem?.isVisible = false
                 resumeItem?.isVisible = false
                 resetItem?.isVisible = false
             }
             isSimulationPaused() -> {
                 // 模拟已暂停时，显示恢复和重置选项，隐藏开始和暂停选项
                 startItem?.isVisible = false
                 pauseItem?.isVisible = false
                 resumeItem?.isVisible = true
                 resetItem?.isVisible = true
             }
             else -> {
                 // 模拟运行中时，显示暂停和重置选项，隐藏开始和恢复选项
                 startItem?.isVisible = false
                 pauseItem?.isVisible = true
                 resumeItem?.isVisible = false
                 resetItem?.isVisible = true
             }
         }

        // 设置菜单项点击监听
        popupMenu.setOnMenuItemClickListener { menuItem ->
            when (menuItem.itemId) {
                R.id.option_clear -> {
//                    clearChat()
                    true
                }
                R.id.option_voice -> {
//                    startVoiceInput()
                    true
                }
                R.id.option_share -> {
//                    shareConversation()
                    true
                }
                R.id.option_simulation_start -> {
                    startSimulatedConversation()
                    true
                }
                R.id.option_simulation_pause -> {
                    pauseSimulation()
                    true
                }
                R.id.option_simulation_resume -> {
                    resumeSimulation()
                    true
                }
                R.id.option_simulation_reset -> {
                    resetSimulation()
                    true
                }
                R.id.option_navigate_hospital -> {
                    openBaiduMapForNearbyHospitals()
                    true
                }
                else -> false
            }
        }

        popupMenu.show()
    }

    /**
     * 初始化聊天消息
     */
    private fun loadInitialMessages() {
        if (fromType == Constants.FROM_TYPE_CARD_OPEN_CHAT) {
            // 添加欢迎消息和选项
            chatAdapter.addItem(
                MedAIChatItem.WelcomeAndOptions(
                    welcomeMessage = "Hi，我是健康小医，欢迎使用智能健康咨询服务",
                    introMessage = "我是基于医疗大语言模型技术，学习大量医学知识具备一定医学常识的AI健康助手，可以根据您的问题提供准确、个性化的建议。很高兴为您服务",
                    options = listOf(
                        MedAIChatItem.WelcomeAndOptions.Option("健康咨询", null),
                        MedAIChatItem.WelcomeAndOptions.Option("症状咨询", null),
                        MedAIChatItem.WelcomeAndOptions.Option("药品咨询", null),
                        MedAIChatItem.WelcomeAndOptions.Option("问诊/导诊", null)
                    )
                )
            )

            // 添加分隔线
            chatAdapter.addItem(MedAIChatItem.Divider)

            // 添加AI消息
//            chatAdapter.addItem(
//                MedAIChatItem.AIMessage("我在，请问有什么可以帮您?")
//            )

            startSimulatedConversation()
        } else {
            chatAdapter.addItem(
                MedAIChatItem.HealthDataCard(
                    title = "健康严重异常",
                    healthItems = listOf(
                        MedAIChatItem.HealthDataCard.HealthItem(
                            iconResId = R.drawable.card_icon_heart,
                            name = "心率",
                            value = "120",
                            unit = "次/分钟",
                            status = "心动过速",
                            isAbnormal = true
                        ),
                        MedAIChatItem.HealthDataCard.HealthItem(
                            iconResId = R.drawable.card_icon_blood_pressure,
                            name = "血压",
                            value = "147/97",
                            unit = "mmHg",
                            status = "疑似高血压",
                            isAbnormal = true
                        )
                    )
                )
            )

            chatAdapter.addItem(
                MedAIChatItem.AIMessage("Hi，小医已根据您当前的健康数据，为了进一步分析判断身体状态，还需要跟您确认几个问题，请问您现在是否有胸闷、胸痛的感觉")
            )
        }

        // 添加快捷选项
//        chatAdapter.addItem(
//            MedAIChatItem.QuickOptions(
//                options = listOf("感冒，喉咙痛...", "发烧，四肢酸痛...")
//            )
//        )
    }

    private fun setupEventListeners() {
        // 设置选项点击监听
        chatAdapter.onOptionClickListener = { option ->
            handleOptionClick(option)
        }

        // 设置快捷选项点击监听
        chatAdapter.onQuickOptionClickListener = { option ->
            sendUserMessage(option)
        }
    }

    // 处理选项点击
    private fun handleOptionClick(option: MedAIChatItem.WelcomeAndOptions.Option) {
        when (option.title) {
            "健康咨询" -> {
                // 处理健康咨询点击
                controller?.sendMessage("我想进行健康咨询")
            }
            "症状咨询" -> {
                // 处理症状咨询点击
                controller?.sendMessage("我想咨询一些症状问题")
            }
            "药品咨询" -> {
                // 处理药品咨询点击
                controller?.sendMessage("我想了解一些药品信息")
            }
            "问诊/导诊" -> {
                // 处理问诊/导诊点击
                controller?.sendMessage("我需要进行问诊或导诊")
            }
        }
    }

    // 发送用户消息
    fun sendUserMessage(message: String) {
        // 添加用户消息到列表
        chatAdapter.addItem(MedAIChatItem.UserMessage(message))

        // 滚动到底部
        recyclerView.smoothScrollToPosition(chatAdapter.itemCount - 1)

        // 模拟AI响应
//        simulateAIResponse(message)
    }

    // 模拟AI响应（实际项目中应该调用真实API）
    private fun simulateAIResponse(userMessage: String) {
        // 根据用户消息类型返回不同的响应
        when {
            userMessage.contains("健康") -> {
                // 如果是健康咨询，返回健康数据卡片
                postDelayed({
                    chatAdapter.addItem(
                        MedAIChatItem.HealthDataCard(
                            title = "健康严重异常",
                            healthItems = listOf(
                                MedAIChatItem.HealthDataCard.HealthItem(
                                    iconResId = R.drawable.card_icon_heart,
                                    name = "心率",
                                    value = "120",
                                    unit = "次/分钟",
                                    status = "心动过速",
                                    isAbnormal = true
                                ),
                                MedAIChatItem.HealthDataCard.HealthItem(
                                    iconResId = R.drawable.card_icon_blood_pressure,
                                    name = "血压",
                                    value = "147/97",
                                    unit = "mmHg",
                                    status = "疑似高血压",
                                    isAbnormal = true
                                )
                            )
                        )
                    )

                    chatAdapter.addItem(
                        MedAIChatItem.AIMessage("Hi，小医已根据您当前的健康数据，为了进一步分析判断身体状态，还需要跟您确认几个问题，请问您现在是否有胸闷、胸痛的感觉")
                    )

//                    chatAdapter.addItem(
//                        MedAIChatItem.QuickOptions(
//                            options = listOf("呼吸不畅，吸气胸痛...", "胸闷，胸口疼...")
//                        )
//                    )

                    recyclerView.scrollToPosition(chatAdapter.itemCount - 1)
                }, 800)
            }
            userMessage.contains("症状") -> {
                // 如果是症状咨询
                postDelayed({
                    chatAdapter.addItem(
                        MedAIChatItem.AIMessage("请描述一下您的具体症状，包括持续时间、疼痛程度等，这样我能更准确地为您提供建议。")
                    )
                    recyclerView.scrollToPosition(chatAdapter.itemCount - 1)
                }, 800)
            }
            else -> {
                // 通用回复
                postDelayed({
                    chatAdapter.addItem(
                        MedAIChatItem.AIMessage("我已收到您的问题，正在为您分析中...")
                    )
                    recyclerView.scrollToPosition(chatAdapter.itemCount - 1)
                }, 800)
            }
        }
    }

    // ==================== 模拟对话核心逻辑 ====================
    
    /**
     * 开始模拟对话
     */
    fun startSimulatedConversation() {
        try {
            // 状态检查
            if (isSimulationRunning) {
                Log.w(TAG, "模拟对话已在运行中")
                return
            }
            
            if (isDestroyed) {
                Log.e(TAG, "视图已销毁，无法启动模拟对话")
                return
            }
            
            // 错误冷却检查
            if (isInErrorCooldown()) {
                showErrorMessage("请稍后再试，系统正在恢复中...")
                return
            }
            
            // 备份当前真实对话数据
            backupRealChatData()
            
            // 重置错误计数
            errorCount = 0
            simulationStartTime = System.currentTimeMillis()
            
            // 开始性能监控
            currentSessionId = UUID.randomUUID().toString()
            performanceMonitor.startSession(currentSessionId!!, simulatedConversation.size)
            
            isSimulationRunning = true
            currentMessageIndex = 0
            simulationPaused = false
            
            Log.d(TAG, "开始模拟对话，共${simulatedConversation.size}条消息，会话ID: $currentSessionId")
            
            // 清空现有消息
//            chatAdapter.clearItems()
            
            // 开始显示第一条消息
            showNextMessage()
            
        } catch (e: Exception) {
            handleSimulationError("启动模拟对话失败", e)
        }
    }
    
    /**
     * 显示下一条模拟消息
     */
    private fun showNextMessage() {
        try {
            // 状态检查
            if (!isSimulationRunning || simulationPaused || isDestroyed) {
                Log.d(TAG, "模拟暂停或已停止，跳过消息显示")
                return
            }
            
            // 边界检查
            if (currentMessageIndex >= simulatedConversation.size) {
                // 对话结束
                finishSimulation()
                return
            }
            
            val message = simulatedConversation[currentMessageIndex]
            
            // 记录消息开始显示
            currentSessionId?.let { sessionId ->
                performanceMonitor.recordMessageStart(sessionId, message.id, message.role)
            }
            
            simulationHandler?.postDelayed({
                if (!isSimulationRunning || simulationPaused) return@postDelayed
                
                when (message.role) {
                    "user" -> {
                        chatAdapter.addItem(MedAIChatItem.UserMessage(message.content))
                        scrollToBottomIfNeeded()
                        
                        // 记录用户消息完成
                        currentSessionId?.let { sessionId ->
                            performanceMonitor.recordMessageComplete(sessionId, message.id)
                        }
                        
                        // 立即显示下一条消息（AI回复）
                        currentMessageIndex++
                        showNextMessage()
                    }
                    "assistant" -> {
                        // 使用打字机效果显示AI消息
                        showMessageWithTypingEffect(message.content) {
                            // 记录AI消息完成
                            currentSessionId?.let { sessionId ->
                                performanceMonitor.recordMessageComplete(sessionId, message.id)
                            }
                            
                            // 打字机效果完成后，继续下一条消息
                            currentMessageIndex++
                            showNextMessage()
                        }
                    }
                }
            }, message.delay)
            
        } catch (e: Exception) {
            handleSimulationError("显示消息失败", e)
        }
    }
    
    /**
     * 带打字机效果显示消息
     * 使用新的TypingEffectManager实现高效的打字机效果
     */
    private fun showMessageWithTypingEffect(content: String, onComplete: () -> Unit) {
        try {
            if (content.isEmpty()) {
                Log.w(TAG, "尝试显示空消息")
                onComplete()
                return
            }

            // 使用新的打字机效果管理器
            chatAdapter.addAIMessageWithTypingEffect(content)
            scrollToBottomIfNeeded()

            // 由于新的打字机效果是在ViewHolder中处理的，
            // 我们需要延迟调用onComplete，确保打字机效果有时间启动
            val estimatedDuration = estimateTypingDuration(content)
            typingHandler?.postDelayed({
                onComplete()
            }, estimatedDuration)

        } catch (e: Exception) {
            Log.e(TAG, "启动打字机效果失败", e)
            // 降级处理：直接显示完整消息
            chatAdapter.addAIMessageInstant(content)
            scrollToBottomIfNeeded()
            onComplete()
        }
    }

    /**
     * 估算打字机效果的持续时间
     */
    private fun estimateTypingDuration(text: String): Long {
        var totalDelay = 0L

        for (char in text) {
            totalDelay += when {
                char in "。！？；：" -> 300L
                char in "，、" -> 200L
                char in "()（）[]【】{}｛｝" -> 150L
                char == '\n' -> 400L
                char == ' ' -> 100L
                char.isDigit() || (char.isLetter() && char.code < 128) -> 60L
                char.code > 128 -> 80L
                else -> 70L
            }
        }

        return totalDelay + 500L // 额外缓冲时间
    }

    /**
     * 暂停模拟对话
     */
    fun pauseSimulation() {
        try {
            if (isSimulationRunning && !simulationPaused) {
                simulationPaused = true
                
                // 停止所有延迟任务
                simulationHandler?.removeCallbacksAndMessages(null)
                typingHandler?.removeCallbacksAndMessages(null)
                
                // 记录暂停事件
                currentSessionId?.let { sessionId ->
                    performanceMonitor.recordPause(sessionId)
                }
                
                Log.d(TAG, "模拟对话已暂停，当前进度: ${currentMessageIndex}/${simulatedConversation.size}")
            } else {
                Log.w(TAG, "无法暂停：模拟未运行或已暂停")
            }
        } catch (e: Exception) {
            Log.e(TAG, "暂停模拟对话失败", e)
        }
    }
    
    /**
     * 恢复模拟对话
     */
    fun resumeSimulation() {
        try {
            if (isSimulationRunning && simulationPaused) {
                simulationPaused = false
                
                // 记录恢复事件
                currentSessionId?.let { sessionId ->
                    performanceMonitor.recordResume(sessionId)
                }
                
                Log.d(TAG, "模拟对话已恢复，继续从第${currentMessageIndex + 1}条消息")
                
                // 检查是否还有消息要显示
                if (currentMessageIndex < simulatedConversation.size) {
                    // 继续显示下一条消息
                    scheduleNextMessage(500) // 恢复时稍微延迟一下
                } else {
                    // 已经到达末尾
                    finishSimulation()
                }
            } else {
                Log.w(TAG, "无法恢复：模拟未运行或未暂停")
            }
        } catch (e: Exception) {
            Log.e(TAG, "恢复模拟对话失败", e)
            handleSimulationError("恢复模拟对话失败", e)
        }
    }
    
    /**
     * 重置模拟对话
     */
    fun resetSimulation() {
        try {
            Log.d(TAG, "开始重置模拟对话")
            
            // 停止所有定时器
            simulationHandler?.removeCallbacksAndMessages(null)
            typingHandler?.removeCallbacksAndMessages(null)
            
            // 结束性能监控会话
            currentSessionId?.let { sessionId ->
                performanceMonitor.endSession(sessionId, "reset")
                val report = performanceMonitor.generateReport(sessionId)
                Log.d(TAG, "性能报告: $report")
            }
            
            // 重置状态
            isSimulationRunning = false
            currentMessageIndex = 0
            simulationPaused = false
            autoScrollPaused = false
            currentSessionId = null
            
            // 重置错误状态
            errorCount = 0
            lastErrorTime = 0
            
            // 重置滚动状态
            resetScrollState()
            
            // 清空聊天记录
            chatAdapter.clearItems()
            
            // 恢复真实对话数据（如果有备份）
            restoreRealChatData()
            
            // 重新加载初始消息
            loadInitialMessages()
            
            // 强制滚动到底部
            forceScrollToBottom()
            
            Log.d(TAG, "模拟对话已重置，耗时: ${System.currentTimeMillis() - simulationStartTime}ms")
            
        } catch (e: Exception) {
            Log.e(TAG, "重置模拟对话失败", e)
            // 强制重置关键状态
            isSimulationRunning = false
            simulationPaused = false
            currentSessionId = null
        }
    }
    
    /**
     * 智能滚动到底部（如果用户没有主动滚动且自动滚动未被暂停）
     */
    private fun scrollToBottomIfNeeded() {
        if (!isUserScrolling && !autoScrollPaused) {
            recyclerView.post {
                recyclerView.smoothScrollToPosition(chatAdapter.itemCount - 1)
            }
        }
    }
    
    /**
     * 检查模拟对话是否正在运行
     */
    fun isSimulationActive(): Boolean {
        return isSimulationRunning
    }
    
    /**
     * 检查模拟对话是否暂停
     */
    fun isSimulationPaused(): Boolean {
        return simulationPaused
    }

    // ==================== 智能滚动控制辅助方法 ====================
    
    /**
     * 检查RecyclerView是否滚动到底部
     */
    private fun isAtBottom(): Boolean {
        val layoutManager = recyclerView.layoutManager as? LinearLayoutManager ?: return false
        val lastVisiblePosition = layoutManager.findLastCompletelyVisibleItemPosition()
        val totalItemCount = chatAdapter.itemCount
        
        // 如果最后一个完全可见的项目是最后一项，或者只差1-2项，认为在底部
        return lastVisiblePosition >= totalItemCount - 2
    }
    
    /**
     * 暂停自动滚动
     */
    private fun pauseAutoScroll() {
        autoScrollPaused = true
        Log.d(TAG, "自动滚动已暂停 - 用户正在查看历史消息")
    }
    
    /**
     * 恢复自动滚动
     */
    private fun resumeAutoScroll() {
        autoScrollPaused = false
        isUserScrolling = false
        Log.d(TAG, "自动滚动已恢复 - 用户回到底部")
        
        // 立即滚动到底部以跟上最新消息
        recyclerView.post {
            recyclerView.smoothScrollToPosition(chatAdapter.itemCount - 1)
        }
    }
    
    /**
     * 重置滚动状态
     */
    private fun resetScrollState() {
        isUserScrolling = false
        autoScrollPaused = false
    }
    
    /**
     * 强制滚动到底部（忽略用户滚动状态）
     */
    private fun forceScrollToBottom() {
        recyclerView.post {
            if (chatAdapter.itemCount > 0) {
                recyclerView.scrollToPosition(chatAdapter.itemCount - 1)
            }
        }
    }
    
    // ==================== 错误处理和性能优化方法 ====================
    
    /**
     * 处理模拟对话错误
     */
    private fun handleSimulationError(message: String, exception: Exception) {
        errorCount++
        lastErrorTime = System.currentTimeMillis()
        
        // 记录错误到性能监控器
         currentSessionId?.let { sessionId ->
             performanceMonitor.recordError(sessionId, "simulation_error", "$message: ${exception.message ?: "Unknown error"}")
         }
        
        Log.e(TAG, "$message (错误次数: $errorCount)", exception)
        
        if (errorCount >= maxRetryCount) {
            // 达到最大重试次数，停止模拟
            Log.e(TAG, "模拟对话错误次数过多，自动停止")
            resetSimulation()
            showErrorMessage("模拟对话遇到问题已停止，请稍后重试")
        } else {
            // 尝试恢复
            showErrorMessage("模拟对话遇到小问题，正在尝试恢复...")
        }
    }
    
    /**
     * 检查是否在错误冷却期内
     */
    private fun isInErrorCooldown(): Boolean {
        return errorCount >= maxRetryCount && 
               (System.currentTimeMillis() - lastErrorTime) < errorCooldownTime
    }
    
    /**
     * 显示错误消息
     */
    private fun showErrorMessage(message: String) {
        // 可以通过Toast或Snackbar显示错误信息
        Log.w(TAG, "错误提示: $message")
        // Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 备份真实对话数据
     */
    private fun backupRealChatData() {
        try {
            realChatBackup = chatAdapter.getItems().toList()
            Log.d(TAG, "已备份${realChatBackup?.size ?: 0}条真实对话数据")
        } catch (e: Exception) {
            Log.e(TAG, "备份真实对话数据失败", e)
        }
    }
    
    /**
     * 恢复真实对话数据
     */
    private fun restoreRealChatData() {
        try {
            realChatBackup?.let { backup ->
                if (backup.isNotEmpty()) {
                    chatAdapter.submitMedAIChatItems(backup)
                    Log.d(TAG, "已恢复${backup.size}条真实对话数据")
                    recyclerView.post {
                        recyclerView.scrollToPosition(backup.size - 1)
                    }
                }
            }
            realChatBackup = null
        } catch (e: Exception) {
            Log.e(TAG, "恢复真实对话数据失败", e)
        }
    }
    
    /**
     * 安全滚动到底部
     */
    private fun scrollToBottomSafely() {
        try {
            if (!isDestroyed && chatAdapter.itemCount > 0) {
                recyclerView.post {
                    recyclerView.smoothScrollToPosition(chatAdapter.itemCount - 1)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "滚动失败", e)
        }
    }
    
    /**
     * 安排下一条消息显示
     */
    private fun scheduleNextMessage(delay: Long) {
        try {
            simulationHandler?.postDelayed({
                if (!isDestroyed) {
                    showNextMessage()
                }
            }, delay)
        } catch (e: Exception) {
            Log.e(TAG, "安排下一条消息失败", e)
        }
    }
    
    /**
     * 完成模拟对话
     */
    private fun finishSimulation() {
        try {
            isSimulationRunning = false
            val duration = System.currentTimeMillis() - simulationStartTime
            
            // 结束性能监控会话
            currentSessionId?.let { sessionId ->
                performanceMonitor.endSession(sessionId, "completed")
                val report = performanceMonitor.generateReport(sessionId)
                Log.d(TAG, "模拟对话完成性能报告: $report")
            }
            
            Log.d(TAG, "模拟对话完成，总耗时: ${duration}ms，共显示${currentMessageIndex}条消息")
            
            // 可以在这里添加完成回调或统计
            // 导航到附近医院
            openBaiduMapForNearbyHospitals()
        } catch (e: Exception) {
            Log.e(TAG, "完成模拟对话时出错", e)
        }
    }
    
    /**
     * 获取性能监控报告
     */
    fun getPerformanceReport(): String? {
        return currentSessionId?.let { sessionId ->
            performanceMonitor.generateReport(sessionId)
        }
    }
    
    /**
     * 获取所有会话的性能统计
     */
    fun getAllPerformanceStats(): String {
        return performanceMonitor.getAllSessionsStats()
    }
    
    /**
     * 清理性能监控数据
     */
    fun clearPerformanceData() {
        performanceMonitor.clearOldSessions()
    }
    
    /**
     * 生命周期管理 - 在Activity/Fragment销毁时调用
     */
    fun onDestroy() {
        isDestroyed = true

        // 结束当前会话（如果存在）
        currentSessionId?.let { sessionId ->
            performanceMonitor.endSession(sessionId, "destroyed")
        }

        // 清理打字机效果管理器
        chatAdapter.cleanup()

        simulationHandler?.removeCallbacksAndMessages(null)
        typingHandler?.removeCallbacksAndMessages(null)
        Log.d(TAG, "MedAIChatView已销毁")
    }

    /**
     * 打开百度地图搜索附近医院
     */
    private fun openBaiduMapForNearbyHospitals() {
        try {
            // 百度地图包名
            val baiduMapPackage = "com.baidu.BaiduMap"
            
            // 检查是否安装了百度地图
            if (isBaiduMapInstalled(baiduMapPackage)) {
                // 使用百度地图URI Scheme搜索附近医院
                val uri = Uri.parse("baidumap://map/place/search?query=医院&location=auto&src=healthlink")
                val intent = Intent(Intent.ACTION_VIEW, uri)
                intent.setPackage(baiduMapPackage)
                context.startActivity(intent)
                
                Log.d(TAG, "已打开百度地图搜索附近医院")
                Toast.makeText(context, "正在打开百度地图搜索附近医院...", Toast.LENGTH_SHORT).show()
            } else {
                // 百度地图未安装，提供备选方案
                showMapAlternatives()
            }
        } catch (e: Exception) {
            Log.e(TAG, "打开百度地图失败", e)
            // 发生异常时也提供备选方案
            showMapAlternatives()
        }
    }
    
    /**
     * 检查百度地图是否已安装
     */
    private fun isBaiduMapInstalled(packageName: String): Boolean {
        return try {
            context.packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }
    
    /**
     * 显示地图应用备选方案
     */
    private fun showMapAlternatives() {
        try {
            // 尝试使用通用地图Intent搜索医院
            val geoUri = Uri.parse("geo:0,0?q=医院")
            val mapIntent = Intent(Intent.ACTION_VIEW, geoUri)
            
            // 检查是否有应用可以处理地图Intent
            if (mapIntent.resolveActivity(context.packageManager) != null) {
                context.startActivity(mapIntent)
                Toast.makeText(context, "正在打开地图搜索附近医院...", Toast.LENGTH_SHORT).show()
            } else {
                // 如果没有地图应用，打开应用商店下载百度地图
                openAppStoreForBaiduMap()
            }
        } catch (e: Exception) {
            Log.e(TAG, "打开备选地图方案失败", e)
            Toast.makeText(context, "无法打开地图应用，请手动搜索附近医院", Toast.LENGTH_LONG).show()
        }
    }
    
    /**
     * 打开应用商店下载百度地图
     */
    private fun openAppStoreForBaiduMap() {
        try {
            // 尝试打开应用商店的百度地图页面
            val appStoreUri = Uri.parse("market://details?id=com.baidu.BaiduMap")
            val appStoreIntent = Intent(Intent.ACTION_VIEW, appStoreUri)
            
            if (appStoreIntent.resolveActivity(context.packageManager) != null) {
                context.startActivity(appStoreIntent)
                Toast.makeText(context, "正在打开应用商店下载百度地图...", Toast.LENGTH_SHORT).show()
            } else {
                // 如果没有应用商店，使用浏览器打开网页版
                val webUri = Uri.parse("https://map.baidu.com/search/医院")
                val webIntent = Intent(Intent.ACTION_VIEW, webUri)
                context.startActivity(webIntent)
                Toast.makeText(context, "正在打开百度地图网页版...", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "打开应用商店失败", e)
            Toast.makeText(context, "请手动前往应用商店下载百度地图", Toast.LENGTH_LONG).show()
        }
    }

}