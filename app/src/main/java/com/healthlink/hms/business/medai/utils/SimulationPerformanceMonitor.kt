package com.healthlink.hms.business.medai.utils

import android.util.Log
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong

/**
 * 模拟对话性能监控器
 * 用于监控和统计模拟对话功能的性能指标
 */
class SimulationPerformanceMonitor {
    
    companion object {
        private const val TAG = "SimulationPerformanceMonitor"
        
        @Volatile
        private var INSTANCE: SimulationPerformanceMonitor? = null
        
        fun getInstance(): SimulationPerformanceMonitor {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SimulationPerformanceMonitor().also { INSTANCE = it }
            }
        }
    }
    
    // 性能统计数据
    private val simulationCount = AtomicInteger(0)
    private val totalSimulationTime = AtomicLong(0)
    private val errorCount = AtomicInteger(0)
    private val pauseResumeCount = AtomicInteger(0)
    private val resetCount = AtomicInteger(0)
    
    // 当前会话数据
    private val sessionStartTimes = ConcurrentHashMap<String, Long>()
    private val sessionMessageCounts = ConcurrentHashMap<String, Int>()
    private val sessionErrors = ConcurrentHashMap<String, Int>()
    
    // 性能阈值
    private val maxTypingEffectTime = 10000L // 10秒
    private val maxMessageDisplayTime = 5000L // 5秒
    private val maxSimulationTime = 300000L // 5分钟
    
    /**
     * 开始监控模拟会话
     */
    fun startSession(sessionId: String, totalMessages: Int = 0) {
        sessionStartTimes[sessionId] = System.currentTimeMillis()
        sessionMessageCounts[sessionId] = 0
        sessionErrors[sessionId] = 0
        simulationCount.incrementAndGet()
        
        Log.d(TAG, "开始监控模拟会话: $sessionId, 预计消息数: $totalMessages")
    }
    
    /**
     * 记录消息开始显示
     */
    fun recordMessageStart(sessionId: String, messageId: String, messageType: String) {
        Log.d(TAG, "消息开始显示: $sessionId, 消息ID: $messageId, 类型: $messageType")
    }
    
    /**
     * 记录消息显示完成
     */
    fun recordMessageComplete(sessionId: String, messageId: String) {
        sessionMessageCounts[sessionId] = (sessionMessageCounts[sessionId] ?: 0) + 1
        Log.d(TAG, "消息显示完成: $sessionId, 消息ID: $messageId")
    }
    
    /**
     * 记录暂停事件
     */
    fun recordPause(sessionId: String) {
        pauseResumeCount.incrementAndGet()
        Log.d(TAG, "模拟会话暂停: $sessionId")
    }
    
    /**
     * 记录恢复事件
     */
    fun recordResume(sessionId: String) {
        pauseResumeCount.incrementAndGet()
        Log.d(TAG, "模拟会话恢复: $sessionId")
    }
    
    /**
     * 结束监控模拟会话
     */
    fun endSession(sessionId: String, reason: String = "completed") {
        val startTime = sessionStartTimes.remove(sessionId)
        val messageCount = sessionMessageCounts.remove(sessionId) ?: 0
        val errorCount = sessionErrors.remove(sessionId) ?: 0
        
        if (startTime != null) {
            val duration = System.currentTimeMillis() - startTime
            totalSimulationTime.addAndGet(duration)
            
            Log.d(TAG, "模拟会话结束: $sessionId, 原因: $reason, 耗时: ${duration}ms, 消息数: $messageCount, 错误数: $errorCount")
            
            // 性能分析
            analyzeSessionPerformance(sessionId, duration, messageCount, errorCount)
        }
    }
    
    /**
     * 生成单个会话的性能报告
     */
    fun generateReport(sessionId: String): String {
        val startTime = sessionStartTimes[sessionId]
        val messageCount = sessionMessageCounts[sessionId] ?: 0
        val errorCount = sessionErrors[sessionId] ?: 0
        
        return if (startTime != null) {
            val duration = System.currentTimeMillis() - startTime
            val avgMessageTime = if (messageCount > 0) duration / messageCount else 0L
            
            "会话报告[$sessionId]: 运行时间=${duration}ms, 消息数=$messageCount, 错误数=$errorCount, 平均消息时间=${avgMessageTime}ms"
        } else {
            "会话[$sessionId]未找到或已结束"
        }
    }
    
    /**
     * 获取所有会话的统计信息
     */
    fun getAllSessionsStats(): String {
        val report = getPerformanceReport()
        return "总体统计: ${report}"
    }
    
    /**
     * 清理旧会话数据
     */
    fun clearOldSessions() {
        cleanupExpiredSessions()
    }
    
    /**
     * 记录消息显示事件
     */
    fun recordMessageDisplay(sessionId: String, messageType: String, displayTime: Long) {
        sessionMessageCounts[sessionId] = (sessionMessageCounts[sessionId] ?: 0) + 1
        
        // 检查显示时间是否超过阈值
        val threshold = if (messageType == "typing_effect") maxTypingEffectTime else maxMessageDisplayTime
        if (displayTime > threshold) {
            Log.w(TAG, "消息显示时间过长: $sessionId, 类型: $messageType, 耗时: ${displayTime}ms")
        }
    }
    
    /**
     * 记录错误事件
     */
    fun recordError(sessionId: String, errorType: String, errorMessage: String) {
        sessionErrors[sessionId] = (sessionErrors[sessionId] ?: 0) + 1
        errorCount.incrementAndGet()
        
        Log.e(TAG, "模拟会话错误: $sessionId, 类型: $errorType, 消息: $errorMessage")
    }
    
    /**
     * 记录暂停/恢复事件
     */
    fun recordPauseResume(sessionId: String, action: String) {
        pauseResumeCount.incrementAndGet()
        Log.d(TAG, "模拟会话$action: $sessionId")
    }
    
    /**
     * 记录重置事件
     */
    fun recordReset(sessionId: String, reason: String) {
        resetCount.incrementAndGet()
        Log.d(TAG, "模拟会话重置: $sessionId, 原因: $reason")
    }
    
    /**
     * 检查会话是否超时
     */
    fun checkSessionTimeout(sessionId: String): Boolean {
        val startTime = sessionStartTimes[sessionId]
        if (startTime != null) {
            val duration = System.currentTimeMillis() - startTime
            if (duration > maxSimulationTime) {
                Log.w(TAG, "模拟会话超时: $sessionId, 已运行: ${duration}ms")
                return true
            }
        }
        return false
    }
    
    /**
     * 获取性能统计报告
     */
    fun getPerformanceReport(): PerformanceReport {
        val avgSimulationTime = if (simulationCount.get() > 0) {
            totalSimulationTime.get() / simulationCount.get()
        } else 0L
        
        return PerformanceReport(
            totalSimulations = simulationCount.get(),
            totalSimulationTime = totalSimulationTime.get(),
            averageSimulationTime = avgSimulationTime,
            totalErrors = errorCount.get(),
            pauseResumeCount = pauseResumeCount.get(),
            resetCount = resetCount.get(),
            activeSessionCount = sessionStartTimes.size
        )
    }
    
    /**
     * 分析会话性能
     */
    private fun analyzeSessionPerformance(sessionId: String, duration: Long, messageCount: Int, errorCount: Int) {
        val avgMessageTime = if (messageCount > 0) duration / messageCount else 0L
        
        // 性能评级
        val performanceGrade = when {
            errorCount > 0 -> "差"
            avgMessageTime > 3000 -> "一般"
            avgMessageTime > 1500 -> "良好"
            else -> "优秀"
        }
        
        Log.i(TAG, "会话性能分析: $sessionId, 评级: $performanceGrade, 平均消息时间: ${avgMessageTime}ms")
        
        // 性能建议
        if (avgMessageTime > 2000) {
            Log.w(TAG, "性能建议: 考虑优化打字机效果或减少消息延迟")
        }
        
        if (errorCount > 0) {
            Log.w(TAG, "稳定性建议: 检查错误处理机制，提高容错能力")
        }
    }
    
    /**
     * 清理过期会话数据
     */
    fun cleanupExpiredSessions() {
        val currentTime = System.currentTimeMillis()
        val expiredSessions = mutableListOf<String>()
        
        sessionStartTimes.forEach { (sessionId, startTime) ->
            if (currentTime - startTime > maxSimulationTime * 2) {
                expiredSessions.add(sessionId)
            }
        }
        
        expiredSessions.forEach { sessionId ->
            sessionStartTimes.remove(sessionId)
            sessionMessageCounts.remove(sessionId)
            sessionErrors.remove(sessionId)
            Log.d(TAG, "清理过期会话: $sessionId")
        }
    }
    
    /**
     * 重置所有统计数据
     */
    fun resetStatistics() {
        simulationCount.set(0)
        totalSimulationTime.set(0)
        errorCount.set(0)
        pauseResumeCount.set(0)
        resetCount.set(0)
        
        sessionStartTimes.clear()
        sessionMessageCounts.clear()
        sessionErrors.clear()
        
        Log.d(TAG, "性能统计数据已重置")
    }
}

/**
 * 性能报告数据类
 */
data class PerformanceReport(
    val totalSimulations: Int,
    val totalSimulationTime: Long,
    val averageSimulationTime: Long,
    val totalErrors: Int,
    val pauseResumeCount: Int,
    val resetCount: Int,
    val activeSessionCount: Int
) {
    
    fun getErrorRate(): Double {
        return if (totalSimulations > 0) {
            totalErrors.toDouble() / totalSimulations.toDouble() * 100
        } else 0.0
    }
    
    fun getAverageSimulationTimeInSeconds(): Double {
        return averageSimulationTime / 1000.0
    }
    
    override fun toString(): String {
        return """
            |性能报告:
            |  总模拟次数: $totalSimulations
            |  总模拟时间: ${totalSimulationTime}ms
            |  平均模拟时间: ${getAverageSimulationTimeInSeconds()}秒
            |  总错误数: $totalErrors
            |  错误率: ${String.format("%.2f", getErrorRate())}%
            |  暂停/恢复次数: $pauseResumeCount
            |  重置次数: $resetCount
            |  活跃会话数: $activeSessionCount
        """.trimMargin()
    }
}