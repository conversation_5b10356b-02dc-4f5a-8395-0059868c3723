package com.healthlink.hms.business.medai.view

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun UserMessageContent(message: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .padding(horizontal = 25.dp, vertical = 10.dp),
        horizontalArrangement = Arrangement.End
    ) {
        Text(text = message,
            fontSize = 20.sp,
            color = Color.White,
            modifier = Modifier.background(
                color = Color(0xFF0087FF),
                shape = RoundedCornerShape(20.dp, 20.dp, 0.dp, 20.dp)
            ).padding(20.dp))
    }
}

@Preview(showBackground = true)
@Composable
fun UserMessageContentPreview() {
    UserMessageContent(message = "我最近眼睛有点干涩")
}