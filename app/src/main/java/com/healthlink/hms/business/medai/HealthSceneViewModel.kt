package com.healthlink.hms.business.medai

import androidx.lifecycle.ViewModel
import com.healthlink.hms.business.medai.model.ExceptionScene
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlin.random.Random

class HealthSceneViewModel : ViewModel() {
    // sceneList 随机规则：共5个ExceptionScene，每5次演示触发为一轮。每轮首次随机，第2345次按表顺序从首次场景继续向下轮巡。
    // 场景列表
    val sceneList = listOf<ExceptionScene>(
        ExceptionScene("1","心脑关怀","心率","监测到您心率过速，心率变异性出现异常，是否需要电话医生或AI健康咨询服务？"),
        ExceptionScene("2","情绪关怀","压力","监测到您近一周内超60%的时间压力“偏高”，是否需要电话医生或AI健康咨询服务？"),
        ExceptionScene("3","作息跟踪","睡眠","严重不足，近一周日均睡眠时间少于4小时"),
        ExceptionScene("4","健康严重异常1","心率、血压","24小时内超60%的时间出现心动过速、Ⅰ级高血压"),
        ExceptionScene("5","健康严重异常2","心率、睡眠、压力","近一周日均睡眠时间均少于4小时，超50%的时间出现心动过速和压力“偏高”"),
    )

    // 当前轮次计数器（0-4）
    private val _currentPositionInRound = MutableStateFlow(0)

    // 当前轮次的起始场景索引
    private val _currentRoundStartIndex = MutableStateFlow(0)

    // 当前选中的场景
    private val _currentScene = MutableStateFlow<ExceptionScene?>(null)
    val currentScene: StateFlow<ExceptionScene?> = _currentScene

    // 获取下一个场景的描述
    fun getNextSceneDescription(): String {
        val index= getNextScene()
        return sceneList[index].exceptionDesc
    }

    // 获取下一个场景
    private fun getNextScene() : Int {
        // 获取当前在轮次中的位置
        val positionInRound = _currentPositionInRound.value

        // 如果是轮次的第一个位置(0)，随机选择起始场景
        if (positionInRound == 0) {
            _currentRoundStartIndex.value = Random.nextInt(0, sceneList.size)
        }

        // 计算当前应该显示的场景索引
        val sceneIndex = (_currentRoundStartIndex.value + positionInRound) % sceneList.size

        // 更新当前场景
        _currentScene.value = sceneList[sceneIndex]

        // 递增轮次内位置计数
        _currentPositionInRound.value = (positionInRound + 1) % 5

        return positionInRound
    }

    // 判断当前轮次是否结束
    private fun isCurrentRoundFinished(): Boolean {
        return _currentPositionInRound.value == 0
    }

    // 重置当前轮次
    private fun resetRound() {
        _currentPositionInRound.value = 0
    }

}