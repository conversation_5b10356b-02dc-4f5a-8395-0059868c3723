package com.healthlink.hms.business.medai.view

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.BlurredEdgeTreatment
import androidx.compose.ui.draw.blur
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RenderEffect
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.healthlink.hms.R
import kotlinx.coroutines.delay

@Composable
fun HealthWarningAlert(
    title: String,
    onDismiss: () -> Unit,
    onCallDoctor: () -> Unit,
    onAIConsultation: () -> Unit,
    onNavigateToHospital: () -> Unit,
    autoHideDuration: Long = 5000, // 默认5秒后自动消失
    animationDuration: Long = 300    // 默认动画时长300ms
) {
    var visible by remember { mutableStateOf(true) }

    // 按钮点击处理
    val handleButtonClick: (action: () -> Unit) -> Unit = { action ->
        visible = false
        action()
    }

    // 处理关闭回调
    LaunchedEffect(key1 = visible) {
        if (!visible) {
            delay(animationDuration) // 动画持续时间
            onDismiss()
        }
    }

    // 添加延迟2秒自动关闭逻辑
    LaunchedEffect(key1 = Unit) {
        delay(autoHideDuration) // 延迟2秒
        visible = false // 触发退出动画
//        delay(300) // 等待动画完成
        onDismiss() // 执行关闭回调
    }

    AnimatedVisibility(
        visible = visible,
        enter = slideInHorizontally(
            initialOffsetX = { it },
            animationSpec = tween(durationMillis = animationDuration.toInt())
        ),
        exit = slideOutHorizontally(
            targetOffsetX = { it },
            animationSpec = tween(durationMillis = animationDuration.toInt())
        )
    ) {
        Box(modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.TopEnd) {
            // 模糊背景
//            Box(modifier = Modifier
//                .width(720.dp)
//                .padding(top = 72.dp, end = 68.dp)
//                .background(Color(0xB3FFFFFF))
//                .blur(
//                    radius = 40.dp,
//                    edgeTreatment = BlurredEdgeTreatment.Rectangle
//                )
//            )

            Card(
                modifier = Modifier
                    .width(720.dp)
                    .padding(top = 72.dp, end = 68.dp),
                shape = RoundedCornerShape(20.dp),
               colors = CardDefaults.cardColors(
                   containerColor = Color(0xFFFFFFFF)
               )
            ) {

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 30.dp, top = 30.dp, end = 50.dp, bottom = 30.dp)
                ) {
                    // 顶部警告信息
                    Image(
                        painter = painterResource(id = R.drawable.ic_warning),
                        contentDescription = "警告",
                        modifier = Modifier.padding(top = 4.dp).size(24.dp)
                    )

                    Column(
                        horizontalAlignment = Alignment.Start,
                        modifier = Modifier.padding(start = 15.dp)
                    ) {

                        Text(
                            text = title,
                            color = Color(0xFF333333),
                            fontSize = 20.sp,
                            maxLines = 3
                        )

                        Spacer(modifier = Modifier.height(32.dp))

                        // 底部按钮区域
                        Row(modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(20.dp)) {
                            // 电话医生按钮
                            Button(
                                text = "电话医生",
                                backgroundColor = Color(0xFF07C160),
                                onClick = { handleButtonClick(onCallDoctor) }
                            )

                            // AI小医咨询按钮
                            Button(
                                text = "AI小医咨询",
                                backgroundColor = Color(0xFF0087FF),
                                onClick = { handleButtonClick(onAIConsultation) }
                            )

                            // 附近医院导航按钮
                            Button(
                                text = "附近医院导航",
                                backgroundColor = Color(0x1A999999),
                                textColor = Color(0xFF666666),
                                onClick = { handleButtonClick(onNavigateToHospital) }
                            )
                        }
                    }
                }
            }
        }

    }
}

@Composable
private fun Button(
    text: String,
    backgroundColor: Color,
    textColor: Color = Color.White,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(164.dp,40.dp)
            .background(backgroundColor, RoundedCornerShape(20.dp))
            .clickable(
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            ) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = textColor,
            fontSize = 18.sp,
            textAlign = TextAlign.Center
        )
    }
}

@Preview(showBackground = true, widthDp = 1920, heightDp = 1080)
@Composable
fun HealthWarningAlertPreview() {
    HealthWarningAlert(
        title = "检测到您最近30分钟内出现心动过缓、疑似高血压的情况持续10分钟以上，如您有明显的身体不适，例如胸口憋闷、疼痛等，建议您停车休息或立即就诊，或者点击下方服务链接及时寻求帮助。",
        onDismiss = {},
        onCallDoctor = {},
        onAIConsultation = {},
        onNavigateToHospital = {}
    )
}