package com.healthlink.hms.business.medai.utils

import androidx.compose.runtime.mutableStateOf
import android.util.Log

/**
 * 打字机效果测试类
 * 用于验证新的打字机效果实现
 */
object TypingEffectTest {
    private const val TAG = "TypingEffectTest"
    
    /**
     * 测试基本打字机效果
     */
    fun testBasicTypingEffect() {
        val manager = TypingEffectManager()
        val textState = mutableStateOf("")
        
        val testText = "Hello, 这是一个测试消息！"
        
        Log.d(TAG, "开始测试基本打字机效果")
        
        val taskId = manager.startTypingEffect(
            fullText = testText,
            textState = textState,
            onComplete = {
                Log.d(TAG, "基本打字机效果测试完成")
                Log.d(TAG, "最终文本: ${textState.value}")
                assert(textState.value == testText) { "文本不匹配" }
            },
            onProgress = { progress ->
                Log.d(TAG, "进度: ${(progress * 100).toInt()}%")
            }
        )
        
        Log.d(TAG, "任务ID: $taskId")
    }
    
    /**
     * 测试批量更新性能
     */
    fun testBatchUpdatePerformance() {
        val manager = TypingEffectManager()
        val textState = mutableStateOf("")
        
        val longText = buildString {
            repeat(100) {
                append("这是第${it + 1}行测试文本，包含中文、English、数字123和标点符号！\n")
            }
        }
        
        Log.d(TAG, "开始测试批量更新性能，文本长度: ${longText.length}")
        val startTime = System.currentTimeMillis()
        
        manager.startTypingEffect(
            fullText = longText,
            textState = textState,
            onComplete = {
                val duration = System.currentTimeMillis() - startTime
                Log.d(TAG, "批量更新性能测试完成，耗时: ${duration}ms")
                Log.d(TAG, "平均每字符耗时: ${duration.toFloat() / longText.length}ms")
            }
        )
    }
    
    /**
     * 测试多任务并发
     */
    fun testConcurrentTasks() {
        val manager = TypingEffectManager()
        
        val tasks = mutableListOf<Pair<String, androidx.compose.runtime.MutableState<String>>>()
        
        repeat(5) { index ->
            val textState = mutableStateOf("")
            val text = "任务${index + 1}: 这是并发测试消息"
            tasks.add(text to textState)
        }
        
        Log.d(TAG, "开始测试${tasks.size}个并发任务")
        
        tasks.forEachIndexed { index, (text, textState) ->
            manager.startTypingEffect(
                fullText = text,
                textState = textState,
                onComplete = {
                    Log.d(TAG, "任务${index + 1}完成: ${textState.value}")
                }
            )
        }
        
        Log.d(TAG, "活跃任务数: ${manager.getActiveTaskCount()}")
    }
    
    /**
     * 测试任务取消
     */
    fun testTaskCancellation() {
        val manager = TypingEffectManager()
        val textState = mutableStateOf("")
        
        val testText = "这是一个会被取消的长文本消息，用于测试取消功能是否正常工作。"
        
        Log.d(TAG, "开始测试任务取消")
        
        val taskId = manager.startTypingEffect(
            fullText = testText,
            textState = textState,
            onComplete = {
                Log.e(TAG, "任务不应该完成，因为已被取消")
            }
        )
        
        // 延迟取消任务
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            Log.d(TAG, "取消任务: $taskId")
            manager.cancelTypingEffect(taskId)
            
            // 检查任务是否被正确取消
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                Log.d(TAG, "取消后活跃任务数: ${manager.getActiveTaskCount()}")
                Log.d(TAG, "取消时的文本状态: ${textState.value}")
            }, 1000)
        }, 500)
    }
    
    /**
     * 测试内存清理
     */
    fun testMemoryCleanup() {
        val manager = TypingEffectManager()
        
        // 创建多个任务
        repeat(10) { index ->
            val textState = mutableStateOf("")
            manager.startTypingEffect(
                fullText = "测试消息 $index",
                textState = textState,
                onComplete = {
                    Log.d(TAG, "任务 $index 完成")
                }
            )
        }
        
        Log.d(TAG, "创建任务后活跃任务数: ${manager.getActiveTaskCount()}")
        
        // 清理所有任务
        manager.cleanup()
        
        Log.d(TAG, "清理后活跃任务数: ${manager.getActiveTaskCount()}")
    }
    
    /**
     * 运行所有测试
     */
    fun runAllTests() {
        Log.d(TAG, "=== 开始运行打字机效果测试 ===")
        
        try {
            testBasicTypingEffect()
            
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                testBatchUpdatePerformance()
            }, 2000)
            
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                testConcurrentTasks()
            }, 4000)
            
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                testTaskCancellation()
            }, 6000)
            
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                testMemoryCleanup()
            }, 8000)
            
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                Log.d(TAG, "=== 所有测试完成 ===")
            }, 10000)
            
        } catch (e: Exception) {
            Log.e(TAG, "测试过程中发生错误", e)
        }
    }
}
