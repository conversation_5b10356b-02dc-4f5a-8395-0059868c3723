package com.healthlink.hms.business.car_seat.ui.spinecare

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.viewmodel.compose.viewModel
import com.healthlink.hms.R
import com.healthlink.hms.business.car_seat.ui.suggestion.DisabilityWarningDialog
import com.healthlink.hms.business.car_seat.ui.suggestion.TripEndedScreen
import com.healthlink.hms.business.car_seat.ui.suggestion.TripInProgressScreen
import com.healthlink.hms.business.car_seat.ui.suggestion.TripNotStartedScreen
import com.healthlink.hms.business.car_seat.viewModel.SeatViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlin.random.Random

/**
 * 坐姿建议 - 支持四种状态：行程未开始、行程中、行程结束、失能预警
 */
@AndroidEntryPoint
class PostureSuggestionFragment : Fragment() {
    
    private val seatViewModel: SeatViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                PostureSuggestionScreen(seatViewModel)
            }
        }
    }

    companion object {
        fun newInstance() = PostureSuggestionFragment()
    }
}

// 定义四种状态
enum class TripStatus {
    NOT_STARTED,    // 行程未开始
    IN_PROGRESS,    // 行程中
    ENDED,          // 行程结束
    DISABILITY_WARNING  // 失能预警
}

@Composable
fun PostureSuggestionScreen(seatViewModel: SeatViewModel) {
    // 模拟状态切换，实际项目中应该从ViewModel获取
    var currentStatus by remember { mutableStateOf(TripStatus.NOT_STARTED) }
    var showDisabilityDialog by remember { mutableStateOf(false) }
    
    // 模拟状态切换按钮（仅用于演示）
    LaunchedEffect(Unit) {
        // 这里可以监听ViewModel的状态变化
    }
    
    Box(modifier = Modifier.fillMaxSize().background(Color(0xFFE8F2FD))) {
        when (currentStatus) {
            TripStatus.NOT_STARTED -> TripNotStartedScreen {
                currentStatus = TripStatus.IN_PROGRESS
            }
            TripStatus.IN_PROGRESS -> TripInProgressScreen(
                onDisabilityWarning = {
                    currentStatus = TripStatus.DISABILITY_WARNING
                    showDisabilityDialog = true
                },
                onTripEnd = {
                    currentStatus = TripStatus.ENDED
                }
            )
            TripStatus.ENDED -> TripEndedScreen {
                currentStatus = TripStatus.NOT_STARTED
            }
            TripStatus.DISABILITY_WARNING -> {
                TripInProgressScreen(
                    onDisabilityWarning = { },
                    onTripEnd = {
                        currentStatus = TripStatus.ENDED
                        showDisabilityDialog = false
                    }
                )
            }
        }
        
        // 失能预警全屏对话框
        if (showDisabilityDialog) {
            DisabilityWarningDialog(
                onDismiss = {
                    showDisabilityDialog = false
                    currentStatus = TripStatus.IN_PROGRESS
                },
                onConfirm = {
                    showDisabilityDialog = false
                    currentStatus = TripStatus.ENDED
                }
            )
        }
        
        // 状态切换按钮（仅用于演示）
        Row(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(16.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Button(
                onClick = { currentStatus = TripStatus.NOT_STARTED },
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (currentStatus == TripStatus.NOT_STARTED) Color.Blue else Color.Gray
                )
            ) {
                Text("未开始", fontSize = 12.sp)
            }
            Button(
                onClick = { currentStatus = TripStatus.IN_PROGRESS },
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (currentStatus == TripStatus.IN_PROGRESS) Color.Blue else Color.Gray
                )
            ) {
                Text("行程中", fontSize = 12.sp)
            }
            Button(
                onClick = { currentStatus = TripStatus.ENDED },
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (currentStatus == TripStatus.ENDED) Color.Blue else Color.Gray
                )
            ) {
                Text("已结束", fontSize = 12.sp)
            }
            Button(
                onClick = {
                    currentStatus = TripStatus.DISABILITY_WARNING
                    showDisabilityDialog = true
                },
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.Red
                )
            ) {
                Text("失能预警", fontSize = 12.sp)
            }
         }
     }
 }

// 辅助组件

// 座椅网格视图
@Composable
fun SeatGridView() {
    Card(
        modifier = Modifier.size(200.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            val gridSize = 8
            val cellSize = size.width / gridSize
            
            // 绘制网格
            for (i in 0 until gridSize) {
                for (j in 0 until gridSize) {
                    val x = i * cellSize
                    val y = j * cellSize
                    
                    // 模拟座椅形状
                    val isInSeatArea = when {
                        j < 2 -> false // 顶部空白
                        j >= 6 -> i in 2..5 // 底部座椅部分
                        else -> i in 1..6 // 中间靠背部分
                    }
                    
                    if (isInSeatArea) {
                        drawRect(
                            color = Color(0xFFE3F2FD),
                            topLeft = Offset(x, y),
                            size = Size(cellSize - 2.dp.toPx(), cellSize - 2.dp.toPx())
                        )
                    }
                }
            }
        }
    }
}

// 压力热力图视图
@Composable
fun PressureHeatmapView() {
    Canvas(
        modifier = Modifier
            .fillMaxWidth()
            .height(200.dp)
    ) {
        val gridSize = 12
        val cellWidth = size.width / gridSize
        val cellHeight = size.height / 8
        
        // 模拟压力数据
        val pressureData = Array(8) { row ->
            Array(gridSize) { col ->
                when {
                    row < 2 -> 0f // 顶部无压力
                    row >= 6 && col in 3..8 -> Random.nextFloat() * (0.8f - 0.3f) + 0.3f // 座椅部分
                    row in 2..5 && col in 2..9 -> Random.nextFloat() * (0.9f - 0.4f) + 0.4f // 靠背部分
                    else -> 0f
                }
            }
        }
        
        // 绘制热力图
        for (row in 0 until 8) {
            for (col in 0 until gridSize) {
                val pressure = pressureData[row][col]
                val color = when {
                    pressure == 0f -> Color.Transparent
                    pressure < 0.3f -> Color(0xFF4CAF50) // 绿色 - 低压力
                    pressure < 0.6f -> Color(0xFFFFEB3B) // 黄色 - 中压力
                    pressure < 0.8f -> Color(0xFFFF9800) // 橙色 - 高压力
                    else -> Color(0xFFFF5722) // 红色 - 很高压力
                }
                
                if (color != Color.Transparent) {
                    drawRect(
                        color = color,
                        topLeft = Offset(col * cellWidth, row * cellHeight),
                        size = Size(cellWidth - 1.dp.toPx(), cellHeight - 1.dp.toPx())
                    )
                }
            }
        }
    }
    
    Spacer(modifier = Modifier.height(16.dp))
    
    // 压力图例
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        PressureLegendItem(color = Color(0xFF4CAF50), label = "低")
        PressureLegendItem(color = Color(0xFFFFEB3B), label = "中")
        PressureLegendItem(color = Color(0xFFFF9800), label = "高")
        PressureLegendItem(color = Color(0xFFFF5722), label = "很高")
    }
}

@Composable
fun PressureLegendItem(color: Color, label: String) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(color, RoundedCornerShape(2.dp))
        )
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color(0xFF666666)
        )
    }
}

// 指标卡片
@Composable
fun MetricCard(
    title: String,
    value: String,
    unit: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = title,
                fontSize = 12.sp,
                color = Color(0xFF666666)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Row(
                verticalAlignment = Alignment.Bottom
            ) {
                Text(
                    text = value,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = color
                )
                if (unit.isNotEmpty()) {
                    Text(
                        text = unit,
                        fontSize = 14.sp,
                        color = Color(0xFF666666),
                        modifier = Modifier.padding(start = 2.dp)
                    )
                }
            }
        }
    }
}


//@Preview(showBackground = true,
//    device = "spec:width=1920px,height=1128px,dpi=240"
//)
//@Composable
//fun PostureSuggestionFragmentPreview() {
//    val viewModel = viewModel<SeatViewModel>()
//    PostureSuggestionScreen(viewModel)
//}