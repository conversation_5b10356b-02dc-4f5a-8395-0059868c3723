package com.healthlink.hms.business.car_seat.views

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.DrawScope
import com.healthlink.hms.R
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.healthlink.hms.business.car_seat.views.TitleInfo
import kotlin.collections.forEach
import kotlin.collections.forEachIndexed
import kotlin.collections.map
import kotlin.let

/**
 * 胸椎力学分布图表数据模型
 * @param position 位置，范围 -1.0 到 1.0，-1.0表示最左，0表示对称，1.0表示最右
 * @param value 压力值，单位Kpa
 */
data class SpineDataPoint(
    val position: Float,
    val value: Float
)

/**
 * 基准线样式配置
 * @param intervals 虚线间隔数组，例如 [线段长度, 间隔长度]
 * @param phase 虚线相位偏移
 */
data class LineStyle(
    val intervals: FloatArray? = null,
    val phase: Float = 0f
) {
    companion object {
        val SOLID = LineStyle() // 实线
        val DASH = LineStyle(floatArrayOf(10f, 5f)) // 虚线
        val DOT = LineStyle(floatArrayOf(2f, 4f)) // 点线
        val DASH_DOT = LineStyle(floatArrayOf(10f, 4f, 2f, 4f)) // 点划线
    }
    
    fun toPathEffect(): PathEffect? {
        return intervals?.let { PathEffect.dashPathEffect(it, phase) }
    }
}


/**
 * 胸椎力学分布图表组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun ThoracicSpineChart(
    model: TitleInfo,
    modifier: Modifier = Modifier.size(574.dp,187.dp),
    dataPoints: List<SpineDataPoint> = generateSampleData(),
    isSymmetric: Boolean = true,
    gridConfig: GridConfig = GridConfig.STANDARD
) {
    ThoracicSpineChartInternal(
        model = model,
        modifier = modifier,
        dataPoints = dataPoints,
        isSymmetric = isSymmetric,
        gridSegments = gridConfig.segments
    )
}

///**
// * 胸椎力学分布图表组件 - 便利函数，支持直接指定网格段数
// */
//@OptIn(ExperimentalMaterial3Api::class)
//@Composable
//fun ThoracicSpineChartWithSegments(
//    modifier: Modifier = Modifier,
//    dataPoints: List<SpineDataPoint> = generateSampleData(),
//    isSymmetric: Boolean = true,
//    gridSegments: Int = 4
//) {
//    ThoracicSpineChartInternal(
//        modifier = modifier,
//        dataPoints = dataPoints,
//        isSymmetric = isSymmetric,
//        gridSegments = gridSegments
//    )
//}

/**
 * 胸椎力学分布图表组件 - 内部实现
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ThoracicSpineChartInternal(
    model: TitleInfo,
    modifier: Modifier = Modifier,
    dataPoints: List<SpineDataPoint> = generateSampleData(),
    isSymmetric: Boolean = false,
    gridSegments: Int = 4
) {
    Card(
        modifier = modifier
            .fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color(0x80FFFFFF)
        )
    ) {
        Column(
            modifier = Modifier.fillMaxSize().padding(horizontal = 25.dp, vertical = 20.dp)
        ) {
            // 标题栏
            TitleSection(
                icon = model.icon,
                title = model.title,
                symmetricType = SymmetricType.Symmetric,
                isSymmetric = isSymmetric
            )
            
            Spacer(modifier = Modifier.height(10.dp))
            
            // 图表主体
            SpineDistributionChart(
                dataPoints = dataPoints,
                gridSegments = gridSegments,
                modifier = Modifier
                    .fillMaxSize()
            )
        }
    }
}

@Composable
private fun TitleSection(
    @DrawableRes icon: Int,
    title: String,
    symmetricType: SymmetricType,
    isSymmetric: Boolean
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标
            Icon(
                painter = painterResource(id = icon),
                contentDescription = "Spine Icon",
                tint = Color.Unspecified,
                modifier = Modifier.size(34.dp)
            )
            
            Spacer(modifier = Modifier.width(10.dp))
            
            Text(
                text = title,
                fontSize = 26.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF333333)
            )
        }
        
        Row(
            horizontalArrangement = Arrangement.End,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "单位：Kpa",
                fontSize = 16.sp,
                color = Color(0xFF666666)
            )
            
            Spacer(modifier = Modifier.width(10.dp))
            
            Box(
                modifier = Modifier
                    .clip(RoundedCornerShape(6.dp))
                    .background(
                        if (isSymmetric) Color(0xFF4CAF50) else Color(0xFFF39C12)
                    )
                    .padding(horizontal = 12.dp, vertical = 4.dp)
            ) {
                Text(
                    text = when (symmetricType) {
                        SymmetricType.Symmetric -> "对称"
                        SymmetricType.Symmetric_Left -> "左测偏大"
                        SymmetricType.Symmetric_Right -> "右测偏大"
                        else -> "基本对称"
                    },
                    color = Color(0xFFFFFFFF),
                    fontSize = 22.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
private fun SpineDistributionChart(
    dataPoints: List<SpineDataPoint>,
    gridSegments: Int = 4,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Y轴标签
        YAxisLabels(
            modifier = Modifier.width(40.dp)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 图表区域
        ChartArea(
            dataPoints = dataPoints,
            gridSegments = gridSegments,
            modifier = Modifier
                .weight(1f).padding(vertical = 12.dp) // 添加明确的高度约束
        )
    }
}

@Composable
internal fun YAxisLabels(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxHeight(),
        verticalArrangement = Arrangement.SpaceBetween,
        horizontalAlignment = Alignment.End
    ) {
        Text(
            text = "偏左",
            fontSize = 18.sp,
            color = Color(0xFF666666)
        )
        Text(
            text = "对称",
            fontSize = 18.sp,
            color = Color(0xFF666666),
            fontWeight = FontWeight.Medium
        )
        Text(
            text = "偏右",
            fontSize = 18.sp,
            color = Color(0xFF666666)
        )
    }
}

@Composable
private fun ChartArea(
    dataPoints: List<SpineDataPoint>,
    gridSegments: Int = 4,
    modifier: Modifier = Modifier
) {
    Canvas(
        modifier = modifier.fillMaxSize()
    ) {
        // 确保Canvas有有效尺寸后再绘制
        if (size.width > 0 && size.height > 0) {
            drawSpineChart(dataPoints, gridSegments)
        }
    }
}

private fun DrawScope.drawSpineChart(
    dataPoints: List<SpineDataPoint>,
    gridSegments: Int = 4
) {
    val chartHeight = size.height
    val chartWidth = size.width
    val centerY = chartHeight / 2f
    
    // 绘制基准线
    drawBaselines(chartWidth, chartHeight, centerY, gridSegments)
    
    // 绘制数据点
    drawDataPoints(dataPoints, chartWidth, centerY)
}

private fun DrawScope.drawBaselines(
    chartWidth: Float,
    chartHeight: Float,
    centerY: Float,
    gridSegments: Int = 4
) {
    val strokeWidth = 1.dp.toPx()
    
    // 定义不同类型的虚线样式
    val symmetryLineStyle = LineStyle.SOLID // 对称线：实线
    val otherLineStyle = LineStyle.DASH // 偏移线：虚线
    
    // 可配置的网格分割数生成网格线
    val linePositions = generateGridLines(chartHeight, gridSegments)
    val symmetryLineIndex = gridSegments / 2 // 中间线为对称线
    
    // 绘制基准线网格
    linePositions.forEachIndexed { index, y ->
        val isSymmetryLine = (index == symmetryLineIndex)
        drawLine(
            color = drawLineColor(index, isSymmetryLine, linePositions.size),
            start = Offset(0f, y),
            end = Offset(chartWidth, y),
            strokeWidth = strokeWidth,
            pathEffect = when {
                isSymmetryLine -> symmetryLineStyle.toPathEffect() // 对称线使用实线
                else -> otherLineStyle.toPathEffect() // 其他线使用虚线
            }
        )
    }
}

/**
 * 统一的线条颜色判断
 * @param index 线条索引（从0开始）
 * @param isSymmetryLine 是否为对称线
 * @param totalLineCount 总线条数
 */
fun drawLineColor(index: Int, isSymmetryLine: Boolean, totalLineCount: Int): Color {
    return when {
        isSymmetryLine -> lineColor                                    // 对称线（中间）：绿色
        index in listOf(0, totalLineCount - 1) -> endPointColor      // 边界线（顶部、底部）：红色
        else -> intervalColor                                         // 分割线：橙色
    }
}

/**
 * 网格配置数据类
 * @param segments 分割段数
 * @param showBoundaryLines 是否显示边界线
 * @param description 配置描述
 */
data class GridConfig(
    val segments: Int,
    val showBoundaryLines: Boolean = true,
    val description: String = ""
) {
    companion object {
        val SIMPLE = GridConfig(2, description = "简单网格：3根线")
        val STANDARD = GridConfig(4, description = "标准网格：5根线") 
        val DETAILED = GridConfig(6, description = "详细网格：7根线")
        val FINE = GridConfig(8, description = "精细网格：9根线")
        
        /**
         * 根据段数创建网格配置
         */
        fun fromSegments(segments: Int): GridConfig {
            return GridConfig(segments, description = "自定义网格：${segments + 1}根线")
        }
    }
}

/**
 * 生成网格线Y坐标
 * @param chartHeight 图表高度
 * @param segments 分割段数
 * @return 网格线Y坐标列表
 */
fun generateGridLines(chartHeight: Float, segments: Int): List<Float> {
    return (0..segments).map { i ->
        chartHeight * i.toFloat() / segments.toFloat()
    }
}

private fun DrawScope.drawDataPoints(
    dataPoints: List<SpineDataPoint>,
    chartWidth: Float,
    centerY: Float
) {
    val pointColor = Color(0xFF8669F0)
    val pointRadius = 10.dp.toPx()
    
    dataPoints.forEach { point ->
        // 将position(-1到1)映射到画布坐标
        val x = chartWidth * ((point.position + 1f) / 2f)
        
        // 根据position计算Y坐标偏移
        val yOffset = point.position * (centerY * 0.7f) // 限制偏移范围
        val y = centerY - yOffset
        
        // 绘制数据点
        drawCircle(
            color = pointColor,
            radius = pointRadius,
            center = Offset(x, y)
        )
        
//        // 绘制数据点外圈
//        drawCircle(
//            color = pointColor.copy(alpha = 0.3f),
//            radius = pointRadius * 1.5f,
//            center = Offset(x, y),
//            style = Stroke(width = 2.dp.toPx())
//        )
    }
}

/**
 * 生成示例数据
 */
fun generateSampleData(): List<SpineDataPoint> {
    return listOf(
        SpineDataPoint(position = -0.8f, value = 12.5f),
        SpineDataPoint(position = -0.4f, value = 12.2f),
        SpineDataPoint(position = -0.1f, value = 18.7f),
        SpineDataPoint(position = 0.2f, value = 16.3f),
        SpineDataPoint(position = 0.6f, value = 14.8f),
        SpineDataPoint(position = 0.8f, value = 11.2f)
    )
}

val lineColor = Color(0xFF07C160) // 中间线颜色
val endPointColor = Color(0xFFFF3333) // 两端线颜色
val intervalColor = Color(0xFFFF9933) // 其他线颜色

enum class SymmetricType {
    Symmetric,
    Symmetric_Left,
    Symmetric_Right
}

class TitleInfo(
    @DrawableRes var icon: Int,
    val title: String)

@Preview(showBackground = true, name = "标准网格")
@Composable 
fun ThoracicSpineChartPreview() {
    val model = TitleInfo(R.drawable.icon_seat_chest, "胸椎")
    ThoracicSpineChart(
        model = model,
        dataPoints = generateSampleData(),
        gridConfig = GridConfig.STANDARD
    )
}
//@Preview(showBackground = true, name = "简单网格")
//@Composable
//fun ThoracicSpineChartSimplePreview() = ThoracicSpineChart(
//    dataPoints = generateSampleData(),
//    gridConfig = GridConfig.SIMPLE
//)
//
//@Preview(showBackground = true, name = "详细网格")
//@Composable
//fun ThoracicSpineChartDetailedPreview() = ThoracicSpineChart(
//    dataPoints = generateSampleData(),
//    gridConfig = GridConfig.DETAILED
//)
//
//@Preview(showBackground = true, name = "精细网格")
//@Composable
//fun ThoracicSpineChartFinePreview() = ThoracicSpineChart(
//    dataPoints = generateSampleData(),
//    gridConfig = GridConfig.FINE
//)