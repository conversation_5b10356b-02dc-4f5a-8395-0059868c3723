package com.healthlink.hms.medchart.repository

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.medchart.api.ChatCompletionRequest
import com.healthlink.hms.medchart.api.LLMClient
import com.healthlink.hms.medchart.api.LoginRequest
import com.healthlink.hms.medchart.api.Message
import com.healthlink.hms.medchart.api.StreamingChatResponse
import com.healthlink.hms.medchart.util.ConfigManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import okio.IOException
import java.io.BufferedReader
import java.io.InputStreamReader

class LLMRepository(context: Context) {
    private val apiClient = LLMClient.getInstance(context)
    private val apiService = apiClient.llmApiService
    private val gson = Gson()
    private val TAG = "LLMRepository"
    
    // 用户登录
    suspend fun login(phone: Long = 13693401988): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
//                val request = LoginRequest(phone)
                val response = apiService.login(phone)
                
                if (response.isSuccessful) {
                    val loginResponse = response.body()
                    if (loginResponse != null && loginResponse.code == 200) {
                        ConfigManager.getInstance(HmsApplication.appContext).setToken(phone)
                        Result.success(loginResponse.data)
                    } else {
                        Result.failure(Exception("登录失败: ${loginResponse?.message ?: "未知错误"}"))
                    }
                } else {
                    Result.failure(Exception("登录失败: ${response.code()} ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    suspend fun getChatCompletion(
        messages: List<Message>,
    ): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                val request = ChatCompletionRequest("", messages.first().content,ConfigManager.getInstance(HmsApplication.appContext).getToken()!!, stream = true)
                val response = apiService.getChatCompletion(request)

                if (response.isSuccessful) {
                    val chatCompletion = response.body()
                    if (chatCompletion != null && chatCompletion.code == 200 &&
                        chatCompletion.data != null &&
                        chatCompletion.data.choices.isNotEmpty()) {
                        Result.success(chatCompletion.data.choices[0].message.content)
                    } else {
                        Result.failure(Exception("AI响应错误: ${chatCompletion?.message ?: "未知错误"}"))
                    }
                } else {
                    Result.failure(Exception("请求失败: ${response.code()} ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    suspend fun getChatCompletion(userPrompt: String, model: String = "gpt-4o"): Result<String> {
        val messages = listOf(Message("user", userPrompt))
        return getChatCompletion(messages.first().content, model)
    }
    
    // 流式聊天接口 - 优化版本
    fun getStreamingChatCompletion(
        messages: List<Message>,
    ): Flow<String> = flow {
        try {
            Log.d(TAG, "开始流式请求: $messages")
            val chatId = ConfigManager.getInstance(HmsApplication.appContext).getChatId()
            if (chatId != null) {
                Log.d(TAG, "当前chatId: $chatId")
            } else {
                Log.d(TAG, "未找到chatId")
            }
            val request = ChatCompletionRequest(chatId, messages.last().content,ConfigManager.getInstance(HmsApplication.appContext).getToken()!!, stream = true)
            val response = apiService.getStreamingChatCompletion(request)
            
            if (response.isSuccessful) {
                Log.d(TAG, "流式请求成功，开始处理响应数据")
                val responseBody = response.body() ?: throw IOException("响应体为空")
                
                // 使用Kotlin的使用范围函数更简洁地处理资源关闭
                val fullResponse = StringBuilder()

                responseBody.byteStream().bufferedReader().use { reader ->
                    val collectedLines = StringBuilder()

                    reader.lineSequence()
                        .filter { it.isNotEmpty() }
                        .forEach { line ->
                            try {
                                Log.d(TAG, "收到流式数据: $line")

                                // 检查是否包含结束标记
                                if (line.contains("[DONE]")) {
                                    // 将最后一行也添加到收集器中
                                    collectedLines.append(line)

                                    // 处理完整的数据
                                    val completeData = collectedLines.toString()
                                    Log.d(TAG, "完整的流式数据: $completeData")

                                    // 解析自定义格式的流式数据
                                    val parts = completeData.split("|||")
                                    if (parts.size >= 3) {
                                        val content = parts[0] // 第一部分是内容
                                        val chatId = parts[1]  // 第二部分是聊天ID
                                        val status = parts[2]  // 第三部分是状态

                                        // 存储chatId
                                        ConfigManager.getInstance(HmsApplication.appContext).setChatId(chatId)

                                        // 如果有内容则发送
                                        if (content.isNotEmpty()) {
                                            Log.d(TAG, "发送完整内容: $content")
                                            fullResponse.append(content)
                                            emit(content)
                                        }
                                    } else {
                                        Log.e(TAG, "流式数据格式错误: $completeData")
                                        emit("[错误] 流式数据格式错误")
                                    }

                                    // 重置收集器，准备接收下一组数据
                                    collectedLines.clear()

                                } else {
                                    // 非结束行，追加到收集器
                                    collectedLines.append(line)
                                }

                            } catch (e: Exception) {
                                Log.e(TAG, "解析流式数据错误: ${e.message}")
                                Log.e(TAG, "源数据: $line")
                                // 清空当前收集的数据
                                collectedLines.clear()
                                // 不中断处理，继续处理下一行
                            }
                        }
                }
                
                // 流结束但没有内容时，发送一个空字符串以通知完成
                if (fullResponse.isEmpty()) {
                    Log.d(TAG, "完整响应为空，发送空字符串")
                    emit("")
                } else {
                    Log.d(TAG, "完整响应: $fullResponse")
                }
            } else {
                val errorBody = response.errorBody()?.string() ?: "无错误信息"
                val errorMessage = "请求失败: ${response.code()} ${response.message()}, 错误信息: $errorBody"
                Log.e(TAG, errorMessage)
                emit("[错误] $errorMessage")
                throw IOException(errorMessage)
            }
        } catch (e: Exception) {
            Log.e(TAG, "流式聊天异常: ${e.message}", e)
            emit("[异常] ${e.message}")
            throw e
        }
    }
}