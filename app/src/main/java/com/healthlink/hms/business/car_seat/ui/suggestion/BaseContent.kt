package com.healthlink.hms.business.car_seat.ui.suggestion

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CornerSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ModalDrawer
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.healthlink.hms.R

// ------------------------自定义组合组件--------------------
/**
 * 实时压力组件
 */
@Composable
fun RealTimePressureContent(content:@Composable () -> Unit) = BaseCardContent("实时压力分布", R.drawable.icon_pressure_dis,content)


/**
 * 实时坐姿建议组件
 * content Text
 */
@Composable
fun RealTimePostureContent(content:@Composable () -> Unit) = BaseCardContent("实时坐姿建议", R.drawable.icon_posture,content)

/**
 * 车机功能实时推荐
 */
@Composable
fun CarMachineContent(content:@Composable () -> Unit) = BaseCardContent("腰背舒缓", R.drawable.icon_seat_waist,content)

/**
 * 通用卡片
 */
@Composable
fun CardCommonContent(title: String, @DrawableRes icon: Int, content:@Composable () -> Unit) = BaseCardContent(title, icon,content)

// ------------------------辅助小组件--------------------
/**
 * header + content
 */
@Composable
fun BaseCardContent(title: String, @DrawableRes icon: Int, content: @Composable () -> Unit){
Box(modifier = Modifier
    .clip(RoundedCornerShape(CornerSize(20.dp)))
    .background(Color(0x80FFFFFF))
    .wrapContentSize()
    .padding(start = 25.dp, end = 25.dp, top = 20.dp, bottom = 30.dp)
) {
        Column(modifier = Modifier.wrapContentSize(), horizontalAlignment = Alignment.CenterHorizontally) {
            HeaderContainer(title,icon)
            Spacer(modifier = Modifier.height(30.dp))
            content()
        }
    }
}

/**
 * 实时压力header
 */
@Composable
fun HeaderContainer(title: String = "实时压力分布", @DrawableRes icon: Int = R.drawable.icon_pressure_dis) {
    Column(modifier = Modifier
        .fillMaxWidth().height(74.dp),
        verticalArrangement = Arrangement.Top
    ) {
        HeaderIconTitle(icon = icon,title = title,modifier = Modifier.weight(1f))
        HorizontalDivider(modifier = Modifier.height(1.dp).background(Color(0x33999999)))
    }
}

/*
实时压力title
 */
@Composable
fun HeaderIconTitle(@DrawableRes icon: Int = R.drawable.icon_pressure_dis,title: String, modifier: Modifier,fontSize: TextUnit = 26.sp) {
    Row(verticalAlignment = Alignment.CenterVertically, modifier = modifier.wrapContentSize()) {
        Icon(painter = painterResource(id = icon), contentDescription = "icon", modifier = Modifier.size(34.dp),tint = Color.Unspecified)
        Text(text = title, modifier = Modifier.padding(start = 10.dp), color = Color(0xFF333333), fontSize = fontSize)
    }
}

@Preview(showBackground = true, device = "spec:width=1920px,height=1128px,dpi=240")
@Composable
fun CheckResultPreview() {
    pressStateContent()
}

/**
 * 行程结束压力分布状况
 */
@Composable
fun pressStateContent() {
    Column(modifier = Modifier.fillMaxSize().padding(top = 90.dp), verticalArrangement = Arrangement.Top) {
        Row(horizontalArrangement = Arrangement.SpaceBetween, modifier = Modifier.fillMaxWidth().wrapContentHeight()) {
            Icon(painter = painterResource(id = R.drawable.icon_left_tooltip), contentDescription = "left_tooltip", modifier = Modifier.size(90.dp,56.dp),tint = Color.Unspecified)
            Icon(painter = painterResource(id = R.drawable.icon_right_tooltip), contentDescription = "right_tooltip", modifier = Modifier.size(90.dp,56.dp),tint = Color.Unspecified)
        }
        Spacer(modifier = Modifier.height(40.dp))
        // 胸椎对称
        Box(modifier = Modifier.height(120.dp).wrapContentSize()){
            Row(verticalAlignment = Alignment.CenterVertically){
                Row(modifier = Modifier.width(130.dp).fillMaxHeight(), verticalAlignment = Alignment.CenterVertically){
                    Icon(painter = painterResource(id = R.drawable.icon_seat_chest), contentDescription = "title", modifier = Modifier.size(34.dp),tint = Color.Unspecified)
                    Text(text = "胸椎", modifier = Modifier.padding(start = 10.dp), color = Color(0xFF333333), fontSize = 26.sp, fontWeight = FontWeight.Bold)
                }

                Spacer(modifier = Modifier.width(5.dp))
                Box(modifier = Modifier.wrapContentSize()){
                    // 背景图片
                    Icon(painter = painterResource(id = R.drawable.img_bg_chest), contentDescription = "title", modifier = Modifier.size(754.dp,120.dp),tint = Color.Unspecified)
                    Row(modifier = Modifier.matchParentSize().wrapContentHeight().padding(horizontal = 34.dp),
                        horizontalArrangement = Arrangement.SpaceBetween, verticalAlignment = Alignment.CenterVertically){
                        Column(horizontalAlignment = Alignment.Start, modifier = Modifier.padding(top = 22.dp).wrapContentSize()) {
                            Icon(painter = painterResource(R.drawable.img_green_arrow_left), modifier = Modifier.wrapContentWidth().height(54.dp), contentDescription = null, tint = Color.Unspecified)
                            Text("20Kpa", fontSize = 20.sp, color = Color(0xFF333333))
                        }

                        Column(horizontalAlignment = Alignment.End, modifier = Modifier.padding(top = 22.dp).wrapContentSize()) {
                            Icon(painter = painterResource(R.drawable.img_green_arrow_left), modifier = Modifier.wrapContentWidth().height(54.dp).scale(-1f,1f),contentDescription = null, tint = Color.Unspecified)
                            Text("19Kpa", fontSize = 20.sp, color = Color(0xFF333333))
                        }
                    }
                }
                Spacer(modifier = Modifier.width(5.dp))
                Box(modifier = Modifier.width(width = 120.dp)) {
                    Column(modifier = Modifier.fillMaxWidth(), verticalArrangement = Arrangement.Center, horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(text = "对称",
                            modifier = Modifier.background(
                                color = Color(0xFF07C160),
                                shape = RoundedCornerShape(6.dp))
                                .padding(horizontal = 10.dp, vertical = 5.dp),
                            color = Color(0xFFFFFFFF), fontSize = 22.sp)
                    }
                }

            }
        }
        Spacer(modifier = Modifier.height(40.dp))
        // 腰椎
        Box(modifier = Modifier.height(120.dp).wrapContentSize()){
            Row(verticalAlignment = Alignment.CenterVertically){
                Row(modifier = Modifier.width(130.dp).fillMaxHeight(), verticalAlignment = Alignment.CenterVertically){
                    Icon(painter = painterResource(id = R.drawable.icon_seat_waist), contentDescription = "title", modifier = Modifier.size(34.dp),tint = Color.Unspecified)
                    Text(text = "腰椎", modifier = Modifier.padding(start = 10.dp), color = Color(0xFF333333), fontSize = 26.sp, fontWeight = FontWeight.Bold)
                }

                Spacer(modifier = Modifier.width(5.dp))
                Box(modifier = Modifier.wrapContentSize()){
                    // 背景图片
                    Icon(painter = painterResource(id = R.drawable.img_bg_waist), contentDescription = "title", modifier = Modifier.size(754.dp,120.dp),tint = Color.Unspecified)
                    Row(modifier = Modifier.matchParentSize().wrapContentHeight().padding(horizontal = 34.dp),
                        horizontalArrangement = Arrangement.SpaceBetween, verticalAlignment = Alignment.CenterVertically){
                        Column(horizontalAlignment = Alignment.Start, modifier = Modifier.padding(top = 22.dp).wrapContentSize()) {
                            Icon(painter = painterResource(R.drawable.img_yellow_arrow_left), modifier = Modifier.wrapContentWidth().height(54.dp), contentDescription = null, tint = Color.Unspecified)
                            Text("33Kpa", fontSize = 20.sp, color = Color(0xFF333333))
                        }

                        Column(horizontalAlignment = Alignment.End, modifier = Modifier.padding(top = 22.dp).wrapContentSize()) {
                            Icon(painter = painterResource(R.drawable.img_green_arrow_left), modifier = Modifier.wrapContentWidth().height(54.dp).scale(-1f,1f),contentDescription = null, tint = Color.Unspecified)
                            Text("26Kpa", fontSize = 20.sp, color = Color(0xFF333333))
                        }
                    }
                }
                Spacer(modifier = Modifier.width(5.dp))
                Box(modifier = Modifier.width(width = 120.dp)) {
                    Column(modifier = Modifier.fillMaxWidth(), verticalArrangement = Arrangement.Center, horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(text = "左侧偏大",
                            modifier = Modifier.background(
                                color = Color(0xFFFF9933),
                                shape = RoundedCornerShape(6.dp))
                                .padding(horizontal = 10.dp, vertical = 5.dp),
                            color = Color(0xFFFFFFFF), fontSize = 22.sp)
                    }
                }

            }
        }
        Spacer(modifier = Modifier.height(40.dp))
        // 臀腿段
        Box(modifier = Modifier.height(120.dp).wrapContentSize()){
            Row(verticalAlignment = Alignment.CenterVertically){
                Row(modifier = Modifier.width(130.dp).fillMaxHeight(), verticalAlignment = Alignment.CenterVertically){
                    Icon(painter = painterResource(id = R.drawable.icon_seat_buttom), contentDescription = "title", modifier = Modifier.size(34.dp),tint = Color.Unspecified)
                    Text(text = "臀腿段", modifier = Modifier.padding(start = 10.dp), color = Color(0xFF333333), fontSize = 26.sp, fontWeight = FontWeight.Bold)
                }

                Spacer(modifier = Modifier.width(5.dp))
                Box(modifier = Modifier.wrapContentSize()){
                    // 背景图片
                    Icon(painter = painterResource(id = R.drawable.img_bg_buttom), contentDescription = "title", modifier = Modifier.size(754.dp,120.dp),tint = Color.Unspecified)
                    Row(modifier = Modifier.matchParentSize().wrapContentHeight().padding(horizontal = 34.dp),
                        horizontalArrangement = Arrangement.SpaceBetween, verticalAlignment = Alignment.CenterVertically){
                        Column(horizontalAlignment = Alignment.Start, modifier = Modifier.padding(top = 22.dp).wrapContentSize()) {
                            Icon(painter = painterResource(R.drawable.img_green_arrow_left), modifier = Modifier.wrapContentWidth().height(54.dp), contentDescription = null, tint = Color.Unspecified)
                            Text("20Kpa", fontSize = 20.sp, color = Color(0xFF333333))
                        }

                        Column(horizontalAlignment = Alignment.End, modifier = Modifier.padding(top = 22.dp).wrapContentSize()) {
                            Icon(painter = painterResource(R.drawable.img_red_arrow_left), modifier = Modifier.wrapContentWidth().height(54.dp).scale(-1f,1f),contentDescription = null, tint = Color.Unspecified)
                            Text("110Kpa", fontSize = 20.sp, color = Color(0xFF333333))
                        }
                    }
                }
                Spacer(modifier = Modifier.width(5.dp))
                Box(modifier = Modifier.width(width = 120.dp)) {
                    Column(modifier = Modifier.fillMaxWidth(), verticalArrangement = Arrangement.Center, horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(text = "右侧偏大",
                            modifier = Modifier.background(
                                color = Color(0xFFFF3333),
                                shape = RoundedCornerShape(6.dp))
                                .padding(horizontal = 10.dp, vertical = 5.dp),
                            color = Color(0xFFFFFFFF), fontSize = 22.sp)
                    }
                }

            }
        }
    }
}

@Composable
fun TripResultItem(){
    Box(modifier = Modifier.height(120.dp).wrapContentSize()){
        Row(verticalAlignment = Alignment.CenterVertically){
            Row(modifier = Modifier.width(130.dp).fillMaxHeight(), verticalAlignment = Alignment.CenterVertically){
                Icon(painter = painterResource(id = R.drawable.icon_seat_chest), contentDescription = "title", modifier = Modifier.size(34.dp),tint = Color.Unspecified)
                Text(text = "胸椎", modifier = Modifier.padding(start = 10.dp), color = Color(0xFF333333), fontSize = 26.sp, fontWeight = FontWeight.Bold)
            }

            Spacer(modifier = Modifier.width(5.dp))
            Box(modifier = Modifier.wrapContentSize()){
                // 背景图片
                Icon(painter = painterResource(id = R.drawable.img_bg_chest), contentDescription = "title", modifier = Modifier.size(754.dp,120.dp),tint = Color.Unspecified)
                Row(modifier = Modifier.matchParentSize().wrapContentHeight().padding(horizontal = 34.dp),
                    horizontalArrangement = Arrangement.SpaceBetween, verticalAlignment = Alignment.CenterVertically){
                    Column(horizontalAlignment = Alignment.Start, modifier = Modifier.padding(top = 22.dp).wrapContentSize()) {
                        Icon(painter = painterResource(R.drawable.img_green_arrow_left), modifier = Modifier.wrapContentWidth().height(54.dp), contentDescription = null, tint = Color.Unspecified)
                        Text("20Kpa", fontSize = 20.sp, color = Color(0xFF333333))
                    }

                    Column(horizontalAlignment = Alignment.End, modifier = Modifier.padding(top = 22.dp).wrapContentSize()) {
                        Icon(painter = painterResource(R.drawable.img_green_arrow_left), modifier = Modifier.wrapContentWidth().height(54.dp).scale(-1f,1f),contentDescription = null, tint = Color.Unspecified)
                        Text("19Kpa", fontSize = 20.sp, color = Color(0xFF333333))
                    }
                }
            }
            Spacer(modifier = Modifier.width(5.dp))
            Box(modifier = Modifier.width(width = 120.dp)) {
                Column(modifier = Modifier.fillMaxWidth(), verticalArrangement = Arrangement.Center, horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(text = "对称",
                        modifier = Modifier.background(
                            color = Color(0xFF07C160),
                            shape = RoundedCornerShape(6.dp))
                            .padding(horizontal = 13.dp, vertical = 5.dp),
                        color = Color(0xFFFFFFFF), fontSize = 22.sp)
                }
            }

        }
    }
}

data class TripResultItem(@DrawableRes val icon: Int,val title: String,@DrawableRes val bg: Int)

@Composable
fun TripResultItemNormal(@DrawableRes icon:Int,title: String,@DrawableRes bg:Int){

}

/**
 * 触发行程容器
 */
@Composable
fun TriggerTripContainer(title: String = "开始", @DrawableRes icon: Int = R.drawable.icon_start, colors: ButtonColors = ButtonDefaults.buttonColors(), onClick: () -> Unit = {}, message: String = "", modifier: Modifier) {
    Box(modifier = Modifier) {
        Row(verticalAlignment = Alignment.CenterVertically, modifier = modifier.fillMaxWidth().wrapContentHeight()) {
            TriggerTripButton(title = title, icon = icon, colors = colors,onClick, modifier = Modifier.wrapContentSize())
            Text(text = message, modifier = Modifier.padding(start = 20.dp), color = Color(0xFF333333), fontSize = 26.sp)
        }
    }
}

/**
 * 带图片的按钮
 */
@Composable
fun TriggerTripButton(title: String = "开始", @DrawableRes icon: Int = R.drawable.icon_start,colors: ButtonColors = ButtonDefaults.buttonColors(),onClick: () -> Unit = {}, modifier: Modifier){
    Button(onClick = onClick, modifier = modifier, colors = colors, shape = RoundedCornerShape(10.dp)) {
        Icon(painter = painterResource(id = icon), contentDescription = "title", tint = Color.Unspecified)
        Text(text = title, modifier = Modifier.padding(start = 10.dp), color = Color(0xFFFFFFFF), fontSize = 24.sp)
    }
}

// ------------------------布局容器--------------------
@Composable
fun BaseContent(leftContent: @Composable () -> Unit, rightContent: @Composable () -> Unit) {
    Box(
        modifier = Modifier
        .padding(start = 68.dp,top = 20.dp, end = 68.dp,bottom = 30.dp)
        .fillMaxSize()
    ) {
        Row(modifier = Modifier.fillMaxSize()) {
            LeftContainer(leftContent, modifier = Modifier.weight(1.5f))
            Spacer(modifier = Modifier.width(30.dp))
            RightContainer(rightContent, modifier = Modifier.weight(1f))
        }
    }
}

@Composable
fun LeftContainer(leftContent: @Composable () -> Unit, modifier: Modifier){
    Box(modifier = modifier.fillMaxSize()) {
        leftContent.invoke()
    }
}

@Composable
fun RightContainer(rightContent: @Composable () -> Unit, modifier: Modifier){
    Box(modifier = modifier.fillMaxSize()) {
        Column(modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Top) {
            rightContent.invoke()
        }
    }
}

//@Preview(showBackground = true,
//    device = "spec:width=1920px,height=1128px,dpi=240"
//)
//@Composable
//fun BaseContentPreview() {
//    val leftContent: @Composable () -> Unit = {
//        TriggerTripContainer(
//            title= "开始",
//            icon = R.drawable.icon_start,
//            colors = ButtonDefaults.buttonColors(
//                containerColor = Color(0xFF32C5FF)
//            ),
//            onClick = {}, message = "提示信息", modifier = Modifier
//            .fillMaxWidth()
//            .height(64.dp))
//    }
//
//    val rightContent: @Composable () -> Unit = {
//        // 实时坐姿建议
//        RealTimePostureContent {
//            Text("请向右侧稍倾斜身体，使脊柱保持在中立位",
//                modifier = Modifier.fillMaxWidth().wrapContentHeight(),
//                fontSize = 24.sp,
//                color = Color(0xFF333333),
//            )
//        }
//        Spacer(modifier = Modifier.height(30.dp))
//        // 腰背舒缓
//        CarMachineContent{
//            Column(modifier = Modifier.fillMaxSize()) {
//                Text("长时间开车注意调整坐姿或者座椅哦")
//                Row {
//                    Button(onClick = {},
//                        colors = ButtonDefaults.buttonColors(
//                            containerColor = Color(0xFF07C160)
//                        ),
//                        shape = RoundedCornerShape(10.dp),
//                        modifier = Modifier.size(280.dp,64.dp)){
//                        Text("座椅调整")
//                    }
//
//                    Spacer(modifier = Modifier.width(20.dp))
//
//                    Button(onClick = {},
//                        colors = ButtonDefaults.buttonColors(
//                            containerColor = Color(0xFF32C5FF)
//                        ),
//                        shape = RoundedCornerShape(10.dp),
//                        modifier = Modifier.size(280.dp,64.dp)){
//                        Text("打开腰托")
//                    }
//                }
//            }
//        }
//
//    }
//    BaseContent(leftContent, rightContent)
//}