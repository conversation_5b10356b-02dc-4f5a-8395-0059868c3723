package com.healthlink.hms.business.car_seat.connect

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import dagger.hilt.android.qualifiers.ApplicationContext
import data.ControlAction
import data.ControlMessage
import data.ResponseMessage
import data.SensorMessage
import data.WebSocketMessage
import data.ConnectionState
import data.DetailedConnectionState
import data.SensorPicMessage
import io.ktor.client.HttpClient
import io.ktor.client.engine.cio.CIO
import io.ktor.client.plugins.logging.LogLevel
import io.ktor.client.plugins.logging.Logging
import io.ktor.client.plugins.websocket.DefaultClientWebSocketSession
import io.ktor.client.plugins.websocket.WebSockets
import io.ktor.client.plugins.websocket.pingInterval
import io.ktor.client.plugins.websocket.webSocket
import io.ktor.http.HttpMethod
import io.ktor.websocket.Frame
import io.ktor.websocket.close
import io.ktor.websocket.readText
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.serialization.json.Json
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.seconds

// 数据模型 - 与服务端共享
// 将上面的消息模型代码复制到Android项目中

// WebSocket管理器
@Singleton
class SeatWSManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val json: Json
) {

    companion object {
        private const val TAG = "SeatWSManager"
    }

    // 控制连接
    private var controlClient: HttpClient? = null
    private var controlSession: DefaultClientWebSocketSession? = null

    // 传感器连接
    private var sensorClient: HttpClient? = null
    private var sensorSession: DefaultClientWebSocketSession? = null

    // 连接状态
    private val _controlConnectionState = MutableStateFlow(DetailedConnectionState())
    val controlConnectionState = _controlConnectionState.asStateFlow()

    private val _sensorConnectionState = MutableStateFlow(DetailedConnectionState())
    val sensorConnectionState = _sensorConnectionState.asStateFlow()

    // 消息流
    private val _controlMessages = MutableSharedFlow<WebSocketMessage>(
        replay = 0, extraBufferCapacity = 100, onBufferOverflow = BufferOverflow.DROP_OLDEST
    )
    val controlMessages = _controlMessages.asSharedFlow()

    // 传感器数据
    private val _sensorMessages = MutableSharedFlow<WebSocketMessage>(
        replay = 0,
        extraBufferCapacity = 1000, // 传感器数据量大，增加缓冲区
        onBufferOverflow = BufferOverflow.DROP_OLDEST)
    val sensorMessages = _sensorMessages.asSharedFlow()

    // 错误流
    private val _errors = MutableSharedFlow<String>()
    val errors = _errors.asSharedFlow()

    // 重连配置
    private val maxReconnectAttempts = 5
    private val reconnectDelayMs = 3000L

    /**
     * 连接到控制端点
     */
    suspend fun connectControl(serverUrl: String, userId: String) {
        try {
            updateControlState { it.copy(state = ConnectionState.CONNECTING) }

            controlClient = HttpClient(CIO) {
                install(WebSockets) {
                    pingInterval = 15.seconds
                }
                install(Logging) {
                    level = LogLevel.INFO
                }
            }

            controlClient?.webSocket(
                method = HttpMethod.Get,
                host = serverUrl.substringAfter("://").substringBefore(":"),
                port = serverUrl.substringAfterLast(":").toInt(),
                path = "/ws/control?id=$userId"
            ) {
                controlSession = this
                val connectTime = System.currentTimeMillis()
                updateControlState {
                    it.copy(
                        state = ConnectionState.CONNECTED,
                        lastConnectedTime = connectTime,
                        reconnectAttempts = 0,
                        error = null
                    )
                }

                // 监听消息
                try {
                    for (frame in incoming) {
                        when (frame) {
                            is Frame.Text -> {
                                Log.i(TAG, "client: 收到控制消息: ${frame.readText()}")
                                try {
                                    val message = json.decodeFromString<ResponseMessage>(frame.readText())
                                    _controlMessages.emit(message)
                                } catch (e: Exception) {
                                    Log.e(TAG, "控制消息解析失败: ${e.message}", e)
                                    _errors.emit("控制消息解析失败: ${e.message}")
                                }
                            }
                            is Frame.Close -> {
                                Log.i(TAG, "client: 控制连接关闭")
                                updateControlState {
                                    it.copy(
                                        state = ConnectionState.CLOSED,
                                        connectionDuration = System.currentTimeMillis() - connectTime
                                    )
                                }
                                break
                            }
                            else -> {}
                        }
                    }
                } catch (e: Exception) {
                    updateControlState {
                        it.copy(
                            state = ConnectionState.FAILED,
                            error = e.message,
                            connectionDuration = System.currentTimeMillis() - connectTime
                        )
                    }
                    throw e
                }
            }
        } catch (e: Exception) {
            updateControlState {
                it.copy(
                    state = ConnectionState.FAILED,
                    error = "控制连接失败: ${e.message}"
                )
            }
            _errors.emit("控制连接失败: ${e.message}")

            // 自动重连
            scheduleControlReconnect(serverUrl, userId)
        }
    }
    /**
     * 连接到传感器端点
     */
    suspend fun connectSensor(serverUrl: String, deviceId: String) {
        try {
            updateSensorState { it.copy(state = ConnectionState.CONNECTING) }

            sensorClient = HttpClient(CIO) {
                install(WebSockets) {
                    pingInterval = 30000.milliseconds
                }
                install(Logging) {
                    level = LogLevel.INFO
                }
            }

            sensorClient?.webSocket(
                method = HttpMethod.Get,
                host = serverUrl.substringAfter("://").substringBefore(":"),
                port = serverUrl.substringAfterLast(":").toInt(),
                path = "/ws"
            ) {
                sensorSession = this
                val connectTime = System.currentTimeMillis()
                updateSensorState {
                    it.copy(
                        state = ConnectionState.CONNECTED,
                        lastConnectedTime = connectTime,
                        reconnectAttempts = 0,
                        error = null
                    )
                }

                // 监听消息
                try {
                    for (frame in incoming) {
                        when (frame) {
                            is Frame.Text -> {
                                try {
                                    val readText = frame.readText()
                                    Log.i(TAG, "client: 收到传感器消息: $readText")
                                    val message = Gson().fromJson(readText, SensorPicMessage::class.java)//json.decodeFromString<SensorPicMessage>(readText)
                                    _sensorMessages.emit(message)
                                } catch (e: Exception) {
                                    _errors.emit("传感器消息解析失败: ${e.message}")
                                }
                            }
                            is Frame.Close -> {
                                updateSensorState {
                                    it.copy(
                                        state = ConnectionState.CLOSED,
                                        connectionDuration = System.currentTimeMillis() - connectTime
                                    )
                                }
                                break
                            }
                            else -> {}
                        }
                    }
                } catch (e: Exception) {
                    updateSensorState {
                        it.copy(
                            state = ConnectionState.FAILED,
                            error = e.message,
                            connectionDuration = System.currentTimeMillis() - connectTime
                        )
                    }
                    throw e
                }
            }
        } catch (e: Exception) {
            updateSensorState {
                it.copy(
                    state = ConnectionState.FAILED,
                    error = "传感器连接失败: ${e.message}"
                )
            }
            _errors.emit("传感器连接失败: ${e.message}")

            // 自动重连
            scheduleSensorReconnect(serverUrl, deviceId)
        }
    }

    /**
     * 更新控制连接状态
     */
    private fun updateControlState(update: (DetailedConnectionState) -> DetailedConnectionState) {
        _controlConnectionState.value = update(_controlConnectionState.value)
    }

    /**
     * 更新传感器连接状态
     */
    private fun updateSensorState(update: (DetailedConnectionState) -> DetailedConnectionState) {
        _sensorConnectionState.value = update(_sensorConnectionState.value)
    }

    /**
     * 控制连接重连调度
     */
    suspend fun scheduleControlReconnect(serverUrl: String, userId: String) {
        val currentState = _controlConnectionState.value
        if (currentState.reconnectAttempts < maxReconnectAttempts) {
            updateControlState {
                it.copy(
                    state = ConnectionState.RECONNECTING,
                    reconnectAttempts = it.reconnectAttempts + 1
                )
            }

            delay(reconnectDelayMs * (currentState.reconnectAttempts + 1)) // 指数退避
            connectControl(serverUrl, userId)
        } else {
            updateControlState {
                it.copy(
                    state = ConnectionState.FAILED,
                    error = "重连次数超过最大限制"
                )
            }
        }
    }

    /**
     * 传感器连接重连调度
     */
    private suspend fun scheduleSensorReconnect(serverUrl: String, deviceId: String) {
        val currentState = _sensorConnectionState.value
        if (currentState.reconnectAttempts < maxReconnectAttempts) {
            updateSensorState {
                it.copy(
                    state = ConnectionState.RECONNECTING,
                    reconnectAttempts = it.reconnectAttempts + 1
                )
            }

            delay(reconnectDelayMs * (currentState.reconnectAttempts + 1))
            connectSensor(serverUrl, deviceId)
        } else {
            updateSensorState {
                it.copy(
                    state = ConnectionState.FAILED,
                    error = "重连次数超过最大限制"
                )
            }
        }
    }

    /**
     * 控制连接心跳
     */
    private suspend fun startControlHeartbeat() {
        while (controlConnectionState.value.isConnected) {
            delay(1500) // 30秒心跳间隔
            try {
                sendControlMessage(
                    ControlMessage(
                        type = "control_heartbeat",
                        action = ControlAction.HEARTBEAT
                    )
                )
            } catch (e: Exception) {
                _errors.emit("心跳发送失败: ${e.message}")
                break
            }
        }
    }

    /**
     * 发送控制消息
     */
    suspend fun sendControlMessage(message: ControlMessage) {
        Log.i(TAG, "sendControlMessage: $message")
        try {
            if (!controlConnectionState.value.isConnected) {
                throw IllegalStateException("控制连接未建立")
            }

            val jsonMessage = json.encodeToString(message)
            controlSession?.send(Frame.Text(jsonMessage))
        } catch (e: Exception) {
            _errors.emit("发送控制消息失败: ${e.message}")
            throw e
        }
    }

    /**
     * 发送传感器数据
     */
    suspend fun sendSensorData(message: SensorMessage) {
        try {
            if (!sensorConnectionState.value.isConnected) {
                throw IllegalStateException("传感器连接未建立")
            }

            val jsonMessage = json.encodeToString(message)
            sensorSession?.send(Frame.Text(jsonMessage))
        } catch (e: Exception) {
            _errors.emit("发送传感器数据失败: ${e.message}")
            throw e
        }
    }

    /**
     * 手动重连
     */
//    suspend fun reconnect(serverUrl: String, userId: String, deviceId: String) {
//        disconnect()
//        delay(1000) // 等待清理完成
//        launch {
//            connectControl(serverUrl, userId)
//        }
//        launch {
//            connectSensor(serverUrl, deviceId)
//        }
//    }

    /**
     * 断开所有连接
     */
    suspend fun disconnect() {
        try {
            controlSession?.close()
            sensorSession?.close()
            controlClient?.close()
            sensorClient?.close()
        } catch (e: Exception) {
            _errors.emit("断开连接时发生错误: ${e.message}")
        } finally {
            updateControlState {
                it.copy(
                    state = ConnectionState.DISCONNECTED,
                    connectionDuration = if (it.lastConnectedTime != null) {
                        System.currentTimeMillis() - it.lastConnectedTime
                    } else 0L
                )
            }
            updateSensorState {
                it.copy(
                    state = ConnectionState.DISCONNECTED,
                    connectionDuration = if (it.lastConnectedTime != null) {
                        System.currentTimeMillis() - it.lastConnectedTime
                    } else 0L
                )
            }
        }
    }
}