package com.healthlink.hms.business.medai.view

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.healthlink.hms.R

@Composable
fun HealthDataCardContent(
    title: String,
    healthItems: List<MedAIChatItem.HealthDataCard.HealthItem>
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight() // 明确使用wrapContentHeight()
            .background(
                color = Color(0x4DFFFFFF),
                shape = RoundedCornerShape(12.dp)
            )
            .border(1.dp, Color(0xFFFFFFFF), shape = RoundedCornerShape(12.dp))
            .padding(16.dp)
    ) {
        // 标题
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontSize = 20.sp,
            color = Color(0xFF333333),
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // 由于项目固定且较少，直接使用Column而不是LazyColumn
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            healthItems.forEach { item ->
                HealthItemRow(
                    iconResId = item.iconResId,
                    name = item.name,
                    value = item.value,
                    unit = item.unit,
                    status = item.status,
                    isAbnormal = item.isAbnormal
                )
            }
        }
    }
}

@Composable
fun HealthItemRow(
    iconResId: Int,
    name: String,
    value: String,
    unit: String,
    status: String,
    isAbnormal: Boolean
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = Color(0xFFFFFFFF),
                shape = RoundedCornerShape(10.dp)
            )
            .padding(horizontal = 20.dp, vertical = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 图标
        Image(
            painter = painterResource(id = iconResId),
            contentDescription = name,
            modifier = Modifier.size(32.dp)
        )

        Spacer(modifier = Modifier.width(9.dp))

        // 名称
        Text(
            text = name,
            fontSize = 20.sp,
            color = Color(0xFF333333)
        )

        Spacer(modifier = Modifier.width(24.dp))

        // 数值
        Text(
            text = value,
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF333333)
        )

        Spacer(modifier = Modifier.width(4.dp))

        // 单位
        Text(
            text = unit,
            fontSize = 20.sp,
            color = Color(0xFF666666)
        )

        Spacer(modifier = Modifier.weight(1f))

        // 状态标签
        Text(
            text = status,
            fontSize = 18.sp,
            modifier = Modifier
                .background(
                    color = if (isAbnormal) Color(0xFFFF3333) else Color(0xFF43A047),
                    shape = RoundedCornerShape(6.dp)
                )
                .padding(horizontal = 10.dp, vertical = 6.dp),
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFFFFFFFF)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun HealthDataCardPreview() {
    HealthDataCardContent(
        title = "健康数据",
        healthItems = listOf(
            MedAIChatItem.HealthDataCard.HealthItem(
                iconResId = R.drawable.card_icon_heart,
                name = "心率",
                value = "70",
                unit = "bpm",
                status = "正常",
                isAbnormal = true
            )
        )
    )
}