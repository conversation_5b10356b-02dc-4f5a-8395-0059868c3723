package com.healthlink.hms.business.car_seat.ui.spinecare

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import com.healthlink.hms.R
import com.healthlink.hms.business.car_seat.anim.RotatableIcon
import com.healthlink.hms.business.car_seat.ui.adjustment.*
import com.healthlink.hms.business.car_seat.viewModel.SeatViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * 座椅调整
 */
@AndroidEntryPoint
class SeatAdjustmentFragment : Fragment() {

    private val sensorViewModel: SeatViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                SeatAdjustmentScreen()
            }
        }
    }

    companion object {
        fun newInstance() = SeatAdjustmentFragment()
        const val TAG = "SeatAdjustmentFragment"
    }
}

@Composable
fun SeatAdjustmentScreen() {
    // 创建一个共享的动画触发状态
    val seatAnimationTrigger = remember { mutableStateOf(false) }
    
    Row(modifier = Modifier.fillMaxSize().padding(start = 68.dp,top = 20.dp, end = 68.dp,bottom = 30.dp)) {
        // 左边
        Box(modifier = Modifier.wrapContentSize().weight(1f)) {
            // 座椅阴影
            Icon(painter = painterResource(id = R.drawable.img_chair_seat_shadow),
                contentDescription = "座椅阴影", modifier = Modifier.wrapContentSize(),
                tint = Color.Unspecified)

            // 座椅靠背
//            Icon(
//                painter = painterResource(id = R.drawable.img_chair_seat_back),
//                contentDescription = "座椅靠背", modifier = Modifier.wrapContentSize(),
//                tint = Color.Unspecified)

            RotatableIcon(
                painter = painterResource(id = R.drawable.img_chair_seat_back),
                contentDescription = "座椅靠背",
                modifier = Modifier.wrapContentSize(),
                tint = Color.Unspecified,
                rotationAngle = -10f,  // 设置较小的旋转角度以模拟座椅调整
                animationDuration = 500,  // 调整动画时长
                repeatCount = 3,  // 增加重复次数
                triggerAnimation = seatAnimationTrigger.value  // 使用状态控制动画触发
            )

            // 座椅座垫
            Icon(
                painter = painterResource(id = R.drawable.img_chair_seat),
                contentDescription = "座椅座垫", modifier = Modifier.wrapContentSize(),
                tint = Color.Unspecified)


            // 座垫底部
            Icon(painter = painterResource(id = R.drawable.img_chair_seat_bottom),
                contentDescription = "座垫底部", modifier = Modifier.wrapContentSize(),
                tint = Color.Unspecified)

            // 方向盘
            Icon(painter = painterResource(id = R.drawable.img_steering_wheel),
                contentDescription = "方向盘", modifier = Modifier.wrapContentSize(),
                tint = Color.Unspecified)

        }

        // 右边
        Column(modifier = Modifier.fillMaxSize().weight(1f), verticalArrangement = Arrangement.Top){
            // 当前驾驶模式
            CurDriverModeContainer()
            Spacer(modifier = Modifier.height(30.dp))
            // 切换用户
            CurrentUserContainer(seatAnimationTrigger = seatAnimationTrigger)
            Spacer(modifier = Modifier.height(30.dp))
            // 新增游客
            AddGuestButton()
        }
    }
}



@Preview(showBackground = true,
    device = "spec:width=1920px,height=1128px,dpi=240"
)
@Composable
fun SeatAdjustmentScreenPreview() {
    SeatAdjustmentScreen()
}
