package com.healthlink.hms.business.medai.adapter

import android.view.ViewGroup
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.recyclerview.widget.RecyclerView
import com.healthlink.hms.R
import com.healthlink.hms.business.medai.view.HealthDataCardContent
import com.healthlink.hms.business.medai.view.MedAIChatItem

class HealthDataCardViewHolder private constructor(
    private val composeView: ComposeView
) : RecyclerView.ViewHolder(composeView) {

    companion object {
        fun create(parent: ViewGroup): HealthDataCardViewHolder {
            val composeView = ComposeView(parent.context)

            // 确保ComposeView有明确的高度
            composeView.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )

            return HealthDataCardViewHolder(composeView)
        }
    }

    fun bind(item: MedAIChatItem.HealthDataCard) {
        composeView.setContent {
            MaterialTheme {
                // 最外层添加固定尺寸的Box
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .padding(top = 10.dp, bottom = 10.dp)
                ) {
                    HealthDataCardContent(
                        title = item.title,
                        healthItems = item.healthItems
                    )
                }
            }
        }
    }
}

