package com.healthlink.hms.business.medai.view

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun AIMessageContent(
    message: String
) {
    // 使用稳定的布局策略，减少重组时的闪烁
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .padding(horizontal = 25.dp, vertical = 10.dp)
    ) {
        // 使用Box来提供稳定的容器
        Box(
            modifier = Modifier
                .background(
                    color = Color(0x80FFFFFF),
                    shape = RoundedCornerShape(20.dp, 20.dp, 20.dp, 0.dp)
                )
                .padding(horizontal = 20.dp, vertical = 15.dp)
        ) {
            Text(
                text = message,
                fontSize = 20.sp,
                textAlign = TextAlign.Start,
                // 添加稳定的文本布局参数
                softWrap = true,
                maxLines = Int.MAX_VALUE,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun AIMessageContentPreview() {
    AIMessageContent(message = "您好，我是健康小医，欢迎使用智能健康咨询服务")
}