package com.healthlink.hms.business.medai.view

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun AIMessageContent(
    message: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight() // 明确使用wrapContentHeight()
            .padding(horizontal = 25.dp, vertical = 10.dp)
    ) {
        Text(text = message,
            fontSize = 20.sp,
            modifier = Modifier.background(
                color = Color(0x80FFFFFF),
                shape = RoundedCornerShape(20.dp, 20.dp, 20.dp, 0.dp)
            ).padding(horizontal = 20.dp, vertical = 15.dp))
    }
}

@Preview(showBackground = true)
@Composable
fun AIMessageContentPreview() {
    AIMessageContent(message = "您好，我是健康小医，欢迎使用智能健康咨询服务")
}