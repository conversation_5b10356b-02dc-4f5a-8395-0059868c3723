package com.healthlink.hms.business.car_seat.ui.spinecare

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.fragment.app.Fragment

class ImageFragment : Fragment() {
    private var imageResId: Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            imageResId = it.getInt(ARG_IMAGE_RES_ID)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val imageView = ImageView(requireContext()).apply {
            scaleType = ImageView.ScaleType.CENTER
            setImageResource(imageResId)
        }
        var layoutParams = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        imageView.layoutParams = layoutParams

        return imageView
    }

    companion object {
        private const val ARG_IMAGE_RES_ID = "image_res_id"

        fun newInstance(imageResId: Int) = ImageFragment().apply {
            arguments = Bundle().apply {
                putInt(ARG_IMAGE_RES_ID, imageResId)
            }
        }
    }
} 