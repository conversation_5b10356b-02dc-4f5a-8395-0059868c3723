package com.healthlink.hms.business.medai.adapter

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.compose.ui.platform.ComposeView
import androidx.recyclerview.widget.RecyclerView
import com.healthlink.hms.R
import com.healthlink.hms.business.medai.view.MedAIChatItem
import com.healthlink.hms.business.medai.view.MedAIChatViewType
import com.healthlink.hms.medchart.viewmodel.ChatMessage

class MedAIChatAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val items = mutableListOf<MedAIChatItem>()

    var onOptionClickListener:((MedAIChatItem.WelcomeAndOptions.Option) -> Unit)? = null

    var onQuickOptionClickListener: ((String) -> Unit)? = null

    // 添加转换方法，将ChatMessage转换为MedAIChatItem
    fun submitList(messages: List<ChatMessage>) {
        // 先清空欢迎信息和分隔线等非对话内容
        // items.removeAll { it !is MedAIChatItem.UserMessage && it !is MedAIChatItem.AIMessage }

        // 转换并添加新消息
        for (message in messages) {
            when (message.role) {
                "user" -> {
                    // 检查是否已存在相同内容的用户消息
                    if (!items.any { it is MedAIChatItem.UserMessage && it.message == message.content }) {
                        addItem(MedAIChatItem.UserMessage(message.content))
                    }
                }
                "assistant" -> {
                    // 检查是否已存在相同内容的AI消息
                    if (!items.any { it is MedAIChatItem.AIMessage && it.message == message.content }) {
                        addItem(MedAIChatItem.AIMessage(message.content))
                    }
                }
            }
        }
    }

    // 添加方法来直接设置MedAIChatItem列表
    fun submitMedAIChatItems(chatItems: List<MedAIChatItem>) {
        val size = items.size
        items.clear()
        notifyItemRangeRemoved(0, size)
        
        items.addAll(chatItems)
        notifyItemRangeInserted(0, chatItems.size)
    }

    override fun getItemViewType(position: Int): Int {
        return when(items[position]) {
            is MedAIChatItem.WelcomeAndOptions -> MedAIChatViewType.WELCOME_AND_OPTIONS.ordinal
            is MedAIChatItem.Divider -> MedAIChatViewType.DIVIDER.ordinal
            is MedAIChatItem.AIMessage -> MedAIChatViewType.AI_MESSAGE.ordinal
            is MedAIChatItem.HealthDataCard -> MedAIChatViewType.HEALTH_DATA_CARD.ordinal
            is MedAIChatItem.QuickOptions -> MedAIChatViewType.QUICK_OPTIONS.ordinal
            is MedAIChatItem.UserMessage -> MedAIChatViewType.USER_MESSAGE.ordinal
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val context = parent.context
        return when(viewType) {
            MedAIChatViewType.WELCOME_AND_OPTIONS.ordinal -> {
                WelcomeAndOptionsViewHolder.create(parent) { option ->
                    onOptionClickListener?.invoke(option)
                }
            }
            MedAIChatViewType.DIVIDER.ordinal -> {
                val view = LayoutInflater.from(context).inflate(
                    R.layout.item_divider, parent, false
                )
                DividerViewHolder(view)
            }
            MedAIChatViewType.AI_MESSAGE.ordinal -> {
                AIMessageViewHolder.create(parent)
            }
            MedAIChatViewType.HEALTH_DATA_CARD.ordinal -> {
                HealthDataCardViewHolder.create(parent)
            }
            MedAIChatViewType.QUICK_OPTIONS.ordinal -> {
                val view = LayoutInflater.from(context).inflate(
                    R.layout.item_quick_options, parent, false
                )
                QuickOptionsViewHolder(view) { option ->
                    onQuickOptionClickListener?.invoke(option)
                }
            }
            MedAIChatViewType.USER_MESSAGE.ordinal -> {
                UserMessageViewHolder.create(parent)
            }
            else -> {
                throw IllegalArgumentException("Unknown view type")
            }
        }
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int
    ) {
        when(holder) {
            is WelcomeAndOptionsViewHolder -> {
                holder.bind(items[position] as MedAIChatItem.WelcomeAndOptions)
            }
            is DividerViewHolder -> {
                // 绑定分隔线
            }
            is AIMessageViewHolder -> {
                holder.bind(items[position] as MedAIChatItem.AIMessage)
            }
            is HealthDataCardViewHolder -> {
                holder.bind(items[position] as MedAIChatItem.HealthDataCard)
            }
            is QuickOptionsViewHolder -> {
                holder.bind(items[position] as MedAIChatItem.QuickOptions)
            }
            is UserMessageViewHolder -> {
                holder.bind(items[position] as MedAIChatItem.UserMessage)
            }
        }
    }

    // 获取所有项
    fun getItems(): List<MedAIChatItem> = items.toList()

    // 清空所有项
    fun clearItems() {
        val size = items.size
        items.clear()
        notifyItemRangeRemoved(0, size)
    }

    override fun getItemCount() = items.size

    fun addItem(item: MedAIChatItem) {
        items.add(item)
        notifyItemInserted(items.size - 1)
    }
    
    /**
     * 更新指定位置的项目（用于打字机效果）
     */
    fun updateItem(position: Int, item: MedAIChatItem) {
        if (position >= 0 && position < items.size) {
            items[position] = item
            notifyItemChanged(position)
        }
    }
}