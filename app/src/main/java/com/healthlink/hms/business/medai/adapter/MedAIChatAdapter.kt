package com.healthlink.hms.business.medai.adapter

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.compose.ui.platform.ComposeView
import androidx.recyclerview.widget.RecyclerView
import com.healthlink.hms.R
import com.healthlink.hms.business.medai.view.MedAIChatItem
import com.healthlink.hms.business.medai.view.MedAIChatViewType
import com.healthlink.hms.business.medai.utils.TypingEffectManager
import com.healthlink.hms.medchart.viewmodel.ChatMessage

class MedAIChatAdapter(
    private val typingEffectManager: TypingEffectManager = TypingEffectManager()
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val items = mutableListOf<MedAIChatItem>()

    var onOptionClickListener:((MedAIChatItem.WelcomeAndOptions.Option) -> Unit)? = null

    var onQuickOptionClickListener: ((String) -> Unit)? = null

    // 添加转换方法，将ChatMessage转换为MedAIChatItem
    fun submitList(messages: List<ChatMessage>) {
        // 先清空欢迎信息和分隔线等非对话内容
        // items.removeAll { it !is MedAIChatItem.UserMessage && it !is MedAIChatItem.AIMessage }

        // 转换并添加新消息
        for (message in messages) {
            when (message.role) {
                "user" -> {
                    // 检查是否已存在相同内容的用户消息
                    if (!items.any { it is MedAIChatItem.UserMessage && it.message == message.content }) {
                        addItem(MedAIChatItem.UserMessage(message.content))
                    }
                }
                "assistant" -> {
                    // 检查是否已存在相同内容的AI消息
                    if (!items.any { it is MedAIChatItem.AIMessage && it.message == message.content }) {
                        addItem(MedAIChatItem.AIMessage(message.content))
                    }
                }
            }
        }
    }

    // 添加方法来直接设置MedAIChatItem列表
    fun submitMedAIChatItems(chatItems: List<MedAIChatItem>) {
        val size = items.size
        items.clear()
        notifyItemRangeRemoved(0, size)
        
        items.addAll(chatItems)
        notifyItemRangeInserted(0, chatItems.size)
    }

    override fun getItemViewType(position: Int): Int {
        return when(items[position]) {
            is MedAIChatItem.WelcomeAndOptions -> MedAIChatViewType.WELCOME_AND_OPTIONS.ordinal
            is MedAIChatItem.Divider -> MedAIChatViewType.DIVIDER.ordinal
            is MedAIChatItem.AIMessage -> MedAIChatViewType.AI_MESSAGE.ordinal
            is MedAIChatItem.HealthDataCard -> MedAIChatViewType.HEALTH_DATA_CARD.ordinal
            is MedAIChatItem.QuickOptions -> MedAIChatViewType.QUICK_OPTIONS.ordinal
            is MedAIChatItem.UserMessage -> MedAIChatViewType.USER_MESSAGE.ordinal
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return try {
            val context = parent.context
            when(viewType) {
                MedAIChatViewType.WELCOME_AND_OPTIONS.ordinal -> {
                    WelcomeAndOptionsViewHolder.create(parent) { option ->
                        try {
                            onOptionClickListener?.invoke(option)
                        } catch (e: Exception) {
                            Log.e("MedAIChatAdapter", "选项点击处理失败", e)
                        }
                    }
                }
                MedAIChatViewType.DIVIDER.ordinal -> {
                    val view = LayoutInflater.from(context).inflate(
                        R.layout.item_divider, parent, false
                    )
                    DividerViewHolder(view)
                }
                MedAIChatViewType.AI_MESSAGE.ordinal -> {
                    AIMessageViewHolder.create(parent, typingEffectManager)
                }
                MedAIChatViewType.HEALTH_DATA_CARD.ordinal -> {
                    HealthDataCardViewHolder.create(parent)
                }
                MedAIChatViewType.QUICK_OPTIONS.ordinal -> {
                    val view = LayoutInflater.from(context).inflate(
                        R.layout.item_quick_options, parent, false
                    )
                    QuickOptionsViewHolder(view) { option ->
                        try {
                            onQuickOptionClickListener?.invoke(option)
                        } catch (e: Exception) {
                            Log.e("MedAIChatAdapter", "快捷选项点击处理失败", e)
                        }
                    }
                }
                MedAIChatViewType.USER_MESSAGE.ordinal -> {
                    UserMessageViewHolder.create(parent)
                }
                else -> {
                    Log.e("MedAIChatAdapter", "未知的ViewType: $viewType")
                    throw IllegalArgumentException("Unknown view type: $viewType")
                }
            }
        } catch (e: Exception) {
            Log.e("MedAIChatAdapter", "创建ViewHolder失败", e)
            // 降级处理：创建一个简单的TextView ViewHolder
            createFallbackViewHolder(parent)
        }
    }

    /**
     * 创建降级ViewHolder，防止崩溃
     */
    private fun createFallbackViewHolder(parent: ViewGroup): RecyclerView.ViewHolder {
        val textView = android.widget.TextView(parent.context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            setPadding(16, 16, 16, 16)
            text = "加载失败，请重试"
        }

        return object : RecyclerView.ViewHolder(textView) {}
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int
    ) {
        try {
            if (position < 0 || position >= items.size) {
                Log.e("MedAIChatAdapter", "无效的position: $position, items.size: ${items.size}")
                return
            }

            val item = items[position]

            when(holder) {
                is WelcomeAndOptionsViewHolder -> {
                    if (item is MedAIChatItem.WelcomeAndOptions) {
                        holder.bind(item)
                    } else {
                        Log.e("MedAIChatAdapter", "类型不匹配: 期望WelcomeAndOptions，实际${item::class.simpleName}")
                    }
                }
                is DividerViewHolder -> {
                    // 绑定分隔线，无需额外操作
                }
                is AIMessageViewHolder -> {
                    if (item is MedAIChatItem.AIMessage) {
                        holder.bind(item)
                    } else {
                        Log.e("MedAIChatAdapter", "类型不匹配: 期望AIMessage，实际${item::class.simpleName}")
                    }
                }
                is HealthDataCardViewHolder -> {
                    if (item is MedAIChatItem.HealthDataCard) {
                        holder.bind(item)
                    } else {
                        Log.e("MedAIChatAdapter", "类型不匹配: 期望HealthDataCard，实际${item::class.simpleName}")
                    }
                }
                is QuickOptionsViewHolder -> {
                    if (item is MedAIChatItem.QuickOptions) {
                        holder.bind(item)
                    } else {
                        Log.e("MedAIChatAdapter", "类型不匹配: 期望QuickOptions，实际${item::class.simpleName}")
                    }
                }
                is UserMessageViewHolder -> {
                    if (item is MedAIChatItem.UserMessage) {
                        holder.bind(item)
                    } else {
                        Log.e("MedAIChatAdapter", "类型不匹配: 期望UserMessage，实际${item::class.simpleName}")
                    }
                }
                else -> {
                    Log.e("MedAIChatAdapter", "未知的ViewHolder类型: ${holder::class.simpleName}")
                }
            }
        } catch (e: Exception) {
            Log.e("MedAIChatAdapter", "绑定ViewHolder失败，position: $position", e)
        }
    }

    // 获取所有项
    fun getItems(): List<MedAIChatItem> = items.toList()

    // 清空所有项
    fun clearItems() {
        val size = items.size
        items.clear()
        notifyItemRangeRemoved(0, size)
    }

    override fun getItemCount() = items.size

    fun addItem(item: MedAIChatItem) {
        items.add(item)
        notifyItemInserted(items.size - 1)
    }
    
    /**
     * 更新指定位置的项目（用于打字机效果）
     */
    fun updateItem(position: Int, item: MedAIChatItem) {
        if (position >= 0 && position < items.size) {
            items[position] = item
            notifyItemChanged(position)
        }
    }

    /**
     * 添加AI消息并启动打字机效果
     */
    fun addAIMessageWithTypingEffect(message: String) {
        val aiMessage = MedAIChatItem.AIMessage(message)
        addItem(aiMessage)
    }

    /**
     * 立即显示完整的AI消息（跳过打字机效果）
     */
    fun addAIMessageInstant(message: String) {
        val aiMessage = MedAIChatItem.AIMessage(message)
        items.add(aiMessage)
        val position = items.size - 1
        notifyItemInserted(position)

        // 通知ViewHolder立即显示完整消息
        // 这需要在ViewHolder绑定时处理
    }

    /**
     * 取消所有打字机效果
     */
    fun cancelAllTypingEffects() {
        typingEffectManager.cancelAllTypingEffects()
    }

    /**
     * 获取打字机效果管理器
     */
    fun getTypingEffectManager(): TypingEffectManager = typingEffectManager

    /**
     * 清理资源
     */
    fun cleanup() {
        typingEffectManager.cleanup()
    }
}