package com.healthlink.hms.business.car_seat

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Base64
import android.util.Log

object ImageUtils {

    /**
     * 将Base64字符串转换为Bitmap
     * @param base64String Base64编码的图片字符串
     * @return Bitmap对象，失败时返回null
     */
    fun base64ToBitmap(base64String: String): Bitmap? {
        Log.i("ImageUtils", "开始转换图片 $base64String")
        return try {
            // 解码Base64字符串为字节数组
            val decodedBytes = Base64.decode(base64String, Base64.DEFAULT)

            // 将字节数组转换为Bitmap
            val bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)

            if (bitmap != null) {
                Log.d("ImageUtils", "成功转换图片，尺寸: ${bitmap.width}x${bitmap.height}")
            } else {
                Log.e("ImageUtils", "Bitmap转换失败")
            }

            bitmap

        } catch (e: IllegalArgumentException) {
            Log.e("ImageUtils", "Base64解码失败: ${e.message}")
            null
        } catch (e: OutOfMemoryError) {
            Log.e("ImageUtils", "内存不足，无法创建Bitmap: ${e.message}")
            null
        } catch (e: Exception) {
            Log.e("ImageUtils", "图片转换异常: ${e.message}")
            null
        }
    }

    /**
     * 带缩放的Base64转Bitmap方法
     * @param base64String Base64编码的图片字符串
     * @param maxWidth 最大宽度
     * @param maxHeight 最大高度
     * @return 缩放后的Bitmap对象
     */
    fun base64ToBitmapWithScale(
        base64String: String,
        maxWidth: Int = 800,
        maxHeight: Int = 600
    ): Bitmap? {
        return try {
            val decodedBytes = Base64.decode(base64String, Base64.DEFAULT)

            // 首先获取图片尺寸，不加载到内存
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size, options)

            // 计算缩放比例
            val scaleFactor = calculateInSampleSize(options, maxWidth, maxHeight)

            // 实际解码图片
            val finalOptions = BitmapFactory.Options().apply {
                inSampleSize = scaleFactor
                inJustDecodeBounds = false
            }

            val bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size, finalOptions)

            Log.d("ImageUtils", "缩放后图片尺寸: ${bitmap?.width}x${bitmap?.height}, 缩放比例: $scaleFactor")
            bitmap

        } catch (e: Exception) {
            Log.e("ImageUtils", "缩放图片转换失败: ${e.message}")
            null
        }
    }

    /**
     * 计算图片缩放比例
     */
    private fun calculateInSampleSize(
        options: BitmapFactory.Options,
        reqWidth: Int,
        reqHeight: Int
    ): Int {
        val height = options.outHeight
        val width = options.outWidth
        var inSampleSize = 1

        if (height > reqHeight || width > reqWidth) {
            val halfHeight = height / 2
            val halfWidth = width / 2

            while ((halfHeight / inSampleSize) >= reqHeight &&
                (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2
            }
        }

        return inSampleSize
    }
}