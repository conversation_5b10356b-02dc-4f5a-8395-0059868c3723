package data

// 消息抽象模型
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

/**
 * 基础消息接口
 * 定义所有消息类型的通用属性
 */
interface BaseMessage {
    val type: String
    val timestamp: Long
    val messageId: String
}

/**
 * 抽象消息包装器
 * 使用密封类确保类型安全
 */
@Serializable
sealed class WebSocketMessage : BaseMessage {
    abstract override val type: String
    abstract override val timestamp: Long
    abstract override val messageId: String
}

/**
 * 控制消息类型
 * 用于连接管理、认证、状态控制等
 */
@Serializable
data class ControlMessage(
    override val type: String,
    override val timestamp: Long = System.currentTimeMillis(),
    override val messageId: String = generateMessageId(),
    val action: ControlAction,
    val payload: JsonElement? = null,
    val userId: String? = null
) : WebSocketMessage()

/**
 * 传感器消息类型
 * 用于传输传感器数据
 */
@Serializable
data class SensorMessage(
    override val type: String,
    override val timestamp: Long = System.currentTimeMillis(),
    override val messageId: String = generateMessageId(),
    val sensorType: SensorType,
    val data: JsonElement,
    val deviceId: String,
    val quality: DataQuality = DataQuality.NORMAL
) : WebSocketMessage()

//@Serializable
//data class SensorMessage(
//    val sensor_data: List<Int>,
//    val ifsit: String,
//    val ifnormal: String?,
//    val about: String?,
//    val around: String?,
//    val disability: Int,
//    val heatmap_data: List<List<Double>>? = null // 如果有热力图数据
//)

/**
 * 响应消息类型
 * 用于服务器响应客户端请求
 */
@Serializable
data class ResponseMessage(
    override val type: String,
    override val timestamp: Long = System.currentTimeMillis(),
    override val messageId: String = generateMessageId(),
    val status: ResponseStatus,
    val message: String,
    val data: String? = null,
    val correlationId: String? = null
) : WebSocketMessage()

@Serializable
data class SensorPicMessage(
    override val type: String,
    override val timestamp: Long = System.currentTimeMillis(),
    override val messageId: String = generateMessageId(),
    val sensor_data: String,  // Base64编码的图片数据
    val ifsit: String,
    val ifnormal: String?,
    val about: String?,
    val around: String?,
    val disability: Int
) : WebSocketMessage()

/**
 * 控制动作枚举
 */
@Serializable
enum class ControlAction {
    CONNECT, DISCONNECT, HEARTBEAT, AUTHENTICATE,
    SUBSCRIBE, UNSUBSCRIBE, CONFIG_UPDATE
}

/**
 * 传感器类型枚举
 */
@Serializable
enum class SensorType {
    TEMPERATURE, HUMIDITY, PRESSURE, LIGHT,
    MOTION, GPS, ACCELEROMETER, GYROSCOPE
}

/**
 * 数据质量枚举
 */
@Serializable
enum class DataQuality {
    HIGH, NORMAL, LOW, CRITICAL
}

/**
 * 响应状态枚举
 */
@Serializable
enum class ResponseStatus {
    SUCCESS, ERROR, WARNING, INFO
}

/**
 * WebSocket连接状态枚举
 * 用于表示连接的各种状态
 */
@Serializable
enum class ConnectionState {
    /** 未连接状态 */
    DISCONNECTED,

    /** 正在连接状态 */
    CONNECTING,

    /** 已连接状态 */
    CONNECTED,

    /** 正在重连状态 */
    RECONNECTING,

    /** 连接失败状态 */
    FAILED,

    /** 连接已关闭状态 */
    CLOSED
}

/**
 * 详细的连接状态信息
 * 包含状态、错误信息、重连次数等详细信息
 */
@Serializable
data class DetailedConnectionState(
    val state: ConnectionState = ConnectionState.DISCONNECTED,
    val error: String? = null,
    val reconnectAttempts: Int = 0,
    val lastConnectedTime: Long? = null,
    val connectionDuration: Long = 0L
) {
    val isConnected: Boolean
        get() = state == ConnectionState.CONNECTED

    val isConnecting: Boolean
        get() = state == ConnectionState.CONNECTING || state == ConnectionState.RECONNECTING

    val canReconnect: Boolean
        get() = state in listOf(
            ConnectionState.DISCONNECTED,
            ConnectionState.FAILED,
            ConnectionState.CLOSED
        )
}

/**
 * 生成唯一消息ID
 */
fun generateMessageId(): String = "msg_${System.currentTimeMillis()}_${(1000..9999).random()}"