package com.healthlink.hms.business.medai.view

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun WelcomeAndOptionsContent(
    welcomeMessage: String,
    introMessage: String,
    options: List<MedAIChatItem.WelcomeAndOptions.Option>,
    onOptionClick: (MedAIChatItem.WelcomeAndOptions.Option) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(color = Color(0x4DFFFFFF), shape = RoundedCornerShape(30.dp))
            .border(1.dp, Color(0xFFFFFFFF), shape = RoundedCornerShape(30.dp))
            .padding(25.dp)
            .wrapContentHeight() // 明确指定高度约束
    ) {
        // 欢迎消息
        Text(
            text = welcomeMessage,
            fontSize = 20.sp,
            color = Color(0xFF333333),
            style = MaterialTheme.typography.titleMedium
        )

        HorizontalDivider(modifier = Modifier.padding(top = 20.dp, bottom = 15.dp), color = Color(0x33999999))
        // AI介绍信息
        Text(
            text = introMessage,
            fontSize = 18.sp,
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 15.dp)
        )

        // 使用纯固定布局，不使用任何滚动容器
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
        ) {
            // 第一行按钮
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Box(modifier = Modifier.weight(1f).background(Color(0x80FFFFFF))) {
                    if (options.size > 0) {
                        OptionButton(options[0], onOptionClick)
                    }
                }

                Box(modifier = Modifier.weight(1f).background(Color(0x80FFFFFF))) {
                    if (options.size > 1) {
                        OptionButton(options[1], onOptionClick)
                    }
                }
            }

            // 第二行按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Box(modifier = Modifier.weight(1f).background(Color(0x80FFFFFF))) {
                    if (options.size > 2) {
                        OptionButton(options[2], onOptionClick)
                    }
                }

                Box(modifier = Modifier.weight(1f).background(Color(0x80FFFFFF))) {
                    if (options.size > 3) {
                        OptionButton(options[3], onOptionClick)
                    }
                }
            }
        }
    }
}

@Composable
private fun OptionButton(
    option: MedAIChatItem.WelcomeAndOptions.Option,
    onOptionClick: (MedAIChatItem.WelcomeAndOptions.Option) -> Unit
) {
    Button(
        onClick = { onOptionClick(option) },
        modifier = Modifier.fillMaxWidth(),
        colors = ButtonDefaults.buttonColors(
            containerColor = Color(0x80FFFFFF)
        ),
        shape = RoundedCornerShape(10.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            option.iconResId?.let { resId ->
                Icon(
                    painter = painterResource(id = resId),
                    contentDescription = null,
                    tint = Color.Unspecified,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
            }

            Text(
                text = option.title,
                fontSize = 20.sp,
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF333333)
            )
        }
    }
}


@Preview(showBackground = true)
@Composable
fun WelcomeAndOptionsContentPreview() {
    WelcomeAndOptionsContent(
        welcomeMessage = "Hi，我是健康小医，欢迎使用智能健康咨询服务",
        introMessage = "我是基于医疗大语言模型技术，学习大量医学知识具备一定医学常识的AI健康助手，可以根据您的问题提供准确、个性化的建议。很高兴为您服务",
        options = listOf(
            MedAIChatItem.WelcomeAndOptions.Option(
                title = "健康咨询",
                iconResId = null
            ),
            MedAIChatItem.WelcomeAndOptions.Option(
                title = "症状咨询",
                iconResId = null
            ),
            MedAIChatItem.WelcomeAndOptions.Option(
                title = "药物咨询",
                iconResId = null
            ),
            MedAIChatItem.WelcomeAndOptions.Option(
                title = "问诊/导诊",
                iconResId = null
            )
        ),
        onOptionClick = {}
    )
}