package com.healthlink.hms.medchart.api

import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Streaming

interface LLMApiService {
//    @POST("med/login")
//    suspend fun login(
//        @Body request: LoginRequest
//    ): Response<LoginResponse>

    @POST("med/login")
    suspend fun login(
        @Body phone: Long
    ): Response<LoginResponse>
    
    @POST("med/chat")
    suspend fun getChatCompletion(
        @Body request: ChatCompletionRequest
    ): Response<ChatCompletionResponse>
    
    @Streaming
    @POST("med/chat3")
    suspend fun getStreamingChatCompletion(
        @Body request: ChatCompletionRequest
    ): Response<ResponseBody>
}

// 登录请求和响应数据类
data class LoginRequest(
    val phone: String
)

data class LoginResponse(
    val extenalId: String,
    val code: Int,
    val message: String,
    val data: String
)

data class LoginData(
    val token: String,
    val userId: String
)

// 聊天请求和响应数据类
data class ChatCompletionRequest(
    val sessionId: String?,
    val message: String,
    val phone: Long?,
    val stream: Boolean = true,
    val model: Int =  0 // model 类型 0-医联， 1-盘古
)

//data class ChatCompletionRequest(
//    val model: String,
//    val messages: List<Message>,
//    val temperature: Double = 0.7,
//    val max_tokens: Int = 1000,
//    val stream: Boolean = false
//)

data class Message(
    val role: String,
    val content: String
)

data class ChatCompletionResponse(
    val code: Int,
    val message: String,
    val data: ChatData?
)

data class ChatData(
    val id: String,
    val created: Long,
    val choices: List<Choice>,
    val usage: Usage?
)

data class Choice(
    val index: Int,
    val message: Message,
    val finish_reason: String
)

data class Usage(
    val prompt_tokens: Int,
    val completion_tokens: Int,
    val total_tokens: Int
)

// 流式响应数据类
data class StreamingChatResponse(
    val code: Int,
    val message: String,
    val data: StreamingChatData?
)

data class StreamingChatData(
    val id: String,
    val created: Long,
    val choices: List<StreamingChoice>
)

data class StreamingChoice(
    val index: Int,
    val delta: DeltaMessage,
    val finish_reason: String?
)

data class DeltaMessage(
    val role: String? = null,
    val content: String? = null
)