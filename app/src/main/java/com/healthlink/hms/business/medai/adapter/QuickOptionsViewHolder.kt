package com.healthlink.hms.business.medai.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.healthlink.hms.R
import com.healthlink.hms.business.medai.view.MedAIChatItem

// QuickOptionsViewHolder
class QuickOptionsViewHolder(
    itemView: View,
    private val onOptionClick: (String) -> Unit
) : RecyclerView.ViewHolder(itemView) {
    private val recyclerView: RecyclerView = itemView.findViewById(R.id.rv_quick_options)

    fun bind(item: MedAIChatItem.QuickOptions) {
        // 创建水平布局的适配器
        val adapter = QuickOptionsAdapter(item.options) { option ->
            onOptionClick(option)
        }

        recyclerView.apply {
            layoutManager = LinearLayoutManager(itemView.context, LinearLayoutManager.HORIZONTAL, false)
            this.adapter = adapter
        }
    }

    // 快捷选项的内部适配器
    private class QuickOptionsAdapter(
        private val options: List<String>,
        private val onOptionClick: (String) -> Unit
    ) : RecyclerView.Adapter<QuickOptionsAdapter.OptionViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OptionViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_quick_option_button, parent, false)
            return OptionViewHolder(view, onOptionClick)
        }

        override fun onBindViewHolder(holder: OptionViewHolder, position: Int) {
            holder.bind(options[position])
        }

        override fun getItemCount() = options.size

        class OptionViewHolder(
            itemView: View,
            private val onOptionClick: (String) -> Unit
        ) : RecyclerView.ViewHolder(itemView) {
            private val optionButton: TextView = itemView.findViewById(R.id.tv_quick_option)

            fun bind(option: String) {
                optionButton.text = option
                optionButton.setOnClickListener {
                    onOptionClick(option)
                }
            }
        }
    }
}