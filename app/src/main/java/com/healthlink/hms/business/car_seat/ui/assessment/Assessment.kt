package com.healthlink.hms.business.car_seat.ui.assessment

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.healthlink.hms.R

@Composable
fun HeaderAssessmentContent() {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ){
        Button(
            onClick = {},
            modifier = Modifier.wrapContentSize(),
            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF07C160)),
            shape = RoundedCornerShape( 10.dp),
            contentPadding = PaddingValues(30.dp,13.dp)
        ){
            Text(text = "脊椎压力分布", color = Color(0xFFFFFFFF), fontSize = 20.sp, fontWeight = FontWeight.Medium)
        }

        Row(modifier = Modifier.wrapContentSize(),
            horizontalArrangement = Arrangement.End,
            verticalAlignment = Alignment.CenterVertically) {
            Text(text = "低压力", color = Color(0xFF666666), fontSize = 18.sp, fontWeight = FontWeight.Medium)
            Spacer(modifier = Modifier.width(10.dp))
            Box(
                modifier = Modifier
                    .size(240.dp, 14.dp)
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(Color(0xFF07C160), Color(0xFFFF9933), Color(0xFFFF3333))
                        ),
                        shape = RoundedCornerShape(10.dp)
                    )
            )
            Spacer(modifier = Modifier.width(10.dp))
            Text(text = "高压力", color = Color(0xFF666666), fontSize = 18.sp, fontWeight = FontWeight.Medium)
        }
    }
}


@Composable
fun SpineAssessmentCard() {
    Card(
        modifier = Modifier.fillMaxSize(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0x80FFFFFF)),
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            //.padding(start = 77.dp,top = 50.dp, end = 77.dp)
            Icon(painter = painterResource(id = R.drawable.img_body_xray), contentDescription = "body_xray", modifier = Modifier.size(546.dp,816.dp), tint = Color.Unspecified)
            Column(modifier = Modifier.fillMaxSize().padding(bottom = 78.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Bottom) {
                Icon(painter = painterResource(id = R.drawable.img_scan_chest), contentDescription = "img_scan_chest", modifier = Modifier.size(528.dp, 170.dp).padding(horizontal = 9.dp),tint = Color.Unspecified) //Modifier.wrapContentSize()d),tint = Color.Unspecified)
                Spacer(modifier = Modifier.height(10.dp))
                Icon(painter = painterResource(id = R.drawable.img_scan_waist), contentDescription = "img_scan_waist", modifier = Modifier.size(528.dp, 170.dp).padding(horizontal = 9.dp),tint = Color.Unspecified)
                Spacer(modifier = Modifier.height(10.dp))
                Icon(painter = painterResource(id = R.drawable.img_scan_bottom), contentDescription = "img_scan_bottom", modifier = Modifier.size(528.dp, 170.dp).padding(horizontal = 9.dp),tint = Color.Unspecified)
            }
        }
    }
}


@Composable
fun TotalTripCard() {
    Card(
        modifier = Modifier.fillMaxWidth().height(310.dp),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0x80FFFFFF))
    ) {
        Text(
            text = "脊柱风险指数图表",
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF333333),
            modifier = Modifier.padding(bottom = 16.dp)
        )
    }
}

@Preview(showBackground = true,
    device = "spec:width=1920px,height=1128px,dpi=240"
)
@Composable
fun TotalTripCardPreview() {
    TotalTripCard()
}


@Preview(showBackground = true,
    device = "spec:width=1920px,height=1128px,dpi=240"
)
@Composable
fun SpineAssessmentCardPreview() {
    SpineAssessmentCard()
}


//
//@Preview(showBackground = true,
//    device = "spec:width=1920px,height=1128px,dpi=240"
//)
//@Composable
//fun HeaderAssessmentContentPreview() {
//    HeaderAssessmentContent()
//}