package com.healthlink.hms.business.medai.utils

import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

/**
 * 打字机效果管理器 - 优化版本，解决文字换行时的闪烁问题
 * 方案1（直接操作TextView）+ 方案6（批量更新）的组合实现
 * 
 * 核心优化：
 * 1. 直接操作TextView，避免频繁的RecyclerView更新
 * 2. 优化的批量更新机制，减少UI刷新频率和闪烁
 * 3. 智能延迟计算，提供自然的打字体验
 * 4. 内存优化，及时清理资源
 * 5. 防闪烁优化，特别针对文字换行场景
 */
class TypingEffectManager {
    companion object {
        private const val TAG = "TypingEffectManager"
        
        // 批量更新配置 - 优化以减少闪烁
        private const val BATCH_UPDATE_INTERVAL = 32L // 30fps，减少更新频率以避免闪烁
        private const val MAX_BATCH_SIZE = 3 // 每批最多处理3个字符，更平滑的显示
        
        // 打字速度配置
        private const val DEFAULT_TYPING_SPEED = 80L
        private const val PUNCTUATION_DELAY = 300L
        private const val COMMA_DELAY = 200L
        private const val BRACKET_DELAY = 150L
        private const val NEWLINE_DELAY = 400L
        private const val SPACE_DELAY = 100L
        private const val ENGLISH_SPEED = 60L
        private const val CHINESE_SPEED = 80L
        private const val DEFAULT_CHAR_SPEED = 70L
        
        // 防闪烁配置
        private const val MIN_UPDATE_INTERVAL = 50L // 最小更新间隔，防止过于频繁的更新
    }
    
    // 打字机任务数据类
    private data class TypingTask(
        val id: String,
        val fullText: String,
        val textState: MutableState<String>,
        val onComplete: () -> Unit,
        val onProgress: ((progress: Float) -> Unit)? = null,
        var currentIndex: Int = 0,
        var isActive: Boolean = true,
        val startTime: Long = System.currentTimeMillis(),
        var lastUpdateTime: Long = 0L // 记录上次更新时间，用于防闪烁
    )
    
    // 批量更新数据类
    private data class BatchUpdate(
        val taskId: String,
        val newText: String,
        val progress: Float,
        val timestamp: Long = System.currentTimeMillis()
    )
    
    private val handler = Handler(Looper.getMainLooper())
    private val activeTasks = ConcurrentHashMap<String, TypingTask>()
    private val batchUpdates = mutableListOf<BatchUpdate>()
    private val isProcessingBatch = AtomicBoolean(false)
    private val taskIdCounter = AtomicInteger(0)
    
    // 批量更新处理器
    private val batchUpdateRunnable = object : Runnable {
        override fun run() {
            if (batchUpdates.isNotEmpty()) {
                processBatchUpdates()
            }
            
            // 如果还有活跃任务，继续调度批量更新
            if (activeTasks.isNotEmpty()) {
                handler.postDelayed(this, BATCH_UPDATE_INTERVAL)
            } else {
                isProcessingBatch.set(false)
            }
        }
    }
    
    /**
     * 开始打字机效果
     * @param fullText 完整文本
     * @param textState Compose状态，用于更新UI
     * @param onComplete 完成回调
     * @param onProgress 进度回调（可选）
     * @return 任务ID，用于取消任务
     */
    fun startTypingEffect(
        fullText: String,
        textState: MutableState<String>,
        onComplete: () -> Unit,
        onProgress: ((progress: Float) -> Unit)? = null
    ): String {
        if (fullText.isEmpty()) {
            Log.w(TAG, "尝试开始空文本的打字机效果")
            onComplete()
            return ""
        }
        
        val taskId = "typing_${taskIdCounter.incrementAndGet()}_${System.currentTimeMillis()}"
        val task = TypingTask(
            id = taskId,
            fullText = fullText,
            textState = textState,
            onComplete = onComplete,
            onProgress = onProgress
        )
        
        activeTasks[taskId] = task
        
        // 启动批量更新处理器（如果尚未启动）
        if (!isProcessingBatch.getAndSet(true)) {
            handler.post(batchUpdateRunnable)
        }
        
        // 开始处理这个任务
        processTypingTask(taskId)
        
        Log.d(TAG, "开始打字机效果: $taskId, 文本长度: ${fullText.length}")
        return taskId
    }
    
    /**
     * 取消打字机效果
     */
    fun cancelTypingEffect(taskId: String) {
        activeTasks[taskId]?.let { task ->
            task.isActive = false
            activeTasks.remove(taskId)
            Log.d(TAG, "取消打字机效果: $taskId")
        }
    }
    
    /**
     * 取消所有打字机效果
     */
    fun cancelAllTypingEffects() {
        val taskIds = activeTasks.keys.toList()
        taskIds.forEach { taskId ->
            activeTasks[taskId]?.isActive = false
        }
        activeTasks.clear()
        batchUpdates.clear()
        Log.d(TAG, "取消所有打字机效果")
    }
    
    /**
     * 处理单个打字机任务 - 优化版本，减少闪烁
     */
    private fun processTypingTask(taskId: String) {
        val task = activeTasks[taskId] ?: return
        
        if (!task.isActive || task.currentIndex >= task.fullText.length) {
            // 任务完成或被取消
            if (task.isActive && task.currentIndex >= task.fullText.length) {
                // 确保最终文本正确显示
                addBatchUpdate(taskId, task.fullText, 1.0f)
                
                // 延迟执行完成回调，确保最后的批量更新已处理
                handler.postDelayed({
                    task.onComplete()
                    activeTasks.remove(taskId)
                    Log.d(TAG, "打字机效果完成: $taskId, 耗时: ${System.currentTimeMillis() - task.startTime}ms")
                }, BATCH_UPDATE_INTERVAL * 2)
            } else {
                activeTasks.remove(taskId)
            }
            return
        }
        
        // 防闪烁优化：检查是否需要等待更长时间
        val currentTime = System.currentTimeMillis()
        val timeSinceLastUpdate = currentTime - task.lastUpdateTime
        
        if (timeSinceLastUpdate < MIN_UPDATE_INTERVAL) {
            // 如果距离上次更新时间太短，延迟处理
            handler.postDelayed({
                processTypingTask(taskId)
            }, MIN_UPDATE_INTERVAL - timeSinceLastUpdate)
            return
        }
        
        // 批量处理字符，但要考虑换行符的特殊处理
        val batchEndIndex = calculateOptimalBatchEndIndex(task)
        
        val currentText = task.fullText.substring(0, batchEndIndex)
        val progress = batchEndIndex.toFloat() / task.fullText.length
        
        // 添加到批量更新队列
        addBatchUpdate(taskId, currentText, progress)
        
        // 更新任务状态
        task.currentIndex = batchEndIndex
        task.lastUpdateTime = currentTime
        
        // 计算下次更新的延迟时间
        val delay = if (batchEndIndex < task.fullText.length) {
            calculateBatchDelay(task.fullText, task.currentIndex - MAX_BATCH_SIZE, batchEndIndex)
        } else {
            0L
        }
        
        // 调度下次更新
        if (delay > 0) {
            handler.postDelayed({
                processTypingTask(taskId)
            }, delay)
        } else {
            // 立即处理下一批
            handler.post {
                processTypingTask(taskId)
            }
        }
    }
    
    /**
     * 计算最优的批量结束索引，避免在换行符中间截断
     */
    private fun calculateOptimalBatchEndIndex(task: TypingTask): Int {
        val startIndex = task.currentIndex
        val maxEndIndex = minOf(startIndex + MAX_BATCH_SIZE, task.fullText.length)
        
        // 检查是否包含换行符，如果有，优先在换行符处结束
        for (i in startIndex until maxEndIndex) {
            if (task.fullText[i] == '\n') {
                return i + 1 // 包含换行符
            }
        }
        
        // 检查是否在单词中间，尽量在空格或标点符号处结束
        if (maxEndIndex < task.fullText.length) {
            for (i in maxEndIndex - 1 downTo startIndex + 1) {
                val char = task.fullText[i]
                if (char == ' ' || char in "，。！？；：") {
                    return i + 1
                }
            }
        }
        
        return maxEndIndex
    }
    
    /**
     * 添加批量更新
     */
    private fun addBatchUpdate(taskId: String, newText: String, progress: Float) {
        synchronized(batchUpdates) {
            batchUpdates.add(BatchUpdate(taskId, newText, progress))
        }
    }
    
    /**
     * 处理批量更新 - 优化版本，减少闪烁
     */
    private fun processBatchUpdates() {
        val updates = synchronized(batchUpdates) {
            val currentUpdates = batchUpdates.toList()
            batchUpdates.clear()
            currentUpdates
        }
        
        // 按任务ID分组，只保留每个任务的最新更新
        val latestUpdates = updates.groupBy { it.taskId }
            .mapValues { (_, taskUpdates) -> taskUpdates.maxByOrNull { it.timestamp } }
            .values
            .filterNotNull()
        
        latestUpdates.forEach { update ->
            activeTasks[update.taskId]?.let { task ->
                if (task.isActive) {
                    // 直接更新Compose状态
                    task.textState.value = update.newText
                    task.onProgress?.invoke(update.progress)
                }
            }
        }
    }
    
    /**
     * 计算批量延迟时间
     */
    private fun calculateBatchDelay(text: String, startIndex: Int, endIndex: Int): Long {
        var totalDelay = 0L
        
        for (i in startIndex until endIndex) {
            if (i < text.length) {
                totalDelay += calculateCharDelay(text[i])
            }
        }
        
        return totalDelay / MAX_BATCH_SIZE // 平均延迟
    }
    
    /**
     * 计算单个字符的延迟时间
     */
    private fun calculateCharDelay(char: Char): Long {
        return when {
            char in "。！？；：" -> PUNCTUATION_DELAY
            char in "，、" -> COMMA_DELAY
            char in "()（）[]【】{}｛｝" -> BRACKET_DELAY
            char == '\n' -> NEWLINE_DELAY
            char == ' ' -> SPACE_DELAY
            char.isDigit() || (char.isLetter() && char.code < 128) -> ENGLISH_SPEED
            char.code > 128 -> CHINESE_SPEED
            else -> DEFAULT_CHAR_SPEED
        }
    }
    
    /**
     * 获取活跃任务数量
     */
    fun getActiveTaskCount(): Int = activeTasks.size
    
    /**
     * 清理资源
     */
    fun cleanup() {
        cancelAllTypingEffects()
        handler.removeCallbacks(batchUpdateRunnable)
        isProcessingBatch.set(false)
        Log.d(TAG, "打字机效果管理器已清理")
    }
}
