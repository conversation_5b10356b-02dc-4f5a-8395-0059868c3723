package com.healthlink.hms.business.car_seat.viewModel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.healthlink.hms.business.car_seat.SERVER_IP
import com.healthlink.hms.business.car_seat.connect.SeatWSManager
import dagger.hilt.android.lifecycle.HiltViewModel
import data.ControlAction
import data.ControlMessage
import data.SensorMessage
import data.SensorType
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonElement
import javax.inject.Inject


data class SensorUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val lastSensorData: SensorMessage? = null
)

@HiltViewModel
class SeatViewModel @Inject constructor(
    private val webSocketManager: SeatWSManager
) : ViewModel() {
    companion object {
        private const val TAG = "SeatViewModel"
    }
    val controlConnectionState = webSocketManager.controlConnectionState
    val sensorConnectionState = webSocketManager.sensorConnectionState
    val controlMessages = webSocketManager.controlMessages
    val sensorMessages = webSocketManager.sensorMessages
    val errors = webSocketManager.errors

    // UI状态
    private val _uiState = MutableStateFlow(SensorUiState())
    val uiState = _uiState.asStateFlow()

    /**
     * 连接到服务器
     */
    fun connect(serverUrl: String, userId: String, deviceId: String) {
        viewModelScope.launch {
            try {
                // 并行建立两个连接
//                launch { webSocketManager.connectControl(serverUrl, userId) }
                launch { webSocketManager.connectSensor(serverUrl, deviceId) }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "连接失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 发送传感器数据
     */
    fun sendSensorData(sensorType: SensorType, data: JsonElement, deviceId: String) {
        viewModelScope.launch {
            val message = SensorMessage(
                type = "sensor_data",
                sensorType = sensorType,
                data = data,
                deviceId = deviceId
            )
            webSocketManager.sendSensorData(message)
        }
    }

    /**
     * 发送控制消息
     */
    fun sendControlMessage(type: String, action: ControlAction, userId: String) {
        viewModelScope.launch {
            val message = ControlMessage(
                type = type,
                action = action,
                userId = userId
            )
            if(webSocketManager.controlConnectionState.value.isConnected) {
                webSocketManager.sendControlMessage(message)
            } else {
                Log.i(TAG, "控制连接未建立")
                webSocketManager.scheduleControlReconnect(SERVER_IP, userId)
            }

        }
    }
    /**
     * 断开连接
     */
    fun disconnect() {
        viewModelScope.launch {
            webSocketManager.disconnect()
        }
    }

    override fun onCleared() {
        super.onCleared()
        viewModelScope.launch {
            webSocketManager.disconnect()
        }
    }
}