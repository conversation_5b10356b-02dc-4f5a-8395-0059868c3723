package com.healthlink.hms.business.car_seat.ui.suggestion

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.healthlink.hms.R

// 定义UI状态枚举
enum class PostureState {
    INITIAL,
    TRIP_STARTED,
    TRIP_END
}

/**
 * 1. 行程未开始界面
 */
@Composable
fun TripNotStartedScreen(onStartTrip: () -> Unit) {
    // 状态管理
    var postureState by remember { mutableStateOf(PostureState.INITIAL) }
    postureState = PostureState.TRIP_END
    BaseContent(leftContent = {
        CardCommonContent("实时压力分布",R.drawable.icon_pressure_dis) {
            Box(modifier = Modifier
                .fillMaxSize()){
                // 图片
                ChairBaseContent(postureState)
                // 检测结果
                if (postureState == PostureState.TRIP_END) {
                    pressStateContent()
                }
                // 按钮
                TriggerTripContainer(
                    title = when(postureState) {
                        PostureState.INITIAL -> "开始"
                        else -> "结束"
                    },
                    icon = when(postureState) {
                        PostureState.INITIAL -> R.drawable.icon_start
                        PostureState.TRIP_STARTED -> R.drawable.icon_end
                        else -> R.drawable.icon_start
                    },
                    colors = when(postureState) {
                        PostureState.INITIAL -> ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF07C160)
                        )
                        else -> ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF0087FF)
                        )
                    },
                    onClick = {
                        // 状态切换逻辑
                        postureState = when(postureState) {
                            PostureState.INITIAL -> {
//                                onStartTrip() // 调用外部回调
                                PostureState.TRIP_STARTED
                            }
                            PostureState.TRIP_STARTED -> PostureState.TRIP_END
                            PostureState.TRIP_END -> PostureState.INITIAL
                        }
                    },
                    message = when(postureState) {
                        PostureState.INITIAL -> ""
                        PostureState.TRIP_STARTED -> "数据采集中......"
                        else -> ""
                    },
                    modifier = Modifier.fillMaxWidth().height(64.dp))
            }
        }
    }, rightContent = {
        when(postureState) {
            // 初始化
            PostureState.INITIAL -> RealTimePostureContent("暂无")
            // 开始
            PostureState.TRIP_STARTED -> {
                RealTimePostureContent("请向右侧稍倾斜身体，使脊柱保持在中立位")
                Spacer(modifier = Modifier.height(30.dp))
                // 腰背舒缓
                CardCommonContent("腰背舒缓",R.drawable.icon_seat_waist) {
                    Column(modifier = Modifier.fillMaxSize()) {
                        Text("长时间开车注意调整坐姿或者座椅哦",fontSize = 24.sp)
                        Spacer(modifier = Modifier.height(50.dp))
                        Row {
                            Button(onClick = {},
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFF07C160)
                                ),
                                modifier = Modifier.size(280.dp,64.dp)){
                                Text("座椅调整", fontSize = 24.sp, color = Color(0xFFFFFFFF))
                            }

                            Spacer(modifier = Modifier.width(20.dp))

                            Button(onClick = {},
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFF32C5FF)
                                ),
                                modifier = Modifier.size(280.dp,64.dp)){
                                Text("打开腰托",fontSize = 24.sp, color = Color(0xFFFFFFFF))
                            }
                        }
                    }
                }
            }
            PostureState.TRIP_END -> {
                // 行程建议
                CardCommonContent("行程小结",R.drawable.icon_trip) {
                    Column(modifier = Modifier.fillMaxSize()) {
                        Text("长时间坐姿倾斜容易使脊柱局部水平向受力不均，从而增加脊柱负担尤其胸腰椎段，增加脊柱侧弯风险，容易诱发腰椎问盘膨出等，严重时会感到腰部疼痛，腰肌疲劳等。 请及时调整并维持正常坐姿，如有条件尽量避免长时间坐立，定时起身活动。",fontSize = 24.sp)
                        Spacer(modifier = Modifier.height(50.dp))
                        Row {
                            Button(onClick = {},
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFF07C160)
                                ),
                                modifier = Modifier.size(280.dp,64.dp)){
                                Text("座椅调整", fontSize = 24.sp, color = Color(0xFFFFFFFF))
                            }

                            Spacer(modifier = Modifier.width(20.dp))

                            Button(onClick = {},
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFF32C5FF)
                                ),
                                modifier = Modifier.size(280.dp,64.dp)){
                                Text("打开腰托",fontSize = 24.sp, color = Color(0xFFFFFFFF))
                            }
                        }
                    }
                }
            }
        }
    })
}

@Composable
fun ChairBaseContent(postureState: PostureState){
    Column(horizontalAlignment = Alignment.CenterHorizontally, verticalArrangement = Arrangement.Center, modifier = Modifier.fillMaxSize()) {
        Box(modifier = Modifier.width(570.dp).wrapContentHeight()) {
            // 背景 - 座椅
            Icon(painter = painterResource(id = R.drawable.img_chair), contentDescription = "chair", modifier = Modifier.wrapContentSize(), tint = Color.Unspecified)
            when(postureState) {
                PostureState.INITIAL -> {Icon(painter = painterResource(id = R.drawable.img_chair_net_mask), contentDescription = "chair_mask", modifier = Modifier.padding(start = 118.dp,top = 138.dp).size(346.dp,548.dp),tint = Color.Unspecified)}
                PostureState.TRIP_STARTED, PostureState.TRIP_END -> {
                    Icon(painter = painterResource(id = R.drawable.img_heapmap_up), contentDescription = "heapmap_up", modifier = Modifier.padding(start = 125.dp, top = 100.dp).size(320.dp,404.dp),tint = Color.Unspecified)
                    Icon(painter = painterResource(id = R.drawable.img_heapmap_down), contentDescription = "heapmap_down", modifier = Modifier.padding(start = 93.dp,top = 455.dp).size(390.dp,260.dp),tint = Color.Unspecified)
                }
            }
        }
    }
}

/**
 * 坐姿实时建议
 */
@Composable
fun RealTimePostureContent(message: String) {
    CardCommonContent("实时坐姿建议",R.drawable.icon_posture) {
        Text(message,
            modifier = Modifier.fillMaxWidth().wrapContentHeight(),
            fontSize = 24.sp,
            color = Color(0xFF333333),
        )
    }
}


@Preview(showBackground = true,
    device = "spec:width=1920px,height=1128px,dpi=240"
)
@Composable
fun TripNotStartedScreenPreview() {
    TripNotStartedScreen(onStartTrip = {})
}