package com.healthlink.hms.business.doctorcall

import android.Manifest
import android.app.Activity
import android.app.ActivityManager
import android.app.Service
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.ServiceConnection
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioFormat
import android.media.AudioManager
import android.media.MediaRecorder
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import android.view.View
import androidx.lifecycle.Observer
import com.cincc.reduce.CallCtrl
import com.cincc.reduce.constance.CallStatus
import com.cincc.reduce.constance.RegisterStatus
import com.cincc.reduce.iface.CallEvent
import com.cincc.reduce.iface.InitCallBack
import com.healthlink.hms.BuildConfig
import com.healthlink.hms.HmsSettings
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.base.AppContext
import com.healthlink.hms.business.doctorcall.DoctorCallModel.DoctorCallState
import com.healthlink.hms.business.doctorcall.view.DoctorCallView
import com.healthlink.hms.sceneEngine.SceneManager
import com.healthlink.hms.utils.HMSDialogUtils
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.ToastUtil
import com.petterp.floatingx.FloatingX
import com.petterp.floatingx.assist.FxGravity
import com.petterp.floatingx.assist.FxScopeType
import com.petterp.floatingx.imp.FxAppLifecycleProvider
import com.petterp.floatingx.listener.IFxViewLifecycle
import com.vmadalin.easypermissions.EasyPermissions
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

object DoctorCallManager {

    const val TAG = "DoctorCall"
    // 权限请求代码
    const val PERMISSION_REQUEST_CODE = 0x1001
    //采样率
//    const val AUDIO_SAMPLE_RATE: Int = 44100
    const val AUDIO_SAMPLE_RATE: Int = 8000
    /**
     * 1 corresponds to AudioFormat.CHANNEL_IN_MONO;
     * 2 corresponds to AudioFormat.CHANNEL_IN_STEREO
     */
    const val DEFAULT_CHANNEL_COUNT: Int = 1
    // 渠道
    const val CHANNEL_ID = "GWM-EC-BJS2221001C026-TEST";

    // 上行通道的音源代码
    const val UPSTREAM_MIC_SOURCE = MediaRecorder.AudioSource.MIC
//    const val UPSTREAM_MIC_SOURCE = 2019
//    const val UPSTREAM_MIC_SOURCE = MediaRecorder.AudioSource.VOICE_COMMUNICATION
//    const val UPSTREAM_MIC_SOURCE = AudioManager.STREAM_VOICE_CALL
    // 上行通道的焦点代码
//    const val UPSTREAM_FOCUS_CODE = 0
//    const val UPSTREAM_FOCUS_CODE = 2
//      const val UPSTREAM_FOCUS_CODE = 19
//    const val UPSTREAM_FOCUS_CODE = 100
//    const val UPSTREAM_FOCUS_CODE = 101
//    const val UPSTREAM_FOCUS_CODE = 102
//    const val UPSTREAM_FOCUS_CODE = 103
//    const val UPSTREAM_FOCUS_CODE = 104
//    const val UPSTREAM_FOCUS_CODE = 105
    const val UPSTREAM_FOCUS_CODE = AudioManager.STREAM_VOICE_CALL

        // 下行通道的焦点代码
//    const val DOWN_STREAM_FOCUS_CODE = 0
    const val DOWN_STREAM_FOCUS_CODE = 2

    fun doRequestFocus(context:Activity?){
        // 申请下行(扬声器）焦点
        requestAudioFocusForDownStream(context)
    }


    /**
     * 判断是否已经初始化完成
     */
    var isInitialized = false
        private set

    /**
     * 记录是否正在初始化中
     */
    private var initializing = false

    /**
     * 记录上一次挂断电话的时间，在方法 call() 中用来检查两次拨打是否过于频繁，目前是间隔不能超过 2000 ms
     */
    private var lastHangUpTime = 0L



    /**
     * 方便获取网络电话 SDK 的实例
     */
    private val callCtrl: CallCtrl
        get() = CallCtrl.getInstance()

    /**
     * 当前的通话会话中所需的数据模型
     * 每次通话会话结束后都需要丢弃上一次的数据模型
     */
    private var currentSessionModel: DoctorCallModel? = null

    /**
     * 当前的通话会话的状态获取和设定的便捷方式
     */
    private var doctorCallState
        get() = currentSessionModel?.doctorCallState?.value
        set(value) {
            currentSessionModel?.doctorCallState?.postValue(value)
        }

    /**
     * 记录上一次的通话状态
     */
    private var lastCallState: DoctorCallState? = null

    /**
     * 记录拨打电话时的错误信息，用于显示错误信息
     */
    private var callErrorMsg: String? = null

    /**
     * 标记是否正在等待录音权限
     */
    private var isWaitingRecordPermission = false

    /**
     * 判断是否正在通话中
     */
    val inCallSession: Boolean
        get() = currentSessionModel != null

    /**
     * 通话中
     */
    var inTalking : Boolean = false

    /**
     * 电话医生录音后台服务
     */
    var recordService: DoctorCallService? = null

    /**
     * 记录电话医生录音服务是否已经绑定
     */
    private var isServiceBind = false

    /**
     * 电话医生录音服务绑定
     */
    private val recordConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName, service: IBinder) {
            recordService = (service as RecordBinder).service
            isServiceBind = true
        }

        override fun onServiceDisconnected(name: ComponentName) {
            isServiceBind = false
        }
    }

    /**
     * 下行焦点监听器，监听到音频焦点变化时，挂断电话
     */
    private val downStreamAudioFocusChangeListener =
        AudioManager.OnAudioFocusChangeListener { focusChange: Int ->
            log("focusChanged: $focusChange")
            when (focusChange) {
                AudioManager.AUDIOFOCUS_LOSS,
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK,
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                    // TODO 是否要在失去焦点的时候挂断电话
                    hangup()
                    ToastUtil.makeText(
                        HmsApplication.appContext,
                        "您当前的音频通道被占用，健康医生电话已挂断",
                        300
                    ).show()
                    log("失去下行焦点")
                }
            }
        }


    /**
     * 获取音频管理器
     */
    private val audioManager
        get() = HmsApplication.appContext.getSystemService(Service.AUDIO_SERVICE) as? AudioManager


    /**
     * 初始化电话医生服务，目前会在 MainActivity 的 onCreate 中调用，每次拨打电话中也会检查调用
     */
    fun init(context: Activity?, needShowError: Boolean, onSuccess: () -> Unit = {}) {

        if (isInitialized) {
            onSuccess()
            log("WARNING：重复初始化")
            return
        }
        if (initializing) {
            return
        }
        val vin = MMKVUtil.getVinCode()
//        val channelId = CHANNEL_ID
        if (vin.isNullOrEmpty()) {
            log("ERROR：初始化失败，vin is null")
            return
        }
        initializing = true
//        Handler().post {
//        callCtrl.clearInfo(context)

        val sipURL = BuildConfig.SIP_URL
        var channelId = BuildConfig.SIP_CHANNEL_ID

        log("初始化网络电话医生 $vin")
        callCtrl.init(context?.application, vin, channelId, sipURL, object : InitCallBack {
            override fun onInitSuccess(ext: String) {
                log("onInitSuccess, ext: $ext")
                initializing = false
                isInitialized = true
                onSuccess()
                registerCallEvent()
            }

            override fun onInitError(errorMsg: String) {
                log("onInitError, errorMsg: $errorMsg")
                initializing = false
                if (needShowError) {
                    context ?: return
                    runOnUi {
                        ToastUtil.makeText(context, "电话医生初始化失败", 300).show()
                    }
                }
            }
        })
//        }
    }

    fun onRecordPermissionGranted(context: Activity?) {
        if (isWaitingRecordPermission) {
            call(context)
        }
        isWaitingRecordPermission = false
    }

    fun call(context: Activity?) {
        if(!HmsSettings.DOCTOR_CALL_OPEN_FLAG || ActivityManager.isUserAMonkey()){
            Log.i(TAG, "doctor call is closed")
            return
        }

        if ((System.currentTimeMillis() - lastHangUpTime) < 5 * 1000) {
            context?.let {
                ToastUtil.makeText(it, "操作过于频繁，请稍后再试", 300).show()
            }
            return
        }

        // 先申请权限，再获取焦点
        val hasPermission = checkNeededPermission(context!!)
        if (!hasPermission) {
            log("未获取权限，开始申请权限...")
            requestPermission(context)
            return
        }
        log("获取权限成功.")
        isWaitingRecordPermission = false

        //优先获取焦点
        doRequestFocus(context)
    }

    /**
     * 拨打电话，在方法中会判断 SDK 是否初始化，必要权限是否授予，通话焦点是否被占有等情况，如果不满足条件会弹出相应的提示。
     * 在检查完毕后会正式调用 SDK 的拨打电话方法，这里需要使用 SDK 自采集音频的呼出方法，并且弹出通话悬浮窗。
     */
    fun doCall(context: Activity?) {
        context ?: return

        if (!isInitialized) {
            ToastUtil.makeText(context, "电话医生服务正在初始化中，请稍候片刻", 300).show()
            init(context, true)
            return
        }

        if ((System.currentTimeMillis() - lastHangUpTime) < 5 * 1000) {
            ToastUtil.makeText(context, "操作过于频繁，请稍后再试", 300).show()
            return
        }

        if (audioManager?.isMicrophoneMute == true) {
            ToastUtil.makeText(context, "您当前的麦克风被占用请稍后拨打。", 300).show()
            Log.i(TAG,"麦克风占用中")
            return
        }

        if (!isMicrophoneAvailable(context)) {
            ToastUtil.makeText(context, "您当前的麦克风不可用。", 300).show()
            Log.i(TAG,"麦克风不可用")
            return
        }

//        doRequestFocus(context)

        if (currentSessionModel != null) {
            clearDoctorCallModel("doCall")
        }
        val doctorCallView = DoctorCallView.create()
            .onMuteButtonClicked {
                if (doctorCallState != DoctorCallState.Talking) {
                    return@onMuteButtonClicked
                }
                val muted = currentSessionModel?.isMuted?.value ?: false
                currentSessionModel?.isMuted?.postValue(!muted)
            }
            .onKeyboardButtonClicked {
                if (doctorCallState != DoctorCallState.Talking) {
                    return@onKeyboardButtonClicked
                }
                val showKeyboard = currentSessionModel?.showKeyboard?.value ?: false
                currentSessionModel?.showKeyboard?.postValue(!showKeyboard)
            }
            .onHangupButtonClicked {
                lastHangUpTime = System.currentTimeMillis()
                hangup()
            }
            .onInput { key ->
                /**
                 * @param tapKey   int：二次拨号键 键值：
                 *                 1(1),2(2),3(3),4(4),5(5),6(6),
                 *                 7(7),8(8),9(9),10(*),0(0),11(#),
                 *                 12(A),13(B),14(C),15(D)
                 * @param dtmfType Int 二次播放方式
                 *                 0：Info消息，application/dtmf-relay
                 *                 1：Info消息，application/dtmf
                 *                 2：带内
                 */
                Log.i(TAG,"sendDtmf, key: $key")

                callCtrl.sendDtmf(key, 0)
                val input = currentSessionModel?.input?.value ?: ""

                // 拨号盘转义
                var displayChar = key.toString()
                if(key == 10) {
                    displayChar = "*"
                }
                else if(key == 11){
                   displayChar = "#"
                }
                currentSessionModel?.input?.postValue(input + displayChar)
            }
            .build(AppContext.sAppContext)
        val helper = FloatingX.install {
            setContext(AppContext.sAppContext)
            setTag(TAG)
            setLayoutView(doctorCallView)
            setEnableAnimation(true)
            setScopeType(FxScopeType.APP)
            setGravity(FxGravity.LEFT_OR_CENTER)
        }
        helper.configControl.addViewLifecycleListener(object : IFxViewLifecycle {
            override fun attach(view: View) {
                Log.i(TAG,"doctor call view was attached")
                if (currentSessionModel != null) {
                    Log.i(TAG,"doctor call view was inited，return")
                    return
                }
                createDoctorCallModel().let {
                    doctorCallView.viewModel = it
                    currentSessionModel = it
                }
                callOut(context)
                doctorCallState = DoctorCallState.Calling
                //启动录音服务
                startAudioRecord(AppContext.sAppContext)
            }
        })
        runOnUi {
            helper.show()
        }
        // 拨打电话后，不再触发干预类场景（严重健康关怀及高反关怀2）
        SceneManager.resetSceneCountAfterCallDoctorService()
        // 注册音量监听器
//        registVolumeListener()
        // 启动监听线程
        try {
            if(networkMonitorThread!=null){
                try {
                    if (!networkMonitorThread?.isInterrupted!!) {
                        networkMonitorThread?.interrupt()
                    }
                }catch (ex: Exception){
                    log(" network monitor still alive , interrupt fail , ${ex.message}")
                }
            }
            networkMonitorThread = Thread(runnable)
            isNetworkMonitorStoped = false
            networkMonitorThread?.start()

        }catch(ex: Exception){
            log("fail to start network monitor error , ${ex.message}")
        }

    }


    /**
     * 在收到 SDK 通话结束的回调时（对面挂断），内部会自己调用该方法，无需手动调用。
     * 并且因为挂断操作其实也是内部完成，这个方法暂时没有外部调用的时机。
     */
    @OptIn(DelicateCoroutinesApi::class)
    fun hangup() {
        try {
            Log.i(TAG,"hangup, callState: $doctorCallState")
//            lastHangUpTime = System.currentTimeMillis()
            // 停止录音
            stopAudioRecord(AppContext.sAppContext)
            // 释放焦点
            releaseAudioFocus()
    //            unRegistVolumeListener()
            // 隐藏面板
            dismissDial()
            // 清除数据
            clearCurDoctorCallModel("hangup")
            // 挂断
            GlobalScope.launch(Dispatchers.IO) {
                callCtrl.hangup()
            }
    //        doctorCallState = DoctorCallState.Idle

            isNetworkMonitorStoped = true
            if(networkMonitorThread!=null){
                try {
                    if (!networkMonitorThread?.isInterrupted!!) {
    //                    log(" interrupted network monitor")
    //                    networkMonitorThread?.interrupt()
                        log(" stop network monitor")
                        networkMonitorThread?.stop()
                    }
                }catch (ex: Exception){
                    log(" network monitor still alive , interrupt fail , ${ex.message}")
                }
                networkMonitorThread = null
            }
        }catch (ex: Exception){
            Log.i(TAG,"hangup error , ${ex.message}")
        }
    }

    /**
     * 实际调用 SDK 呼出电话的方法
     */
    private fun callOut(context: Context?) {
        if (context == null) {
            log("callOut, context is null")
            return
        }
        Log.i(TAG,"callOut, callState: ${currentSessionModel?.doctorCallState?.value}")
        try {

            if (context as Activity != null) {
                //  STREAM_RING 2 通信类型应用使用
                callCtrl.setAudioCustomRenderParam(
                    0,
                    AUDIO_SAMPLE_RATE, 1, AudioFormat.ENCODING_PCM_16BIT, 20, false
                )
                callCtrl.setUseCustomAudio(AUDIO_SAMPLE_RATE, 1)
                CoroutineScope(Dispatchers.IO).launch {
                    try {
                        callCtrl.mute(false)
                    } catch (ex: Exception) {
                        log("callOut error , ${ex.message}")
                    }
                }
                callCtrl.callOut(context.application, true, true)
            }
        } catch (e: Exception) {
            Log.i(TAG,"callOut exception: ${e.message}")
        }
    }

    /**
     * 关闭悬浮窗，悬浮窗的消失意味着一次通话会话的结束，需要管理好所有使用的资源
     */
    private fun dismissDial() {
        runOnUi {
            if (!FloatingX.isInstalled(TAG)) {
                return@runOnUi
            }
             FloatingX.control(TAG).cancel()
        }
//        clearDoctorCallModel()
    }

    /**
     * 清除通话会话的数据模型
     */
    private fun clearCurDoctorCallModel(fromTag: String?) {
        clearDoctorCallModel(fromTag)
    }

    /**
     * 检查下是否授予了录音权限
     */
    private fun checkNeededPermission(activity: Activity): Boolean {
        val permissions = arrayOf(Manifest.permission.RECORD_AUDIO)
        Log.i(TAG,"正在检查麦克风权限...")
        return EasyPermissions.hasPermissions(activity, *permissions)
    }

    /**
     * 请求录音权限
     */
    private fun requestPermission(activity: Activity) {
        Log.i(TAG,"开始请求麦克风权限...")
        try {
            val permissions = arrayOf(Manifest.permission.RECORD_AUDIO)
            EasyPermissions.requestPermissions(
                host = activity,
                rationale = "需要以下权限才能正常使用通话功能",
                requestCode = PERMISSION_REQUEST_CODE,
                perms = permissions
            )
            isWaitingRecordPermission = true
        }catch (ex:Exception){
            Log.i(TAG,"申请权限时发生异常，异常信息：${ex.message}")
        }
    }

    /**
     * 显示错误弹窗
     */
    private fun showErrorDialog() {
        runOnUi {
            val context = FxAppLifecycleProvider.getTopActivity() ?: return@runOnUi
            HMSDialogUtils.showHMSDialog(
                context,
                R.layout.hms_dialog_confirm_auto_align,
                "您的通话网络异常",
                "重新拨打",
                "取消"
            ) { isPositive ->
                if (isPositive) {
                    call(context)
                } else {
                    // 挂断，释放焦点
                    log("showErrorDialog, hangup")
                    hangup()
                    doctorCallState = DoctorCallState.Idle
                }
            }
        }
    }

    /**
     * 显示错误弹窗
     */
    private fun showErrorDialogForForceHangUp() {
        runOnUi {
            val context = FxAppLifecycleProvider.getTopActivity() ?: return@runOnUi
            HMSDialogUtils.showHMSDialog(
                context,
                R.layout.hms_dialog_confirm_auto_align,
                "您的通话网络异常",
                "重新拨打",
                "取消"
            ) { isPositive ->
                if (isPositive) {
                    if(!HmsApplication.isNetworkConn()){
                        showErrorDialogForForceHangUp()
                    }else{
                        call(context)
                    }
                } else {
                    doctorCallState = DoctorCallState.Idle
                }
            }
        }
    }

    /**
     * 响应用户的静音操作
     */
    private val mutedObserver = Observer<Boolean> { value ->
        currentSessionModel?.doctorCallState?.value?.let { callState ->
            if (callState == DoctorCallState.Talking) {
                CoroutineScope(Dispatchers.IO).launch {
                    try {
                        callCtrl.mute(value)
                    } catch (ex: Exception){
                        log("mutedObserver error , ${ex.message}")
                    }
                }
            }
        }
    }

    /**
     * 响应通话状态的变化
     */
    private val callStateObserver = Observer { callState: DoctorCallState ->
        log("callStateObserver, callState: $callState")
        when (callState) {
            DoctorCallState.Calling -> {
            }

            DoctorCallState.Error -> {
//                stopAudioRecord(AppContext.sAppContext)
//                dismissDial()
                hangup()
                showErrorDialog()
            }

            DoctorCallState.Talking -> {
                if(isServiceBind){
                    recordService?.startRecording()
                }
//                startAudioRecord(AppContext.sAppContext)
                currentSessionModel?.showKeyboard?.postValue(true)
            }

            DoctorCallState.Idle -> {
                if (lastCallState != null && lastCallState !== DoctorCallState.Idle) {
                    dismissDial()
                    clearCurDoctorCallModel("Idle")
                }
                GlobalScope.launch(Dispatchers.IO) {
                    stopAudioRecord(AppContext.sAppContext)
                }
            }

            DoctorCallState.Hanging -> {
                dismissDial()
                GlobalScope.launch(Dispatchers.IO) {
                    stopAudioRecord(AppContext.sAppContext)
                }
            }
        }
        lastCallState = callState
    }

    /**
     * 创建通话会话的数据模型
     */
    private fun createDoctorCallModel(): DoctorCallModel {
        return DoctorCallModel().apply {
            isMuted.observeForever(mutedObserver)
            doctorCallState.observeForever(callStateObserver)
        }
    }

    /**
     * 清除通话会话的数据模型
     */
    private fun clearDoctorCallModel(fromTag: String?) {
        fromTag?.let { log("clearDoctorCallModel from $it") }
        currentSessionModel?.apply {
            doctorCallState.removeObserver(callStateObserver)
            isMuted.removeObserver(mutedObserver)
        }
        currentSessionModel = null
    }

    /**
     * 在 SDK 中注册相关事件的监听回调
     */
    private fun registerCallEvent() {
        callCtrl.setCallEvent(object : CallEvent {
            override fun onRegisterState(registerStatus: RegisterStatus?, message: String?) {
                log("onRegisterState, callStatus: $registerStatus, message: $message")
            }

            override fun onCallState(callStatus: CallStatus?, message: String?) {
                log("onCallState, callStatus: $callStatus, message: $message")
                when (callStatus) {
                    // 按 SDK 本身设计， CallStatus.CALL_CONNECT 应该是在对方接听后才会回调，
                    // 但实际调试中发现，CALL_CONNECT 总会在 CALL_MEDIA_SUCCESS 之前回调，所以这里
                    // 就没法真的实现显示对方接听后才显示通话中的效果，而只能在认为建立链接后就算通话中了
                    CallStatus.CALL_OUT_GOING_PROGRESS,
                    CallStatus.CALL_OUT_GOING_RINGING -> {
                        doctorCallState = DoctorCallState.Calling
                    }

                    CallStatus.CALL_CONNECT,
                    CallStatus.CALL_MEDIA_SUCCESS -> {
                        doctorCallState = DoctorCallState.Talking
                        inTalking = true
                    }

                    CallStatus.CALL_NET_INTERRUPT,
                    CallStatus.CALL_NET_ERROR->{

                    }

                    CallStatus.CALL_ERROR,
                    CallStatus.CALL_PARAM_ERROR,
                    CallStatus.CALL_MEDIA_ERROR -> {
                        doctorCallState = DoctorCallState.Error
                        callErrorMsg = callStatus.des
                    }
                    CallStatus.CALL_POWER_DOWN -> {
                        doctorCallState = DoctorCallState.Idle
                        isInitialized = false
                    }
                    CallStatus.CALL_RELEASE,
                    CallStatus.CALL_END -> {
                        isNetworkMonitorStoped = true
                        doctorCallState = DoctorCallState.Idle
                        releaseAudioFocus()
                        inTalking = false
                    }
                    CallStatus.CALL_RETRY->{
                        // 重新连接通话
//                        doctorCallState = DoctorCallState.Error
//                        callErrorMsg = callStatus.des
                    }
                    CallStatus.CALL_INIT_RETRY -> {
                        // 重新初始化
//                        doctorCallState = DoctorCallState.Error
//                        callErrorMsg = callStatus.des
                    }
                    else -> {

                    }
                }
            }

            override fun onCallingIn() {
                log("onCallingIn")
            }

            override fun onMessage(code: Int, message: String?) {
                log("onMessage, code: $code, message: $message")
            }

            override fun onDebugEvent(tag: String?, message: String?) {

                tag?.let {
                    if(it.contains("SIPSTACK")){
                        return
                    }
                }

                message?.let {
                    if(it.contains("pullPlaybackAudioFrame")||
                        it.contains("SIPSTACK")){
                        return
                    }
                    log("onDebugEvent[$tag], $it,")
                }

            }

        })
    }

    /**
     * 开始后台录音服务，需要在通话媒体连接成功后也就是 SDK 回调到 `CALL_MEDIA_SUCCESS` 后调用
     */
    private fun startAudioRecord(context: Context?) {
        log("startAudioRecord to bind service")
        context ?: return
        val intent = Intent(context, DoctorCallService::class.java)
        context.bindService(intent, recordConnection, Context.BIND_AUTO_CREATE)
    }

    /**
     * 停止后台录音服务，需要在通话结束时调用
     */
    private fun stopAudioRecord(context: Context?) {
        context ?: return
        if (isServiceBind) {
            isServiceBind = false
            context.unbindService(recordConnection)
        }
    }

    /**
     * 在 debug 包中打印开发日志
     */
    fun log(msg: String) {
        if (!BuildConfig.DEBUG) {
            return
        }
        Log.d(TAG, msg)
    }

    private fun runOnUi(block: () -> Unit) {
        Handler(Looper.getMainLooper()).post(block)
    }

    var audioFocusRequest: AudioFocusRequest? = null
    private fun requestAudioFocusForDownStream(context: Activity?) {
        var builder = AudioAttributes.Builder()
        builder.setUsage(DOWN_STREAM_FOCUS_CODE)
        var aa = builder.build()

        audioFocusRequest = AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN_TRANSIENT)
            .setAudioAttributes(aa)
            .setAcceptsDelayedFocusGain(true)
            .setOnAudioFocusChangeListener (downStreamAudioFocusChangeListener )
            .build()
        // 会打印语音进程的打印。
        log("request audioFocusRequest $audioFocusRequest")
        if(audioFocusRequest!=null) {
            // 申请上行(麦克风）焦点
            val result = audioManager?.requestAudioFocus(audioFocusRequest!!)
            // 获取焦点结果处理
            if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                // 音频焦点请求成功，可以开始播放音频
                Log.i(TAG,"获取下行(扬声器）焦点成功")
                doCall(context)
            } else {
                Log.i(TAG,"获取下行(扬声器）焦点失败，退出")
                hangup()
                val msg = context?.resources?.getString(R.string.doctor_call_audio_busy_msg)
                HMSDialogUtils.showHMSDoctorPhoneExitsDialog(
                    context!!,
                    R.layout.hms_dialog_doctor_phone_exist,
                    msg!!,
                    "知道了"
                ) {}

            }
        }else{
            // 构建焦点请求失败
            log("构建焦点请求失败")
        }
    }

    private fun releaseAudioFocus() {
        if (audioFocusRequest != null) {
            try {
                log("release audioFocusRequest $audioFocusRequest")
                val result = audioManager?.abandonAudioFocusRequest(audioFocusRequest!!)
                if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                    // 成功释放音频焦点
                    Log.i(TAG, "释放下行(扬声器)焦点成功")
                } else {
                    // 释放焦点失败
                    Log.i(TAG, "释放下行(扬声器)焦点失败")
                }
            }catch (ex:Exception){
                Log.i(TAG,"释放焦点时发生错误，${ex.message}")
            }
        } else {
            log("releaseAudioFocus audioFocusRequest is null")
        }
    }


    private var networkMonitorThread: Thread? = null
    private var isNetworkMonitorStoped = false
    private var networkDisconnectedTime : Long = -1L
    private var preNetworkState = true
    val runnable = Runnable {
        try {

            networkDisconnectedTime = System.currentTimeMillis()

            while (!isNetworkMonitorStoped) {


                var currentNetworkState = HmsApplication.isNetworkConn()
                if (currentNetworkState != preNetworkState) {
                    preNetworkState = currentNetworkState
                    // 如果是从有网变为无网
                    if(!currentNetworkState ){
                        // 如果是通话中，记录无网开始的时间
//                        if(inTalking){
//                            Log.i(TAG, "record network change time.")
//                            networkDisconnectedTime = System.currentTimeMillis()
//                        }
//                        // 否则直接挂断
//                        else{
//                            Log.i(TAG, "hangup after network disconnect immediately on not calling state.")
                        Log.i(TAG, "hangup after network disconnect immediately .")
                        runOnUi {
                            lastHangUpTime = System.currentTimeMillis()
                            hangup()
                            showErrorDialogForForceHangUp()
                        }
//                        }

                    }else{
                        networkDisconnectedTime = -1L
                    }
                }
                // 如果网络未发生变化
                else{
                    // 如果是无网状态
                    if(!currentNetworkState ){
                        // 如果是呼叫中
                        if(inTalking){
                            var networkDisconnectedPeriod = System.currentTimeMillis() - networkDisconnectedTime
                            // 网络断开30秒未连接上，直接挂断，通话中
                            if(networkDisconnectedPeriod > 30000){
                                Log.i(TAG, "hangup after network disconnect 30s.")
                                runOnUi {
                                    lastHangUpTime = System.currentTimeMillis()
                                    hangup()
                                    showErrorDialogForForceHangUp()
                                }
                            }
                        }
                    }
                }

                Thread.sleep(1000L)
            }
        }catch (ex: Exception){
            log("network monitoring exception , ${ex.message}")
            ex.printStackTrace()
        }
    }
}