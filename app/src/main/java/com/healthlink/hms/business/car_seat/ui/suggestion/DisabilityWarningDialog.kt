package com.healthlink.hms.business.car_seat.ui.suggestion

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.healthlink.hms.R

/**
 * 4. 失能预警全屏对话框
 */
@Composable
fun DisabilityWarningDialog(
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            dismissOnBackPress = false,
            dismissOnClickOutside = false
        )
    ) {
        Box(
            modifier = Modifier
                .clip(shape = RoundedCornerShape(30.dp))
                .size(960.dp,560.dp)
                .background(Color(0xFFF9FBFD))
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Spacer(modifier = Modifier.height(70.dp))

                Icon(
                    painter = painterResource(R.drawable.icon_disability_warning),
                    contentDescription = "警告",
                    modifier = Modifier.size(110.dp),
                    tint = Color.Unspecified
                )

                Spacer(modifier = Modifier.height(30.dp))

                Text(
                    text = "失能预警",
                    fontSize = 48.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFFFF3333)
                )

                Spacer(modifier = Modifier.height(40.dp))

                Text(
                    text = "检测到您可能处于疲劳状态\n建议立即停车休息",
                    fontSize = 34.sp,
                    color = Color(0xFF333333),
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    lineHeight = 48.sp
                )

                Spacer(modifier = Modifier.weight(1f))

                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(16.dp),
                    modifier = Modifier.height(88.dp).background(Color(0xFFF2F6FA))
                ) {
                    Button(
                        onClick = onDismiss,
                        modifier = Modifier
                            .weight(1f)
                            .height(56.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.White.copy(alpha = 0.2f)
                        ),
                        shape = RoundedCornerShape(28.dp)
                    ) {
                        Text(
                            text = "需要",
                            fontSize = 26.sp,
                            color = Color(0xFFFF3333)
                        )
                    }

                    VerticalDivider(modifier = Modifier.width(1.dp))

                    Button(
                        onClick = onConfirm,
                        modifier = Modifier
                            .weight(1f)
                            .height(56.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFF2F6FA)
                        ),

                    ) {
                        Text(
                            text = "不需要",
                            textAlign = TextAlign.Center,
                            fontSize = 26.sp,
                            color = Color(0xFF999999),
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true, device = "spec:width=1920px,height=1128px,dpi=240")
@Composable
fun DisabilityWarningDialogPreView() {
    DisabilityWarningDialog(onDismiss = {}, onConfirm = {})
}