package com.healthlink.hms.business.car_seat.ui.spinecare

import android.graphics.Bitmap
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.google.android.material.tabs.TabLayoutMediator
import com.google.gson.Gson
import com.healthlink.hms.R
import com.healthlink.hms.business.car_seat.ImageUtils
import com.healthlink.hms.business.car_seat.SERVER_IP
import com.healthlink.hms.business.car_seat.SERVER_PORT
import com.healthlink.hms.business.car_seat.viewModel.SeatViewModel
import com.healthlink.hms.databinding.ActivitySpineCareBinding
import com.healthlink.hms.ktExt.setUpSystemBar
import com.healthlink.hms.utils.TimeUtils
import dagger.hilt.android.AndroidEntryPoint
import data.ConnectionState
import data.ControlAction
import data.ResponseMessage
import data.SensorPicMessage
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json


data class ProcessedSensorData(
    val heatmapImage: Bitmap,     // 解码后的热力图
    val ifsit: String?,            // 坐姿状态
    val ifnormal: String?,        // 姿势是否正常
    val about: String?,           // 左右倾斜
    val around: String?,          // 前后倾斜
    val disability: Int?           // 失能等级
)

@AndroidEntryPoint
class SpineCareActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "SpineCareActivity"
    }

    private lateinit var binding: ActivitySpineCareBinding
    private val sensorViewModel: SeatViewModel by viewModels()

    private val tabTitles = arrayOf("坐姿建议", "脊柱评估", "座椅调整")
    private val tabImages = arrayOf(
        R.drawable.img_posture_suggestion,
        R.drawable.img_spine_assessment,
        R.drawable.img_seat_adjustment
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 设置状态栏
        window.apply {
            statusBarColor = Color.TRANSPARENT
            decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                    View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
        }
        setUpSystemBar()
        binding = ActivitySpineCareBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initViews()
        setupListeners()
        // 使用 ViewModel
        observeViewModel()
        connectToServer()
    }

    private fun observeViewModel() {
        // 观察连接状态
        lifecycleScope.launch {
            sensorViewModel.controlConnectionState.collect { state ->
                // 处理控制连接状态变化
                Log.d(TAG, "client控制连接状态: ${state.state}")
                when (state.state) {
                    ConnectionState.CONNECTED -> {
                        Log.d(TAG, "控制连接已建立")
                        Toast.makeText(this@SpineCareActivity, "控制连接已建立", Toast.LENGTH_SHORT).show()
                        // 连接建立后发送认证消息
                        sensorViewModel.sendControlMessage("control_auth", ControlAction.AUTHENTICATE, "user123")
                    }
                    ConnectionState.FAILED -> {
                        Log.e(TAG, "控制连接失败: ${state.error}")
                        Toast.makeText(this@SpineCareActivity, "控制连接失败: ${state.error}", Toast.LENGTH_SHORT).show()
                    }
                    else -> {
                        Log.d(TAG, "连接状态: ${state.state}")
                    }
                }
            }
        }

        // 观察传感器连接状态
        lifecycleScope.launch {
            sensorViewModel.sensorConnectionState.collect { state ->
                Log.d(TAG, "client传感器连接状态变化: ${state.state}")
            }
        }

        // 2. 监听控制消息 - 避免循环发送
        lifecycleScope.launch {
            sensorViewModel.controlMessages.collect { message ->
                Log.d(TAG, "client接收到控制消息: $message")

                // 根据消息类型进行不同处理，避免无限循环
                when (message) {
                    is ResponseMessage -> {

                        // 只在特定条件下发送认证消息
                        if (message.type == "control_response") {
                        }
                    }
                    else -> {
                        Log.d(TAG, "收到其他类型消息: ${message.type}")
                    }
                }
            }
        }

         // 3. 监听传感器消息
        lifecycleScope.launch {
            sensorViewModel.sensorMessages.collect { message ->
                Log.d(TAG, "client接收到传感器消息: $message")
                val response = message as SensorPicMessage
                response.sensor_data?.let {
                    Log.d(TAG, "传感器数据: $it")
                    // 解码压力热力图
                    val heatmapBitmap = ImageUtils.base64ToBitmap(it)
                    if (heatmapBitmap != null) {
                        binding.ivPlaceholder.setImageBitmap(heatmapBitmap)
                        binding.tvIfsit.text = "离开坐下: ${if(response.ifsit == "leave") "离开" else "坐下"}"
                        binding.tvIfnormal.text = "是否异常: ${if(response.ifnormal == null) "" else "正常姿态"}"
                        binding.tvAbout.text = "左右姿态: ${if(response.about == null) "" else ""}"
                        binding.tvAround.text = "前后姿态: ${if(response.around == null) "" else ""}"
                        binding.tvDisability.text = "失能等级: ${if(response.disability == 0) "正常" else "失能"}"
                    } else {
                        Log.e(TAG, "无法解码热力图图片")
                    }
                }
//                response.data?.let {
//                    Log.d(TAG, "传感器数据: $it")
//                    val sensorPicMessage = Gson().fromJson(it, SensorPicMessage::class.java)
//                    // 解码压力热力图
//                    val heatmapBitmap = ImageUtils.base64ToBitmap(sensorPicMessage.sensor_data)
//
//                    if (heatmapBitmap != null) {
//                        // 创建包含Bitmap的数据对象
////                        val processedData = ProcessedSensorData(
////                            heatmapImage = heatmapBitmap,null, null, null, null, null)
//
////                        listener?.onSensorDataReceived(processedData)
//                        binding.ivPlaceholder.setImageBitmap(heatmapBitmap)
//                    } else {
//                        Log.e(TAG, "无法解码热力图图片")
////                        listener?.onError("热力图解码失败")
//                    }
//                }
            }
        }

         // 4. 监听错误
         lifecycleScope.launch {
            sensorViewModel.errors.collect { error ->
                Log.e(TAG, "WebSocket错误: $error")
            }
        }
    }

    private fun connectToServer() {
        // 连接到服务器
        sensorViewModel.connect(
            serverUrl = "ws://${SERVER_IP}:${SERVER_PORT}",
            userId = "user12",
            deviceId = "device123"
        )
    }

    private fun initViews() {
        // 设置ViewPager
        binding.viewPager.adapter = object : FragmentStateAdapter(this) {
            override fun getItemCount() = tabTitles.size
            override fun createFragment(position: Int) = when (position) {
                0 -> PostureSuggestionFragment.newInstance()
                1 -> SpineAssessmentFragment.newInstance()
                2 -> SeatAdjustmentFragment.newInstance()
                else -> PostureSuggestionFragment.newInstance()
            }
        }

        // 设置TabLayout
        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = tabTitles[position]
        }.attach()
    }

    private fun setupListeners() {
        // 返回按钮点击事件
        binding.topArea.setOnClickListener {
            finish()
            overridePendingTransition(R.anim.activity_enter_slide_in_left,R.anim.activity_enter_slide_out_right)
        }

        // 标题区域点击事件
        binding.tvTitle.setOnClickListener {
            finish()
        }

        // 设置按钮点击事件
        binding.btnSettings.setOnClickListener {
            // TODO: 实现设置功能
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        sensorViewModel.disconnect()
    }
}