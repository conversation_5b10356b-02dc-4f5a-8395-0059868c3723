package com.healthlink.hms.business.car_seat.ui.spinecare

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.healthlink.hms.R
import com.healthlink.hms.business.car_seat.ui.assessment.HeaderAssessmentContent
import com.healthlink.hms.business.car_seat.ui.assessment.SpineAssessmentCard
import com.healthlink.hms.business.car_seat.ui.assessment.TotalTripCard
import com.healthlink.hms.business.car_seat.viewModel.SeatViewModel
import com.healthlink.hms.business.car_seat.views.GridConfig
import com.healthlink.hms.business.car_seat.views.ThoracicSpineChart
import com.healthlink.hms.business.car_seat.views.TitleInfo
import com.healthlink.hms.business.car_seat.views.generateSampleData
import dagger.hilt.android.AndroidEntryPoint

/**
 * 脊柱评估
 */
@AndroidEntryPoint
class SpineAssessmentFragment : Fragment() {

    private val sensorViewModel: SeatViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                SpineAssessmentScreen()
            }
        }
    }

    companion object {
        fun newInstance() = SpineAssessmentFragment()
    }
}

@Composable
fun SpineAssessmentScreen() {
    Row(modifier = Modifier.fillMaxSize().padding(start = 68.dp,top = 20.dp, end = 68.dp,bottom = 30.dp)) {
        // 左边
        Column(
            modifier = Modifier.weight(1f),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Top
        ) {
            // Assessment Header
            HeaderAssessmentContent()
            Spacer(modifier = Modifier.height(20.dp))
            // content
            SpineAssessmentCard()
//            SpineVisualizationCard(viewModel.spineData.value)
        }
        Spacer(modifier = Modifier.width(30.dp))
        // 右边
        Column(
            modifier = Modifier.weight(1.5f),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Top
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Top
            ) {
                // 累计行程card
                TotalTripCard()
                Spacer(modifier = Modifier.height(20.dp))
                Row(modifier = Modifier.fillMaxSize().weight(1f)) {
                    // 左边
                    Column(modifier = Modifier.weight(1.24f)) {
                        // 胸椎
                        ThoracicSpineChart(
                            model = TitleInfo(R.drawable.icon_seat_chest,"胸椎"),
                            dataPoints = generateSampleData(),
                            gridConfig = GridConfig.STANDARD
                        )
                        Spacer(modifier = Modifier.height(20.dp))
                        // 腰椎
                        ThoracicSpineChart(
                            model = TitleInfo(R.drawable.icon_seat_waist,"腰椎"),
                            dataPoints = generateSampleData(),
                            gridConfig = GridConfig.STANDARD
                        )
                        Spacer(modifier = Modifier.height(20.dp))
                        // 臀腿段
                        ThoracicSpineChart(
                            model = TitleInfo(R.drawable.icon_seat_buttom,"臀腿段"),
                            dataPoints = generateSampleData(),
                            gridConfig = GridConfig.STANDARD
                        )
                    }
                    Spacer(modifier = Modifier.width(20.dp))
                    // 右边
                    Column(modifier = Modifier.weight(1f)) {
                        Card(modifier = Modifier.fillMaxSize(),
                            shape = RoundedCornerShape(20.dp),
                            colors = CardDefaults.cardColors(containerColor = Color(0x80FFFFFF))) {
                            Text("脊柱风险分析", modifier = Modifier.fillMaxSize(), fontSize = 18.sp,)
                        }
                    }
                }
            }

        }
    }
}

@Preview(showBackground = true,
    device = "spec:width=1920px,height=1128px,dpi=240"
)
@Composable
fun SpineAssessmentScreenPreview() {
    SpineAssessmentScreen()
}

fun getStatusColor(status: SpineStatus): Color {
    return when (status) {
        SpineStatus.NORMAL -> Color(0xFF4CAF50)
        SpineStatus.MILD -> Color(0xFFFF9800)
        SpineStatus.MODERATE -> Color(0xFFFF5722)
        SpineStatus.SEVERE -> Color(0xFFF44336)
    }
}

data class SpineData(
    val cervicalStatus: SpineStatus = SpineStatus.NORMAL,
    val thoracicStatus: SpineStatus = SpineStatus.NORMAL,
    val lumbarStatus: SpineStatus = SpineStatus.MILD,
    val symmetryScore: Int = 85,
    val stabilityScore: Int = 78,
    val pressureScore: Int = 82,
    val overallScore: Int = 81,
    val recommendations: List<String> = listOf(
        "建议调整座椅高度，使大腿与地面平行",
        "增加腰部支撑，保持腰椎自然前凸",
        "每30分钟起身活动，缓解肌肉疲劳",
        "进行颈部和肩部拉伸运动"
    )
)

enum class SpineStatus(val displayName: String) {
    NORMAL("正常"),
    MILD("轻微"),
    MODERATE("中度"),
    SEVERE("严重")
}