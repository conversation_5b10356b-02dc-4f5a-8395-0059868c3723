# MedAI聊天界面模拟对话需求文档

## 1. 功能概述
实现一个医疗AI聊天界面的模拟对话功能，通过预设的对话内容，模拟用户与AI医生的真实交互场景，提供流畅的打字机效果和自然的对话节奏。

## 2. 模拟对话内容

### 2.1 对话场景：扁桃体发炎咨询
```
用户：Hi，小医
小医：我在，请问有什么可以帮您的？
用户：扁桃体发炎了怎么办？
小医：嗯，扁桃体发炎确实会让人很不舒服。请问您喉咙痛得厉害吗？有没有发烧或者其他症状？这样我们可以更好地判断情况。
用户：有点发烧
小医：明白了，发烧确实会让您更难受。请问体温大概多少度？发烧持续多长时间了？同时建议您多喝温水，可以先用温盐水漱口缓解喉咙不适。
用户：39度
小医：39度属于高热了，需要特别注意。建议您立即采取以下措施：用温水擦拭额头、腋下等部位。 可以服用布洛芬或对乙酰氨基酚，但一定要按照说明书剂量服用。 建议您尽快到附近医院就诊，可能需要血常规检查判断感染类型。如果出现呼吸困难、意识模糊等情况，请立即前往急诊。您现在感觉怎么样？需要我帮您导航附近医院么？
用户：我扁桃体发炎吃什么药啊
小医：您的情况需要根据具体症状来用药。建议您：可以服用布洛芬或对乙酰氨基酚，既能退烧又能缓解喉咙疼痛。如果是细菌感染引起的扁桃体炎，可能需要抗生素治疗，但必须由医生开具处方。含片或喷雾剂可以暂时缓解症状，比如西瓜霜含片或碘甘油喷雾。 重要提醒：**39度高热建议尽快就医，医生会根据检查结果开具最适合的药物。您现在能去医院吗？需要我帮您导航附近医院么？
用户：帮我导航到附近的医院吧
小医：好的，已经帮您导航到附近的医院，祝您早日康复，如果有什么问题随时叫我，我一直都在。
```

## 3. 核心功能需求

### 3.1 消息展示流程
- **初始化延迟**：界面初始化后延迟1秒开始展示第一条消息
- **顺序展示**：按照对话顺序逐条展示消息
- **消息间隔**：每条消息展示完毕后，间隔1秒展示下一条消息
- **循环执行**：重复以上步骤直到所有消息展示完毕

### 3.2 打字机效果
- **文字逐字显示**：所有消息的文字内容需要实现打字机效果
- **打字速度**：建议每个字符间隔50-80ms，营造自然的打字感
- **标点符号处理**：标点符号可适当延长停顿时间（100-150ms）
- **换行处理**：遇到换行符时适当增加停顿时间

### 3.3 自动滚动机制
- **底部检测**：在显示新消息前，检测RecyclerView是否已滚动到底部
- **智能滚动**：如果最后一条消息在可视区域底部，自动滚动到新消息位置
- **平滑滚动**：使用smooth scroll确保滚动动画自然流畅
- **用户控制**：如果用户主动向上滚动查看历史消息，暂停自动滚动

## 4. 技术实现规范

### 4.1 数据结构
```kotlin
// 消息类型枚举
enum class MedAIChatViewType {
    WELCOME_AND_OPTIONS, // 欢迎消息和功能选项
    DIVIDER,             // 分隔线
    AI_MESSAGE,          // AI消息
    USER_MESSAGE,        // 新增：用户消息
    HEALTH_DATA_CARD,    // 健康数据卡片
    QUICK_OPTIONS        // 快捷选项
}

// 消息数据模型
sealed class MedAIChatItem {
    // 欢迎消息和功能选项
    data class WelcomeAndOptions(
        val welcomeMessage: String,
        val introMessage: String,
        val options: List<Option>
    ) : MedAIChatItem() {
        data class Option(val title: String, val iconResId: Int?)
    }

    // 分隔线
    object Divider : MedAIChatItem()

    // AI消息数据结构
    data class AIMessage(val message: String) : MedAIChatItem()

    // 用户消息数据结构
    data class UserMessage(val message: String) : MedAIChatItem()

    // 健康数据卡片数据结构
    data class HealthDataCard(
        val title: String,
        val healthItems: List<HealthItem>
    ) : MedAIChatItem() {
        data class HealthItem(
            val iconResId: Int,
            val name: String,
            val value: String,
            val unit: String,
            val status: String,
            val isAbnormal: Boolean
        )
    }

    data class QuickOptions(val options: List<String>) : MedAIChatItem()
}
```

### 4.2 核心方法
- `startSimulatedConversation()`: 开始模拟对话
- `showNextMessage()`: 显示下一条消息
- `showMessageWithTypingEffect(message: String, callback: () -> Unit)`: 打字机效果显示
- `checkAndScrollToBottom()`: 检查并滚动到底部
- `pauseSimulation()`: 暂停模拟（用户滚动时）
- `resumeSimulation()`: 恢复模拟

### 4.3 状态管理
- `isSimulationRunning`: 模拟是否正在进行
- `currentMessageIndex`: 当前显示的消息索引
- `isTyping`: 是否正在打字
- `isUserScrolling`: 用户是否在手动滚动
  
### 4.4 消息交互
- 往消息列表里添加用户消息示例： 
  ```Kotlin
    chatAdapter.addItem(
                MedAIChatItem.UserMessage("Hi，小医")
            )
    ```
- 往消息列表里添加AI消息 
  ```Kotlin
    MedAIChatItem.AIMessage("我在，请问有什么可以帮您的？")
    ```
## 5. 用户体验优化

### 5.1 交互反馈
- **打字指示器**：AI消息显示前可显示"正在输入..."状态
- **消息状态**：显示消息发送时间和状态
- **暂停/继续**：提供暂停和继续模拟的控制选项

### 5.2 性能优化
- **内存管理**：及时释放不必要的动画资源
- **渲染优化**：使用ViewHolder复用机制
- **滚动优化**：避免频繁的滚动操作

### 5.3 可访问性
- **屏幕阅读器支持**：为消息添加适当的contentDescription
- **字体大小适配**：支持系统字体大小设置
- **高对比度支持**：确保在高对比度模式下的可读性

## 6. 错误处理

### 6.1 异常情况
- **消息为空**：跳过空消息，继续下一条
- **网络异常**：模拟对话不依赖网络，本地处理
- **内存不足**：优雅降级，减少动画效果

### 6.2 边界条件
- **快速切换页面**：正确清理定时器和动画
- **屏幕旋转**：保持当前模拟状态
- **应用后台**：暂停模拟，前台时恢复

## 7. 测试用例

### 7.1 功能测试
- 验证消息按顺序正确显示
- 验证打字机效果正常工作
- 验证自动滚动机制
- 验证用户手动滚动时的行为

### 7.2 性能测试
- 长时间运行的内存泄漏测试
- 快速操作的稳定性测试
- 不同设备性能的适配测试

## 8. 扩展功能（可选）

### 8.1 多场景支持
- 支持配置不同的对话场景
- 动态加载对话内容
- 场景切换功能

### 8.2 个性化设置
- 打字速度调节
- 消息间隔时间设置
- 自动滚动开关

### 8.3 数据统计
- 用户观看完整度统计
- 交互行为分析
- 性能指标监控