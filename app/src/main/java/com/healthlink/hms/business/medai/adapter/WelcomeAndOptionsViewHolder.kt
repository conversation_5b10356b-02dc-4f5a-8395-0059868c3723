package com.healthlink.hms.business.medai.adapter

import android.view.ViewGroup
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.recyclerview.widget.RecyclerView
import com.healthlink.hms.business.medai.view.MedAIChatItem
import com.healthlink.hms.business.medai.view.WelcomeAndOptionsContent

class WelcomeAndOptionsViewHolder private constructor(
    private val composeView: ComposeView,
    private val onOptionClick: (MedAIChatItem.WelcomeAndOptions.Option) -> Unit
) : RecyclerView.ViewHolder(composeView) {

    companion object {
        fun create(parent: ViewGroup, onOptionClick: (MedAIChatItem.WelcomeAndOptions.Option) -> Unit): WelcomeAndOptionsViewHolder {
            val composeView = ComposeView(parent.context)

            // 确保ComposeView有明确的高度
            composeView.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )

            return WelcomeAndOptionsViewHolder(composeView, onOptionClick)
        }
    }

    fun bind(item: MedAIChatItem.WelcomeAndOptions) {
        composeView.setContent {
            MaterialTheme {
                // 最外层添加固定尺寸的Box
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                ) {
                    WelcomeAndOptionsContent(
                        welcomeMessage = item.welcomeMessage,
                        introMessage = item.introMessage,
                        options = item.options,
                        onOptionClick = onOptionClick
                    )
                }
            }
        }
    }
}