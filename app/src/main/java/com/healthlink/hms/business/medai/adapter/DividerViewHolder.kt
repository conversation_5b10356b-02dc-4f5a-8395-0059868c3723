package com.healthlink.hms.business.medai.adapter

import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.healthlink.hms.R

// DividerViewHolder
class DividerViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
    private val dividerTextView: TextView = itemView.findViewById(R.id.tv_divider_text)

    fun bind() {
        // 可以不需要特殊绑定逻辑，因为内容固定
        dividerTextView.text = "以下内容由医疗大模型MedGPT生成"
    }
}