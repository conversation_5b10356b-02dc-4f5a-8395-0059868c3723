package com.healthlink.hms.business.car_seat.di

import android.content.Context
import com.healthlink.hms.business.car_seat.connect.SeatWSManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.serialization.json.Json
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object WebSocketModule {
    @Provides
    @Singleton
    fun provideJson(): Json{
        return Json {
            ignoreUnknownKeys = true
            encodeDefaults = true
            prettyPrint = true
        }
    }

    @Provides
    @Singleton
    fun provideWebSocketManager(@ApplicationContext context: Context, json: Json): SeatWSManager{
        return SeatWSManager(context = context, json = json)
    }
}