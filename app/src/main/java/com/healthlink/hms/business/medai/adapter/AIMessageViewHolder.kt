package com.healthlink.hms.business.medai.adapter

import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.tooling.preview.Preview
import androidx.recyclerview.widget.RecyclerView
import com.healthlink.hms.business.medai.view.AIMessageContent
import com.healthlink.hms.business.medai.view.MedAIChatItem

class AIMessageViewHolder private constructor(private val composeView: ComposeView) : RecyclerView.ViewHolder(composeView) {
    companion object {
        fun create(parent: ViewGroup): AIMessageViewHolder {
            val composeView = ComposeView(parent.context)

            // 确保ComposeView有明确的高度
            composeView.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )

            return AIMessageViewHolder(composeView)
        }
    }

    fun bind(item: MedAIChatItem.AIMessage) {
        composeView.setContent {
            MaterialTheme {
                // 最外层添加固定尺寸的Box
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                ) {
                    AIMessageContent(
                        message = item.message)
                }
            }
        }
    }
}

