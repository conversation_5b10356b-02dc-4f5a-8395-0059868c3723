package com.healthlink.hms.business.medai.adapter

import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import android.util.Log
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.tooling.preview.Preview
import androidx.recyclerview.widget.RecyclerView
import com.healthlink.hms.business.medai.view.AIMessageContent
import com.healthlink.hms.business.medai.view.MedAIChatItem
import com.healthlink.hms.business.medai.utils.TypingEffectManager

class AIMessageViewHolder private constructor(
    private val composeView: ComposeView,
    private val typingEffectManager: TypingEffectManager
) : RecyclerView.ViewHolder(composeView) {

    companion object {
        fun create(parent: ViewGroup, typingEffectManager: TypingEffectManager): AIMessageViewHolder {
            val composeView = ComposeView(parent.context)

            // 确保ComposeView有明确的高度
            composeView.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )

            return AIMessageViewHolder(composeView, typingEffectManager)
        }
    }

    private var currentTypingTaskId: String? = null

    fun bind(item: MedAIChatItem.AIMessage) {
        try {
            // 取消之前的打字机效果
            currentTypingTaskId?.let { taskId ->
                typingEffectManager.cancelTypingEffect(taskId)
            }

            composeView.setContent {
                MaterialTheme {
                    try {
                        // 创建稳定的文本状态，避免重组时闪烁
                        val textState = remember(item.message) { mutableStateOf("") }

                        // 最外层添加固定尺寸的Box，使用稳定的高度计算
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .wrapContentHeight()
                        ) {
                            AIMessageContent(
                                message = textState.value
                            )
                        }

                        // 启动打字机效果
                        LaunchedEffect(item.message) {
                            try {
                                if (item.message.isNotEmpty()) {
                                    currentTypingTaskId = typingEffectManager.startTypingEffect(
                                        fullText = item.message,
                                        textState = textState,
                                        onComplete = {
                                            // 打字机效果完成
                                            currentTypingTaskId = null
                                        }
                                    )
                                }
                            } catch (e: Exception) {
                                Log.e("AIMessageViewHolder", "启动打字机效果失败", e)
                                // 降级处理：直接显示完整文本
                                textState.value = item.message
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("AIMessageViewHolder", "Compose内容创建失败", e)
                        // 降级处理：显示简单文本
                        Text(
                            text = item.message,
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("AIMessageViewHolder", "绑定ViewHolder失败", e)
            // 最后的降级处理：设置简单的文本内容
            composeView.setContent {
                Text(
                    text = item.message,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }
    }

    /**
     * 立即显示完整消息（跳过打字机效果）
     */
    fun showCompleteMessage(item: MedAIChatItem.AIMessage) {
        currentTypingTaskId?.let { taskId ->
            typingEffectManager.cancelTypingEffect(taskId)
            currentTypingTaskId = null
        }

        composeView.setContent {
            MaterialTheme {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                ) {
                    AIMessageContent(
                        message = item.message
                    )
                }
            }
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        currentTypingTaskId?.let { taskId ->
            typingEffectManager.cancelTypingEffect(taskId)
            currentTypingTaskId = null
        }
    }
}

