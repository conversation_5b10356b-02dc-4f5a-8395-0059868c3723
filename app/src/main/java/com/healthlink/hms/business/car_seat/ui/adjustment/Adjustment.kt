package com.healthlink.hms.business.car_seat.ui.adjustment

import android.util.Log
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.PrimaryTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.healthlink.hms.R
import com.healthlink.hms.business.car_seat.ui.spinecare.SeatAdjustmentFragment
import com.healthlink.hms.business.car_seat.ui.suggestion.HeaderIconTitle


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CurDriverModeContainer() {
    HeaderIconTitle(
        icon = R.drawable.icon_seat_driver_mode,
        title = "当前驾驶模式",
        modifier = Modifier.height(34.dp)
    )
    Spacer(modifier = Modifier.height(20.dp))

    var state by remember { mutableStateOf(0) }
    val titles = listOf("通勤","经济", "标准", "雪地", "沙滩","泥地","四驱")
    PrimaryTabRow(selectedTabIndex = state,
        modifier = Modifier.fillMaxWidth().height(64.dp).clip(RoundedCornerShape(20.dp)).padding(0.dp),
        containerColor = Color(0xFFFFFFFF),
        divider = {},
        indicator = {},
    ) {
        titles.forEachIndexed { index, title ->
            Tab(
                selected = state == index,
                onClick = {
                    state = index
                    Log.i(SeatAdjustmentFragment.TAG, "CurDriverModeContainer: $state")
                },
                modifier = Modifier
                    .fillMaxHeight()
                    .background(color = if (state == index) Color(0xFF32C5FF) else Color.Transparent,
                        shape = if(state == index) RoundedCornerShape(20.dp) else RoundedCornerShape(0.dp))
                    .padding(0.dp),
                text = {
                    Text(
                        text = title,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        textAlign = TextAlign.Center,
                        fontWeight = FontWeight.Medium,
                        fontSize = 24.sp,
                        color = if (state == index) Color(0xFFFFFFFF) else Color(0xFF333333),)
                },
                selectedContentColor = Color(0xFF32C5FF),
                unselectedContentColor = Color(0xFF333333),
            )
        }
    }
}

@Composable
fun CurrentUserContainer(seatAnimationTrigger: MutableState<Boolean>) {
    SwitchUser()
    Spacer(modifier = Modifier.height(20.dp))
    // 用户信息卡片
    CurrentUserCard(seatAnimationTrigger = seatAnimationTrigger)
}

/**
 * 切换用车人
 */
@Composable
fun SwitchUser() {
    Row(modifier = Modifier.fillMaxWidth().wrapContentHeight(), horizontalArrangement = Arrangement.SpaceBetween) {
        HeaderIconTitle(icon = R.drawable.icon_seat_user_logo,title = "当前登录用户",modifier = Modifier.wrapContentSize())
        HeaderIconTitle(icon = R.drawable.icon_seat_change_user,title = "切换用车人",modifier = Modifier.wrapContentSize().padding(end = 20.dp),fontSize = 20.sp)
    }
}

/**
 * 当前用户卡片
 */
@Composable
fun CurrentUserCard(seatAnimationTrigger: MutableState<Boolean>) {
    Card(modifier = Modifier.size(892.dp,364.dp),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFFFFFFF))) {
        Box(modifier = Modifier.fillMaxSize().padding(start = 25.dp,top = 20.dp,end = 25.dp, bottom = 20.dp)){
            Column(){
                // 顶部
                UserInfoHeader()
                Spacer(modifier = Modifier.height(16.dp))
                // 分割线
                HorizontalDivider(modifier = Modifier.height(1.dp).background(Color(0x33999999)))
                Spacer(modifier = Modifier.height(25.dp))
                // 用户信息
                UserInfoContainer()
                Spacer(modifier = Modifier.height(30.dp))
                // 座椅调节按钮
                SeatAdjustmentOperatorButtonGroup(seatAnimationTrigger = seatAnimationTrigger)
            }
        }
    }
}

@Composable
fun UserInfoHeader() {
    Row(
        modifier = Modifier
            .height(44.dp)
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        HeaderIconTitle(
            icon = R.drawable.icon_seat_user_avtar,
            title = "可乐",
            modifier = Modifier.wrapContentSize()
        )
        Row(
            modifier = Modifier.wrapContentSize(),
            horizontalArrangement = Arrangement.End,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text("默认用户", color = Color(0xFF999999), fontSize = 22.sp)
            Spacer(modifier = Modifier.width(30.dp))
            Button(
                onClick = {
                    Log.i(SeatAdjustmentFragment.TAG, "编辑用户")
                },
                contentPadding = PaddingValues(0.dp),
                colors = ButtonDefaults.buttonColors(containerColor = Color.Transparent)
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.icon_seat_user_edit),
                    contentDescription = "编辑",
                    modifier = Modifier.size(42.dp),
                    tint = Color.Unspecified
                )
            }

            Spacer(modifier = Modifier.width(20.dp))

            Button(
                onClick = {
                    Log.i(SeatAdjustmentFragment.TAG, "删除用户")
                },
                contentPadding = PaddingValues(0.dp),
                colors = ButtonDefaults.buttonColors(containerColor = Color.Transparent)
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.icon_seat_user_del),
                    contentDescription = "删除",
                    modifier = Modifier.size(42.dp),
                    tint = Color.Unspecified
                )
            }


        }
    }
}

@Composable
fun UserInfoContainer() {
    Row(modifier = Modifier.fillMaxWidth().wrapContentHeight()) {
        UserInfoItem(title = "性别",
            inputValue = "女",
            textStyle = TextStyle(color = Color(0xFF333333), fontSize = 22.sp, textAlign = TextAlign.Center),unitStr = null)
        Spacer(modifier = Modifier.width(40.dp))
        UserInfoItem(title = "身高",
            inputValue = "156",
            textStyle = TextStyle(color = Color(0xFF333333), fontSize = 26.sp,fontWeight = FontWeight.Medium, textAlign = TextAlign.Center),
            unitStr = "cm")
    }
    Spacer(modifier = Modifier.height(15.dp))
    Row(modifier = Modifier.fillMaxWidth().wrapContentHeight()) {
        UserInfoItem(title = "年龄",
            inputValue = "22",
            textStyle = TextStyle(color = Color(0xFF333333), fontSize = 26.sp, fontWeight = FontWeight.Medium,textAlign = TextAlign.Center),
            unitStr = "岁")
        Spacer(modifier = Modifier.width(40.dp))
        UserInfoItem(title = "体重",
            inputValue = "60",
            textStyle = TextStyle(color = Color(0xFF333333), fontSize = 26.sp,fontWeight = FontWeight.Medium, textAlign = TextAlign.Center),
            unitStr = "kg")
    }
}

/**
 * 用户信息 Item
 */
@Composable
fun UserInfoItem(title: String, inputValue: String, textStyle: TextStyle = LocalTextStyle.current, unitStr: String? = null) {
    var inputValue by remember { mutableStateOf(inputValue) }
    Row(modifier = Modifier.size(274.dp,60.dp), verticalAlignment = Alignment.CenterVertically) {
        Text(text = title, color = Color(0xFF333333), fontSize = 26.sp)
        Spacer(modifier = Modifier.width(30.dp))
        ConditionalTrailingIconTextField(inputValue = inputValue, onValueChange = {}, unitStr = unitStr, textStyle = textStyle)
    }
}



/**
 * 有条件尾部图标
 */
@Composable
fun ConditionalTrailingIconTextField(
    inputValue: String,
    onValueChange: (String) -> Unit,
    unitStr: String?,
    textStyle: TextStyle,
    modifier: Modifier = Modifier
) {
    TextField(
        value = inputValue,
        onValueChange = onValueChange,
        trailingIcon = if (!unitStr.isNullOrEmpty()) {
            {
                Text(
                    text = unitStr,
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF666666),
                    fontSize = 22.sp
                )
            }
        } else null,
        colors = TextFieldDefaults.colors(
            focusedContainerColor = Color.Transparent,
            unfocusedContainerColor = Color.Transparent,
            disabledContainerColor = Color.Transparent,
            focusedIndicatorColor = Color.Transparent,
            unfocusedIndicatorColor = Color.Transparent,
            disabledIndicatorColor = Color.Transparent
        ),
        textStyle = textStyle,
        modifier = modifier
            .wrapContentSize()
            .background(Color(0xFFE8F2FD), RoundedCornerShape(10.dp)),
        singleLine = true
    )
}


@Composable
fun SeatAdjustmentOperatorButtonGroup(seatAnimationTrigger: MutableState<Boolean>) {
    Row(modifier = Modifier.fillMaxWidth().wrapContentHeight(), horizontalArrangement = Arrangement.Start) {
        OutlinedButton(onClick = {
            Log.i(SeatAdjustmentFragment.TAG, "座椅调整效果预览")
            // 触发座椅调整动画
            seatAnimationTrigger.value = !seatAnimationTrigger.value
        },
            modifier = Modifier.wrapContentSize(),
            shape = RoundedCornerShape(10.dp),
            contentPadding = PaddingValues(vertical = 20.dp, horizontal = 44.dp),
            border = BorderStroke(1.dp, Color(0xFF07C160))){
            Text("座椅调整效果预览", color = Color(0xFF07C160), fontSize = 24.sp, fontWeight = FontWeight.Medium)
        }
        Spacer(modifier = Modifier.width(30.dp))
        Button(onClick = {
            Log.i(SeatAdjustmentFragment.TAG, "一键调整")
        },
            modifier = Modifier.wrapContentSize(),
            shape = RoundedCornerShape(10.dp),
            contentPadding = PaddingValues(92.dp,20.dp),
            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF07C160))) {
            Text("一键调整", color = Color(0xFFFFFFFF), fontSize = 24.sp, fontWeight = FontWeight.Medium)
        }
    }
}



@Composable
fun AddGuestButton() {
    OutlinedButton(onClick = {
        Log.i(SeatAdjustmentFragment.TAG, "新增游客")
    },
        modifier = Modifier.wrapContentSize(),
        shape = RoundedCornerShape(10.dp),
        contentPadding = PaddingValues(14.dp,10.dp),
        border = BorderStroke(1.dp, Color(0xFF07C160))){
        Icon(
            painter = painterResource(id = R.drawable.icon_seat_user_add),
            contentDescription = "新增游客",
            modifier = Modifier.size(22.dp),
            tint = Color.Unspecified
        )
        Spacer(modifier = Modifier.width(24.dp))
        Text("新增游客", color = Color(0xFF07C160), fontSize = 20.sp, fontWeight = FontWeight.Medium)
    }
}

