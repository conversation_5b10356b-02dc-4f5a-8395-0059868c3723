package com.healthlink.hms.business.car_seat.anim
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.repeatable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.healthlink.hms.R
import kotlinx.coroutines.launch

/**
 * 可旋转的 Icon 组件，支持自定义旋转角度、动画时长、重复次数和旋转中心点。
 *
 * @param painter Icon 的绘制器。
 * @param contentDescription Icon 的内容描述。
 * @param modifier 应用于 Icon 的 Modifier。
 * @param tint Icon 的着色，默认为 Color.Unspecified。
 * @param rotationAngle 每次旋转的目标角度（例如，从0度到10度）。
 * @param animationDuration 单次旋转动画的持续时间（毫秒）。
 * @param repeatCount 动画重复的次数。例如，2次表示从0到targetAngle再回到0，算作一次完整循环，重复2次。
 *                    注意：repeatable 的 iterations 参数是动画的迭代次数，
 *                    如果动画是从 A 到 B 再回到 A，那么一次完整的"来回"算作两次迭代。
 *                    因此，如果希望"来回"重复 repeatCount 次，iterations 应为 repeatCount * 2。
 * @param transformOrigin 旋转的中心点，默认为 Icon 的中心。
 * @param triggerAnimation 是否触发动画的标志，当值变为true时触发动画。
 */
@Composable
fun RotatableIcon(
    painter: Painter,
    contentDescription: String?,
    modifier: Modifier = Modifier,
    tint: Color = Color.Unspecified,
    rotationAngle: Float = 10f,
    animationDuration: Int = 300,
    repeatCount: Int = 2,
    transformOrigin: TransformOrigin = TransformOrigin(0.5f, 0.5f), // 默认中心点
    triggerAnimation: Boolean = true // 默认自动触发动画（向后兼容）
) {
    val animatedRotation = remember { Animatable(0f) }
    // 使用一个key来跟踪triggerAnimation的变化次数
    val triggerCount = remember { mutableStateOf(0) }
    
    // 当triggerAnimation变化时，增加triggerCount
    LaunchedEffect(triggerAnimation) {
        if (triggerAnimation) {
            triggerCount.value++
        }
    }

    // 使用triggerCount作为LaunchedEffect的key，确保每次triggerAnimation变化都会重新执行
    LaunchedEffect(triggerCount.value) {
        if (triggerCount.value > 0) {
            // 先确保从0开始
            animatedRotation.snapTo(0f)
            // 使用 repeatable 实现指定次数的来回动画
            animatedRotation.animateTo(
                targetValue = rotationAngle,
                animationSpec = repeatable(
                    animation = tween(durationMillis = animationDuration / 2, easing = LinearEasing),
                    iterations = repeatCount * 2, // 每次来回算两次迭代 (0->angle, angle->0)
                    repeatMode = RepeatMode.Reverse
                )
            )
            // 动画完成后，确保回到初始角度（0度）
            animatedRotation.snapTo(0f)
        }
    }

    Icon(
        painter = painter,
        contentDescription = contentDescription,
        modifier = modifier
            .wrapContentSize()
            .graphicsLayer {
                rotationZ = animatedRotation.value
                this.transformOrigin = transformOrigin // 设置自定义旋转中心点
            },
        tint = tint
    )
}

/**
 * 带按钮触发的可旋转Icon组件，点击按钮时触发动画。
 *
 * @param painter Icon 的绘制器。
 * @param contentDescription Icon 的内容描述。
 * @param modifier 应用于整个组件的 Modifier。
 * @param buttonText 按钮上显示的文本。
 * @param tint Icon 的着色，默认为 Color.Unspecified。
 * @param rotationAngle 每次旋转的目标角度。
 * @param animationDuration 单次旋转动画的持续时间（毫秒）。
 * @param repeatCount 动画重复的次数。
 * @param transformOrigin 旋转的中心点，默认为 Icon 的中心。
 */
@Composable
fun RotatableIconWithButton(
    painter: Painter,
    contentDescription: String?,
    modifier: Modifier = Modifier,
    buttonText: String = "触发动画",
    tint: Color = Color.Unspecified,
    rotationAngle: Float = 10f,
    animationDuration: Int = 300,
    repeatCount: Int = 2,
    transformOrigin: TransformOrigin = TransformOrigin(0.5f, 0.5f) // 默认中心点
) {
    val coroutineScope = rememberCoroutineScope()
    val triggerAnimation = remember { mutableStateOf(false) }
    
    Column(
        modifier = modifier.padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        RotatableIcon(
            painter = painter,
            contentDescription = contentDescription,
            tint = tint,
            rotationAngle = rotationAngle,
            animationDuration = animationDuration,
            repeatCount = repeatCount,
            transformOrigin = transformOrigin,
            triggerAnimation = triggerAnimation.value
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(
            onClick = {
                coroutineScope.launch {
                    // 重置触发器状态，然后设置为true以触发动画
                    triggerAnimation.value = false
                    // 短暂延迟以确保状态更新
                    kotlinx.coroutines.delay(10)
                    triggerAnimation.value = true
                }
            }
        ) {
            Text(text = buttonText)
        }
    }
}

// 预览函数
@Preview(showBackground = true)
@Composable
fun PreviewRotatableIcon() {
    // 假设您有一个名为 img_chair_seat_back 的 drawable 资源
    // 请确保您的项目中存在此资源，否则预览会失败
    RotatableIcon(
        painter = painterResource(id = R.drawable.img_chair_seat_back),
        contentDescription = "座椅靠背",
        rotationAngle = 10f,
        animationDuration = 300, // 300毫秒内完成一次来回，总共重复2次
        repeatCount = 2, // 来回重复2次
        transformOrigin = TransformOrigin(0.5f, 0.5f) // 默认中心点
    )
}

@Preview(showBackground = true)
@Composable
fun PreviewRotatableIconCustomPivot() {
    RotatableIcon(
        painter = painterResource(id = R.drawable.img_chair_seat_back),
        contentDescription = "座椅靠背",
        rotationAngle = 10f,
        animationDuration = 300,
        repeatCount = 2,
        transformOrigin = TransformOrigin(0f, 0f) // 左上角作为旋转中心
    )
}

@Preview(showBackground = true)
@Composable
fun PreviewRotatableIconWithButton() {
    RotatableIconWithButton(
        painter = painterResource(id = R.drawable.img_chair_seat_back),
        contentDescription = "座椅靠背",
        buttonText = "触发座椅动画",
        rotationAngle = -10f,
        animationDuration = 500,
        repeatCount = 2,
        transformOrigin = TransformOrigin(0.5f, 0.5f)
    )
}