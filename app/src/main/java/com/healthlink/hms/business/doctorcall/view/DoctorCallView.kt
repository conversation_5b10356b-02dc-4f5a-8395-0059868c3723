package com.healthlink.hms.business.doctorcall.view

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.lifecycle.Observer
import com.healthlink.hms.R
import com.healthlink.hms.business.doctorcall.DoctorCallModel
import com.healthlink.hms.business.doctorcall.DoctorCallModel.DoctorCallViewState
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.isDarkModeEnabled
import java.lang.ref.WeakReference
import java.util.Locale

@SuppressLint("ViewConstructor")
class DoctorCallView private constructor(
    context: Context
) : LinearLayout(context, null, 0) {

    companion object {
        fun create(): Builder {
            return Builder()
        }
    }

    private var infoContainer: View? = null
    private var dialPan: DoctorCallDialPan? = null
    private var ivKeyboard: ImageView? = null
    private var ivMute: ImageView? = null
    private var tvDuration: TextView? = null
    private val handler = SafeHandler(this)

    private var onMuteButtonClicked: (() -> Unit)? = null
    private var onKeyboardButtonClicked: (() -> Unit)? = null
    private var onHangupButtonClicked: (() -> Unit)? = null
    private var onInput: ((key: Int) -> Unit)? = null

    private val isMutedObserver = Observer<Boolean> {
        ivMute?.setImageResource(
            if (it) R.drawable.bt_doctor_call_mute_selected_selector
            else R.drawable.bt_doctor_call_mute_selector
        )
    }

    private val showKeyboardObserver = Observer<Boolean> {
        if (it) {
            infoContainer?.animate()?.alpha(0f)?.setDuration(150)?.withEndAction {
                infoContainer?.visibility = GONE
                dialPan?.visibility = VISIBLE
                dialPan?.animate()?.alpha(1f)?.setDuration(200)?.start()
            }?.start()
        } else {
            dialPan?.animate()?.alpha(0f)?.setDuration(150)?.withEndAction {
                dialPan?.visibility = GONE
                infoContainer?.visibility = VISIBLE
                infoContainer?.animate()?.alpha(1f)?.setDuration(200)?.start()
            }?.start()
        }
        if (viewModel?.viewState?.value === DoctorCallViewState.Talking) {
            ivKeyboard?.setImageResource(
                if (it) R.drawable.bt_doctor_call_keyboard_selected_selector
                else R.drawable.bt_doctor_call_keyboard_normal_selector
            )
        }
    }

    private val viewStateObserver = Observer<DoctorCallViewState> {
        when (it) {
            DoctorCallViewState.Talking -> {
                // 恢复按钮点击音效
                findViewById<LinearLayout>(R.id.btn_doctor_call_mute)?.isEnabled = true
                findViewById<LinearLayout>(R.id.btn_doctor_call_keyboard)?.isEnabled = true
                ivMute?.setImageResource(
                    if (isMuted) R.drawable.bt_doctor_call_mute_selected_selector
                    else R.drawable.bt_doctor_call_mute_selector
                )
                ivKeyboard?.setImageResource(
                    if (isKeyboardShown) R.drawable.bt_doctor_call_keyboard_selected_selector
                    else R.drawable.bt_doctor_call_keyboard_normal_selector
                )
            }

            else -> {
                ivMute?.setImageResource(R.drawable.bg_btn_mute_disable)
                ivKeyboard?.setImageResource(R.drawable.bg_btn_dtmf_keyboard_disable)
                // 取消按钮点击音效
                findViewById<LinearLayout>(R.id.btn_doctor_call_mute)?.isEnabled = false
                findViewById<LinearLayout>(R.id.btn_doctor_call_keyboard)?.isEnabled = false
            }
        }
    }

    private val inputObserver = Observer<String> {
        dialPan?.setText(it)
    }

    var viewModel: DoctorCallModel? = null
        set(value) {
            field?.isMuted?.removeObserver(isMutedObserver)
            field?.showKeyboard?.removeObserver(showKeyboardObserver)
            field?.viewState?.removeObserver(viewStateObserver)
            field?.input?.removeObserver(inputObserver)
            value?.isMuted?.observeForever(isMutedObserver)
            value?.showKeyboard?.observeForever(showKeyboardObserver)
            value?.viewState?.observeForever(viewStateObserver)
            value?.input?.observeForever(inputObserver)
            field = value
        }

    private val startTime
        get() = viewModel?.startTime?.value ?: 0L
    private val isMuted
        get() = viewModel?.isMuted?.value ?: false

    private val isKeyboardShown
        get() = viewModel?.showKeyboard?.value ?: false

    private var isDarkModeEnabled: Boolean

    /**
     * 最新通话时间
     */
    private var lastDoctorCallTime = 0L

    init {
        initView()
        startTimer()

        isDarkModeEnabled = isDarkModeEnabled(context)
    }

    private fun initView() {
        removeAllViews()
        LayoutInflater.from(context).inflate(R.layout.view_doctor_call, this, true)

        infoContainer = findViewById(R.id.doctor_call_content_info)
        dialPan = findViewById(R.id.doctor_call_dial_pan)
        dialPan?.setInputListener {
            onInput?.invoke(it)
        }
        ivKeyboard = findViewById(R.id.iv_doctor_call_keyboard)
        tvDuration = findViewById(R.id.tv_doctor_call_duration)
        ivMute = findViewById(R.id.iv_doctor_call_mute)

        findViewById<LinearLayout>(R.id.btn_doctor_call_mute).setOnClickListener {
            onMuteButtonClicked?.invoke()
        }
        findViewById<LinearLayout>(R.id.btn_doctor_call_hang_up).setOnClickListener {
            onHangupButtonClicked?.invoke()
        }
        findViewById<LinearLayout>(R.id.btn_doctor_call_keyboard).setOnClickListener {
            onKeyboardButtonClicked?.invoke()
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        handler.removeCallbacksAndMessages(null)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        val newIsDarkModeEnabled = isDarkModeEnabled(context)
        if (isDarkModeEnabled != newIsDarkModeEnabled) {
            initView()
            viewModel = viewModel
            isDarkModeEnabled = newIsDarkModeEnabled
        }
        startTimer()
    }

    private fun startTimer() {
        handler.post(object : Runnable {
            override fun run() {
                updateDuration()
                handler.postDelayed(this, 1000)
            }
        })
    }

    private fun updateDuration() {
        if (viewModel?.viewState?.value == DoctorCallViewState.Calling) {
            tvDuration?.text = "呼叫中"
            return
        }

        if (viewModel?.viewState?.value == DoctorCallViewState.Hanging) {
            tvDuration?.text = "挂断中"
            return
        }

        if (startTime == 0L) {
            tvDuration?.text = "通话中"
            return
        }

        val elapsedMillis = System.currentTimeMillis() - startTime
        val elapsedSeconds = elapsedMillis / 1000
        val hours = elapsedSeconds / 3600
        val minutes = (elapsedSeconds % 3600) / 60
        val seconds = elapsedSeconds % 60

        val formattedTime = if (hours > 0) {
            String.format(Locale.getDefault(), "%02d:%02d:%02d", hours, minutes, seconds)
        } else {
            String.format(Locale.getDefault(), "%02d:%02d", minutes, seconds)
        }

        // 每10秒记一次最新通话状态
        if(System.currentTimeMillis() - this.lastDoctorCallTime > 10000){
            this.lastDoctorCallTime = System.currentTimeMillis()
            MMKVUtil.storeLastDoctorCallTime(System.currentTimeMillis())
        }

        tvDuration?.text = formattedTime
    }

    private class SafeHandler(view: DoctorCallView) : Handler(Looper.getMainLooper()) {
        private val viewRef = WeakReference(view)

        override fun handleMessage(msg: Message) {
            val view = viewRef.get()
            if (view != null) {
                super.handleMessage(msg)
            }
        }
    }

    class Builder {

        private var onMuteButtonClicked: (() -> Unit)? = null
        private var onKeyboardButtonClicked: (() -> Unit)? = null
        private var onHangupButtonClicked: (() -> Unit)? = null
        private var onInput: ((Int) -> Unit)? = null
        fun onMuteButtonClicked(onMuteButtonClicked: (() -> Unit)?): Builder {
            this.onMuteButtonClicked = onMuteButtonClicked
            return this
        }

        fun onKeyboardButtonClicked(onKeyboardButtonClicked: (() -> Unit)?): Builder {
            this.onKeyboardButtonClicked = onKeyboardButtonClicked
            return this
        }

        fun onHangupButtonClicked(onHangupButtonClicked: (() -> Unit)?): Builder {
            this.onHangupButtonClicked = onHangupButtonClicked
            return this
        }

        fun onInput(onInput: ((Int) -> Unit)?): Builder {
            this.onInput = onInput
            return this
        }

        fun build(context: Context): DoctorCallView {
            val view = DoctorCallView(context)
            view.onMuteButtonClicked = onMuteButtonClicked
            view.onKeyboardButtonClicked = onKeyboardButtonClicked
            view.onHangupButtonClicked = onHangupButtonClicked
            view.onInput = onInput
            return view
        }
    }

}