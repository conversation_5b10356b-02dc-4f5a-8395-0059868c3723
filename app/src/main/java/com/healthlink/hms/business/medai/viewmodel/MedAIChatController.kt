package com.healthlink.hms.business.medai.viewmodel

import androidx.lifecycle.LiveData
import com.healthlink.hms.medchart.viewmodel.ChatMessage

interface MedAIChatController {
    val chatHistory: LiveData<List<ChatMessage>>
    val isLoading: LiveData<Boolean>
    val errorMessage: LiveData<String?>
    val loginStatus: LiveData<Boolean>
    val isStreaming: LiveData<Boolean>

    fun sendMessage(message: String,seStreamingAPI: Boolean = true)
    fun login()
}