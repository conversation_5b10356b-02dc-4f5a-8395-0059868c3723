package com.healthlink.hms.ktExt

/**
 * Created by imaginedays on 2024/6/19
 * 常用扩展函数
 */


import android.content.Context
import android.content.res.Resources
import android.util.TypedValue
import com.healthlink.hms.application.HmsApplication

val Float.dp: Float
    get() = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP,this,HmsApplication.appContext.resources.displayMetrics)

//val Int.dp: Float
//    get() = this.toFloat().dp

val Int.dp: Int
    get() = (this * Resources.getSystem().displayMetrics.density).toInt()


val Float.sp: Float
    get() = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_SP,this,HmsApplication.appContext.resources.displayMetrics)

val Int.sp: Float
    get() = this.toFloat().sp

// 定义 Context 的扩展函数 pxToDp
fun Context.pxToDp(px: Int): Float {
    return px / resources.displayMetrics.density
}

fun String.maskPhoneNumber(): String {
    // 检查输入是否合法
    if (this.length != 11) {
        return this
    }

    // 使用字符串操作替换指定位置的字符
    val maskedSection = "****"
    return this.substring(0, 3) + maskedSection + this.substring(7)
}

/**
 * 循环获取数组中的元素
 */
fun <T> Array<T>.getCyclic(index: Int): T {
    return this[index % this.size]
}

