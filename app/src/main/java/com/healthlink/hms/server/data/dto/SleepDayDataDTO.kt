package com.healthlink.hms.server.data.dto

import com.healthlink.hms.server.data.dto.charts.heartrate.HeartRateStatItemDTO

/**
 *@Author：付仁秀
 *@Description：
 **/
class SleepDayDataDTO(
    var fetchTime: String,
    var sleep:String,
    var allSleepTime:Int,
    var allSleepTimeHour:Int,
    var allSleepTimeMin:Int,
    var nightSleepTimeHour:Int,
    var nightSleepTimeMin:Int,
    var sporadicSleepTime:Int,
    var lightSleepTime:Int,
    var lightSleepTimeHour:Int,
    var lightSleepTimeMin:Int,
    var deepSleepTime:Int,
    var deepSleepTimeHour:Int,
    var deepSleepTimeMin:Int,
    var dreamTime:Int,
    var dreamTimeHour:Int,
    var dreamTimeMin:Int,
    var awakeTime:Int,
    var awakeTimeHour:Int,
    var awakeTimeMin:Int,
    var deepSleepPercent:String,
    var lightSleepTimePercent:String,
    var dreamTimePercent:String,
    var awakeTimePercent:String,
    var wakeupTime:String,
    var fallSleepTime:String,
    var createTime:String,
    var sleepTypeHms:String,
    var weekDay:String,
    var fragmentList:ArrayList<SleepDayItemDTO>
 ) {
}