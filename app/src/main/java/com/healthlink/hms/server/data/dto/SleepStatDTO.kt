package com.healthlink.hms.server.data.dto

/**
 * Created by imaginedays on 2024/6/7
 * 周月数据
 */
data class SleepStatDTO(
    var fetchTime: String,
    var month :String,
    var avgNightSleepTime: Int,
    var avgNightSleepTimeHour: Int,
    var avgNightSleepTimeMin: Int,
    var avgSleepTime: Int,
    var avgSleepTimeHour : Int,
    var avgSleepTimeMin:Int,
    var avgSleepScore : Int,
    var avgLightSleepTime : Int,
    var avgLightSleepTimeHour : Int,
    var avgLightSleepTimeMin : Int,
    var avgDeepSleepTime : Int,
    var avgDeepSleepTimeHour : Int,
    var avgDeepSleepTimeMin : Int,
    var avgDreamTime : Int,
    var avgDreamTimeHour : Int,
    var avgDreamTimeMin : Int,
    var avgAwakeTime : Int,
    var avgAwakeTimeHour : Int,
    var avgAwakeTimeMin : Int,
    var avgSporadicSleepTime : Int,
    var avgSporadicSleepTimeHour : Int,
    var avgSporadicSleepTimeMin : Int,
    var deepSleepPercent : String,
    var lightSleepTimePercent : String,
    var dreamTimePercent : String,
    var awakeTimePercent : String,
    var avgSporadicSleepPercent : String,
    var nodeList:ArrayList<SleepDayDataDTO> // 日
) {
}