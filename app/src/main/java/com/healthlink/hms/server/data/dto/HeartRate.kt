package com.healthlink.hms.server.data.dto

import com.healthlink.hms.server.data.bean.BaseDTO

/**
 * heartRateCode 1、正常 2、心率过速 3、心率过缓
 */
data class HeartRate(
    var id: Int? = 0,
    var heartRate: Int? = 66,
    /**
     *  正常 (heartRateCode = 1)
     *  心率过速 (heartRateCode = 2)
     *  心率过缓 (heartRateCode = 3)
     */
    var heartRateType: String? = "未知",
    /**
     * 心率类型，-1：测量值不在评估范围内； 0:正常； 1:疑似心动过缓； 2:疑似心动过速；
     */
    var heartRateCode: Int? = 1,
    var createTime: String? = null, // 时间戳 2024-05-27 09:33:44
    var type: String? = "heartrate" // heartrate
    ): BaseDTO(){

}
