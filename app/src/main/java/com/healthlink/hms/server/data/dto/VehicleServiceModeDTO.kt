package com.healthlink.hms.server.data.dto

import com.healthlink.hms.server.data.dto.init.VehicleCapacityModeFunsDTO

/**
 * Created by imaginedays on 2024/7/12
 * 车机服务功能，多个功能组成一个模式
 */

data class VehicleServiceModeDTO(
    val modeName: String? = null, // 模式名称
    val modeCode: String? = null, // 模式名称code
    val bgIconName: String? = null, // 模式背景图片名称
//    val bgIconOpenName: String? = null, // 开启后背景图片名称
//    var funs: List<VehicleCapacityModeFunsDTO> = listOf(), // 该模式具备功能列表
    var isOpen : Boolean = false
) {
}