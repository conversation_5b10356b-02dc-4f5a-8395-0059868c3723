package com.healthlink.hms.server.data

import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.healthlink.hms.server.data.dto.QtGetHealthInfoDTO
import com.healthlink.hms.server.data.dto.SpBaseDTO
import com.healthlink.hms.server.data.dto.SpGetHealthInfoDTO
import com.healthlink.hms.utils.network.okhttp.HttpUtil
import java.lang.reflect.Type

class HmsDataApi {

    private val TAG = "HmsDataApi"

    /**
     * 获取用户最新数据接口
     */
    private val HMS_DATA_GET_HEALTH_INFO = "https://hms-test.healthlinkiot.com/gwm/health/info"

    /**
     * 报事件接口
     */
    fun getHealthInfo(qtGetHealthInfoDTO: QtGetHealthInfoDTO): SpBaseDTO<SpGetHealthInfoDTO> {
        val httpUtil = HttpUtil()
        var spDTO = SpBaseDTO<SpGetHealthInfoDTO>()
        try {

            var spBody = httpUtil.makeOkHttpPostRequest(HMS_DATA_GET_HEALTH_INFO, qtGetHealthInfoDTO)
            Log.d(TAG , "获取健康数据报文：$spBody")

            if (!spBody.isNullOrBlank()) {
                val targetClass: Type = object : TypeToken<SpBaseDTO<SpGetHealthInfoDTO?>?>() {}.type
                spDTO = Gson().fromJson(spBody, targetClass)
            }
        }catch(ex : Exception){
            Log.i(TAG,"获取健康数据发生错误，错误内容：${ex.toString()}")
            spDTO.code = "-1";
            spDTO.code = "服务端请求错误";
        }

        return spDTO
    }

}