package com.healthlink.hms.server.data.dto

/**
 *@Author: 付仁秀
 *@Description：
 **/
data class BloodPressureItemDTO(
    val startTime: String?,
    val endTime: String?,
    /**
     * 平均收缩压
     */
    val systolicPressureAvg: Float?,
    /**
     * 最大收缩压
     */
    val systolicPressureMax: Float?,
    /**
     * 最新一次收缩压
     */
    val systolicPressureLast: Float?,
    /**
     * 平均舒张压
     */
    val diastolicPressureAvg: Float?,
    /**
     * 最大舒张压
     */
    val diastolicPressureMax: Float?,
    /**
     * 最小舒张压
     */
    val diastolicPressureMin: Float?,
    /**
     * 最新一次舒张压
     */
    val diastolicPressureLast: Float?,
    /**
     * 平均脉搏
     */
    val sphygmusAvg: Float?,
    /**
     * 最大脉搏
     */
    val sphygmusMax: Float?,
    /**
     * 最小脉搏
     */
    val sphygmusMin: Float?,
    /**
     * 最新一次脉搏
     */
    val sphygmusLast: Float?,
    /**
     *
     */
    val createTime: String?
)
