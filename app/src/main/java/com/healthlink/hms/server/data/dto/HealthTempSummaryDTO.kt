package com.healthlink.hms.server.data.dto

import com.healthlink.hms.server.data.dto.charts.HealthSummeryBaseDTO

/**
 *@Author: 付仁秀
 *@Description：
 **/
data class HealthTempSummaryDTO(
    /**
     * fetchtime
     */
    var createTime: String,
    /**
     * 体温平均值
     */
    var temperatureAvg: String,
    /**
     * 体温等级
     */
    var level: String,
    /**
     * 正常占比
     */
    var normalProp: String,
    /**
     * 疑似低热占比
     */
    var lowProp: String,
    /**
     * 疑似中热占比
     */
    var mediumProp: String,
    /**
     * 正常次数
     */
    var normalSum: String,
    /**
     * 疑似低热次数
     */
    var lowSum: String,
    /**
     * 疑似中热以上次数
     */
    var mediumSum: String,

    var valueList: ArrayList<HealthTempSummaryDTO>

) : HealthSummeryBaseDTO()