package com.healthlink.hms.server.data.dto

import com.healthlink.hms.server.data.bean.BaseDTO

/**
 * 0：较差， 1：一般，2：较好，3:正常， 4：较长
 */
data class Sleep(
    var sleep: Int? = null,
    var sleepTimeHour: Int? = 0,
    var sleepTimeMin: Int? = 0,
    var sleepType: String? = null,
    // 0：较差， 1：一般，2：较好，3:正常， 4：较长
    var sleepCode: Int? = null,
    var lightSleepTime: Int? = null,
    var deepSleepTime: Int? = null,
    var dreamTime: Int? = null,
    var awakeTime: Int? = null,
    var type: String? = "sleep",
    var createTime: String? = null // 格式 2024-05-27 09:33:44
) : BaseDTO(){

}
