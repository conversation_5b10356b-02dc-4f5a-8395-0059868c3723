package com.healthlink.hms.server.data.dto.charts.pressure

/**
 * Created by imaginedays on 2024/6/2
 * 压力统计数据
 */
data class PressureStatItemDTO(
    val month: String? = null, // 年数据中的每月
    val startTime: String? = null, // 数据开始时间
    val endTime: String? = null, // 数据结束时间
    val createTime: String, // 创建时间
    val avg: Int? = null, // 平均值
    val max: Int? = null, // 最大值
    val min: Int? = null, // 最小值
    val last: Int? = null, // 最新值
    val measureCount: Int? = null, // 计量次数
    val pressureType: String? = null, // 压力类型 放松
    val pressureCode: Int? = null, // 压力类型代码：1-放松，2-正常， 3-中等， 4-偏高
) {
}