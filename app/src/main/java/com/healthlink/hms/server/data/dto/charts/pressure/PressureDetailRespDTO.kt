package com.healthlink.hms.server.data.dto.charts.pressure

import com.healthlink.hms.server.data.dto.charts.CDPressureDTO

/**
 * Created by imaginedays on 2024/6/25
 * 压力详情页请求结果DTO
 */
data class PressureDetailRespDTO(
    var nodeList: ArrayList<PressureStatItemDTO>,
    var highCount: Int, // 偏高次数
    var middleCount: Int, // 中等次数
    var normalCount: Int, // 正常次数
    var relaxCount: Int, // 放松次数
    var highCountPercent: String, // 偏高次数百分比
    var middleCountPercent: String, // 中等次数百分比
    var normalCountPercent: String, // 正常次数百分比
    var relaxCountPercent: String, // 放松次数百分比
    var min: Int, // 最低值
    var max: Int, // 最高值
    var fetchTime: String?, // 获取时间 2024-06-25 16:42:39
    var startTime: String?, // 开始时间
    var endTime: String?, // 结束时间
    var createTime: String, // 结束时间
    var avgScore: Int, // 平均得分
    var pressureType: String, // 压力类型：1-放松，2-正常， 3-中等， 4-偏高
    var pressureTypeCode: Int, // 压力类型代码：1-放松，2-正常， 3-中等， 4-偏高
) {
}