package com.healthlink.hms.server.data.dto

/**
 * Created by imaginedays on 2024/6/9
 * 睡眠二级界面图表下卡片需要展示的数据
 * 从睡眠日、月、周、年里获取
 */
class SleepCardShowDTO(
    /**
     * 获取时间
     */
    var fetchTime: String? = null,
    /**
     * 当日睡眠得分
     */
    var sleep:String?,
    /**
     * 当日睡眠得分标题
     */
    var sleepTitle: String = "当日睡眠得分",
    val sleepAllTitle: String = "总睡眠",
    /**
     * 总睡眠时
     */
    var allSleepTimeHour:String?,
    /**
     * 总睡眠分钟
     */
    var allSleepTimeMin:String?,
    /**
     * 夜间睡眠时
     */
    var nightSleepTimeHour:Int?,
    /**
     * 夜间睡眠分
     */
    var nightSleepTimeMin:Int?,

){
}