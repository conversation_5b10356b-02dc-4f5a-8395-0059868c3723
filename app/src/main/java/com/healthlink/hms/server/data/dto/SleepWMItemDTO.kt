package com.healthlink.hms.server.data.dto

class SleepWMItemDTO(
    var sleep: Int?,
    var allSleepTime: Int,
    var allSleepTimeHour: Int,
    var allSleepTimeMin: Int,
    var nightSleepTime: Int,
    var nightSleepTimeHour: Int,
    var nightSleepTimeMin: Int,
    var manualSleepTime: Int,
    var manualSleepTimeHour: Int,
    var manualSleepTimeMin: Int,
    var sporadicSleepTime: Int,
    var sporadicSleepTimeHour: Int,
    var sporadicSleepTimeMin: Int,
    var lightSleepTime: Int,
    var lightSleepTimeHour: Int,
    var lightSleepTimeMin: Int,
    var deepSleepTime: Int,
    var deepSleepTimeHour: Int,
    var deepSleepTimeMin: Int,
    var dreamTime: Int,
    var dreamTimeHour: Int,
    var dreamTimeMin: Int,
    var awakeTime: Int,
    var awakeTimeHour: Int,
    var awakeTimeMin: Int,
    var deepSleepPercent: String,
    var lightSleepTimePercent: String,
    var dreamTimePercent: String,
    var awakeTimePercent: String,
    var sporadicSleepTimePercent: String,
    var fallSleepTime: String,
    var wakeupTime: String,
    var createTime: String,
    var sleepTypeHms: Any,//?
    var weekDay: String,
    var fragmentList: Any
) {
}