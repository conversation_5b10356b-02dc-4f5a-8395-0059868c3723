package com.healthlink.hms.server.data.dto

/**
 *@Author: 付仁秀
 *@Description：
 **/
data class BloodPressureSummaryItemDTO(
    val singleDeclaration: String?,
    val nounExplain: String?,
    val riskDescription: String?,
    val riskSource: String?,
    val healthAdvice: String?,
    val healthSource: String?,
    val sbpAvg: String?,
    val dbpAvg: String?,
    val bpLevel: String?,
    val normalProportion: String?,
    val lowProportion: String?,
    val normalHighProportion: String?,
    val primaryProportion: String?,
    val secondaryProportion: String?,
    val tertiaryHighProportion: String?,
    val normalBp: String?,
    val lowBp: String?,
    val normalHighBp: String?,
    val primaryBp: String?,
    val secondaryBp: String?,
    val tertiaryBp: String?,
    val sbpMinValue: String?,
    val sbpMaxValue: String?,
    val dbpMinValue: String?,
    val dbpMaxValue: String?,
    val createTime: String?
)
