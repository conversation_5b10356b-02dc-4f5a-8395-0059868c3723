package com.healthlink.hms.server.data.dto.charts

import com.healthlink.hms.server.data.dto.charts.pressure.PressureSummaryDTO

/**
 * Created by imaginedays on 2024/6/5
 * 健康风险建议
 */
data class HealthSummeryDTO(
    /**
     * 正常占比
     */
    val normalProp: String? = null,
    /**
     * 正常次数
     */
    val normalSum: String? = null,
    /**
     * 疑似心动过缓占比
     */
    val lowProp: String? = null,
    /**
     * 疑似心动过缓次数
     */
    val lowSum: String? = null,
    /**
     * 疑似心动过速占比
     */
    val highProp: String? = null,
    /**
     * 疑似心动过速次数
     */
    val highSum: String? = null,
    val createTime: String? = null,
    val valueList :ArrayList<HealthSummeryDTO>? = null
) : HealthSummeryBaseDTO() {
}