package com.healthlink.hms.server.data.dto

import android.util.Log
import androidx.compose.animation.fadeIn
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.TimeUtils

/**
 * Created by imaginedays on 2024/8/2
 * 1、当天首次启动App
 * 2、开机首次启动App
 */
class LaunchAfterBootDTO {
    private var launchDate: String? = null // yyyy-MM-dd App启动日期
    private var currBootTimestamp: Long = 0L // 设备启动时间
    private var appLaunchCount: Int =   0 // App在设备启动后，启动了多少次
    companion object {
        private const val TAG = "LaunchAfterBootDTO"
    }
    /**
     * 只记录设备启动时间戳
     */
    fun recordBootTimestamp() {
        // 1、记录启动时间戳
        currBootTimestamp = System.currentTimeMillis()
        // 开机首次启动App
        MMKVUtil.storeIsFirstLaunchAppAfterBoot(true)
        Log.i(TAG, "recordBootTimestamp 开机首次启动: ${MMKVUtil.getIsFirstLaunchAppAfterBoot()}")
    }

    /**
     * 增加App启动次数
     * 1、当天首次启动App
     * 2、开机首次启动App
     */

    fun increaseAppLaunchCount() {
        // 开机首次启动App
        MMKVUtil.storeIsFirstLaunchAppAfterBoot(false)
        Log.i(TAG, "recordBootTimestamp 开机首次启动: ${MMKVUtil.getIsFirstLaunchAppAfterBoot()}")
        appLaunchCount++
    }

    /**
     * 重值App启动次数
     */
    fun resetAppLaunchCount() {
        appLaunchCount = 0
    }

    override fun toString(): String {
        return "LaunchAfterBootDTO(launchDate=$launchDate, currBootTimestamp=$currBootTimestamp, appLaunchCount=$appLaunchCount)"
    }

}
