package com.healthlink.hms.server.data.dto

/**
 *@Author: 付仁秀
 *@Description：
 **/
data class ReportHealthPartDTO(
    val riskLevel: String?,
    val healthAdvice: String?,
    val healthSource: String?,
    val riskDescription: String?,
    val riskReason: String?,
    val riskSource: String?,
    val cvRiskLevel: String?,
    val cvRiskAnalyse: String?,
    val cvHealthAdvice: String?,
    val cvHealthSource: String?,
    /**
     * 心率平均值
     */
    val hrAvg: String?,
    /**
     * 收缩压平均值
     */
    val sbpAvg: String?,
    /**
     * 舒张压平均值
     */
    val dbpAvg: String?,
    /**
     * 血糖平均值
     */
    val bloodSugarAvg: String?,
    /**
     * 压力分值平均值
     */
    val pressureAvg: String?,
    /**
     * 心率最大值
     */
    val hrMax: String?,
    /**
     * 收缩压最大值
     */
    val sbpMax: String?,
    /**
     * 舒张压最大值
     */
    val dbpMax: String?,
    /**
     * 血糖最大值
     */
    val bloodSugarMax: String?,
    /**
     * 压力最大值
     */
    val pressureMax: String?,
    val score: String?,
    /**
     * 影响因素
     */
    val influenceFactor: ArrayList<AnnengInfluenceFactor>?,
    /**
     * 睡眠等级
     */
    val sleepLevel: String?,
    val sleepText: String?, //
    /**
     * 健康评分影响因素说明
     */
    val scoreFactorDesc: String?,
    /**
     * 健康评分变动原因
     */
    val scoreChangeReason: String?,
    /*
     * 上周期健康评分
     */
    val scoreLastCycle: String?,
    /**
     * 健康评分增减值
     */
    val scoreChangeValue: String?,
    /**
     * 同期对比文案
     */
    val samePeriodCompare :String?,
    /**
     * 触发阈值 默认值 5分钟
     */
    val swithValue: String? = "5"
)
