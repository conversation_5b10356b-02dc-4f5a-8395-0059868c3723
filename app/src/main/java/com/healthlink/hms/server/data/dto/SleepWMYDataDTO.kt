package com.healthlink.hms.server.data.dto

class SleepWMYDataDTO(
    var createTime:String,
    var fetchTime: String,
    var month: String,
    var avgNightSleepTime: Int,
    var avgNightSleepTimeHour: Int,
    var avgNightSleepTimeMin: Int,
    var avgSleepTime: Int?,
    var avgSleepTimeHour: Int,
    var avgSleepTimeMin: Int,
    var avgSleepScore: Int,
    var avgLightSleepTime: Int,
    var avgLightSleepTimeHour: Int,
    var avgLightSleepTimeMin: Int,
    var avgDeepSleepTime: Int,
    var avgDeepSleepTimeHour: Int,
    var avgDeepSleepTimeMin: Int,
    var avgDreamTime: Int,
    var avgDreamTimeHour: Int,
    var avgDreamTimeMin: Int,
    var avgAwakeTime: Int,
    var avgAwakeTimeHour: Int,
    var avgAwakeTimeMin: Int,
    var avgSporadicSleepTime: Int,
    var avgSporadicSleepTimeHour: Int,
    var avgSporadicSleepTimeMin: Int,
    var avgManualSleepTime:Int,
    var avgManualSleepTimeHour:Int,
    var avgManualSleepTimeMin:Int,
    var deepSleepPercent: String,
    var lightSleepTimePercent: String,
    var dreamTimePercent: String,
    var awakeTimePercent: String,
    var avgSporadicSleepPercent: String,
    var nodeList: ArrayList<SleepWMItemDTO>
) {

}