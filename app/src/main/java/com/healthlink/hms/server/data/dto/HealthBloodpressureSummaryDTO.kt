package com.healthlink.hms.server.data.dto

import com.healthlink.hms.server.data.dto.charts.HealthSummeryBaseDTO

/**
 *@Author: 付仁秀
 *@Description：
 **/
data class HealthBloodpressureSummaryDTO(
    /**
     * fetchtime
     */
    var createTime: String,
    /**
     * 收缩压平均值
     */
    var sbpAvg: String,
    /**
     * 舒张压平均值
     */
    var dbpAvg: String,
    /**
     * 血压等级
     */
    var bpLevel: String,
    /**
     * 正常占比
     */
    var normalProportion: String,
    /**
     * 低血压占比
     */
    var lowProportion: String,
    /**
     * 正常高值
     */
    var normalHighProportion: String,
    /**
     * 一级高血压占比
     */
    var primaryProportion: String,
    /**
     * 二级高血压占比
     */
    var secondaryProportion: String,
    /**
     * 三级高血压占比
     */
    var tertiaryHighProportion: String,
    /**
     * 正常等级数量
     */
    var normalBp: String,
    /**
     * 低血压等级数量
     */
    var lowBp: String, /**
     * 正常高值等级数量
     */
    var normalHighBp: String,
    /**
     * 一级高血压等级数量
     */
    var primaryBp: String,
    /**
     * 二级高血压等级数量
     */
    var secondaryBp: String,
    /**
     * 三级高血压等级数量
     */
    var tertiaryBp: String, /**
     * 收缩压最小值
     */
    var sbpMinValue: String,
    /**
     * 收缩压最大值
     */
    var sbpMaxValue: String,
    /**
     * 舒张压最小值
     */
    var dbpMinValue: String, /**
     * 舒张压最大值
     */
    var dbpMaxValue: String,

    var valueList: ArrayList<HealthBloodpressureSummaryDTO>

) : HealthSummeryBaseDTO()