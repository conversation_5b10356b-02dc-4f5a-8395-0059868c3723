package com.healthlink.hms.server.data.dto


class SleepCardShow2DTO(
//    val adviceSource: String? = null,
//    val level : String? = null,
//    val sleepTimeAvg : String? = null, // 睡眠总时长
//    val wakeSleepAvg : String? = null, // 深睡
//    val deepSleepAvg : String? = null,
//    val lightSleepAvg : String? = null,
//    val remAvg : String? = null,
//    val sporadicAvg:String?=null,
//    val wakeSleepProp : String? = null,
//    val deepSleepProp : String? = null,
//    val lightSleepProp : String? = null,
//    val remProp : String? = null,
//    val sporadicProp:String? = null,
    /**
     * 深睡时长
     */
    val deepSleepTime: String? = null,
    /**
     * 浅睡时长
     */
    val lightSleepTime: String? = null,
    /**
     * 快速眼动时长
     */
    val dreamTime: String? = null,
    /**
     * 清醒时长
     */
    var awakeTime: String? = null,
    /**
     * 零星睡眠时长
     */
    var sporadicSleepTime: String? = null,
){
}