package com.healthlink.hms.server.data.dto.charts

import com.healthlink.hms.server.data.dto.SleepCardShow2DTO
import com.healthlink.hms.server.data.dto.SleepCardShowDTO
import com.healthlink.hms.server.data.dto.SleepDayItemDTO

/**
 * Created by imaginedays on 2024/6/5
 * 睡眠健康风险建议
 */
data class HealthSleepSummeryDTO(
    val level : String? = null,
    val sleepTimeAvg : String? = null,
    val wakeSleepAvg : String? = null,
    val deepSleepAvg : String? = null,
    val lightSleepAvg : String? = null,
    val remAvg : String? = null,
    val sporadicAvg:String?=null,
    val wakeSleepProp : String? = null,
    val deepSleepProp : String? = null,
    val lightSleepProp : String? = null,
    val remProp : String? = null,
    val sporadicProp:String? = null,
    val createTime:String?=null,
    val valueList :ArrayList<SleepCardShow2DTO>
    ) : HealthSummeryBaseDTO() {
}