package com.healthlink.hms.server.data.dto

import com.healthlink.hms.server.data.bean.BaseDTO
import com.healthlink.hms.server.data.dto.charts.CDBloodOxygenDTO
import com.healthlink.hms.server.data.dto.charts.CDPressureDTO

/**
 * Created by imaginedays on 2024/5/27
 *
 *
 */
data class ChartDataDTO(
    var stress: List<CDPressureDTO> = listOf(),
    var sp2: List<CDBloodOxygenDTO> = listOf(),
    var heartrate: List<ChartDataEntry> = listOf(),
    ){
}