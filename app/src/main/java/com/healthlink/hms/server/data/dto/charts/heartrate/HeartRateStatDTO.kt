package com.healthlink.hms.server.data.dto.charts.heartrate

/**
 * Created by imaginedays on 2024/6/2
 * 心率详情页面
 */
data class HeartRateStatDTO(
    var startTime: String, // 开始时间
    var endTime: String, // 结束时间
    var avg: Float, // 平均值
    var max: Float, // 最大值
    var min: Float, // 最小值
    var last: Float, // 最新值
    var fetchTime: String, // 获取时间
    var tachycardia: Int, // 心动过速
    var tachycardiaPercent: String, // 心动过速比例
    var bradycardia: Int, // 心动过缓
    var bradycardiaPercent: String, // 心动过缓比例
    var normal: Int, // 正常心率
    var normalPercent: String, // 正常比例
    var valueList: ArrayList<HeartRateStatItemDTO> // 心率详细，周-月-年数据
) {
}