package com.healthlink.hms.service

import android.annotation.SuppressLint
import android.appwidget.AppWidgetManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.work.Worker
import androidx.work.WorkerParameters
import com.healthlink.hms.HmsSettings
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.sceneEngine.SceneManager
import com.healthlink.hms.biz.HealthDataProcessor
import com.healthlink.hms.journey.JourneyManager
import com.healthlink.hms.mvvm.model.BaseResponse
import com.healthlink.hms.mvvm.model.BaseResponseCallback
import com.healthlink.hms.mvvm.model.request.HealthInfoRequestParam
import com.healthlink.hms.mvvm.repository.MainRepository
import com.healthlink.hms.reciever.HMSAction
import com.healthlink.hms.server.data.dto.LiveHealthStatusDTO
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.widget.HMSWidgetProvider
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

class WidgetHmsDataWorker(appContext: Context, workerParams: WorkerParameters) : Worker(appContext, workerParams) {

    // WIDGET更新运行间隔
    private val WORKER_TASK_WIDGET_RUN_INTERVAL = 5 * 60 * 1000L  // 5分钟刷新间隔



    @SuppressLint("CheckResult")
    override fun doWork(): Result {
        // 在这里执行你的后台任务
        Log.d(mTag, "WidgetHmsDataWorker is doing its job...")
        if (MMKVUtil.isVisitorMode()) {
            Log.i(mTag, "游客模式不进行数据刷新")
            return Result.success()
        }

        // 桌面卡片更新数据处理
        try{
//            if(isTimeIntervalReach(WORKER_TASK_WIDGET_RUN_SUCCESS_TIME,WORKER_TASK_WIDGET_RUN_INTERVAL)){
                doWidgetUpdate()
//            }

        }catch (ex:Exception){
            Log.i(mTag,"do widget update failed, ${ex.message}")
        }

        // 行程采集服务启动或确保服务运行
        try{
            JourneyManager().startJourneyCollectService(applicationContext,"WidgetHmsDataWorker")
            JourneyManager().startJourneyCollectProtectService(applicationContext,"WidgetHmsDataWorker")
        }catch (ex:Exception){
            Log.i(mTag,"start journey service failed, ${ex.message}")
        }

        // 主动关怀场景启动
        try{
            if(isTimeIntervalReach(WORKER_TASK_SCENE_ENGINE_RUN_SUCCESS_TIME, HmsSettings.WORKER_TASK_SCENE_ENGINE_RUN_INTERVAL)){
                doSceneEngine()
            }

        }catch (ex:Exception){
            Log.i(mTag,"run scene engine failed, ${ex.message}")
        }

        return Result.success()
    }

    /**
     *  主动关怀场景启动
     */
    private fun doSceneEngine() {
        Log.d(mTag, "调起场景引擎")
        if (shouldRunSceneManager()) {
            Log.d(mTag, "开始执行场景引擎")
            GlobalScope.launch(Dispatchers.IO) {
                SceneManager()
                    .initScenes()
                    .doScenesFilterMethod()
                    .doScenesExecuteMethod()
            }
        }
    }

    /**
     * 桌面卡片更新
     */
    private fun doWidgetUpdate() {
        var userId = MMKVUtil.getUserId()

        //请求
        val healthInfoRequestParam = HealthInfoRequestParam()
        healthInfoRequestParam.extenalId = "extenalId";//"DCETOHY3JJ0383608"
        healthInfoRequestParam.channelId = "channelId";//"GMW-001"
        healthInfoRequestParam.userId = userId //"hl20240509155403109345014";//

        MMKVUtil.getUserId()?.let {
            var parameters = mapOf(
                "userId" to it
            )
            MainRepository().getLiveHealthStatus(parameters,
                object : BaseResponseCallback<LiveHealthStatusDTO> {
                    override fun onSuccess(response: BaseResponse<LiveHealthStatusDTO>) {
                        if ("0".equals(response.code)) {

                            var healthStatusDTO = response.data
                            if (healthStatusDTO != null) {

                                var riskLevel = healthStatusDTO.riskLevel
                                var score = healthStatusDTO.score
                                var result = healthStatusDTO.healthResult
                                // TODO 校验数据的合理性
                                if (result == null || "".equals(result)) {
                                    result = "数字健康，守护驾驶健康"
                                }

    //                            score = Random.nextInt(45,101).toString()
                                riskLevel = HealthDataProcessor.getHealthStatusFromScore(score)
                                if (!(score != null && score.isNotEmpty() && score.toInt() >= 45)) {
                                    riskLevel = "-1"
                                }

                                // 保存数据
                                val provider = HMSWidgetProvider()
                                provider.saveData(
                                    applicationContext,
                                    HMSWidgetProvider.DATA_KEY_RISK_LEVEL,
                                    riskLevel
                                )
                                provider.saveData(
                                    applicationContext,
                                    HMSWidgetProvider.DATA_KEY_SCORE,
                                    score
                                )
                                provider.saveData(
                                    applicationContext,
                                    HMSWidgetProvider.DATA_KEY_RESULT,
                                    result
                                )

                                // 更新桌面卡片
                                val appWidgetManager =
                                    AppWidgetManager.getInstance(applicationContext)
                                var appWidgetIds = appWidgetManager.getAppWidgetIds(
                                    ComponentName(
                                        applicationContext,
                                        HMSWidgetProvider::class.java
                                    )
                                )
                                provider.updateHmsWidget(applicationContext, appWidgetIds)
                                Log.d(mTag, "invoke updateHmsWidget() From ${mTag}")

                                //发送广播给主App，通知更新
                                val intentToApp = Intent(HMSAction.ACTION_HMS_WIDGET_DATA_UPDATED)
                                intentToApp.setPackage(applicationContext.packageName)
                                applicationContext.sendBroadcast(intentToApp)
                            }
                            MMKVUtil.storeTime(WORKER_TASK_WIDGET_RUN_SUCCESS_TIME,System.currentTimeMillis())
                        }
                    }

                    override fun onFailed(response: BaseResponse<LiveHealthStatusDTO>) {

                    }
                })
        }
    }

    /**
     * 判断是否达到了触发时间
     */
    private fun isTimeIntervalReach(workType: String, interval: Long): Boolean{
        var lastRunSuccessTime = MMKVUtil.getTime(workType)
        var currentTime = System.currentTimeMillis()

        Log.d(mTag,"isTimeIntervalReach of ${workType} " +
                ": lastRunSuccessTime = ${lastRunSuccessTime}, " +
                "  currentTime = ${currentTime}" )

        if(lastRunSuccessTime == null
            || (lastRunSuccessTime!=null && currentTime - lastRunSuccessTime >= interval)){
            return true
        }
        return false
    }

    private fun shouldRunSceneManager() : Boolean {
        // 更新桌面卡片
//        val appWidgetManager = AppWidgetManager.getInstance(HmsApplication.appContext)
//        var appWidgetIds = appWidgetManager.getAppWidgetIds(ComponentName(HmsApplication.appContext, HMSWidgetProvider::class.java))
//        var hasWidget = appWidgetIds.isNotEmpty()
        var isLogin = !MMKVUtil.isVisitorMode()
        var isOpenNotification = MMKVUtil.getNotificationOpen()
        val isNotPrivacyMode =  !HmsApplication.isPrivacyModeEnabled()

        Log.i(mTag,"isLogin:$isLogin,isOpenNotification:$isOpenNotification,isNotPrivacyMode:$isNotPrivacyMode")
        return isLogin && isOpenNotification && isNotPrivacyMode
    }



    companion object{
        val mTag = "WidgetHmsDataWorker"
        // WIDGET更新运行成功时间
        const val WORKER_TASK_WIDGET_RUN_SUCCESS_TIME = "WORKER_TASK_WIDGET_RUN_SUCCESS_TIME"
        // 场景引擎运行成功时间
        const val WORKER_TASK_SCENE_ENGINE_RUN_SUCCESS_TIME = "WORKER_TASK_SCENE_ENGINE_RUN_SUCCESS_TIME"


    }
}