package com.healthlink.hms.reciever

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.SystemClock
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.work.OneTimeWorkRequest
import androidx.work.WorkManager
import com.gwm.android.adapter.client.GwmAdapterClient
import com.gwm.android.adapter.client.ServiceStateListener
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.sceneEngine.SceneManager
import com.healthlink.hms.base.Constants
import com.healthlink.hms.journey.JourneyCollectService
import com.healthlink.hms.journey.JourneyManager
import com.healthlink.hms.sceneEngine.scenes.SceneLongDriveCare2Impl
import com.healthlink.hms.service.HmsDataWorker
import com.healthlink.hms.service.WidgetHmsDataWorker
import com.healthlink.hms.utils.LaunchAfterBootManager
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.widget.HMSWidgetManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * STR 退出事件监听器
 */
class StrExitReceiver: BroadcastReceiver() {

    private val mTag = "StrExitReceiver"

    private var mGwmAdapterClient: GwmAdapterClient =  GwmAdapterClient.getInstance()

    companion object{
        private const val ACTION_STR_EXIT = "com.gwm.action.str.exit"
    }
    override fun onReceive(context: Context?, intent: Intent?) {

        Log.i(mTag, "onReceive , action = ${intent!!.action}")

        if (ACTION_STR_EXIT == intent.action) {
            // 注册数据加载Worker
            HMSWidgetManager.scheduleTask(context!!, mTag)
            // 存储开机启动信息
            LaunchAfterBootManager.storeLaunchAfterBootInfo()
            try {
                //启动行程记录程序
                JourneyManager().startJourneyCollectService(context, mTag)
                JourneyManager().startJourneyCollectProtectService(context,mTag)
            }catch (ex:Exception){
                Log.i(mTag, "startService error: ${ex.message}")
            }

            // 重置场景引擎信号
            SceneManager.resetJourneySceneTriggerCount()

            // 重置场景引擎信号
            SceneLongDriveCare2Impl.resetJourney()

            // 存储上电时间
            MMKVUtil.storePowerMode2StartTime(System.currentTimeMillis())

            //重置banner显示状态，需再次提示
            MMKVUtil.storeAlreadyBannerTips(false)

            // STR事件，10秒内不处理：STR广播可能重复发送
            val currentTime = System.currentTimeMillis()
            if(currentTime - HmsApplication.STR_EXIT_TIMESTAMP > 5000){
                HmsApplication.STR_EXIT_TIMESTAMP = currentTime
                // 收到事件后，请求处理一次DataWorker
                try{
                    //定义回调广播
//                    val workManager = WorkManager.getInstance(context)
//                    // 创建WorkRequest
//                    val hmsHealthDataWorker = OneTimeWorkRequest.Builder(WidgetHmsDataWorker::class.java).build()
//                    // 调度WorkRequest
//                    workManager.enqueue(hmsHealthDataWorker)
                }catch (ex: Exception){
                    Log.i(mTag, "enqueue error: ${ex.message}")
                }

                // 处理行程上报事件
                try {
                    var thread = object : Thread() {
                        override fun run() {
                            Log.i(mTag, "doJourneyStart will delay 30000")
                            // 延迟30秒执行，等待GwmAdapter初始化完成。
                            sleep(30000)
                            // 上报行程
                            doJourneyStart(context)
                            Log.i(mTag, "doJourneyStart finished")
                        }
                    }
                    thread.start()
                }catch (ex: Exception){
                    Log.i(mTag, "doJourneyStart error: ${ex.message}")
                }
            }
        }
    }

    /**
     * 上报行程开始：
     *  首先判断mGwmAdapterClient是否已连接，如果未连接，则先建立连接，然后进行读取里程并上报；
     *  如果已连接，则读取里程并上报
     */
    fun doJourneyStart(context: Context){
        if(!mGwmAdapterClient.isServiceConnected) {
            Log.i(mTag, "GwmAdapterClient is not connected , do connecting first")
            val servers = arrayListOf("gwm_adapter_weather","gwm_adapter_media","gwm_adapter")
            mGwmAdapterClient.init(context, servers.toTypedArray(), object : ServiceStateListener {
                override fun onServiceConnected() {
                    Log.i(mTag, "GwmAdapterClient is connected now, do upload journey start...")
                    uploadJourneyStart()
                }

                /**
                 * 断开连接时，成功新链接
                 */
                override fun onServiceDisconnected() {
                    super.onServiceDisconnected()
                }
            })
        }else{
            Log.i(mTag, "GwmAdapterClient is already connected , do upload journey start directly")
            uploadJourneyStart()
        }
    }

    /**
     * 读取里程，并上报行程开始
     */
    private fun uploadJourneyStart(){
        Log.i(mTag, "update journey start from StrExitReceiver...")
        val mileageStr = mGwmAdapterClient.getData(JourneyCollectService.TOTAL_ODOMETER)
        if (mileageStr != null && mileageStr.isNotEmpty()) {
            JourneyManager().onJourneyStart(mileageStr.toFloat())
        }
    }
}