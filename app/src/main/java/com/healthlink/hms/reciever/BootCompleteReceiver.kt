package com.healthlink.hms.reciever

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.SystemClock
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.work.OneTimeWorkRequest
import androidx.work.WorkManager
import com.healthlink.hms.sceneEngine.SceneManager
import com.healthlink.hms.base.Constants
import com.healthlink.hms.journey.JourneyManager
import com.healthlink.hms.sceneEngine.scenes.SceneLongDriveCare2Impl
import com.healthlink.hms.service.HmsDataWorker
import com.healthlink.hms.service.WidgetHmsDataWorker
import com.healthlink.hms.utils.LaunchAfterBootManager
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.widget.HMSWidgetManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch


class BootCompleteReceiver: BroadcastReceiver() {

    private val mTag = "BootCompleteReceiver"
    override fun onReceive(context: Context?, intent: Intent?) {
        if (Intent.ACTION_BOOT_COMPLETED == intent!!.action) {
            val bootTimeMillis = System.currentTimeMillis()
            val elapsedTime = SystemClock.elapsedRealtime()
            Log.i(mTag, "Received Action ACTION_BOOT_COMPLETE on $bootTimeMillis")

            // 保存或处理bootTimeMillis
            var mSharePreference =
                context!!.getSharedPreferences(Constants.SHARE_HMS_INFO, AppCompatActivity.MODE_PRIVATE)
            mSharePreference.edit().putLong(Constants.SHARE_HMS_INFO_BOOT_TIME, bootTimeMillis).commit()

            // 注册数据加载Worker
            HMSWidgetManager.scheduleTask(context, mTag)

            // 存储开机启动信息
            LaunchAfterBootManager.storeLaunchAfterBootInfo()

            // 重置行程内场景引擎触发通知的次数
            SceneManager.resetJourneySceneTriggerCount()
            // 重置场景引擎信号
            SceneLongDriveCare2Impl.resetJourney()

            // 存储上电时间
            MMKVUtil.storePowerMode2StartTime(System.currentTimeMillis())

            //启动行程记录程序
            JourneyManager().startJourneyCollectService(context, "BootCompleteReceiver")
            JourneyManager().startJourneyCollectProtectService(context,"BootCompleteReceiver")

            //重置banner显示状态，需再次提示
            MMKVUtil.storeAlreadyBannerTips(false)

            // 开机启动时，运行一次 TODO imaginedays
//            doWorker(context)
        }
    }

    /**
     * 通过Worker去获取最新健康数据
     */
    public fun doWorker(context: Context){

//        MMKVUtil.storeTime(WidgetHmsDataWorker.WORKER_TASK_SCENE_ENGINE_RUN_SUCCESS_TIME,0)
        MMKVUtil.storeTime(WidgetHmsDataWorker.WORKER_TASK_WIDGET_RUN_SUCCESS_TIME,0)

        //定义回调广播
        val workManager = WorkManager.getInstance(context!!)
        // 创建WorkRequest
        val hmsHealthDataWorker = OneTimeWorkRequest.Builder(WidgetHmsDataWorker::class.java).build()
        // 调度WorkRequest
        workManager.enqueue(hmsHealthDataWorker)

//        GlobalScope.launch(Dispatchers.IO) {
//            var worker = HmsDataWorker(context)
//            worker.doWork()
//        }
    }
}