package com.healthlink.hms.reciever

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.work.OneTimeWorkRequest
import androidx.work.WorkManager
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.service.HmsDataWorker
import com.healthlink.hms.service.WidgetHmsDataWorker
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.widget.HMSWidgetManager
import com.healthlink.hms.widget.HMSWidgetProvider
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch


class HMSWidgetUpdateDataReceiver: BroadcastReceiver() {

    private val mTag = "HMSWidgetUpdateDataReceiver"
    companion object{
        const val ACTION_HMS_WIDGET_UPDATE_DATE = "com.healthlink.hms.action.ACTION_HMS_WIDGET_UPDATE_DATE"
        // 用户状态变化通知
        const val ACTION_HMS_USER_STATUS_UPDATE = "com.healthlink.hms.action.ACTION_HMS_USER_STATUS_UPDATE"
    }
    override fun onReceive(context: Context?, intent: Intent?) {
        if (ACTION_HMS_WIDGET_UPDATE_DATE == intent!!.action) {
            Log.d(mTag , "调起Worker，进行数据拉取、并更新")
            // 获取WorkManager的实例
//            val workManager = WorkManager.getInstance(context!!)
//            // 创建WorkRequest
//            val hmsHealthDataWorker = OneTimeWorkRequest.Builder(WidgetHmsDataWorker::class.java).build()
//            // 调度WorkRequest
//            workManager.enqueue(hmsHealthDataWorker)

            GlobalScope.launch(Dispatchers.IO) {
                var worker = HmsDataWorker(context!!)
                worker.doWork()
            }

//            1、桌面卡片存在（widgetManager获取卡片数量>0）&&
//            &&2、用户已登录
//            &&3、未打开私密模式
//            &&4、主动消息推送打开 抽取成一个函数
//            Log.d(mTag , "调起场景引擎")
//            if (shouldRunSceneManager()) {
//                Log.d(mTag , "开始执行场景引擎")
//                GlobalScope.launch(Dispatchers.IO) {
//                    SceneManager()
//                        .initScenes()
//                        .doScenesFilterMethod()
//                        .doScenesExecuteMethod()
//                }
//            }
        }

        //绑定服务
        HMSWidgetManager.scheduleTask(context!!, HMSWidgetProvider.mTag)
    }

    /**
     * 1、桌面卡片存在（widgetManager获取卡片数量>0）&&
     * 2、用户已登录 &&
     * 3、未打开私密模式 &&
     * 4、主动消息推送打开
     */
    private fun shouldRunSceneManager() : Boolean {
        // 更新桌面卡片
//        val appWidgetManager = AppWidgetManager.getInstance(HmsApplication.appContext)
//        var appWidgetIds = appWidgetManager.getAppWidgetIds(ComponentName(HmsApplication.appContext, HMSWidgetProvider::class.java))
//        var hasWidget = appWidgetIds.isNotEmpty()
        var isLogin = !MMKVUtil.isVisitorMode()
        var isOpenNotification = MMKVUtil.getNotificationOpen()
        val isNotPrivacyMode =  !HmsApplication.isPrivacyModeEnabled()

        Log.i(mTag,"isLogin:$isLogin,isOpenNotification:$isOpenNotification,isNotPrivacyMode:$isNotPrivacyMode")
        return isLogin && isOpenNotification && isNotPrivacyMode
    }
}