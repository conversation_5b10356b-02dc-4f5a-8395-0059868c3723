package com.healthlink.hms.viewmodels


import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.healthlink.hms.mvvm.model.BaseResponse
import com.healthlink.hms.mvvm.model.BaseResponseCallback
import com.healthlink.hms.mvvm.repository.MainRepository
import com.healthlink.hms.server.data.dto.UserInfoDTO
import com.kunminx.architecture.domain.message.MutableResult

/**
 * Created by zwb on 2024/10/20
 * 设置页面的ViewModel类
 */
class SettingsViewModel : ViewModel() {
    // 数据使用说明
    private val dataUsageData: MutableLiveData<BaseResponse<String>> by lazy {
        MutableLiveData<BaseResponse<String>>()
    }

    //注销
//    var deleteuserResult = MutableLiveData<BaseResponse<Boolean>>()
    var deleteuserResult = MutableResult<BaseResponse<Boolean>>()


    //关联账户解绑
//    var unbindAccountResult = MutableLiveData<BaseResponse<Boolean>>()
    var unbindAccountResult = MutableResult<BaseResponse<Boolean>>()

    //获取用户信息
//    var userInfoData = MutableLiveData<BaseResponse<UserInfoDTO>>()
    var userInfoData = MutableResult<BaseResponse<UserInfoDTO>>()


    //保存用户信息
//    var saveUserInfoResult = MutableLiveData<BaseResponse<Boolean>>()
    var saveUserInfoResult = MutableResult<BaseResponse<Boolean>>()


    /**
     * 注销
     */
    fun getDeleteUserData(userId: String) {
        MainRepository().getDeleteUser(userId, object : BaseResponseCallback<Boolean> {
            override fun onSuccess(response: BaseResponse<Boolean>) {
                Log.d("MainViewModel", "getDeleteUser onSuccess")
                deleteuserResult.value = response
            }

            override fun onFailed(response: BaseResponse<Boolean>) {
                Log.d("MainViewModel", "getDeleteUser onFailed")
                deleteuserResult.value = response
            }

        })
    }

    /**
     * 关联账户解绑
     */
    fun getUnbindUserData(userId: String) {
        MainRepository().getUnbindAccount(userId, object : BaseResponseCallback<Boolean> {
            override fun onSuccess(response: BaseResponse<Boolean>) {
                unbindAccountResult.postValue(response)
            }

            override fun onFailed(response: BaseResponse<Boolean>) {
                unbindAccountResult.postValue(response)
            }

        })
    }

    /**
     * 获取用户数据
     */
    fun getUserInfoData(userId: String) {
        MainRepository().getUserInfo(userId, object : BaseResponseCallback<UserInfoDTO> {
            override fun onSuccess(response: BaseResponse<UserInfoDTO>) {
                userInfoData.postValue(response)
            }

            override fun onFailed(response: BaseResponse<UserInfoDTO>) {
                userInfoData.postValue(response)
            }

        })
    }

    /**
     * 保存用户数据
     */
    fun saveUserData(requestParam: Map<*, *>) {
        MainRepository().saveUserInfoData(requestParam, object : BaseResponseCallback<Boolean> {
            override fun onSuccess(response: BaseResponse<Boolean>) {
                saveUserInfoResult.postValue(response)
            }

            override fun onFailed(response: BaseResponse<Boolean>) {
                saveUserInfoResult.postValue(response)
            }

        })
    }

    /**
     * 数据使用说明liveData
     */
    fun getDataUsageLiveData(): LiveData<BaseResponse<String>> {
        return dataUsageData
    }

    override fun onCleared() {

        Log.d("ViewModel","onCleared")
//        this.deleteuserResult.value = null

        super.onCleared()
    }

}
