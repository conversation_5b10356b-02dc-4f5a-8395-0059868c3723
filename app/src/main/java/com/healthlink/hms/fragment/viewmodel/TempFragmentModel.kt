package com.healthlink.hms.fragment.viewmodel

/**
 * 体温二级页面的DataModel
 */
import com.healthlink.hms.mvvm.model.BaseResponse
import com.healthlink.hms.server.data.dto.HealthTempSummaryDTO
import com.healthlink.hms.server.data.dto.TempItemResponseDTO
import com.healthlink.hms.server.data.dto.charts.pressure.PressureSummaryDTO

class TempFragmentModel{

    /**
     * 页面滚动的Y值
     */
    var scollY : Int = 0

    /**
     * 健康数据
     */
    var healthData : BaseResponse<TempItemResponseDTO>? = null

    /**
     * 健康建议响应对象
     */
    var healthSummaryResponse : BaseResponse<HealthTempSummaryDTO>? = null

    var isShowExplainDialog : Boolean = false

}


