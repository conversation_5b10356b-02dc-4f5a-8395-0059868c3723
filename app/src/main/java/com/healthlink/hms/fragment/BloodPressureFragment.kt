package com.healthlink.hms.fragment

import android.app.Dialog
import android.content.DialogInterface
import android.graphics.Rect
import android.graphics.Typeface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.text.method.ScrollingMovementMethod
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.TouchDelegate
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.Button
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.blankj.utilcode.util.ClickUtils
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.R
import com.healthlink.hms.activity.card.HMSCardFragmentInteractWithAcInterface
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.databinding.FragmentBloodPressureBinding
import com.healthlink.hms.fragment.viewmodel.BloodPressureFragmentModel
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.ktExt.sp
import com.healthlink.hms.mvvm.model.BaseResponse
import com.healthlink.hms.server.data.dto.BloodPressureCard1DTO
import com.healthlink.hms.server.data.dto.BloodPressureCard2DTO
import com.healthlink.hms.server.data.dto.BloodPressureItemDTO
import com.healthlink.hms.server.data.dto.BloodPressureResponseDTO
import com.healthlink.hms.server.data.dto.HealthBloodpressureSummaryDTO
import com.healthlink.hms.server.data.dto.HealthDataStatusDTO
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.HMSDialogUtils
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.TimeUtils
import com.healthlink.hms.utils.TimeUtils.getMonthListStr
import com.healthlink.hms.utils.TimeUtils.getWeekListStr
import com.healthlink.hms.utils.TimeUtils.resetWMDateTime
import com.healthlink.hms.utils.TimeUtils.resetYDateTime
import com.healthlink.hms.utils.getPrivacyModeDate
import com.healthlink.hms.viewmodels.MainViewModel
import com.healthlink.hms.views.ImmersiveDialog
import com.healthlink.hms.views.MiddleEllipsesTextView
import java.lang.ref.WeakReference
import java.util.Calendar

/**
 *@Author: 付仁秀
 *@Description：
 **/
class BloodPressureFragment : BaseCardFragment<FragmentBloodPressureBinding, MainViewModel>(
    MainViewModel::class.java,
    R.layout.fragment_blood_pressure
), MiddleEllipsesTextView.UpdateSeeMore {
    private var chartDayList = arrayListOf<BloodPressureItemDTO>()
    private var chartWeekList = arrayListOf<BloodPressureItemDTO>()
    private var chartMonthList = arrayListOf<BloodPressureItemDTO>()
    private var chartYearList = arrayListOf<BloodPressureItemDTO>()
    private var healthDayData: HealthBloodpressureSummaryDTO? = null
    private var healthWeekData: HealthBloodpressureSummaryDTO? = null
    private var healthMonthData: HealthBloodpressureSummaryDTO? = null
    private var healthYearData: HealthBloodpressureSummaryDTO? = null
    private var reqMap = mapOf<String, String>()
    private var healthAdviceStr = ""
    private var introString = ""
    private var isNoDataMode = false
    private val defaultDataCard1 = BloodPressureCard1DTO(
        "",
        "--/--"
    )
    private var isProvacyMode = false

    private val defaultAdviceCard2 = BloodPressureCard2DTO(
        "0",
        "0",
        "0",
        "0", "0", "0"
    )

    private var fragmentDataModel = BloodPressureFragmentModel()

    companion object {
        private const val TAG = "BloodPressureFragment"
        private const val ARG_PARAM_TYPE = "ARG_PARAM_TYPE"
        private val fragmentInteractWithAC
            get() = _fragmentInteractWithAC?.get()
        private var _fragmentInteractWithAC: WeakReference<HMSCardFragmentInteractWithAcInterface>? =
            null
        private lateinit var mUserId: String

        fun newInstance(
            cartTimeType: TimeCode,
            userId: String,
            interact: HMSCardFragmentInteractWithAcInterface
        ): BloodPressureFragment {
            val fragment = BloodPressureFragment()
            val args = Bundle()
            args.putString(ARG_PARAM_TYPE, cartTimeType.timeCode)
            fragment.arguments = args
            _fragmentInteractWithAC = WeakReference(interact)
            mUserId = userId
            return fragment
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            cardTimeType = it.getString(ARG_PARAM_TYPE)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initUI()

//        if (!HmsApplication.isPrivacyModeEnabled()) {
        initData()
        // 恢复数据
        if(savedInstanceState!=null){
            restoreDataIfPossible(savedInstanceState)
        }
    }

    private fun restoreDataIfPossible(savedInstanceState: Bundle) {
        try {
            var fragmentSavedData = savedInstanceState.getString(KEY_SAVED_DATA_SUMMARY)
            if (fragmentSavedData != null) {
                var fragmentDataModel =
                    gson.fromJson(fragmentSavedData, BloodPressureFragmentModel::class.java)
                if(fragmentDataModel!=null){
                    this.fragmentDataModel = fragmentDataModel
                    binding.svContainer.scrollY = this.fragmentDataModel.scollY
                    Log.i(TAG, "init BloodPressureFragment data from saved fragment success.")
                }
            }

            if(savedInstanceState.getBoolean(KEY_SHOW_DIALOG_FLAG)) {
                showExplainDialog()
            }
        }catch (ex: Exception){
            Log.i(TAG, "init BloodPressureFragment data from saved fragment fail. error : ${ex.message}")
        }
    }

    override fun onResume() {
        super.onResume()
        if (!HmsApplication.isPrivacyModeEnabled()) setScrollEnable(binding.svContainer)
        if (isDataReady)
            sendRequest(mUserId, cardTimeType!!)
        else
            handler.post(runnable)
    }

    override fun onPause() {
        super.onPause()
        handler.removeCallbacks(runnable)
    }


    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)

        // 保存数据
        if(this.fragmentDataModel!=null
            && this.fragmentDataModel.healthData!=null
            && this.fragmentDataModel.healthData!!.code == "0"
            && fragmentDataModel.healthSummaryResponse!=null
            && fragmentDataModel.healthSummaryResponse!!.code == "0"
        ){
            fragmentDataModel.scollY = binding.svContainer.scrollY
            outState.putString(KEY_SAVED_DATA_SUMMARY, gson.toJson(fragmentDataModel));
        }

        outState.putBoolean(KEY_SHOW_DIALOG_FLAG, fragmentDataModel.isShowExplainDialog)

    }

    override fun sendRequest(userId: String, timeCode: String) {
        reqMap = mapOf(
            "userId" to userId,
            "unit" to timeCode
        )
        if (!HmsApplication.isPrivacyModeEnabled()) {
            resetData()
            if (!HmsApplication.isNetworkConn()) {
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            } else {
                if (isDataReady) showLoading()

                if(this.fragmentDataModel!=null
                        && this.fragmentDataModel.healthData!=null
                        && this.fragmentDataModel.healthData!!.code == "0"){
                    when (cardTimeType) {

                        TimeCode.TIME_CODE_DAY.timeCode -> {
                            processHealthDataDay(fragmentDataModel.healthData!!)
                        }

                        TimeCode.TIME_CODE_WEEK.timeCode -> {
                            processHealthDataWeek(fragmentDataModel.healthData!!)
                        }

                        TimeCode.TIME_CODE_MONTH.timeCode -> {
                            processHealthDataMonth(fragmentDataModel.healthData!!)
                        }

                        TimeCode.TIME_CODE_YEAR.timeCode -> {
                            processHealthDataYear(fragmentDataModel.healthData!!)
                        }
                    }
                }else {
                    viewModel.getBloodPressureData(reqMap)
                }
            }
        } else initPrivacyUI(timeCode)
    }

    private fun resetData() {
        binding.tvC41Per.text = "0%"
        binding.tvC42Per.text = "0%"
        binding.tvC43Per.text = "0%"
        binding.tvC44Per.text = "0%"
        binding.tvC45Per.text = "0%"
        binding.tvC46Per.text = "0%"

        moveToBottom(binding.tvPreBloodPressure)
        moveToBottom(binding.sleepHourUnit)
        moveToBottom(binding.tvSpo2MinUnit)
    }

    override fun sendDataReadyRequest(userId: String, timeCode: String) {
        isProvacyMode = HmsApplication.isPrivacyModeEnabled()
        when (timeCode) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Bloodpressurereports_Daytab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
                binding.tvPreBloodPressure.text = ""
                binding.cTempWmy.setXData(null, TimeCode.TIME_CODE_DAY.timeCode)
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Bloodpressurereports_Weektab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
                binding.tvPreBloodPressure.text = "平均血压"
                binding.cTempWmy.setXData(getWeekListStr(), TimeCode.TIME_CODE_WEEK.timeCode)
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Bloodpressurereports_Mouthtab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
                binding.tvPreBloodPressure.text = "平均血压"
                binding.cTempWmy.setXData(getMonthListStr(), TimeCode.TIME_CODE_MONTH.timeCode)
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Bloodpressurereports_Yeartab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
                binding.tvPreBloodPressure.text = "平均血压"
                binding.cTempWmy.setXData(null, TimeCode.TIME_CODE_YEAR.timeCode)
            }
        }
        applyPrivacyStyleChanged(isProvacyMode)
        if (!isProvacyMode) {
            if (!HmsApplication.isNetworkConn()) {
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            }else{
                showLoading()
            }
            resetData()
            setScrollEnable(binding.svContainer, false)
            viewModel.getHistoryStatusData(userId)
        } else
            initPrivacyUI(timeCode)
    }

    private val runnable = object : Runnable {
        private val weekFragment = WeakReference(this@BloodPressureFragment)
        override fun run() {
            val fragment = weekFragment.get()
            if (fragment != null && !fragment.isDetached) {
                if (!fragment.isDataReady) {
                    fragment.sendDataReadyRequest(fragment.mUserId, fragment.cardTimeType!!)
                    // 15 秒后再次调用
                    if (HmsApplication.isNetworkConn()) {
                        fragment.handler.postDelayed(this, 15000)
                    }
                }
            }
        }
    }

    fun getDataReady() {
        isDataReady = true
        handler.removeCallbacks(runnable)
        sendRequest(mUserId, cardTimeType!!)
    }

    fun readyDataNoAuth() {
        handler.removeCallbacks(runnable)
        showNoAuthView()
        fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
    }

    private fun initData() {
        viewModel.healthHistoryData.observe(viewLifecycleOwner) {
            val notReadyText =
                "血压${requireContext().resources.getString(R.string.text_data_not_ready)}"
            if (it.code == "0" && it.data != null) {
                val statusList = it.data?.dataStatusList
                if (statusList.isNullOrEmpty()) //如果是空的 也认为是有数据的
                {
                    getDataReady()
                    return@observe
                }
                var status: HealthDataStatusDTO? = null
                if (!statusList.isNullOrEmpty()) {
                    val statusArray = statusList.filter { it.dataType == "bloodpressureRead" }
                    if (!statusArray.isNullOrEmpty()) {
                        status = statusArray[0]
                    } else {
                        getDataReady()
                        return@observe
                    }
                }
                if (status != null) {
                    when (cardTimeType) {
                        TimeCode.TIME_CODE_DAY.timeCode -> {
                            if (status.dayDataStatus == null || status.dayDataStatus == 2) {
                                //如果返回值是null  也认为是有值的
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_WEEK.timeCode -> {
                            if (status.weekDataStatus == null || status.weekDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_MONTH.timeCode -> {
                            if (status.monthDataStatus == null || status.monthDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_YEAR.timeCode -> {
                            if (status.yearDataStatus == null || status.yearDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }
                    }
                } else {
                    showNetErrorOrSettingView()
                    fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
                }
            } else if (it.code == "5") {
                showNoAuthView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            } else {
                // 无网络或者刷新失败处理
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            }
        }


        viewModel.healthBloodPressureSummary.observe(requireActivity()) {
            this.fragmentDataModel.healthSummaryResponse = it
            processSummaryData(it)
        }
        viewModel.bloodPressureDayChartData.observe(requireActivity()) {
            this.fragmentDataModel.healthData = it
            processHealthDataDay(it)
        }

        viewModel.bloodPressureWeekChartData.observe(requireActivity()) {
            this.fragmentDataModel.healthData = it
            processHealthDataWeek(it)
        }

        viewModel.bloodPressureMonthChartData.observe(requireActivity()) {
            this.fragmentDataModel.healthData = it
            processHealthDataMonth(it)
        }

        viewModel.bloodPressureYearChartData.observe(requireActivity()) {
            this.fragmentDataModel.healthData = it
            processHealthDataYear(it)
        }

        initChartData()

        // 给周月年注册滑动监听
        regisSelectListener()
    }

    private fun processHealthDataYear(it: BaseResponse<BloodPressureResponseDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            chartYearList = it.data!!.nodeList
            if (!chartYearList.isNullOrEmpty() && isDataListNotEmpty(chartYearList)) {
    //                    val showCardDTO = BloodPressureCard1DTO(
    //                        getFetchTime(chartYearList.last().createTime),
    //                        currentValueStr(
    //                            chartYearList.last().systolicPressureAvg,
    //                            chartYearList.last().diastolicPressureAvg
    //                        )
    //                    )
    //                    binding.cardShowInfo = showCardDTO
                val map = mutableMapOf<String, String>()
                map.putAll(reqMap)
                map.put("startTime", it.data!!.startTime)
                map.put("endTime", it.data!!.endTime)

                initChartData()

                processLoadingSummaryData(map)
            } else {
                initChartNoData(TimeCode.TIME_CODE_YEAR.timeCode)
            }
        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            setScrollEnable(binding.svContainer)
        }, 500)
    }

    private fun processHealthDataMonth(it: BaseResponse<BloodPressureResponseDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            chartMonthList = it.data!!.nodeList
            if (!chartMonthList.isNullOrEmpty() && isDataListNotEmpty(chartMonthList)) {
    //                    val showCardDTO = BloodPressureCard1DTO(
    //                        getFetchTime(chartMonthList.last().createTime),
    //                        currentValueStr(
    //                            chartMonthList.last().systolicPressureAvg,
    //                            chartMonthList.last().diastolicPressureAvg
    //                        )
    //                    )
    //                    binding.cardShowInfo = showCardDTO
                val map = mutableMapOf<String, String>()
                map.putAll(reqMap)
                map.put("startTime", it.data!!.startTime)
                map.put("endTime", it.data!!.endTime)

                initChartData()

                processLoadingSummaryData(map)
            } else {
                initChartNoData(TimeCode.TIME_CODE_MONTH.timeCode)
            }
        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            setScrollEnable(binding.svContainer)
        }, 400)
    }

    private fun processHealthDataWeek(it: BaseResponse<BloodPressureResponseDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            chartWeekList = it.data!!.nodeList
            if (!chartWeekList.isNullOrEmpty() && isDataListNotEmpty(chartWeekList)) {
    //                    val showCardDTO = BloodPressureCard1DTO(
    //                        getFetchTime(chartWeekList.last().createTime),
    //                        currentValueStr(
    //                            chartWeekList.last().systolicPressureAvg,
    //                            chartWeekList.last().diastolicPressureAvg
    //                        ),
    //
    //                        )
    //                    binding.cardShowInfo = showCardDTO
                val map = mutableMapOf<String, String>()
                map.putAll(reqMap)
                map.put("startTime", it.data!!.startTime)
                map.put("endTime", it.data!!.endTime)

                initChartData()

                processLoadingSummaryData(map)
            } else {
                initChartNoData(TimeCode.TIME_CODE_WEEK.timeCode)
            }
        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            setScrollEnable(binding.svContainer)
        }, 400)
    }

    private fun processHealthDataDay(it: BaseResponse<BloodPressureResponseDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            chartDayList = it.data!!.nodeList
            if (!chartDayList.isNullOrEmpty() && isDataListNotEmpty(chartDayList)) {
    //                    val showCardDTO = BloodPressureCard1DTO(
    //                        getFetchTime(chartDayList.last().createTime),
    //                        currentValueStr(
    //                            chartDayList.last().systolicPressureLast,
    //                            chartDayList.last().diastolicPressureLast
    //                        )
    //                    )
    //                    binding.cardShowInfo = showCardDTO
                val map = mutableMapOf<String, String>()
                map.putAll(reqMap)
                map.put("startTime", it.data!!.startTime)
                map.put("endTime", it.data!!.endTime)

                initChartData()

                processLoadingSummaryData(map)
            } else {
                initChartNoData(TimeCode.TIME_CODE_DAY.timeCode)
            }
        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            setScrollEnable(binding.svContainer)
        }, 400)
    }

    /**
     * 判断是否有合理的健康建议数据。如果有，则直接使用，否则去服务器获取
     */
    fun processLoadingSummaryData(map: MutableMap<String,String>){
        if(fragmentDataModel==null
            || fragmentDataModel.healthSummaryResponse == null
            || fragmentDataModel.healthSummaryResponse!!.code != "0") {
            viewModel.getHealthBloodPressureSummery(map)
        }else{
            processSummaryData(fragmentDataModel.healthSummaryResponse!!)
        }
    }

    private fun processSummaryData(it: BaseResponse<HealthBloodpressureSummaryDTO>){
        if (it.code == "0" && it.data != null) {
            binding.healthBloodpressureSummeryVo = it.data
            binding.svContainer.post {
                binding.svContainer.requestLayout()
            }
            Handler(Looper.getMainLooper()).postDelayed({
                hideLoading()
                setScrollEnable(binding.svContainer)
            }, 100)
            it.data!!.healthAdvice?.let { item ->
                healthAdviceStr = item
            }

            binding.tvSpo2MinValue.text =
                "${it.data!!.sbpAvg ?: "--"}/${it.data!!.dbpAvg ?: "--"}"
            when (cardTimeType) {
                TimeCode.TIME_CODE_DAY.timeCode -> {
                    healthDayData = it.data
                    binding.healthRiskAll.tvHealthAdviceContent.setIsEllipsized(false)
                    buildCard3Info(it)
                }

                TimeCode.TIME_CODE_WEEK.timeCode -> {
                    healthWeekData = it.data
                    binding.healthRiskAll.tvHealthAdviceContent.setIsEllipsized(false)
                    buildCard3Info(it)
                }

                TimeCode.TIME_CODE_MONTH.timeCode -> {
                    healthMonthData = it.data
                    binding.healthRiskAll.tvHealthAdviceContent.setIsEllipsized(false)
                    buildCard3Info(it)
                }

                TimeCode.TIME_CODE_YEAR.timeCode -> {
                    healthYearData = it.data
                    binding.healthRiskAll.tvHealthAdviceContent.setIsEllipsized(false)
                    buildCard3Info(it)
                }
            }
        }

        // 恢复滚动位置
        if(this.fragmentDataModel.scollY>0) {
            binding.svContainer.post({
                binding.svContainer.scrollY = this.fragmentDataModel.scollY
            })
        }
    }

    private fun buildCard3Info(it: BaseResponse<HealthBloodpressureSummaryDTO>) {
        var bloodPressureCard2 = BloodPressureCard2DTO(
            it.data!!.normalProportion,
            it.data!!.lowProportion,
            it.data!!.normalHighProportion,
            it.data!!.primaryProportion,
            it.data!!.secondaryProportion,
            it.data!!.tertiaryHighProportion
        )
        showPerOrNot(bloodPressureCard2)
        binding.cardShowInfo2 = bloodPressureCard2
    }

    private fun regisSelectListener() {
        when (cardTimeType) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                binding.cTempWmy.setOnDaySelectListener { index, item ->
                    var showCardDTO = BloodPressureCard1DTO(
                        "",
                        "--/--",
                    )
                    if (item != null) {
                        if (!chartDayList.isNullOrEmpty()) {
                            showCardDTO = BloodPressureCard1DTO(
                                getFetchTime(chartDayList[index].createTime),
                                currentValueStr(
                                    chartDayList[index].systolicPressureLast,
                                    chartDayList[index].diastolicPressureLast
                                )
                            )
                        }
                    } else {
                        var fetchTime = ""
                        if(index>=0&&index<chartDayList.size && chartDayList[index].createTime!=null){
                            fetchTime = chartDayList[index].createTime!!
                        }
                        showCardDTO = BloodPressureCard1DTO(
                            fetchTime,
                            "--/--",
                        )
                    }

                    binding.cardShowInfo = showCardDTO

                }
                val currentDateStr = getDayFetchTime()
                binding.cTempWmy.setOnXTextSelectListener { index, xText ->
                    if (isNoDataMode)
                        binding.tvSleepDate.text = currentDateStr + " " + xText
                }
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                binding.cTempWmy.setOnDaySelectListener { index, item ->
                    if (chartWeekList.isEmpty() || index >= chartWeekList.size) return@setOnDaySelectListener
                    scrollChangeValue(
                        item,
                        index,
                        chartWeekList,
                        healthWeekData,
                        TimeCode.TIME_CODE_WEEK.timeCode
                    )
                }
                binding.cTempWmy.setOnXTextSelectListener { index, xText ->
                    if (isNoDataMode || (chartWeekList.isNotEmpty() && index >= chartWeekList.size)) {
                        val showCard1 = BloodPressureCard1DTO(
                            resetWMDateTime(xText),
                            "--/--"
                        )
                        binding.cardShowInfo = showCard1
                    }
                }
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                binding.cTempWmy.setOnDaySelectListener { index, item ->
                    if (chartMonthList.isEmpty() || index >= chartMonthList.size) return@setOnDaySelectListener
                    scrollChangeValue(
                        item,
                        index,
                        chartMonthList,
                        healthMonthData,
                        TimeCode.TIME_CODE_MONTH.timeCode
                    )
                }
                val monthDayList = getMonthListStr()
                binding.cTempWmy.setOnXTextSelectListener { index, xText ->
                    if (isNoDataMode || (chartMonthList.isNotEmpty() && index >= chartMonthList.size))
                        try {
                            val showCard1 = BloodPressureCard1DTO(
                                resetWMDateTime(monthDayList[index]),
                                "--/--"
                            )
                            binding.cardShowInfo = showCard1
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }

                }

            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                binding.cTempWmy.setOnDaySelectListener { index, item ->
                    if (chartYearList.isEmpty() || index >= chartYearList.size) return@setOnDaySelectListener
                    scrollChangeValue(
                        item,
                        index,
                        chartYearList,
                        healthYearData,
                        TimeCode.TIME_CODE_YEAR.timeCode
                    )
                }
                binding.cTempWmy.setOnXTextSelectListener { index, xText ->
                    if (isNoDataMode || (chartYearList.isNotEmpty() && index >= chartYearList.size)) {
                        val showCard1 = BloodPressureCard1DTO(
                            resetYDateTime(xText),
                            "--/--"
                        )
                        binding.cardShowInfo = showCard1
                    } else if (chartYearList.isNotEmpty()) {
                        val dto = chartYearList[index]
                        if (dto.systolicPressureAvg == null || dto.systolicPressureAvg == 0F) {
                            val showCard1 = BloodPressureCard1DTO(
                                resetYDateTime(xText),
                                "--/--"
                            )
                            binding.cardShowInfo = showCard1
                        } else {
                            val showCard = BloodPressureCard1DTO(
                                getFetchTime(dto.createTime),
                                currentValueStr(
                                    dto.systolicPressureAvg,
                                    dto.diastolicPressureAvg
                                )
                            )
                            binding.cardShowInfo = showCard
                        }
                    }
                }

            }

        }
    }

    private fun scrollChangeValue(
        item: Any?,
        index: Int,
        dataList: ArrayList<BloodPressureItemDTO>,
        advice: HealthBloodpressureSummaryDTO?,
        timeCode: String
    ) {
        var showCard1 = defaultDataCard1
//        var showCard2 = defaultAdviceCard2
        if (item != null) {
            if (!dataList.isNullOrEmpty()) {
                showCard1 = BloodPressureCard1DTO(
                    getFetchTime(dataList[index].createTime),
                    currentValueStr(
                        dataList[index].systolicPressureAvg,
                        dataList[index].diastolicPressureAvg
                    )
                )
            }
//            if (advice != null && index < advice.valueList.size) {
//                showCard2 = BloodPressureCard2DTO(
//                    advice.valueList[index].normalProportion,
//                    advice.valueList[index].lowProportion,
//                    advice.valueList[index].normalHighProportion,
//                    advice.valueList[index].primaryProportion,
//                    advice.valueList[index].secondaryProportion,
//                    advice.valueList[index].tertiaryHighProportion
//                )
//            }
        } else {
            showCard1 = BloodPressureCard1DTO(
                getFetchTime(dataList[index].createTime),
                "--/--"
            )
//            showCard2 = defaultAdviceCard2
        }
//        showPerOrNot(showCard2)
//        binding.cardShowInfo2 = showCard2
        binding.cardShowInfo = showCard1

    }

    private fun initUI() {
        initExplain()
        binding.ivIntroTips.findViewById<ImageView>(R.id.iv_intro_tips)
        ClickUtils.applySingleDebouncing(binding.ivIntroTips, 1000) {
//            showHmsDialog(R.layout.hms_dialog_tips, "血压说明", introString)
            showExplainDialog()
        }
        binding.healthRiskAll.tvHealthAdviceContent.setCallback(this)
        val scrollView = binding.svContainer
        binding.healthRiskAll.tvHealthAdviceContent.setEndPercentage(80)
        binding.healthRiskAll.tvSeeMore.text = Html.fromHtml("<u>查看更多</u>")
        scrollView.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
            val height = scrollView.getChildAt(0).height // 获取ScrollView内容的总高度
            val scrollViewHeight = scrollView.height // 获取ScrollView的可见高度
            val diff = (height - scrollViewHeight) * 0.75f //scrollview中判定的距离 动画view位置底部约为总长度的的75%
            if (scrollY >= diff) {
                MMKVUtil.getUserId()?.let {
                    DataTrackUtil.dtScroll(
                        "Health_Bloodpressurereports_Bottom_Show",
                        DataTrackUtil.userIDMap(it)
                    )
                }
            }
        }
    }

    fun initChartData() {
        isNoDataMode = false
        setUnitVisiable(true)
        binding.sPrivacyText.visibility = View.GONE
        when (cardTimeType) {
            // 时间类型日 - 折线图
            TimeCode.TIME_CODE_DAY.timeCode -> {
                if (chartDayList.isEmpty()) {
                    binding.cTempWmy.visibility = View.GONE
                    binding.tempNoDataText.visibility = View.VISIBLE
                    return
                }
                binding.cTempWmy.visibility = View.VISIBLE
                binding.tempNoDataText.visibility = View.GONE
                initDayChart()
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                if (chartWeekList.isEmpty()) {
                    binding.cTempWmy.visibility = View.GONE
                    binding.tempNoDataText.visibility = View.VISIBLE
                    return
                }
                binding.cTempWmy.visibility = View.VISIBLE
                binding.tempNoDataText.visibility = View.GONE
                initWeekChart()
            }
//
            TimeCode.TIME_CODE_MONTH.timeCode -> {
                if (chartMonthList.isEmpty()) {
                    binding.cTempWmy.visibility = View.GONE
                    binding.tempNoDataText.visibility = View.VISIBLE
                    return
                }
                binding.cTempWmy.visibility = View.VISIBLE
                binding.tempNoDataText.visibility = View.GONE
                initMonthChart()
            }
//
            TimeCode.TIME_CODE_YEAR.timeCode -> {
                if (chartYearList.isEmpty()) {
                    binding.cTempWmy.visibility = View.GONE
                    binding.tempNoDataText.visibility = View.VISIBLE
                    return
                }
                binding.cTempWmy.visibility = View.VISIBLE
                binding.tempNoDataText.visibility = View.GONE
                initYearChart()
            }


        }
    }

    fun initChartNoData(timeType: String) {
        isNoDataMode = true
        binding.cTempWmy.visibility = View.VISIBLE
        binding.sPrivacyText.visibility = View.GONE
        binding.tvSpo2MinValue.text = "--"
        binding.sleepHourValue.text = "--"
        binding.tvPreBloodPressure.visibility = View.GONE
        setUnitVisiable(false)
        when (timeType) {
            TimeCode.TIME_CODE_DAY.timeCode -> binding.cTempWmy.setValue(
                null, null,
                timeType,
                0
            )

            TimeCode.TIME_CODE_WEEK.timeCode -> binding.cTempWmy.setValue(
                null, getWeekListStr(),
                timeType,
                0
            )

            TimeCode.TIME_CODE_MONTH.timeCode -> binding.cTempWmy.setValue(
                null, getMonthListStr(),
                timeType,
                0
            )

            TimeCode.TIME_CODE_YEAR.timeCode -> binding.cTempWmy.setValue(
                null,
                arrayListOf("01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"),
                timeType,
                0
            )
        }
        if (cardTimeType == TimeCode.TIME_CODE_YEAR.timeCode) {
            binding.tvSleepDate.text = "${Calendar.getInstance()[Calendar.YEAR]}年7月"
        }
        if (cardTimeType == TimeCode.TIME_CODE_MONTH.timeCode) {
            binding.tvSleepDate.text = "${Calendar.getInstance()[Calendar.MONTH] + 1}月16日"
        }
    }

    fun setUnitVisiable(sw: Boolean) {
        if (sw) {
            binding.sleepHourUnit.visibility = View.VISIBLE
            binding.tvSpo2MinUnit.visibility = View.VISIBLE
        } else {
            binding.sleepHourUnit.visibility = View.GONE
            binding.tvSpo2MinUnit.visibility = View.GONE
        }
    }

    private fun initDayChart() {
        binding.cTempWmy.setValue(
            change2ValueList(chartDayList, true), null,
            TimeCode.TIME_CODE_DAY.timeCode,
            pressureType(chartDayList)
        )
    }


    private fun initWeekChart() {
        binding.cTempWmy.setValue(
            change2ValueListAVG(chartWeekList, false),
            getWeekListStr(),
            TimeCode.TIME_CODE_WEEK.timeCode,
            pressureType(chartWeekList)
        )

    }

    private fun initMonthChart() {
        binding.cTempWmy.setValue(
            change2ValueListAVG(chartMonthList, false),
            getMonthListStr(),
            TimeCode.TIME_CODE_MONTH.timeCode,
            pressureType(chartMonthList)
        )
    }

    private fun initYearChart() {
        binding.cTempWmy.setValue(
            change2ValueListAVG(chartYearList, false),
            null,
            TimeCode.TIME_CODE_YEAR.timeCode,
            pressureType(chartYearList)
        )
    }

    private fun showExplainDialog() {
        showHmsDialog(R.layout.hms_dialog_tips, "血压说明", introString)
    }

    private var dialog: Dialog? = null

    /**
     * 显示自定义对话框
     */
    fun showHmsDialog(layoutId: Int, title: String, message: String?) {
        // 创建自定义视图
        val view = LayoutInflater.from(requireActivity()).inflate(layoutId, null)
        val contentView = view.findViewById<RelativeLayout>(R.id.dialog_content)
        var btnPositive = view.findViewById<Button>(R.id.positiveButton)
        var tvMessage = view.findViewById<TextView>(R.id.textView)
        var titleView = view.findViewById<TextView>(R.id.tv_tips_title_small)
        titleView.text = title
        tvMessage.movementMethod = ScrollingMovementMethod()

        if (message != null && !message.isNullOrBlank()) {
            tvMessage.setText(message)
        }
        // 创建并显示对话框
        dialog = ImmersiveDialog(
            requireContext(),
            R.style.MyDialogStyle
        )
        //dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)

        dialog!!.setContentView(view)

        // 设置按钮点击事件
        btnPositive.setOnClickListener {
            fragmentDataModel.isShowExplainDialog = false
            dialog?.dismiss()
        }
        dialog!!.setOnShowListener {
            HMSDialogUtils.doDialogAnimateEnter(dialog!!)
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    fragmentDataModel.isShowExplainDialog = false
                    dialog?.dismiss()
                }
                false
            }
        }
        dialog!!.show()
        fragmentDataModel.isShowExplainDialog = true
    }

    fun currentValueStr(max: Float?, min: Float?): String {
        if (max == null) {
            return "--/--"
        } else {
            return max!!.toInt().toString() + "/" + min!!.toInt().toString()
        }

    }


    override fun update(isEllipsized: Boolean, viewID: Int) {
        if (isEllipsized) {
            binding.healthRiskAll.cardAdviceMore.visibility = View.VISIBLE
            binding.healthRiskAll.cardAdviceMore.setOnClickListener { v ->
                HMSDialogUtils.showHMSNotiDialog(
                    requireContext(),
                    R.layout.hms_dialog_see_more,
                    "健康建议",
                    healthAdviceStr,
                    "知道了"
                ) { isPositive ->
                }
            }
        } else {
            binding.healthRiskAll.cardAdviceMore.visibility = View.GONE
        }
    }

    fun change2ValueList(
        oriList: ArrayList<BloodPressureItemDTO>,
        isDay: Boolean
    ): ArrayList<Pair<Int, Int>?> {
        var list = arrayListOf<Pair<Int, Int>?>()
        oriList.forEach {
            if (it.systolicPressureLast != null && it.diastolicPressureLast != null) {
                list.add(
                    Pair(
                        it.systolicPressureLast.toInt(),
                        it.diastolicPressureLast.toInt()
                    )
                )
            } else {
                list.add(null)
            }
        }
        return list
    }

    fun change2ValueListAVG(
        oriList: ArrayList<BloodPressureItemDTO>,
        isDay: Boolean
    ): ArrayList<Pair<Int, Int>?> {
        var list = arrayListOf<Pair<Int, Int>?>()
        oriList.forEach {
            if (it.systolicPressureAvg != null && it.diastolicPressureAvg != null) {
                list.add(
                    Pair(
                        it.systolicPressureAvg.toInt(),
                        it.diastolicPressureAvg.toInt()
                    )
                )
            } else {
                list.add(null)
            }
        }
        return list
    }

    fun getLastAdvice(advice: HealthBloodpressureSummaryDTO): HealthBloodpressureSummaryDTO? {
        for (i in advice.valueList.size - 1 downTo 0) {
            if (advice.valueList[i].sbpAvg != null && advice.valueList[i].sbpAvg != "0")
                return advice.valueList[i]
        }
        return null
    }

    private fun initPrivacyUI(timeCode: String) {
        setScrollEnable(binding.svContainer, false)
        setUnitVisiable(true)
        binding.sPrivacyText.visibility = View.VISIBLE
        binding.tempNoDataText.visibility = View.GONE
        binding.cTempWmy.visibility = View.GONE
        binding.tvSleepDate.text = getPrivacyModeDate(timeCode)
        binding.sleepHourValue.text = "***"
        binding.tvSpo2MinValue.text = "***"
        binding.tvC41Per.text = "***%"
        binding.tvC42Per.text = "***%"
        binding.tvC43Per.text = "***%"
        binding.tvC44Per.text = "***%"
        binding.tvC45Per.text = "***%"
        binding.tvC46Per.text = "***%"
        // 健康建议在私密模式下不展示
        binding.healthBloodpressureSummeryVo = null

        moveToTop(binding.tvPreBloodPressure)
        moveToTop(binding.sleepHourUnit)
        moveToTop(binding.tvSpo2MinUnit)
        binding.svContainer.visibility = View.VISIBLE
    }

    private fun applyPrivacyStyleChanged(isPrivacyMode: Boolean) {
        binding.llIntroContainer.apply {
            val params = layoutParams as ViewGroup.MarginLayoutParams
            params.bottomMargin = if (isPrivacyMode) 30f.dp.toInt() else 0f.dp.toInt()
            layoutParams = params
        }
        listOf(
            binding.tvC41Per,
            binding.tvC42Per,
            binding.tvC43Per,
            binding.tvC44Per,
            binding.tvC45Per,
            binding.tvC46Per
        ).forEach {
            it.setTypeface(it.typeface, if (isPrivacyMode) Typeface.BOLD else Typeface.NORMAL)
        }
    }

    fun showPerOrNot(cardData: BloodPressureCard2DTO) {
        //高血压占比是否显示
        //穷举布局
        if ((cardData.primaryProportion != "0") && (cardData.secondaryProportion == null || cardData.secondaryProportion == "0") && (cardData.tertiaryHighProportion == null || cardData.tertiaryHighProportion == "0")) {
            binding.loHighBpL1.visibility = View.VISIBLE
            binding.loBlLow.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL1.gravity = Gravity.CENTER_VERTICAL
            binding.loNormalPer.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL2.visibility = View.GONE
            binding.loHighBpL3.visibility = View.GONE
        } else if ((cardData.secondaryProportion != "0") && (cardData.primaryProportion == null || cardData.primaryProportion == "0") && (cardData.tertiaryHighProportion == null || cardData.tertiaryHighProportion == "0")) {
            binding.loNormalPer.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL2.visibility = View.VISIBLE
            binding.loBlLow.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL2.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL1.visibility = View.GONE
            binding.loHighBpL3.visibility = View.GONE
        } else if ((cardData.tertiaryHighProportion != "0") && (cardData.primaryProportion == null || cardData.primaryProportion == "0") && (cardData.secondaryProportion == null || cardData.secondaryProportion == "0")) {
            binding.loNormalPer.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL3.visibility = View.VISIBLE
            binding.loBlLow.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL3.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL2.visibility = View.GONE
            binding.loHighBpL1.visibility = View.GONE
        } else if ((cardData.primaryProportion != "0") && (cardData.secondaryProportion != "0") && (cardData.tertiaryHighProportion == null || cardData.tertiaryHighProportion == "0")) {
            binding.loNormalPer.gravity = Gravity.CENTER_VERTICAL
            binding.loBlLow.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL1.visibility = View.VISIBLE
            binding.loHighBpL1.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL2.visibility = View.VISIBLE
            binding.loHighBpL2.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL3.visibility = View.GONE
        } else if ((cardData.primaryProportion != "0") && (cardData.tertiaryHighProportion != "0") && (cardData.secondaryProportion == null || cardData.secondaryProportion == "0")) {
            binding.loNormalPer.gravity = Gravity.CENTER_VERTICAL
            binding.loBlLow.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL1.visibility = View.VISIBLE
            binding.loHighBpL1.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL3.visibility = View.VISIBLE
            binding.loHighBpL3.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL2.visibility = View.GONE
        } else if ((cardData.secondaryProportion != "0") && (cardData.tertiaryHighProportion != "0") && (cardData.primaryProportion == null || cardData.primaryProportion == "0")) {
            binding.loNormalPer.gravity = Gravity.CENTER_VERTICAL
            binding.loBlLow.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL2.visibility = View.VISIBLE
            binding.loHighBpL2.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL3.visibility = View.VISIBLE
            binding.loHighBpL3.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL1.visibility = View.GONE
        } else if ((cardData.secondaryProportion != "0") && (cardData.tertiaryHighProportion != "0") && (cardData.primaryProportion != "0")) {
            binding.loNormalPer.gravity = Gravity.CENTER_VERTICAL
            binding.loBlLow.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL1.visibility = View.VISIBLE
            binding.loHighBpL1.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL2.visibility = View.VISIBLE
            binding.loHighBpL2.gravity = Gravity.CENTER_VERTICAL
            binding.loHighBpL3.visibility = View.VISIBLE
            binding.loHighBpL3.gravity = Gravity.CENTER_VERTICAL
        } else {
            binding.loNormalPer.gravity = Gravity.BOTTOM
            binding.loBlLow.gravity = Gravity.START
            binding.loHighBpL1.visibility = View.GONE
            binding.loHighBpL2.visibility = View.GONE
            binding.loHighBpL3.visibility = View.GONE
        }
    }

    fun getFetchTime(time: String?): String {
        return time ?: ""
    }

    fun isDataListNotEmpty(dataList: ArrayList<BloodPressureItemDTO>): Boolean {
        dataList.forEach {
            if (it.systolicPressureAvg == null) return@forEach
            if (it.systolicPressureAvg != 0f) return true
        }
        return false
    }

    fun pressureType(dataList: ArrayList<BloodPressureItemDTO>?): Int {
        if (dataList == null) return 0
        dataList.forEach {
            if (it.systolicPressureAvg == null) return@forEach
            if (it.diastolicPressureAvg == null) return@forEach
            if (it.systolicPressureAvg > 200) return 1
            if (it.diastolicPressureAvg > 200) return 1
        }
        return 0
    }

    fun getDayFetchTime(): String {
        val calendar = Calendar.getInstance()
        val month = calendar[Calendar.MONTH] + 1 // 月份是从0开始的，需要加1
        val day = calendar[Calendar.DAY_OF_MONTH]
        return "${month}月${day}日"
    }

    override fun onPrivacyModeChange(provacyMode: Boolean) {
        if (isProvacyMode != provacyMode) {
            //当前模式与变化模式不同
            sendRequest(mUserId, cardTimeType!!)
            isProvacyMode = provacyMode
        }
    }

    fun initExplain() {
        val tips = binding.ivIntroTips
        tips.visibility = View.VISIBLE
//        tips.viewTreeObserver.addOnGlobalLayoutListener(object :ViewTreeObserver.OnGlobalLayoutListener{
//            override fun onGlobalLayout() {
//                tips.viewTreeObserver.removeGlobalOnLayoutListener { this }
//                val parentView=tips.parent as View
//                val rect = Rect()
//                tips.getHitRect(rect)
//                rect.top-=24
//                rect.bottom+=100
//                rect.left-=100
//                rect.right+=24
//                parentView.touchDelegate = TouchDelegate(rect, tips)
//            }
//        })
        viewAddOnGlobalLayoutListener(tips)
        this.introString = getString(R.string.description_bloodPressure)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        try {
            dialog?.dismiss()
            dialog = null
            viewRemoveOnGlobalLayoutListener(binding.ivIntroTips)
            binding.healthRiskAll.tvHealthAdviceContent.setCallback(null)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


}