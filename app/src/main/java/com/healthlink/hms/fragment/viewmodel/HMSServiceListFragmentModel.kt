package com.healthlink.hms.fragment.viewmodel

import com.healthlink.hms.server.data.dto.HealthTipsEntry

class HMSServiceListFragmentModel {
    // 健康Tips
    var healthTipsEntry: HealthTipsEntry? = null
    // 语音播报 ttsId与内容关联
    var playTTSIDAndMsgMap = mutableMapOf<String, String>()
    var mCurrentPlayTTSId: String? = null
    // 健康建议tips
    var tipsArray: ArrayList<HealthTipsEntry> = arrayListOf()
    // 健康建议tips 下标
    var tipsPlayIndex = 0
    var mBusy = false
}