package com.healthlink.hms.fragment.viewmodel

/**
 * 血压二级页面的DataModel
 */
import com.healthlink.hms.mvvm.model.BaseResponse
import com.healthlink.hms.server.data.dto.BloodPressureResponseDTO
import com.healthlink.hms.server.data.dto.HealthBloodpressureSummaryDTO
import com.healthlink.hms.server.data.dto.HealthTempSummaryDTO
import com.healthlink.hms.server.data.dto.charts.pressure.PressureSummaryDTO

class BloodPressureFragmentModel{

    /**
     * 页面滚动的Y值
     */
    var scollY : Int = 0

    /**
     * 健康数据
     */
    var healthData : BaseResponse<BloodPressureResponseDTO>? = null

    /**
     * 健康建议响应对象
     */
    var healthSummaryResponse : BaseResponse<HealthBloodpressureSummaryDTO>? = null

    var isShowExplainDialog : Boolean = false
}


