package com.healthlink.hms.fragment

import android.content.Context
import android.content.Intent
import android.graphics.Canvas
import android.os.Bundle
import android.os.Handler
import android.util.DisplayMetrics
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.EdgeEffect
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.EdgeEffectFactory
import com.drakeet.multitype.MultiTypeAdapter
import com.healthlink.hms.R
import com.healthlink.hms.activity.MainActivity
import com.healthlink.hms.activity.card.HMSCardBloodPressureDetailActivity
import com.healthlink.hms.activity.card.HMSCardEmotionalIndexDetailActivity
import com.healthlink.hms.activity.card.HMSCardHeartRateDetailActivity
import com.healthlink.hms.activity.card.HMSCardPressureDetailActivity
import com.healthlink.hms.activity.card.HMSCardSleepDetailActivity
import com.healthlink.hms.activity.card.HMSCardSpO2DetailActivity
import com.healthlink.hms.activity.card.HMSCardTempDetailActivity
import com.healthlink.hms.adapter.GridSpaceItemDecoration
import com.healthlink.hms.adapter.OnItemClickListener
import com.healthlink.hms.adapter.card.itemBinder.CardAFibBinder
import com.healthlink.hms.adapter.card.itemBinder.CardBloodOxygenBinder
import com.healthlink.hms.adapter.card.itemBinder.CardBloodPressureBinder
import com.healthlink.hms.adapter.card.itemBinder.CardBloodSugarBinder
import com.healthlink.hms.adapter.card.itemBinder.CardEmotionalIndexBinder
import com.healthlink.hms.adapter.card.itemBinder.CardHeartRateBinder
import com.healthlink.hms.adapter.card.itemBinder.CardMoreBinder
import com.healthlink.hms.adapter.card.itemBinder.CardPressureBinder
import com.healthlink.hms.adapter.card.itemBinder.CardSleepBinder
import com.healthlink.hms.adapter.card.itemBinder.CardTemperatureBinder
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.application.HmsApplication.Companion.showGlobalDialog
import com.healthlink.hms.ktExt.disableMultiTouch
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.mvvm.model.BaseResponse
import com.healthlink.hms.mvvm.model.BaseResponseCallback
import com.healthlink.hms.mvvm.model.BusinessRespCode
import com.healthlink.hms.mvvm.model.HealthInfoDTO
import com.healthlink.hms.mvvm.model.request.HealthInfoRequestParam
import com.healthlink.hms.mvvm.repository.MainRepository
import com.healthlink.hms.server.data.bean.BaseDTO
import com.healthlink.hms.server.data.bean.HomeCardDTO
import com.healthlink.hms.server.data.dto.ChartDataDTO
import com.healthlink.hms.server.data.dto.HWAuthStatusDTO
import com.healthlink.hms.server.data.dto.Pressure
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.HMSDialogUtils
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.NotificationUtil
import com.healthlink.hms.viewmodels.MainViewModel
import com.healthlink.hms.views.HMSCardHeaderView
import com.healthlink.hms.views.NoLoadFooter
import com.healthlink.hms.widget.HMSWidgetProvider
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import java.lang.ref.WeakReference
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date


/**
 * 健康卡片页面
 */
class HMSHealthDetailFragment : Fragment(){
    private val TAG = "HMSHealthDetailFragment"
    private lateinit var mainViewModel: MainViewModel
    private lateinit var smartRefreshLayout: SmartRefreshLayout
    private lateinit var recyclerView: RecyclerView
    private var exceptionCountMap: HashMap<String, Int> = HashMap<String, Int>()

    // multiType
    private lateinit var multiTypeAdapter: MultiTypeAdapter
    private lateinit var items: ArrayList<HomeCardDTO<BaseDTO>>
    private var authDialogListenter: OpenAuthorizedInterface? = null
    //上次刷新数据的时间戳
    private var lastTimestamp: Long = 0L

    private lateinit var cardHeartRateBinder: CardHeartRateBinder
    private lateinit var cardBloodOxygenBinder: CardBloodOxygenBinder
    private lateinit var cardPressureBinder: CardPressureBinder
    private lateinit var cardTemperatureBinder: CardTemperatureBinder
    private lateinit var cardBloodPressureBinder: CardBloodPressureBinder
    private lateinit var cardSleepBinder: CardSleepBinder
    private lateinit var cardEmotionalIndexBinder: CardEmotionalIndexBinder
    private lateinit var cardAFibBinder: CardAFibBinder
    //
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        Log.i(TAG, "onCreateView: ")
        val view = inflater.inflate(R.layout.fragment_center_container, container, false)
        mainViewModel = ViewModelProvider(requireActivity()).get(MainViewModel::class.java)
        initUI(view)
        initLiveDataObserver()
        // 监听是否停止下拉刷新
        mainViewModel.userRefreshFlag.observe(requireActivity()) {
            if (!it && smartRefreshLayout != null) {
                smartRefreshLayout.finishRefresh()
            }
        }
        return view
    }

    private fun initUI(view: View) {
        // 下拉刷新
        smartRefreshLayout = view.findViewById(R.id.refreshLayout)
        val weakContext = WeakReference(requireActivity())
        smartRefreshLayout.setRefreshHeader(HMSCardHeaderView(weakContext.get()!!));
        smartRefreshLayout.setOnRefreshListener {
            Log.i(TAG, "setOnRefreshListener: refresh")
            mainViewModel?.userRefreshFlag?.value = true
        }
        smartRefreshLayout.setRefreshFooter(NoLoadFooter(requireContext()))
        recyclerView = view.findViewById(R.id.group_list)
        val gridLayoutMgr =
            GridLayoutManager(requireActivity(), 2, GridLayoutManager.VERTICAL, false)
        recyclerView.layoutManager = gridLayoutMgr
        recyclerView.edgeEffectFactory = object : EdgeEffectFactory() {
            override fun createEdgeEffect( view: RecyclerView, direction: Int): EdgeEffect {
                return object : EdgeEffect(view.context) {
                    override fun onPull(deltaDistance: Float, displacement: Float) {
                        // Do nothing, disable the stretch effect
                    }

                    override fun onRelease() {
                        // Do nothing
                    }

                    override fun onAbsorb(velocity: Int) {
                        // Do nothing
                    }

                    override fun draw(canvas: Canvas?): Boolean {
                        // Do nothing
                        return super.draw(canvas)
                    }
                }
            }
        }
//        recyclerView.isMotionEventSplittingEnabled = false
        recyclerView.disableMultiTouch()
        // 获取窗口管理器
        val windowManager =
            requireActivity().getSystemService(Context.WINDOW_SERVICE) as WindowManager?
        // 获取显示指标
        val displayMetrics = DisplayMetrics()
        windowManager!!.defaultDisplay.getMetrics(displayMetrics)
        // 获取屏幕分辨率
        val screenWidth = displayMetrics.widthPixels
        val screenHeight = displayMetrics.heightPixels

        var columnSpacing = 40f.dp.toInt()
        var rowSpacing = 30f.dp.toInt()

        recyclerView.addItemDecoration(GridSpaceItemDecoration(2, rowSpacing, columnSpacing))

        // 初始化
        multiTypeAdapter = MultiTypeAdapter()

        cardHeartRateBinder =  CardHeartRateBinder {
                cardType: Int, cardDTO: HomeCardDTO<*> ->
                onItemClick(cardType, cardDTO)
        }
        cardBloodOxygenBinder =  CardBloodOxygenBinder {
                cardType: Int, cardDTO: HomeCardDTO<*> ->
                onItemClick(cardType, cardDTO)
        }
        cardPressureBinder =  CardPressureBinder {
                cardType: Int, cardDTO: HomeCardDTO<*> ->
                onItemClick(cardType, cardDTO)
        }
        cardTemperatureBinder =  CardTemperatureBinder{
                cardType: Int, cardDTO: HomeCardDTO<*> ->
                onItemClick(cardType, cardDTO)
        }
        cardBloodPressureBinder =  CardBloodPressureBinder {
                cardType: Int, cardDTO: HomeCardDTO<*> ->
                onItemClick(cardType, cardDTO)
        }
        cardSleepBinder =  CardSleepBinder {
                cardType: Int, cardDTO: HomeCardDTO<*> ->
                onItemClick(cardType, cardDTO)
        }

        cardEmotionalIndexBinder = CardEmotionalIndexBinder {
                cardType: Int, cardDTO: HomeCardDTO<*> ->
            onItemClick(cardType, cardDTO)
        }

        cardAFibBinder = CardAFibBinder {
                cardType: Int, cardDTO: HomeCardDTO<*> ->
            onItemClick(cardType, cardDTO)
        }

        multiTypeAdapter.register(HomeCardDTO::class).to(
            cardHeartRateBinder,
            cardBloodOxygenBinder,
            cardPressureBinder,
            cardTemperatureBinder,
            cardBloodPressureBinder,
            cardSleepBinder,
            cardEmotionalIndexBinder,
            cardAFibBinder
        ).withKotlinClassLinker { _, data ->
            when (data.cardType) {
                1 -> CardHeartRateBinder::class
                2 -> CardBloodOxygenBinder::class
                3 -> CardPressureBinder::class
                4 -> CardTemperatureBinder::class
                5 -> CardBloodPressureBinder::class
                6 -> CardSleepBinder::class
                7 -> CardEmotionalIndexBinder::class
                8 -> CardAFibBinder::class
                else -> CardSleepBinder::class
            }
        }

        // 赋值 adapter
        recyclerView.adapter = multiTypeAdapter
        // 初始化一个空的数据集
        items = arrayListOf()
        // 设置默认的数据集
        addDefaultDataToAdapter()
    }

    override fun onResume() {
        super.onResume()
        refreshData()

    }

    private fun refreshData() {
        // 私密模式下或者未登录模式下，首页不会进行刷新数据
        // 设置下拉刷新 (私密模式和未登录模式不能刷新)
        if (HmsApplication.isPrivacyModeEnabled() || MMKVUtil.isVisitorMode()) {
            smartRefreshLayout.setEnableRefresh(false)
            // 给adapter设置默认数据
            showDefaultUI()
            return
        } else if(!HmsApplication.isNetworkConn()){
            showDefaultUI()
            // 无网络模式下允许下拉刷新
            smartRefreshLayout?.setEnableRefresh(true)
            return
        }else {
            Log.i(TAG, "refreshData privacyUi")
            privacyUi()
            smartRefreshLayout.setEnableRefresh(true)
        }

        // 设置自动下拉刷新
        if (!MMKVUtil.isVisitorMode()) {
            // 进入自动界面下拉刷新 自动下拉刷新的前提需要有RefreshHeader
            if (mainViewModel.isAutoRefresh.value != true) {
                smartRefreshLayout?.autoRefresh()
                mainViewModel.setAutoRefresh()
                Log.i(TAG, "refreshData setOnRefreshListener autoRefresh")
            }
        }
        multiTypeAdapter.items = items

    }

    private fun privacyUi() {
        if (!MMKVUtil.getHeartRateAuthority()){
            cardHeartRateBinder.privacyUi()
        }
        if (!MMKVUtil.getBloodOxygenAuthority()){
            cardBloodOxygenBinder.privacyUi()
        }
        if (!MMKVUtil.getStressAuthority()){
            cardPressureBinder.privacyUi()
        }
        if (!MMKVUtil.getTemperatureAuthority()){
            cardTemperatureBinder.privacyUi()
        }
        if (!MMKVUtil.getBloodPressureAuthority()){
            cardBloodPressureBinder.privacyUi()
        }
        if (!MMKVUtil.getSleepAuthority()){
            cardSleepBinder.privacyUi()
        }
    }

    /**
     * 初始化数据
     */
    private fun initLiveDataObserver() {
        // 卡片最近健康数据
        initNewestHealthData()
        // 卡片图表数据集
        initCardDetailDataObserver()
    }

    private fun initNewestHealthData() {
        mainViewModel.homeNewHealthInfo.observe(viewLifecycleOwner) { baseResp: BaseResponse<HealthInfoDTO> ->
            if(HmsApplication.isPrivacyModeEnabled()){
                refreshData()
                return@observe
            }  else {
                smartRefreshLayout.setEnableRefresh(true)
            } //如果当前处于私密模式  数据更新动作取消
            Log.i(TAG, "initNewestHealthData ${mainViewModel.homeNewHealthInfo} $this")
            if (baseResp.code == "0") {
                Log.i(TAG, "成功获取到最新加载健康数据...")
                val mHealthInfo = baseResp.data
                // 处理数据构建卡片 HomeCardDTO 数组
                if (mHealthInfo != null) {
                    items = sortCardList(mHealthInfo)
                } else {
                    addDefaultDataToAdapter()
                    Log.i(TAG, "mHealthInfo is null")
                }

                // 获取到最新数据后，加载首页卡片图表数据
                if (isCanRefresh(1)){
                    sendloadChartDataReq()
                }
            }
            // 如果取消了授权
            else if(baseResp.code == BusinessRespCode.USER_LOGIN_TIME_OUT_OR_CANCEL_AUTH) {
                HmsApplication.showGlobalDialog(
                    "",
                    requireActivity()!!.getString(R.string.noti_huawei_oauth_changed_or_timeout),
                    true
                )
            }
            else {
                Log.i(TAG, "获取到最新加载健康数据失败...")
                // 无网络使用默认数据
                showDefaultUI()
            }
            mainViewModel?.setUserRefreshFlag(false)
        }
    }

    /**
     * 默认无数据的UI，适用无网络、未登录模式
     */
    fun showDefaultUI(){
        addDefaultDataToAdapter()
        multiTypeAdapter.items = items
        multiTypeAdapter.notifyDataSetChanged()
    }

    private fun isCanRefresh(time: Int): Boolean {
        //获取当前时间
        val currentTimeMillis = System.currentTimeMillis()
        // 计算时间差
        val timeDifferenceMillis = currentTimeMillis - lastTimestamp
        // 转换为秒、分钟或小时等
        val timeDifferenceSeconds = timeDifferenceMillis / 1000

        return timeDifferenceSeconds >= time
    }


    /**
     * 获取到最新数据后，加载首页卡片图表数据
     */
    private fun sendloadChartDataReq() {
        val reqParam = HealthInfoRequestParam().apply {
            extenalId = "extenalId"
            channelId = "channelId"
            userId = MMKVUtil.getUserId()
        }
        Log.i(TAG, "getChartDataInfos reqParam: $reqParam")
        // 获取图表数据接口
        mainViewModel.getChartDataInfos(reqParam)
        lastTimestamp = System.currentTimeMillis()
    }

    private fun initCardDetailDataObserver() {
        mainViewModel.chartDetailInfos.observe(viewLifecycleOwner) { baseRes: BaseResponse<ChartDataDTO> ->
            if (baseRes.code == "0") {
                Log.i(TAG, "成功获取到卡片详情数据...")
                val chartDataDTO = baseRes.data as ChartDataDTO
                if (items.size > 0) {
                    items.forEach() { outer ->
                        when (outer.cardType) {
                            1 -> chartDataDTO.heartrate?.let { outer.dataList = it }
//                            2 -> chartDataDTO.sp2?.let { outer.dataList = it }
                            3 -> chartDataDTO.stress?.let { outer.dataList = it }
                        }
                    }
                    // 对items根据data和dataList进行排序，将有data和dataList的放在前面
                    val sortedItems = items.sortedWith(compareByDescending<HomeCardDTO<BaseDTO>> {
//                        it.data != null && it.dataList != null
                        it.data != null

                    })
                    items = ArrayList(sortedItems)
                    // 心情指数
                    items.add(HomeCardDTO<BaseDTO>().apply {
                        name = "心情指数"
                        cardType = 7
                        data = null
                    })

                    // 房颤
                    items.add(HomeCardDTO<BaseDTO>().apply {
                        name = "房颤"
                        cardType = 8
                        data = null
                    })

                    multiTypeAdapter.items = items
                    multiTypeAdapter.notifyDataSetChanged()
                    Log.i(TAG, "更新Grid组件...")
                }
            }
            // 如果取消了授权
            else if(baseRes.code == BusinessRespCode.USER_LOGIN_TIME_OUT_OR_CANCEL_AUTH) {
                showGlobalDialog(
                    "",
                    requireActivity()!!.getString(R.string.noti_huawei_oauth_changed_or_timeout),
                    true
                )
            }
            mainViewModel.setUserRefreshFlag(false)
        }
    }

    /**
     * 默认卡片状态（无数据状态）
     */
    private fun addDefaultDataToAdapter() {

        if (items.size > 0) {
            items.clear()
        }

        // 默认无数据卡片
        items.add(HomeCardDTO<BaseDTO>().apply {
            name = "心率"
            cardType = 1
            data = null
            dataList = null
        })

        items.add(HomeCardDTO<BaseDTO>().apply {
            name = "睡眠"
            cardType = 6
            data = null
        })

        items.add(HomeCardDTO<BaseDTO>().apply {
            name = "血氧"
            cardType = 2
            data = null
        })

        items.add(HomeCardDTO<BaseDTO>().apply {
            name = "压力"
            cardType = 3
            data = null
        })

        items.add(HomeCardDTO<BaseDTO>().apply {
            name = "体温"
            cardType = 4
            data = null
        })

        items.add(HomeCardDTO<BaseDTO>().apply {
            name = "血压"
            cardType = 5
            data = null
        })

        items.add(HomeCardDTO<BaseDTO>().apply {
            name = "心情指数"
            cardType = 7
            data = null
        })

        items.add(HomeCardDTO<BaseDTO>().apply {
            name = "房颤"
            cardType = 8
            data = null
        })
    }

    val debounceDelay: Long = 1000 // 防抖延迟时间（毫秒）
    var lastClickTime: Long = 0

    fun onItemClick(position: Int, homeCardDTO: HomeCardDTO<*>) {
        Log.i(TAG," -------onItemClick-$position  $homeCardDTO")
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastClickTime > debounceDelay) {
            lastClickTime = currentTime
            // 游客状态
            if (MMKVUtil.isVisitorMode()) {
                Log.i(TAG, "游客状态")
                // 私密模式 直接返回
                if (HmsApplication.isPrivacyModeEnabled()) {
                    Log.i(TAG, "私密模式 直接返回")
                    return
                }

                // 非私密模式 弹框提示登录。登陆前先判断是否同意了隐私协议
                if(MMKVUtil.getPrivacyPolicy()) {
                    HmsApplication.showLoginDialog()
                }else{
                    // 如果没有同意隐私协议，先弹出隐私协议同意对话框
                    (requireActivity() as MainActivity).doPrivacyDialog()
                }
                return
            }

            val userId = MMKVUtil.getUserId()
            if (userId != null) {

                if (HmsApplication.isPrivacyModeEnabled()) {
                    //继续处理
                    doEnterDetailPage(position, homeCardDTO)
                    Log.i(TAG, "isPrivacyModeEnabled 直接返回")
                    return
                }

                mainViewModel.networkLoadingFinished.value = false
                mainViewModel.networkLoadingFinishedType.value = "0"
                // 注册网络请求超过一定时间时显示的Loading
                showLoadingDialog()
                //请求华为授权状态
                MainRepository().getAuthStatus(
                    userId,
                    object : BaseResponseCallback<HWAuthStatusDTO> {
                        override fun onSuccess(response: BaseResponse<HWAuthStatusDTO>) {
                            mainViewModel.networkLoadingFinished.value = true
                            try {
                                var hwAuthStatusDTO = response.data
                                Log.i(TAG, "--hwAuthStatusDTO is $hwAuthStatusDTO ${hwAuthStatusDTO?.code} ${response.code}")
                                if (hwAuthStatusDTO != null && hwAuthStatusDTO.code != 1) {
                                    // 权限错误，关闭了华为运动健康服务
                                    HmsApplication.showGlobalDialog(
                                        "",
                                        requireActivity().getString(R.string.noti_open_data_auth),
                                        false
                                    )
                                }
                                // 如果取消了授权
                                else if(response.code == BusinessRespCode.USER_LOGIN_TIME_OUT_OR_CANCEL_AUTH) {
                                    showGlobalDialog(
                                        "",
                                        context!!.getString(R.string.noti_huawei_oauth_changed_or_timeout),
                                        true
                                    )
                                }else{
                                    // 如果是手动取消网络，不进入二级页面
                                    if((mainViewModel.networkLoadingFinishedType.value == "1")) {
                                        Log.i(TAG, "手动取消网络，不进入二级页面")
                                        return
                                    }
                                    //继续处理
                                    doEnterDetailPage(position, homeCardDTO)
                                }
                            } catch (ex: Exception) {
                                Log.i(TAG, "判断权限出错，错误信息：${ex.message}")
                                // 如果是手动取消网络，不进入二级页面
                                if((mainViewModel.networkLoadingFinishedType.value == "1")) {
                                    Log.i(TAG, "手动取消网络，不进入二级页面")
                                    return
                                }
                                //继续处理
                                doEnterDetailPage(position, homeCardDTO)
                            }
                        }

                        override fun onFailed(response: BaseResponse<HWAuthStatusDTO>) {
                            // 如果是手动取消网络，不进入二级页面
                            if((mainViewModel.networkLoadingFinishedType.value == "1")) {
                                Log.i(TAG, "手动取消网络，不进入二级页面")
                                return
                            }
                            //继续处理
                            doEnterDetailPage(position, homeCardDTO)
                            mainViewModel.networkLoadingFinished.value = true
                        }

                    })
            } else {
                Log.i(TAG, "用户未登录")
            }
        } else {
            Log.i(TAG, "防抖")
        }
    }

    private fun showLoadingDialog() {
        if (requireActivity() is MainActivity) {
            (requireActivity() as MainActivity).scheduleShowLoadingDialog()
        }
    }

    private fun closeLoadingDialog() {
        if (requireActivity() is MainActivity) {
            (requireActivity() as MainActivity).dismissLoadingDataDialog()
        }
    }

    fun doEnterDetailPage(position: Int, homeCardDTO: HomeCardDTO<*>){
            // 用户状态
//        if (HmsApplication.isPrivacyModeEnabled() ) return
            val title = homeCardDTO.name
            when (val cardType = homeCardDTO.cardType) {
                1 -> {
                    // 心率
                    //增加埋点
                    DataTrackUtil.dtClick("Health_Homepage_Heartrate_Click", DataTrackUtil.userIDMap())
                    if (MMKVUtil.getHeartRateAuthority() || HmsApplication.isPrivacyModeEnabled()) {
                        val intent =
                            Intent(requireActivity(), HMSCardHeartRateDetailActivity::class.java)
                        intent.putExtra("title", title)
                        intent.putExtra("cardType", cardType)
                        requireActivity().startActivity(intent)
                        requireActivity().overridePendingTransition(
                            R.anim.activity_enter_slide_in_right,
                            R.anim.activity_enter_slide_out_left
                        )
                    } else {
                        showUnauthorizedDialog("心率")
                    }
                }

                6 -> {
                    // 睡眠
                    //增加埋点
                    DataTrackUtil.dtClick("Health_Homepage_Sleep_Click", DataTrackUtil.userIDMap())
                    if (MMKVUtil.getSleepAuthority()|| HmsApplication.isPrivacyModeEnabled()) {
                        val intent =
                            Intent(requireActivity(), HMSCardSleepDetailActivity::class.java)
                        intent.putExtra("title", title)
                        intent.putExtra("cardType", cardType)
                        requireActivity().startActivity(intent)
                        requireActivity().overridePendingTransition(
                            R.anim.activity_enter_slide_in_right,
                            R.anim.activity_enter_slide_out_left
                        )
                    } else {
                        showUnauthorizedDialog("睡眠")
                    }
                }

                3 -> {
                    // 压力
                    //增加埋点
                    DataTrackUtil.dtClick("Health_Homepage_Stress_Click", DataTrackUtil.userIDMap())
                    if (MMKVUtil.getStressAuthority()|| HmsApplication.isPrivacyModeEnabled()) {
                        val intent =
                            Intent(requireActivity(), HMSCardPressureDetailActivity::class.java)
                        intent.putExtra("title", title)
                        intent.putExtra("cardType", cardType)
                        requireActivity().startActivity(intent)
                        requireActivity().overridePendingTransition(
                            R.anim.activity_enter_slide_in_right,
                            R.anim.activity_enter_slide_out_left
                        )
                    } else {
                        showUnauthorizedDialog("压力")
                    }
                }

                2 -> {
                    // 血氧
                    //增加埋点
                    DataTrackUtil.dtClick("Health_Homepage_Bloodoxygen_Click", DataTrackUtil.userIDMap())
                    if (MMKVUtil.getBloodOxygenAuthority()|| HmsApplication.isPrivacyModeEnabled()) {
                        val intent =
                            Intent(requireActivity(), HMSCardSpO2DetailActivity::class.java)
                        intent.putExtra("title", title)
                        intent.putExtra("cardType", cardType)
                        requireActivity().startActivity(intent)
                        requireActivity().overridePendingTransition(
                            R.anim.activity_enter_slide_in_right,
                            R.anim.activity_enter_slide_out_left
                        )
                    } else {
                        showUnauthorizedDialog("血氧")
                    }
                }

                4 -> {
                    // 体温
                    //增加埋点
                    DataTrackUtil.dtClick("Health_Homepage_Bodytemperature_Click", DataTrackUtil.userIDMap())
                    if (MMKVUtil.getTemperatureAuthority()|| HmsApplication.isPrivacyModeEnabled()) {
                        val intent =
                            Intent(requireActivity(), HMSCardTempDetailActivity::class.java)
                        intent.putExtra("title", title)
                        intent.putExtra("cardType", cardType)
                        requireActivity().startActivity(intent)
                        requireActivity().overridePendingTransition(
                            R.anim.activity_enter_slide_in_right,
                            R.anim.activity_enter_slide_out_left
                        )
                    } else {
                        showUnauthorizedDialog("体温")
                    }
                }

                5 -> {
                    // 血压
                    //增加埋点
                    DataTrackUtil.dtClick("Health_Homepage_Bloodpressure_Click", DataTrackUtil.userIDMap())
                    if (MMKVUtil.getBloodPressureAuthority()|| HmsApplication.isPrivacyModeEnabled()) {
                        val intent =
                            Intent(
                                requireActivity(),
                                HMSCardBloodPressureDetailActivity::class.java
                            )
                        intent.putExtra("title", title)
                        intent.putExtra("cardType", cardType)
                        requireActivity().startActivity(intent)
                        requireActivity().overridePendingTransition(
                            R.anim.activity_enter_slide_in_right,
                            R.anim.activity_enter_slide_out_left
                        )
                    } else {
                        showUnauthorizedDialog("血压")
                    }
                }

                7 -> {
                    // 心情指数
                    val intent =
                        Intent(
                            requireActivity(),
                            HMSCardEmotionalIndexDetailActivity::class.java
                        )
                    intent.putExtra("title", title)
                    intent.putExtra("cardType", cardType)
                    requireActivity().startActivity(intent)
                    requireActivity().overridePendingTransition(
                        R.anim.activity_enter_slide_in_right,
                        R.anim.activity_enter_slide_out_left
                    )
                }

                8 -> {
                }
            }
        }


    private fun showUnauthorizedDialog(item: String) {
        Log.i(TAG, "showUnauthorizedDialog $item")
        val content =
            "${resources.getString(R.string.dialog_unauthorized_per)}${item}${resources.getString(R.string.dialog_unauthorized_behind)}"
        HMSDialogUtils.showHMSDialog(
            requireContext(),
            R.layout.hms_dialog_confirm_auto_align,
            content,
            "去授权",
            "取消"
        ) { isPositive ->
            if (isPositive) {
                val activity = requireActivity()
                if (authDialogListenter != null) {
                    authDialogListenter!!.openAuthorizedFragment()
                } else if(activity is MainActivity) {
                    try {
                        activity.openAuthorizedFragment()
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
        }
    }

    open fun setCallback(callback: OpenAuthorizedInterface?) {
        this.authDialogListenter = callback
    }

    //给查看更多是否显示标记位
    open interface OpenAuthorizedInterface {
        fun openAuthorizedFragment();
    }
    /** 时效排序
    private fun sortCardList(resData: HealthInfoDTO): ArrayList<HomeCardDTO<BaseDTO>> {
        var resultList: ArrayList<HomeCardDTO<BaseDTO>> = arrayListOf()
        var effectiveList: ArrayList<Pair<String, String>> = arrayListOf() //有效数据  pair<name,time>
        var invalidList: ArrayList<Pair<String, String>> = arrayListOf()  //无效数据
        var noDataList: ArrayList<Pair<String, String>> = arrayListOf()  //无数据
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        val heartRate = resData.heartRate
        val sleep = resData.sleep
        val bloodOxygen = resData.bloodOxygen
        val pressure = resData.pressure
        val temperature = resData.temperature
        val bloodPressure = resData.bloodPressure
        val cardMap = mapOf(
            "heartRate" to HomeCardDTO<BaseDTO>().apply {
                name = "心率"
                cardType = 1
                data = heartRate
            },
            "sleep" to HomeCardDTO<BaseDTO>().apply {
                name = "睡眠"
                cardType = 6
                data = sleep
            },
            "bloodOxygen" to HomeCardDTO<BaseDTO>().apply {
                name = "血氧"
                cardType = 2
                data = bloodOxygen
            },
            "pressure" to HomeCardDTO<BaseDTO>().apply {
                name = "压力"
                cardType = 3
                data = pressure
            },
            "temperature" to HomeCardDTO<BaseDTO>().apply {
                name = "体温"
                cardType = 4
                data = temperature
            },
            "bloodPressure" to HomeCardDTO<BaseDTO>().apply {
                name = "血压"
                cardType = 5
                data = bloodPressure
            }
        )
        if (heartRate == null) {
            noDataList.add(Pair("heartRate", ""))
        } else if (checkEffective(heartRate.createTime)) {
            effectiveList.add(Pair("heartRate", heartRate.createTime ?: ""))
        } else {
            invalidList.add(Pair("heartRate", heartRate.createTime ?: ""))
        }
        if (sleep == null) {
            noDataList.add(Pair("sleep", ""))
        } else if (checkEffective(sleep.createTime)) {
            effectiveList.add(Pair("sleep", sleep.createTime ?: ""))
        } else {
            invalidList.add(Pair("sleep", sleep.createTime ?: ""))
        }
        if (bloodOxygen == null) {
            noDataList.add(Pair("bloodOxygen", ""))
        } else if (checkEffective(bloodOxygen.createTime)) {
            effectiveList.add(Pair("bloodOxygen", bloodOxygen.createTime ?: ""))
        } else {
            invalidList.add(Pair("bloodOxygen", bloodOxygen.createTime ?: ""))
        }
        if (pressure == null) {
            noDataList.add(Pair("pressure", ""))
        } else if (checkEffective(pressure.createTime)) {
            effectiveList.add(Pair("pressure", pressure.createTime ?: ""))
        } else {
            invalidList.add(Pair("pressure", pressure.createTime ?: ""))
        }
        if (temperature == null) {
            noDataList.add(Pair("temperature", ""))
        } else if (checkEffective(temperature.createTime)) {
            effectiveList.add(Pair("temperature", temperature.createTime ?: ""))
        } else {
            invalidList.add(Pair("temperature", temperature.createTime ?: ""))
        }
        if (bloodPressure == null) {
            noDataList.add(Pair("bloodPressure", ""))
        } else if (checkEffective(bloodPressure.createTime)) {
            effectiveList.add(Pair("bloodPressure", bloodPressure.createTime ?: ""))
        } else {
            invalidList.add(Pair("bloodPressure", bloodPressure.createTime ?: ""))
        }
        try {
            effectiveList.sortByDescending {
                LocalDateTime.parse(it.second, formatter)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        try {
            invalidList.sortByDescending {
                LocalDateTime.parse(it.second, formatter)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        effectiveList.forEach {
            cardMap[it.first]?.let { card -> resultList.add(card) }
        }
        invalidList.forEach {
            cardMap[it.first]?.let { card -> resultList.add(card) }
        }
        noDataList.forEach {
            cardMap[it.first]?.let { card -> resultList.add(card) }
        }
        return resultList
    }
    **/

    /** 固定优先级排序**/
    fun sortCardList(resData: HealthInfoDTO): ArrayList<HomeCardDTO<BaseDTO>> {
        var resultList: ArrayList<HomeCardDTO<BaseDTO>> = arrayListOf()
        var effectiveList: ArrayList<String> = arrayListOf() //有效数据
        var invalidList: ArrayList<String> = arrayListOf()  //无效数据
        var noDataList: ArrayList<String> = arrayListOf()  //无数据
        val heartRate = resData.heartRate
        val sleep = resData.sleep
        val bloodOxygen = resData.bloodOxygen
        val pressure = resData.pressure
        val temperature = resData.temperature
        val bloodPressure = resData.bloodPressure
        val cardMap = mapOf(
            "heartRate" to HomeCardDTO<BaseDTO>().apply {
                name = "心率"
                cardType = 1
                data = heartRate
            },
            "sleep" to HomeCardDTO<BaseDTO>().apply {
                name = "睡眠"
                cardType = 6
                data = sleep
            },
            "bloodOxygen" to HomeCardDTO<BaseDTO>().apply {
                name = "血氧"
                cardType = 2
                data = bloodOxygen
            },
            "pressure" to HomeCardDTO<BaseDTO>().apply {
                name = "压力"
                cardType = 3
                data = pressure
            },
            "temperature" to HomeCardDTO<BaseDTO>().apply {
                name = "体温"
                cardType = 4
                data = temperature
            },
            "bloodPressure" to HomeCardDTO<BaseDTO>().apply {
                name = "血压"
                cardType = 5
                data = bloodPressure
            }
        )
        /**
         * 根据优先级和数据有效性做排序
         * 有三个容器:
         * effectiveList：有效数据容器（有数据并且createTime在24h内） >
         * invalidList：无效数据容器（有数据但createTime超出了24h）>
         * noDataList：无数据容器(数据直接为null或者数据不为null但createTime是null)
         */
        if (heartRate == null) {
            noDataList.add("heartRate")
        } else if (null == heartRate.createTime) {
            noDataList.add("heartRate")
        } else if (checkEffective(heartRate.createTime)) {
            effectiveList.add("heartRate")
        } else {
            invalidList.add("heartRate")
        }
        if (sleep == null) {
            noDataList.add("sleep")
        } else if (null == sleep.createTime) {
            noDataList.add("sleep")
        } else if (checkEffective(sleep.createTime)) {
            effectiveList.add("sleep")
        } else {
            invalidList.add("sleep")
        }
        if (bloodOxygen == null) {
            noDataList.add("bloodOxygen")
        } else if (null == bloodOxygen.createTime) {
            noDataList.add("bloodOxygen")
        } else if (checkEffective(bloodOxygen.createTime)) {
            effectiveList.add("bloodOxygen")
        } else {
            invalidList.add("bloodOxygen")
        }
        if (pressure == null) {
            noDataList.add("pressure")
        } else if (null == pressure.createTime) {
            noDataList.add("pressure")
        }  else if (checkEffective(pressure.createTime)) {
            effectiveList.add("pressure")
        } else {
            invalidList.add("pressure")
        }
        if (temperature == null) {
            noDataList.add("temperature")
        } else if (null == temperature.createTime) {
            noDataList.add("temperature")
        }  else if (checkEffective(temperature.createTime)) {
            effectiveList.add("temperature")
        } else {
            invalidList.add("temperature")
        }
        if (bloodPressure == null) {
            noDataList.add("bloodPressure")
        } else if (null == bloodPressure.createTime) {
            noDataList.add("bloodPressure")
        }  else if (checkEffective(bloodPressure.createTime)) {
            effectiveList.add("bloodPressure")
        } else {
            invalidList.add("bloodPressure")
        }
        effectiveList.forEach {
            cardMap.get(it)?.let { card -> resultList.add(card) }
        }
        invalidList.forEach {
            cardMap.get(it)?.let { card -> resultList.add(card) }
        }
        noDataList.forEach {
            cardMap.get(it)?.let { card -> resultList.add(card) }
        }
        return resultList
    }

    fun checkEffective(time: String?): Boolean {
        if (time == null)
            return false
        try {
            val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
            val time = sdf.parse(time)
            val calendar: Calendar = Calendar.getInstance()
            calendar.add(Calendar.DATE, 0) // 将当前时间回退一天
            val now: Date = calendar.getTime()
            val isBefore24Hours =(now.time - time.time) < 24 * 60 * 60 * 1000
            if (isBefore24Hours) {
                return true
            } else {
                return false
            }
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        return false
    }

}
