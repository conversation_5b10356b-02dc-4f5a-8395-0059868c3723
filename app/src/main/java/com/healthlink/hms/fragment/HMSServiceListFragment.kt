package com.healthlink.hms.fragment

import android.appwidget.AppWidgetManager
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.drawable.AnimationDrawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.speech.tts.TextToSpeech
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.annotation.OptIn
import androidx.appcompat.content.res.AppCompatResources
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.media3.common.util.UnstableApi
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ClickUtils
import com.blankj.utilcode.util.ThreadUtils.runOnUiThread
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.healthlink.hms.Contants.VehicleServiceModeType
import com.healthlink.hms.HmsSettings
import com.healthlink.hms.R
import com.healthlink.hms.activity.AromatherapyActivity
import com.healthlink.hms.activity.HMSPersonalActivity
import com.healthlink.hms.activity.HealthReportActivity
import com.healthlink.hms.activity.MainActivity
import com.healthlink.hms.activity.PlaylistActivity
import com.healthlink.hms.business.car_seat.ui.spinecare.SpineCareActivity
import com.healthlink.hms.adapter.ModeAdapter
import com.healthlink.hms.adapter.ModeItemClick
import com.healthlink.hms.adapter.ModeLeftMarginItemDecoration
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.base.Constants
import com.healthlink.hms.biz.HealthDataProcessor
import com.healthlink.hms.business.doctorcall.DoctorCallManager
import com.healthlink.hms.business.medai.HealthSceneViewModel
import com.healthlink.hms.databinding.FragmentServiceListBinding
import com.healthlink.hms.fragment.viewmodel.HMSServiceListFragmentModel
import com.healthlink.hms.ktExt.addClickScale
import com.healthlink.hms.mvvm.model.BaseResponse
import com.healthlink.hms.mvvm.model.BaseResponseCallback
import com.healthlink.hms.mvvm.model.BusinessRespCode
import com.healthlink.hms.mvvm.repository.MainRepository
import com.healthlink.hms.reciever.HMSAction
import com.healthlink.hms.sdks.gwmadapter.GwmAdapterManagerKotCoroutines
import com.healthlink.hms.server.data.dto.HWAuthStatusDTO
import com.healthlink.hms.server.data.dto.HealthTipsDTO
import com.healthlink.hms.server.data.dto.HealthTipsEntry
import com.healthlink.hms.server.data.dto.VehicleServiceModeDTO
import com.healthlink.hms.utils.AuthorizationUtil
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.HMSDialogUtils
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.OnTTSStatusListener
import com.healthlink.hms.utils.TTSHelper
import com.healthlink.hms.utils.ToastUtil
import com.healthlink.hms.viewmodels.MainViewModel
import com.healthlink.hms.widget.HMSWidgetProvider
import me.everything.android.ui.overscroll.HorizontalOverScrollBounceEffectDecorator
import me.everything.android.ui.overscroll.OverScrollBounceEffectDecoratorBase.DEFAULT_DECELERATE_FACTOR
import me.everything.android.ui.overscroll.OverScrollBounceEffectDecoratorBase.DEFAULT_TOUCH_DRAG_MOVE_RATIO_BCK
import me.everything.android.ui.overscroll.adapters.RecyclerViewOverScrollDecorAdapter
import java.lang.ref.WeakReference
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale


/**
 * 包含电话医生、紧急救援、账号相关操作
 */
class HMSServiceListFragment : Fragment(), ModeItemClick{
    companion object {
        const val WARN_OPERATOR_CHECK = 1
        const val COOL_OPERATOR_CHECK = 2
        const val REST_OPERATOR_CHECK = 3
        const val RELAX_OPERATOR_CHECK = 4
    }
    private var TAG = "HMSServiceListFragment"
    lateinit var binding: FragmentServiceListBinding
    private var provider: HMSWidgetProvider? = null
    private var modes = mutableListOf(
        VehicleServiceModeDTO(
            VehicleServiceModeType.MED_AROMATHERAPY.modeName,
            bgIconName = "mode_med_aromatherapy_bg_normal_selector",
//            bgIconOpenName = "mode_warn_bg_open_selector"
        ),
        VehicleServiceModeDTO(
            VehicleServiceModeType.AI_CHAIR.modeName,
            bgIconName = "mode_ai_chair_bg_normal_selector",
//            bgIconOpenName = "mode_cool_bg_open_selector"
        ),
        VehicleServiceModeDTO(
            VehicleServiceModeType.WARN.modeName,
            bgIconName = "mode_warn_bg_normal_selector",
//            bgIconOpenName = "mode_warn_bg_open_selector"
        ),
        VehicleServiceModeDTO(
            VehicleServiceModeType.COOL.modeName,
            bgIconName = "mode_cool_bg_normal_selector",
//            bgIconOpenName = "mode_cool_bg_open_selector"
        ),
        VehicleServiceModeDTO(
            VehicleServiceModeType.REST.modeName,
            bgIconName = "mode_rest_bg_normal_selector",
//            bgIconOpenName = "mode_rest_bg_open_selector"
        ),
        VehicleServiceModeDTO(
            VehicleServiceModeType.RELAX.modeName,
            bgIconName = "mode_relax_bg_normal_selector",
//            bgIconOpenName = "mode_relax_bg_open_selector"
        ),
    )
    private lateinit var mainViewModel: MainViewModel

    // 健康建议tips
    private var tipsArray: ArrayList<HealthTipsEntry> = arrayListOf()

    // 健康建议tips 下标
    private var tipsPlayIndex = 0

    // TTS
//    private var ttsProvider: HMSTTSProvider = HMSTTSProvider()

    // TTS播报的检测健康提示文字
    private var ttsHealthTips: String? = ""

//    // TTS 是否忙线
    @Volatile
    private var mBusy = false

    // 健康Tips
    private var healthTipsEntry: HealthTipsEntry? = null

    // 语音播报 ttsId与内容关联
    private var playTTSIDAndMsgMap = mutableMapOf<String, String>()
    private var mCurrentPlayTTSId: String? = null

    /**
     * operation: 0-关闭模式 1-开启模式
     * level: 档位0-关闭 1-低档 2-中档 3-高档
     */
    data class VehicleMsgDTO(val operation: Int, val position: Int, val level: Array<String>)

    private var backgroundThread : BackgroundThread? = null

    private val mainLooperHandler: Handler =  Handler(Looper.getMainLooper())

    var savedUIDataMode: HMSServiceListFragmentModel? = null

    /**
     * 动态广播接收器，接收车辆服务更新事件、TTS取消广播事件
     */
    private var reciever = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            var currentTimeStampMillis = System.currentTimeMillis()
            if (intent?.action == HMSAction.ACTION_HMS_UPDATE_VEHICLE_SERVICE) {
                // 该方法会重新刷新数据源
                var dataId = intent?.getStringExtra("DATA_ID")

                // 是座椅加热或方向盘加热新信号，且温暖模式未被点击，则重构
                if(dataId == GwmAdapterManagerKotCoroutines.CAR_BASIC_SEAT_HEATING_LEVEL||
                    dataId == GwmAdapterManagerKotCoroutines.CAR_BASIC_STEER_WHEEL_HEATING ){
                    var clickTime = mapItemClick[VehicleServiceModeType.WARN.modeName]
                    // 如果没点击过，或点击时间超过2秒，则处理
                    if(clickTime == null || currentTimeStampMillis - clickTime > 2000){
                        createVehicleServiceDataList()
                    }
                }

                // 是座椅通风信号，且清凉模式未被点击，则重构
                if(dataId == GwmAdapterManagerKotCoroutines.CAR_BASE_SEAT_VENTILATION_3_LEVEL||
                    dataId == GwmAdapterManagerKotCoroutines.CAR_BASE_SEAT_VENTILATION_9_LEVEL ){
                    var clickTime = mapItemClick[VehicleServiceModeType.COOL.modeName]
                    // 如果没点击过，或点击时间超过2秒，则处理
                    if(clickTime == null || currentTimeStampMillis - clickTime > 2000){
                        createVehicleServiceDataList()
                    }
                }

                // 是座椅按摩信号，且放松模式未被点击，则重构
                if(dataId == GwmAdapterManagerKotCoroutines.CAR_COMFORT_SETTING_SEAT_MESSAGE_LEVEL){
                    var clickTime = mapItemClick[VehicleServiceModeType.RELAX.modeName]
                    // 如果没点击过，或点击时间超过2秒，则处理
                    if(clickTime == null || currentTimeStampMillis - clickTime > 2000){
                        createVehicleServiceDataList()
                    }
                }


            } else if (intent?.action == HMSAction.ACTION_CANCEL_PLAY_HEALTH_TIPS) {
                // 取消健康Tips语音播报
                // 停止当前播放
                if (isPlaying()) {
                    TTSHelper.stop()
                }
            }
            Log.i(TAG, "onReceive: 收到广播 $intent")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 启动子线程
        backgroundThread = BackgroundThread(this)
        backgroundThread!!.start()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding =
            DataBindingUtil.inflate(inflater, R.layout.fragment_service_list, container, false)
        mainViewModel = ViewModelProvider(requireActivity())[MainViewModel::class.java]
        // 初始化车机服务广播
        registerBroadcast()
        //初始化首页健康总结界面
        initUI()
        //初始化基础医疗服务
        initHealthServiceUI()

        return binding.root
    }

    open fun initUI() {
        // 健康Tips 没有模式限制
        initTTS()
        //初始化语音播报健康提示内容
        initClickPlayTTSMsg()

        // 初始化急救小课堂
        initEmergencyTips()

        // 初始化按钮点击事件
        initButtonsClickListener()
        // 健康周报默认不展示
        binding.btnHealthReport.visibility = View.GONE
    }

    /**
     * 初始化话基础医疗服务
     */
    private fun initHealthServiceUI(){
        // 车辆模式功能
        initVehicleServiceModeRecyclerView()
        // 根据车机配置字判断车辆模式功能
        val modelsSize = createVehicleServiceDataList()
        binding.recyclerView.visibility = if (0 == modelsSize) View.GONE else View.VISIBLE
        binding.lowCards.visibility = if (0 == modelsSize) View.VISIBLE else View.GONE
        // 初始化电话医生
        initServiceTelephoneDoctor()
        // 初始化AI健康小医
        initServiceAIDoctor()
    }

    public fun refreshHealthLiveCardUI() {
        // 私密模式
        if (HmsApplication.isPrivacyModeEnabled()) {
            initPrivacyUI()
        } else {
            // 判断网络 有网络
//            if (HmsApplication.isNetworkConn()) {
            // 游客模式卡片展示
            if (MMKVUtil.isVisitorMode()) {
                initVisitorModeUI()
            }
            // 私密模式
            else if (HmsApplication.isPrivacyModeEnabled()) {
                initPrivacyUI()
            }
            // 非访客且非私密模式
            else {
                // 初始化界面
                initNormalUI()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // 监听数据变化，获取数据
        initLiveDataListener()
        // 首页健康实时健康状态（非访客模式、非私密模式）
        mainViewModel.getLiveHealthStatusLiveData().observe(viewLifecycleOwner) {
            // 如果开启了私密模式，不做处理
            if (HmsApplication.isPrivacyModeEnabled()) {
                return@observe
            }
            val weakFragment = WeakReference(this@HMSServiceListFragment)
            val weakBinding = WeakReference(binding)
            weakBinding.get() ?: return@observe

            var riskLevel = ""
            // 有数据且有全部主生理指标的权限，才显示报告
            if (it.code == "0" && it.data != null
                && AuthorizationUtil.hasMainPrivillegesAuth()
            ) {
                var score = it.data!!.score
                var healthStatusCode = "normal"

                //有分数且分数大于45
                if (score != null && score.isNotEmpty()
                    && score != "" && score.toInt() >= 45
                ) {
                    if (score.toInt() in 90..100) {
                        healthStatusCode = "normal"
                    } else if (score.toInt() in 75..89) {
                        healthStatusCode = "low"
                    } else if (score.toInt() in 60..74) {
                        healthStatusCode = "middle"
                    } else if (score.toInt() in 45..59) {
                        healthStatusCode = "high"
                    }
                    riskLevel = HealthDataProcessor.getHealthStatusFromScore(score)
                    binding.ivHealthStatus.background = null
                    weakBinding.get()!!.hsView.setHealthStatus("", healthStatusCode, score.toInt())
                    weakBinding.get()!!.hsView.visibility = View.VISIBLE
                    if (it.data!!.healthResult != null && it.data!!.healthResult != "") {
                        binding.tvHealthStatus.text = it.data!!.healthResult
                    }

                    binding.btnHealthReport.visibility = View.VISIBLE
                    binding.btnToAuthorization.visibility = View.GONE
                    binding.btnToLogin.visibility = View.GONE

                    setLastUpdateTime()
                    binding.tvDataUpdateTime.visibility = View.VISIBLE
                }
                //没有分数或分数低于45
                else {
                    binding.ivHealthStatus.background =
                        resources.getDrawable(R.drawable.health_status_disable)
                    binding.tvHealthStatus.text =
                        resources.getString(R.string.no_health_advice_tip)
                    weakBinding.get()!!.hsView.visibility = View.GONE
                    binding.tvDataUpdateTime.visibility = View.GONE

                    binding.btnHealthReport.visibility = View.GONE
                    binding.btnToAuthorization.visibility = View.GONE
                    binding.btnToLogin.visibility = View.GONE
                }

                binding.tvPrivacyTitleText.visibility = View.GONE
                binding.homeTopLeftLayout.visibility = View.VISIBLE
                binding.ivSettings.visibility = View.VISIBLE

                sendHealthDataToWidget(
                    riskLevel = riskLevel,
                    score = it.data!!.score,
                    result = it.data!!.healthResult
                )
            }
            // 网络异常 TODO
            else if (it.code == "3") {
//                initNoNetUI()

            }
            // 如果取消了授权
            else if(it.code == BusinessRespCode.USER_LOGIN_TIME_OUT_OR_CANCEL_AUTH) {
                HmsApplication.showGlobalDialog(
                    "",
                    requireActivity()!!.getString(R.string.noti_huawei_oauth_changed_or_timeout),
                    true
                )
            }
            // 如果无数据或者主权限不全
            else {
                var message = resources.getString(R.string.no_health_advice_tip)

                // 在取消授权时，系统会退出登陆，而评分接口可能会正常返回，因此需要判断是否时访客模式。
                if(MMKVUtil.isVisitorMode()){
                    message = getString(R.string.binding_health_account)
                    initVisitorModeUI()
                }
                // 未开启所有权限
                else {
                    if (!AuthorizationUtil.isAllMainPrivillegesAuth()) {
                        message = resources.getString(R.string.no_enogh_authorizatoin_tips)
                        binding.btnToAuthorization.visibility = View.VISIBLE

                        HMSWidgetProvider().updateHmsWidgetForNoEnoughAuthorization(requireActivity())
                    } else {
                        binding.btnToAuthorization.visibility = View.GONE
                    }

                    binding.tvHealthStatus.text =
                        message // resources.getString(R.string.no_health_advice_tip)
                    binding.ivHealthStatus.background =
                        resources.getDrawable(R.drawable.health_status_disable)
                    weakBinding.get()!!.hsView.visibility = View.GONE

                    binding.btnHealthReport.visibility = View.GONE
                    binding.btnToLogin.visibility = View.GONE

                    binding.tvDataUpdateTime.visibility = View.GONE
                }
            }
        }

        // 判断 healthTipsEntry 是否存在savedInstanceState中
        if (savedInstanceState != null) {
            try {
               savedUIDataMode = Gson().fromJson(
                    savedInstanceState.getString("savedUIDataMode"),
                    HMSServiceListFragmentModel::class.java
                )
                savedUIDataMode?.let { processSavedUIData(it) }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun processSavedUIData(savedUIDataMode: HMSServiceListFragmentModel) {
        tipsArray = savedUIDataMode.tipsArray
        tipsPlayIndex = savedUIDataMode.tipsPlayIndex
        healthTipsEntry = savedUIDataMode.healthTipsEntry
        playTTSIDAndMsgMap = savedUIDataMode.playTTSIDAndMsgMap
        mCurrentPlayTTSId = savedUIDataMode.mCurrentPlayTTSId
        mBusy = savedUIDataMode.mBusy
        setTipsTitle(healthTipsEntry?.title)
        setPlayTipsIcon(mBusy)
    }

    override fun onResume() {
        super.onResume()
        Log.i(TAG, "HMSServiceListFragment onResume")

        // 刷新UI
        refreshHealthLiveCardUI()

        // 每次界面展现都判断是否登录，未登录时，设置卡片点击事件,登录则取消设置监听
        initHomeLiveStatusContainerClickListener()

        // 去授权按钮重新注册监听事件
        initAuthorizationBtnOnClickListener()

        // 刷新状态卡片
        createVehicleServiceDataList()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        val savedMode = HMSServiceListFragmentModel()
        savedMode.tipsArray = tipsArray
        savedMode.tipsPlayIndex = tipsPlayIndex
        savedMode.healthTipsEntry = healthTipsEntry
        savedMode.playTTSIDAndMsgMap = playTTSIDAndMsgMap
        savedMode.mCurrentPlayTTSId = mCurrentPlayTTSId
        savedMode.mBusy = mBusy
        outState.putString("savedUIDataMode", Gson().toJson(savedMode))
    }

    private fun initVehicleServiceModeRecyclerView() {
        binding.recyclerView.layoutManager =
            LinearLayoutManager(requireActivity(), LinearLayoutManager.HORIZONTAL, false)

        binding.recyclerView.adapter = ModeAdapter(modes, this)

        // 设置间距
        val spaceInPixels = resources.getDimensionPixelSize(R.dimen.main_body_service_list_item_2_mt)
        binding.recyclerView.addItemDecoration(ModeLeftMarginItemDecoration(spaceInPixels))
//        OverScrollDecoratorHelper.setUpOverScroll(binding.recyclerView, OverScrollDecoratorHelper.ORIENTATION_HORIZONTAL);
        HorizontalOverScrollBounceEffectDecorator(
            RecyclerViewOverScrollDecorAdapter(binding.recyclerView),
            3F,
            DEFAULT_TOUCH_DRAG_MOVE_RATIO_BCK,
            DEFAULT_DECELERATE_FACTOR
        );
    }

    /**
     * 初始化按钮点击事件
     * 电话医生、健康报告、设置按钮、登录、去授权、未登录时卡片整体
     */
    private fun initButtonsClickListener() {

        // 健康报告按钮监听事件
        initHealthReportClickListener()
        // 初始化设置按钮点击事件
        initSettingOnclickListener()
        // 初始化登录按钮点击事件
        initLoginBtnOnClickListener()
        // 初始化去授权按钮点击事件
        initAuthorizationBtnOnClickListener()
        // 初始化 演示按钮
        initLiveShowBtnOnClickListener()
    }

    private fun initLiveShowBtnOnClickListener() {
        binding.btnToLiveShow.visibility = if(MMKVUtil.isVisitorMode()) View.GONE else View.VISIBLE
        binding.btnToLiveShow.setOnClickListener {
            val healthSceneViewModel : HealthSceneViewModel by viewModels()
            val title = healthSceneViewModel.getNextSceneDescription()
            playTts(title,true,false)
            (activity as? MainActivity)?.showGlobalHealthWarningAlert(title,
                onCallDoctor = {
                    // 这里写电话医生的逻辑
                    doDoctorCall()
                },
                onAIConsultation = {
                    // 这里写AI小医的逻辑
                    showMedAIChatView(Constants.FROM_TYPE_TOAST_OPEN_CHAT)
                },
                onNavigateToHospital = {
                    // 这里写导航医院的逻辑
                }
            )
        }
    }

    /**
     * 设置状态卡片整体点击
     * 1、未登录状态
     * 2、去登录按钮可见
     */
    private fun initHomeLiveStatusContainerClickListener() {
        Log.d(TAG, "initHomeLiveStatusContainerClickListener")

        // 不管Login按钮是否可见，都进行单击事件初始化
        initLoginBtnOnClickListener()
        //只有在登录按钮可见时，才初始化面板点击事件；否则不可点击
        if (MMKVUtil.isVisitorMode() && binding.btnToLogin.visibility == View.VISIBLE) {
            Log.d(TAG, "initHomeLiveStatusContainerClickListener btnLogin is visible")
            ClickUtils.applySingleDebouncing(binding.rlHomeLiveStatusContainer, 500) {
                if(MMKVUtil.getPrivacyPolicy()) {
                    HmsApplication.showLoginDialog()
                }else{
                    // 如果没有同意隐私协议，先弹出隐私协议同意对话框
                    (requireActivity() as MainActivity).doPrivacyDialog()
                }
            }
        } else {
            binding.rlHomeLiveStatusContainer.setOnClickListener(null)
            Log.d(TAG, "initHomeLiveStatusContainerClickListener btnLogin is invisible")
        }
    }

    /**
     * 点击登录按钮
     */
    private fun initLoginBtnOnClickListener() {
        Log.d(TAG, "initLoginBtnOnClickListener ")
        // 无需判断按钮是否可见，即可给点击事件。
//        if (binding.btnToLogin.visibility == View.VISIBLE) {
//            Log.d(TAG, "initLoginBtnOnClickListener  btnLogin is visible")
            binding.btnToLogin.setOnClickListener {
                if(MMKVUtil.getPrivacyPolicy()) {
                    HmsApplication.showLoginDialog()
                }else{
                    // 如果没有同意隐私协议，先弹出隐私协议同意对话框
                    (requireActivity() as MainActivity).doPrivacyDialog()
                }
            }
//        }else{
//            Log.d(TAG, "initLoginBtnOnClickListener  btnLogin is invisible")
//        }
    }

    /**
     * 初始化去授权按钮点击事件
     */
    private fun initAuthorizationBtnOnClickListener() {
//        if (binding.btnToAuthorization.visibility == View.VISIBLE) {
        binding.btnToAuthorization.setOnClickListener {
            HmsApplication.showLoginDialog()
        }
//        }
    }

    /**
     * 初始化健康周报按钮点击
     */
    private fun initHealthReportClickListener() {
        binding.btnHealthReport.setOnClickListener {
            //增加埋点
            DataTrackUtil.dtClick("Health_Homepage_HealthReports_Click")

            val userId = MMKVUtil.getUserId()
            if (userId != null) {
                MainRepository().getAuthStatus(
                    userId,
                    object : BaseResponseCallback<HWAuthStatusDTO> {
                        override fun onSuccess(response: BaseResponse<HWAuthStatusDTO>) {
                            try {
                                var hwAuthStatusDTO = response.data
                                if (hwAuthStatusDTO != null && hwAuthStatusDTO.code != 1) {
                                    // 权限错误，关闭了华为运动健康服务
                                    HmsApplication.showGlobalDialog(
                                        "",
                                        requireActivity().getString(R.string.noti_open_data_auth),
                                        false
                                    )
                                }
                                // 如果取消了授权
                                else if(response.code == BusinessRespCode.USER_LOGIN_TIME_OUT_OR_CANCEL_AUTH) {
                                    HmsApplication.showGlobalDialog(
                                        "",
                                        context!!.getString(R.string.noti_huawei_oauth_changed_or_timeout),
                                        true
                                    )
                                }else {
                                    //继续处理
                                    doEnterHealthReport()
                                }
                            } catch (ex: Exception) {
                                Log.i(TAG, "判断权限出错，错误信息：${ex.message}")
                                //继续处理
                                doEnterHealthReport()
                            }
                        }

                        override fun onFailed(response: BaseResponse<HWAuthStatusDTO>) {
                            //TODO 提示网络异常
                            doEnterHealthReport()
                        }
                    })
            }
        }
    }

    /**
     * 打开健康报告页面
     */
    private fun doEnterHealthReport() {
        val intent = Intent(requireActivity(), HealthReportActivity::class.java)
        intent.putExtra("title", requireContext().getString(R.string.health_report))
        startActivity(intent)
        requireActivity().overridePendingTransition(
            R.anim.activity_enter_slide_in_right,
            R.anim.activity_enter_slide_out_left
        );
    }

    /**
     * 初始化设置按钮点击事件
     */
    private fun initSettingOnclickListener() {
        // 注册长按事件
        binding.ivSettings.setOnClickListener {
            DataTrackUtil.dtClick("Health_Homepage_Set_Click")
            val intent = Intent(requireActivity(), HMSPersonalActivity::class.java)
            requireActivity().startActivity(intent)
            requireActivity().overridePendingTransition(
                R.anim.activity_enter_slide_in_right,
                R.anim.activity_enter_slide_out_left
            )
        }
    }

    /**
     * 初始化网络请求接口监听
     */
    private fun initLiveDataListener() {
         mainViewModel.getHealthTipsInfoLiveData().observe(viewLifecycleOwner) { it: BaseResponse<HealthTipsDTO> ->
             Log.i(TAG,"to loading tips - getHealthTipsInfoLiveData")
             if (it.code == "0" && it.data != null && it.data!!.tips.size > 0) {
                tipsArray = it.data!!.tips
                if (tipsArray.isNotEmpty() && !isPlaying()) {
                    healthTipsEntry = getNextTipsInfo()
                    setPlayTipsIcon(false)
                    setTipsTitle(healthTipsEntry?.title)

                     // 存储健康tips
                     storeHealthTipToDisk(tipsArray)
                 }
             } else {
                 try {
                     //TODO 如果获取健康tips失败，从本地缓存里读取
                     val tipsArrayJson = MMKVUtil.getHealthTips()
                     Log.i(TAG, "tipsArrayJson: $tipsArrayJson")
                     if (!tipsArrayJson.isNullOrEmpty()) {
                         val listType = object : TypeToken<ArrayList<HealthTipsEntry>>() {}.type
                         val tipsArrayObj: ArrayList<HealthTipsEntry> =
                             Gson().fromJson(tipsArrayJson, listType)
                         if(tipsArrayObj.size > 0){
                             tipsArray = tipsArrayObj
                             if(!isPlaying()) {
                                 healthTipsEntry = getNextTipsInfo()
                                 setPlayTipsIcon(false)
                                 setTipsTitle(healthTipsEntry?.title)
                             }
                         }
                     }
                 } catch (ex: Exception) {
                     Log.i(TAG, "restore health tips failed")
                 }
             }
         }
    }

    private fun storeHealthTipToDisk(tips: ArrayList<HealthTipsEntry>){
        MMKVUtil.storeHealthTips(Gson().toJson(tips))
    }

    /**
     * 更新桌面卡片
     */
    private fun sendHealthDataToWidget(
        riskLevel: String = "",
        score: String = "",
        result: String = ""
    ) {
        try {
            // 如果数据异常，不更新桌面卡片。
            if(score == null ){
                Log.i(TAG, "数据异常，分数为null，不更新桌面卡片。")
                return
            }
            if(riskLevel == null ){
                Log.i(TAG, "数据异常，健康等级为null，不更新桌面卡片。")
                return
            }
            if(result == null ){
                Log.i(TAG, "数据异常，健康总结为null，不更新桌面卡片。")
                return
            }
            // 保存数据
            provider = HMSWidgetProvider()
            provider!!.saveData(requireContext(), HMSWidgetProvider.DATA_KEY_RISK_LEVEL, riskLevel)
            provider!!.saveData(requireContext(), HMSWidgetProvider.DATA_KEY_SCORE, score)
            provider!!.saveData(requireContext(), HMSWidgetProvider.DATA_KEY_RESULT, result)

            refreshWidget()

        } catch (ex: Exception) {
            Log.i(TAG, "发送数据给桌面卡片发生错误，错误如下：${ex.message}")
        }
    }

    private var lastRefreshTime: Long = 0  // 记录上一次刷新时间
    private val refreshInterval: Long = 1000  // 设置时间间隔，单位：毫秒 (这里设置为 2 秒)

    /**
     * 车机功能打开超时2秒
     */
    private val vehicleServiceOperationTimeOut : Long = 2000L
    fun refreshWidget() {
        val currentTime = System.currentTimeMillis()
        // 检查当前时间与上一次刷新时间的间隔
        if (currentTime - lastRefreshTime < refreshInterval) {
            return  // 如果时间间隔未到，则直接返回
        }
        // 更新上一次刷新时间
        lastRefreshTime = currentTime
        if (provider == null) {
            // 杀进程
            HMSWidgetProvider().let {
                // 使用 context 而不是 requireContext
                val context = HmsApplication.appContext  // 或者传递 context 参数
                // 获取 AppWidgetManager 实例
                val appWidgetManager = AppWidgetManager.getInstance(context)
                // 获取当前小部件的所有 AppWidgetId
                val appWidgetIds = appWidgetManager.getAppWidgetIds(
                    ComponentName(context, HMSWidgetProvider::class.java)
                )
                // 更新小部件
                it.updateHmsWidget(context, appWidgetIds)
            }
        } else {
            // 进程活跃
            provider?.let {
                // 更新桌面卡片
                val appWidgetManager = AppWidgetManager.getInstance(requireContext())
                val appWidgetIds = appWidgetManager.getAppWidgetIds(
                    ComponentName(requireContext(), HMSWidgetProvider::class.java)
                )
                it.updateHmsWidget(requireContext(), appWidgetIds)
            }
        }
    }

    //region 车机服务对话框
    /**
     * 刷新车机基础服务数据源，并刷新
     */
    private fun createVehicleServiceDataList() : Int {
        modes.clear()
        // 中医香薰
        val isAddAromatherapyMode = true
        if (isAddAromatherapyMode) {
            modes.add(
                VehicleServiceModeDTO(
                    VehicleServiceModeType.MED_AROMATHERAPY.modeName,
                    bgIconName = "mode_med_aromatherapy_bg_normal_selector",
//                    bgIconOpenName = "mode_aromatherapy_bg_open_selector",
                )
            )
        }

        // 智能座椅
        val isAddAIChairMode = true
        if (isAddAIChairMode) {
            modes.add(
                VehicleServiceModeDTO(
                    VehicleServiceModeType.AI_CHAIR.modeName,
                    bgIconName = "mode_ai_chair_bg_normal_selector"
                )
            )
        }

        // 温暖模式 = 主驾座椅加热 + 方向盘加热
        var isAddWarnMode =
            true//GwmAdapterManagerKotCoroutines.isSupportSeatHeat() || GwmAdapterManagerKotCoroutines.isSupportSteerWheelHeating()
        if (isAddWarnMode) {
            modes.add(
                VehicleServiceModeDTO(
                    VehicleServiceModeType.WARN.modeName,
                    bgIconName = "mode_warn_bg_normal_selector",
//                    bgIconOpenName = "mode_warn_bg_open_selector",
                    isOpen = GwmAdapterManagerKotCoroutines.isWarnModeOpen()
                )
            )
        }

        // 清凉模式 = 主驾座椅通风
        var isAddWristMode = true//GwmAdapterManagerKotCoroutines.isSupportSeatVentilation()
        if (isAddWristMode) {
            modes.add(
                VehicleServiceModeDTO(
                    VehicleServiceModeType.COOL.modeName,
                    bgIconName = "mode_cool_bg_normal_selector",
//                    bgIconOpenName = "mode_cool_bg_open_selector",
                    isOpen = GwmAdapterManagerKotCoroutines.isCoolModeOpen()
                )
            )
        }

        // 小憩模式
        var isAddRestMode = true//GwmAdapterManagerKotCoroutines.isSupportRestMode()
        if (isAddRestMode) {
            modes.add(
                VehicleServiceModeDTO(
                    VehicleServiceModeType.REST.modeName,
                    bgIconName = "mode_rest_bg_normal_selector",
//                    bgIconOpenName = "mode_rest_bg_open_selector"
                )
            )
        }

        // 放松模式 = 座椅按摩
        var isAddRelaxMode = true//GwmAdapterManagerKotCoroutines.isSupportSeatMassage()
        if (isAddRelaxMode) {
            modes.add(
                VehicleServiceModeDTO(
                    VehicleServiceModeType.RELAX.modeName,
                    bgIconName = "mode_relax_bg_normal_selector",
//                    bgIconOpenName = "mode_relax_bg_open_selector",
                    isOpen = GwmAdapterManagerKotCoroutines.isRelaxModeOpen()
                )
            )
        }

        // 刷新数据
        if (modes.size > 0) {
            binding.recyclerView.adapter?.notifyDataSetChanged()
        }

        return modes.size
    }



    private var mapItemClick = mutableMapOf<String,Long>()

    override fun onItemClick(mode: VehicleServiceModeDTO,position: Int) {
        // item 点击事件
//        val currentTime = System.currentTimeMillis()
//        if (currentTime - lastClickTime > debounceDelay) {
//            lastClickTime = currentTime
            // 温暖模式
        if (mode.modeName == VehicleServiceModeType.WARN.modeName) {
            if (mapItemClick[mode.modeName] != null && System.currentTimeMillis() - mapItemClick[mode.modeName]!! < HmsSettings.ACTION_DELAY_TIME_MS) {
                return
            }
            Log.i(TAG,"WARN VehicleServiceModeDTO $mode")
            mapItemClick[mode.modeName] = System.currentTimeMillis()

            if (!mode.isOpen) {
                // 关闭状态曝光与点击
                DataTrackUtil.dtScrollWithMode("Health_Homepage_WarmOff_Show")
                DataTrackUtil.dtClickWithMode("Health_Homepage_WarmOff_Click")
                openWarnModeDialog(position)
            } else {
                // 打开状态曝光与点击
                DataTrackUtil.dtScrollWithMode("Health_Homepage_WarmOn_Show")
                DataTrackUtil.dtClickWithMode("Health_Homepage_WarmOn_Click")
                backgroundThread?.handler?.removeMessages(WARN_OPERATOR_CHECK)
                val level = "0"
                GwmAdapterManagerKotCoroutines.closeWarnMode(level)
                val msg = Message.obtain().apply {
                    what = WARN_OPERATOR_CHECK
                    obj = VehicleMsgDTO(0,position,arrayOf("0","0"))
                }
                backgroundThread?.handler?.sendMessageDelayed(msg,vehicleServiceOperationTimeOut)
                DataTrackUtil.dtClickWithMode("Health_Homepage_WarmOffPrompt_Agree_Click")
            }
        } else if (mode.modeName == VehicleServiceModeType.COOL.modeName) {
            if (mapItemClick[mode.modeName] != null && System.currentTimeMillis() - mapItemClick[mode.modeName]!! < HmsSettings.ACTION_DELAY_TIME_MS) {
                return
            }
            mapItemClick[mode.modeName] = System.currentTimeMillis()
            // 清凉模式
            Log.i(TAG,"COOL VehicleServiceModeDTO $mode")
            if (!mode.isOpen) {
//            if (!GwmAdapterManagerKotCoroutines.isPowerModeOpen()) {
                // 关闭状态曝光与点击
                DataTrackUtil.dtScrollWithMode("Health_Homepage_LumbarOff_Show")
                DataTrackUtil.dtClickWithMode("Health_Homepage_LumbarOff_Click")
                openCoolModeDialog(position)
            } else {
                // 打开状态曝光与点击
                DataTrackUtil.dtScrollWithMode("Health_Homepage_LumbarOn_Show")
                DataTrackUtil.dtClickWithMode("Health_Homepage_LumbarOn_Click")

                // 2、取消之前的操作
                backgroundThread?.handler?.removeMessages(COOL_OPERATOR_CHECK)
                val level = "0"
                GwmAdapterManagerKotCoroutines.closeCoolMode(level)
//                GwmAdapterManagerKotCoroutines.closePowerMode(level)
                val msg = Message.obtain().apply {
                    what = COOL_OPERATOR_CHECK
                    obj = VehicleMsgDTO(0,position,arrayOf(level))
                }
                backgroundThread?.handler?.sendMessageDelayed(msg,vehicleServiceOperationTimeOut)
                DataTrackUtil.dtClickWithMode("Health_Homepage_CoolnessOffPrompt_Agree_Click")
            }
        } else if (mode.modeName == VehicleServiceModeType.REST.modeName) {
            if (mapItemClick[mode.modeName] != null && System.currentTimeMillis() - mapItemClick[mode.modeName]!! < HmsSettings.ACTION_DELAY_TIME_MS) {
                return
            }
            mapItemClick[mode.modeName] = System.currentTimeMillis()

            // 获取档位 3是P档
            DataTrackUtil.dtScrollWithMode("Health_Homepage_SleepOn_Show")
            DataTrackUtil.dtClickWithMode("Health_Homepage_SleepOn_Click")
            val gearStatus = GwmAdapterManagerKotCoroutines.getGearStatus()
            if (gearStatus == "3") {
                openRestModeDialog()
            } else {
                ToastUtil.makeText(requireContext(), "请在P挡下使用", Toast.LENGTH_SHORT).show()
            }
        } else if (mode.modeName == VehicleServiceModeType.RELAX.modeName) {
            if (mapItemClick[mode.modeName] != null && System.currentTimeMillis() - mapItemClick[mode.modeName]!! < HmsSettings.ACTION_DELAY_TIME_MS) {
                return
            }
            mapItemClick[mode.modeName] = System.currentTimeMillis()
            Log.i(TAG,"RELAX VehicleServiceModeDTO $mode")
            if (!mode.isOpen) {
                // 关闭状态曝光与点击
                DataTrackUtil.dtScrollWithMode("Health_Homepage_RestOff_Show")
                DataTrackUtil.dtClickWithMode("Health_Homepage_RestOff_Click")
                openRelaxModeDialog(position)
            } else {
                // 打开状态曝光与点击
                DataTrackUtil.dtScrollWithMode("Health_Homepage_RestOn_Show")
                DataTrackUtil.dtClickWithMode("Health_Homepage_RestOn_Click")
                backgroundThread?.handler?.removeMessages(RELAX_OPERATOR_CHECK)
                val level = "0"
                GwmAdapterManagerKotCoroutines.closeRelaxMode(level)
                val msg = Message.obtain().apply {
                    what = RELAX_OPERATOR_CHECK
                    obj = VehicleMsgDTO(0,position, arrayOf(level))
                }
                backgroundThread?.handler?.sendMessageDelayed(msg,vehicleServiceOperationTimeOut)
                DataTrackUtil.dtClickWithMode("Health_Homepage_RestOffPrompt_Agree_Click")
            }
//            }
        } else if (mode.modeName == VehicleServiceModeType.MED_AROMATHERAPY.modeName) {
            if (mapItemClick[mode.modeName] != null && System.currentTimeMillis() - mapItemClick[mode.modeName]!! < HmsSettings.ACTION_DELAY_TIME_MS) {
                return
            }
            mapItemClick[mode.modeName] = System.currentTimeMillis()
            startActivity(Intent(requireContext(), AromatherapyActivity::class.java))
            requireActivity().overridePendingTransition(
                R.anim.activity_enter_slide_in_right,
                R.anim.activity_enter_slide_out_left
            )
        } else if (mode.modeName == VehicleServiceModeType.AI_CHAIR.modeName) {
            if (mapItemClick[mode.modeName] != null && System.currentTimeMillis() - mapItemClick[mode.modeName]!! < HmsSettings.ACTION_DELAY_TIME_MS) {
                return
            }
            mapItemClick[mode.modeName] = System.currentTimeMillis()
            startActivity(Intent(requireContext(), SpineCareActivity::class.java))
            requireActivity().overridePendingTransition(
                R.anim.activity_enter_slide_in_right,
                R.anim.activity_enter_slide_out_left
            )
        }
        (binding.recyclerView.adapter as ModeAdapter).toggleSelection(position)
    }

    /**
     * 初始化车机组合服务 顺序及内容
     * 温暖模式（座椅加热+方向盘加热）
     * 护腰模式（电动腰托）
     * 小憩模式（小憩模式）
     * 放松模式（座椅按摩+智能香氛）
     */
    //region 车机服务对话框
    /**
     * 温暖模式对话框 座椅加热+方向盘加热
     */
    private fun openWarnModeDialog(position: Int) {
        var sb = StringBuffer()
        sb.append("开启${VehicleServiceModeType.WARN.modeName}将会打开")

        // 方向盘加热
        val isShowSteerWheelHeat =
            true // GwmAdapterManagerKotCoroutines.isSupportSteerWheelHeating() //&& !GwmAdapterManagerKotCoroutines.isOpenSteerWheelHeating()
        // 座椅加热
        val isShowSeatHeat =
            true // GwmAdapterManagerKotCoroutines.isSupportSeatHeat() // && !GwmAdapterManagerKotCoroutines.isOpenSeatHeat()

        // 显示自定义对话框
        val message = "是否开启${VehicleServiceModeType.WARN.modeName}"

        Log.i(TAG, "openWarnModeDialog = isShowSteerWheelHeat = $isShowSeatHeat isShowSeatHeat = $isShowSteerWheelHeat")

        var isTowFun = isShowSeatHeat && isShowSteerWheelHeat
        if (isTowFun) {
            sb.append("方向盘加热和主驾座椅加热")
        } else {
            if (isShowSteerWheelHeat) {
                sb.append("方向盘加热")
            }
            if (isShowSeatHeat) {
                sb.append("主驾座椅加热")
            }
        }
        val modeTips = sb.toString()

        HMSDialogUtils.showHMSModeDialog(
            requireActivity(),
            isTowFun,
            message,
            if (isShowSteerWheelHeat) R.drawable.icon_wheel_heating else null, // 方向盘加热
            if (isShowSeatHeat) R.drawable.icon_seat_heating else null, // 座椅加热
            modeTips,
            "确定",
            "取消"
        ) { isPositive ->
            if (isPositive) {
                DataTrackUtil.dtClickWithMode("Health_Homepage_WarmOnPrompt_Agree_Click")
                // 打开 方向盘加热 + 主驾座椅加热
                backgroundThread?.handler?.removeMessages(WARN_OPERATOR_CHECK)
                val openSteerWheelLevel = "1"
                val openSeatHeatLevel = "3"
                val levelArr = arrayOf(openSteerWheelLevel, openSeatHeatLevel)
                GwmAdapterManagerKotCoroutines.openWarnMode(levelArr[0], levelArr[1])
                val msg = Message.obtain().apply {
                    what = WARN_OPERATOR_CHECK
                    obj = VehicleMsgDTO(1,position,levelArr)
                }
                backgroundThread?.handler?.sendMessageDelayed(msg, vehicleServiceOperationTimeOut)
            } else {
                DataTrackUtil.dtClickWithMode("Health_Homepage_WarmOnPrompt_Disagree_Click")
                val adapter = binding.recyclerView.adapter as ModeAdapter
                adapter.changeModelOpenStatus(position,GwmAdapterManagerKotCoroutines.isWarnModeOpen())
            }
        }
    }

    /**
     * 清凉模式对话框 座椅通风
     */
    private fun openCoolModeDialog(position: Int) {
        // 显示自定义对话框
        val message = "是否开启${VehicleServiceModeType.COOL.modeName}"
        val modeTips = "开启清凉模式将会打开主驾座椅通风"
        val isShowSeatVentilation =
            true //GwmAdapterManagerKotCoroutines.isSupportSeatVentilation() //&& !GwmAdapterManagerKotCoroutines.isOpenSeatVentilation()

        HMSDialogUtils.showHMSModeDialog(
            requireActivity(),
            false,
            message,
            R.drawable.icon_seat_ventilation,
            R.drawable.icon_seat_ventilation,
            modeTips,
            "确定",
            "取消"
        ) { isPositive ->
            if (isPositive) {
                DataTrackUtil.dtClickWithMode("Health_Homepage_CoolnessOnPrompt_Agree_Click")
                if (isShowSeatVentilation) {
                    // 2、取消之前的操作
                    backgroundThread?.handler?.removeMessages(COOL_OPERATOR_CHECK)
                    val level = "3"
                    GwmAdapterManagerKotCoroutines.openCoolMode(level)
//                    GwmAdapterManagerKotCoroutines.openPowerMode(level)

                    val msg = Message.obtain().apply {
                        what = COOL_OPERATOR_CHECK
                        obj = VehicleMsgDTO(1, position, arrayOf(level))
                    }
                    backgroundThread?.handler?.sendMessageDelayed(msg,vehicleServiceOperationTimeOut)
                }
            } else {
                DataTrackUtil.dtClickWithMode("Health_Homepage_CoolnessOnPrompt_Disagree_Click")
                val adapter = binding.recyclerView.adapter as ModeAdapter
                adapter.changeModelOpenStatus(position,GwmAdapterManagerKotCoroutines.isCoolModeOpen())
            }
        }
    }

    /**
     * 小憩模式对话框
     */
    private fun openRestModeDialog() {
        val message = "是否打开${VehicleServiceModeType.REST.modeName}"
        HMSDialogUtils.showHMSDialog(
            requireActivity(),
            R.layout.hms_dialog_open_rest_mode,
            message,
            "确定",
            "取消"
        ) { isPositive ->
            if (isPositive) {
                DataTrackUtil.dtClickWithMode("Health_Homepage_SleepOnPrompt_Agree_Click")
                mainLooperHandler.postDelayed({
                    GwmAdapterManagerKotCoroutines.setRestModeEnable(requireActivity())
                }, 300)
            } else {
                DataTrackUtil.dtClickWithMode("Health_Homepage_SleepOnPrompt_Disagree_Click")
            }
        }
    }

    /**
     * 放松模式对话框 座椅按摩+智能香氛
     */
    private fun openRelaxModeDialog(position: Int) {
        var sb = StringBuffer()
        sb.append("开启${VehicleServiceModeType.RELAX.modeName}将会打开主驾")
        // 座椅按摩
        val isShowSeatMessage =
            true //GwmAdapterManagerKotCoroutines.isSupportSeatMassage() //&& !GwmAdapterManagerKotCoroutines.isOpenSeatMessage()
        // 智能香氛
        val isSupportFragrance = true // GwmAdapterManagerKotCoroutines.isSupportFragrance()

        var isTowFun = isShowSeatMessage && isSupportFragrance
        if (isTowFun) {
            sb.append("座椅按摩和香氛")
        } else {
            if (isShowSeatMessage) {
                sb.append("座椅按摩")
            }
            if (isSupportFragrance) {
                sb.append("香氛")
            }
        }
        val modeTips = sb.toString()
        //
        var message = "是否打开${VehicleServiceModeType.RELAX.modeName}"
        HMSDialogUtils.showHMSModeDialog(
            requireActivity(),
            isTowFun,
            message,
            if (isShowSeatMessage) R.drawable.icon_seat_anmo else null,
            if (isSupportFragrance) R.drawable.icon_fragrance else null,
            modeTips,
            "确定",
            "取消"
        ) { isPositive ->
            if (isPositive) {
                DataTrackUtil.dtClickWithMode("Health_Homepage_RestOnPrompt_Agree_Click")
                // 打开座椅按摩3档
                if (isShowSeatMessage) {
                    backgroundThread?.handler?.removeMessages(RELAX_OPERATOR_CHECK)
                    val level = "3"
                    GwmAdapterManagerKotCoroutines.openRelaxMode(level)
                    val msg = Message.obtain().apply {
                        what = RELAX_OPERATOR_CHECK
                        obj = VehicleMsgDTO(1,position,arrayOf(level))
                    }
                    backgroundThread?.handler?.sendMessageDelayed(msg,vehicleServiceOperationTimeOut)
                }
            } else {
                DataTrackUtil.dtClickWithMode("Health_Homepage_RestOnPrompt_Disagree_Click")
                val adapter = binding.recyclerView.adapter as ModeAdapter
                adapter.changeModelOpenStatus(position,GwmAdapterManagerKotCoroutines.isRelaxModeOpen())
            }
        }
    }

    fun showOpenModeFailToast(context: Context,message: String) {
        val weakContext = WeakReference<Context>(context)
//        binding.recyclerView.adapter?.notifyDataSetChanged()
        try {
            weakContext.get()?.let {
                runOnUiThread {
                    ToastUtil.makeText(it, message, Toast.LENGTH_SHORT).show()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    //endregion 车机服务对话框

    //region 健康提示播放相关
    /**
     * 初始化点击播报健康提示点击事件
     */
    private fun initClickPlayTTSMsg() {

        // 防抖单击
        ClickUtils.applySingleDebouncing(binding.includeRlHealthTips.rlHealthTips, 500){

            DataTrackUtil.dtClickWithMode("Health_Homepage_Tips_Click")

            //如果是打电话过程中，则不播放语音
            if (DoctorCallManager.inCallSession){
                return@applySingleDebouncing
            }

            // 播放中停止播放后返回
            if (isPlaying()) {
                // 设置停止播放图片
                DataTrackUtil.dtClickWithMode("Health_Homepage_TipsClose_Click")
                setPlayTipsIcon(false)
                TTSHelper.stop()
                // 随机获取下一条健康提示并设置标题
                getNextTipsInfo().let {
                    healthTipsEntry = it
                    setTipsTitle(it!!.title)
                    Log.i(TAG,"set next tips: ${it!!.title}")
                }
                return@applySingleDebouncing
            }

            // 没播放则播放
            if (healthTipsEntry != null) {
                Log.i(TAG,"set play tips: ${healthTipsEntry?.title}")
                setPlayTipsIcon(true)
                playTts(healthTipsEntry?.content, true, true)
            }
        }
    }

    private fun setPlayTipsIcon(isPlay: Boolean) {
        if (isAdded) {
            if (isPlay) {
                try {
                    requireContext()?.let {
                        binding.includeRlHealthTips.ivHealthPlayIcon.setImageDrawable(
                            AppCompatResources.getDrawable(
                                it,
                                R.drawable.animation_play_icon
                            )
                        )
                        val animation = binding.includeRlHealthTips.ivHealthPlayIcon.drawable as AnimationDrawable
                        animation.start()
                    }
                } catch (e: Exception) {
//                    e.printStackTrace()
                    Log.i(TAG,"setPlayTipsIcon: ${e.message}")
                }


            } else {
                try {
                    requireContext()?.let {
                        binding.includeRlHealthTips.ivHealthPlayIcon.setImageDrawable(
                            AppCompatResources.getDrawable(
                                it,
                                R.drawable.ic_volumn_03
                            )
                        )
                    }
                } catch (e: Exception) {
//                    e.printStackTrace()
                    Log.i(TAG,"setPlayTipsIcon: ${e.message}")
                }

            }
        }
    }

    // 切换健康tips标题
    private fun getNextTipsInfo(): HealthTipsEntry? {
        var default: HealthTipsEntry? = null
        if (tipsArray.isNotEmpty()) {
            val nextTip = tipsArray[tipsPlayIndex % tipsArray.size]
            default = nextTip
            tipsPlayIndex++

            // 重新获取数据
            if(tipsPlayIndex%tipsArray.size == 0){
                Log.i(TAG,"to loading tips - reloading")
                mainViewModel.getHealthTipsWithVin(MMKVUtil.getVinCode()!!)
            }
        }
        return default
    }

    /**
     * 初始化语音播报模块
     */
    private fun initTTS() {
        tipsPlayIndex = 0

        // 获取并初始化 TTSHelper
        TTSHelper.init(requireContext())
        TTSHelper.setOnTTSStatusListener(object : OnTTSStatusListener {
            override fun onTTSInitialized(success: Boolean) {
                Log.i(TAG,"ttsListener onTTSInitialized: $success")
            }

            override fun onTTSSpeakStart(utteranceId: String?) {
                Log.i(TAG,"ttsListener onTTSSpeakStart: $utteranceId")
            }

            override fun onTTSSpeakStop(utteranceId: String?, interrupted: Boolean) {
                Log.i(TAG,"ttsListener onTTSSpeakStop: $utteranceId interrupted $interrupted")
                healthTipsEntry = getNextTipsInfo()
                setPlayTipsIcon(false)
                setTipsTitle(healthTipsEntry?.title)
            }

            override fun onTTSSpeakDone(utteranceId: String?) {
                Log.i(TAG,"ttsListener onTTSSpeakDone: $utteranceId")
                healthTipsEntry = getNextTipsInfo()
                setPlayTipsIcon(false)
                setTipsTitle(healthTipsEntry?.title)
            }

            override fun onTTSSpeakError(utteranceId: String?, errorMessage: String) {
                Log.i(TAG,"ttsListener onTTSSpeakError: $utteranceId errorMessage: $errorMessage")
                healthTipsEntry = getNextTipsInfo()
                setPlayTipsIcon(false)
                setTipsTitle(healthTipsEntry?.title)
            }

            override fun onTTSError(errorMessage: String) {

            }

        })
    }

    private fun isPlaying(): Boolean {
        return TTSHelper.isPlaying() == true
    }

    /**
     * 开始播报
     */
    private fun playTts(msg: String?, stopCurrent: Boolean = true, isHealthTips: Boolean = false) {
        Log.d(TAG,"start to play tts ,$msg")
        if (TTSHelper.isPlaying()) TTSHelper.stop()

        msg?.let {
            TTSHelper.speak(it, if (stopCurrent) TextToSpeech.QUEUE_FLUSH else TextToSpeech.QUEUE_ADD)
        }
    }

    private fun setTipsTitle(text: String?) {
        text?.let {
            binding.includeRlHealthTips.tvHealthTipsTitle.text = it
        }
    }
    //endregion 健康提示播放相关

    //region 急救小课堂
    @OptIn(UnstableApi::class)
    private fun initEmergencyTips(){
        ClickUtils.applySingleDebouncing(binding.includeRlEmergencyTips.rlEmergencyTips) {
            startActivity(Intent(requireContext(), PlaylistActivity::class.java))
            requireActivity().overridePendingTransition(
                R.anim.activity_enter_slide_in_right,
                R.anim.activity_enter_slide_out_left
            )
        }
    }
    //endregion
    private fun setLastUpdateTime() {
        val formatter = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
        val date = Date()
        val formattedDate = formatter.format(date)
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, -1)
        val formattedYesterday = formatter.format(calendar.time)
        binding.tvDataUpdateTime.visibility = View.VISIBLE
        binding.tvDataUpdateTime.text = "评分时间：${formattedYesterday} 至 ${formattedDate}"

    }

    var lastClickDoctorCallTime = 0L

    /**
     * 呼叫电话医生前先检查
     */
    fun doDoctorCall() {
        DataTrackUtil.dtScroll("Health_Homepage_Calldoctor_Show", DataTrackUtil.userIDModeMap())
        DataTrackUtil.dtClick("Health_Homepage_Calldoctor_Click", DataTrackUtil.userIDModeMap())

        // 500ms 防抖
        if(System.currentTimeMillis() - lastClickDoctorCallTime < HmsSettings.ACTION_DELAY_TIME_MS){
            return
        }else{
            this.lastClickDoctorCallTime = System.currentTimeMillis()
        }

        // 未同意隐私政策直接toast提示并返回
        if (!HmsApplication.isAgreePrivacy()) {
            ToastUtil.makeText(HmsApplication.appContext, getString(R.string.agree_privacy_toast_text), Toast.LENGTH_SHORT).show()
            return
        }


        // 私密模式弹框 返回
        if (HmsApplication.isPrivacyModeEnabled()) {
            var message =
                requireActivity().getString(R.string.dialog_service_tel_doc_content_privacy)
            ToastUtil.makeText(HmsApplication.appContext, message, Toast.LENGTH_SHORT).show()
            return
        }

        // 无网络
        if (!HmsApplication.isNetworkConn()) {
            retryCallDoctorCall()
            return
        }

        when {
            // 通话中
            DoctorCallManager.inCallSession -> {
                val msg = resources.getString(R.string.doctor_call_warning_on_calling_msg)
                HMSDialogUtils.showHMSDoctorPhoneExitsDialog(
                    requireContext(),
                    R.layout.hms_dialog_doctor_phone_exist,
                    msg,
                    "知道了"
                ) {}
            }

            else -> {
                stopCurrentTTS()
                val weakContext = WeakReference(requireActivity())
                DoctorCallManager.call(weakContext.get())
            }
        }
    }

    fun stopCurrentTTS() {
        if (isPlaying()) {
            TTSHelper.stop()
        }
    }

    // 电话医生按钮
    private fun initServiceTelephoneDoctor() {
        binding.includeTelephoneDoctor.mainBodyServiceTelephoneDoctor.addClickScale()
        DataTrackUtil.dtScroll("Health_Homepage_Calldoctor_Show", DataTrackUtil.userIDModeMap())
        ClickUtils.applySingleDebouncing(binding.includeTelephoneDoctor.mainBodyServiceTelephoneDoctor) {
            doDoctorCall()
        }
    }

    // AI健康小医
    private fun initServiceAIDoctor() {
        binding.includeAiHealthDoctor.mainBodyServiceAiDoctor.addClickScale()
        ClickUtils.applySingleDebouncing(binding.includeAiHealthDoctor.mainBodyServiceAiDoctor) {
            showMedAIChatView(Constants.FROM_TYPE_CARD_OPEN_CHAT)
        }
    }

    private fun showMedAIChatView(from : String) {
        activity?.let {
            if (it is MainActivity) {
                it.showMedAIChatView(from)
            }
        }
    }

    private fun retryCallDoctorCall() {
        val msg = resources.getString(R.string.doctor_call_warning_on_no_network_msg)
        HMSDialogUtils.showHMSDoctorPhoneExitsDialog(
            requireContext(),
            R.layout.hms_dialog_call_no_network,
            msg,
            "重新拨打"
        ) { isPositive ->
            if (isPositive) {
                if (!HmsApplication.isNetworkConn()) {
                    Handler().postDelayed({retryCallDoctorCall()}, 100)
                } else {
                    when {
                        // 通话中
                        DoctorCallManager.inCallSession -> {
                            val msg = resources.getString(R.string.doctor_call_warning_on_calling_msg)
                            HMSDialogUtils.showHMSDoctorPhoneExitsDialog(
                                requireContext(),
                                R.layout.hms_dialog_doctor_phone_exist,
                                msg,
                                "知道了"
                            ) {}
                        }

                        else -> {
                            if (isPlaying()) {
                                TTSHelper.stop()
                            }
                            val weakContext = WeakReference(requireActivity())
                            DoctorCallManager.call(weakContext.get())
                        }
                    }
                }
            }
        }
    }

    /**
     * 游客模式UI
     */
    fun initVisitorModeUI() {
        // 游客模式初始化
        // 健康状态图片和健康状态文字
        binding.tvHealthStatus.text = getString(R.string.binding_health_account)
        binding.ivHealthStatus.background =
            resources.getDrawable(R.drawable.health_status_disable)
        // 接口更新时间
        binding.tvDataUpdateTime.visibility = View.GONE
        // 设置不可见
        binding.ivSettings.visibility = View.GONE
        // 健康报告隐藏
        binding.btnHealthReport.visibility = View.GONE
        // 去登陆打开
        binding.btnToLogin.visibility = View.VISIBLE
        // 去授权隐藏
        binding.btnToAuthorization.visibility = View.GONE
        // 分数动效组件隐藏
        binding.hsView.visibility = View.GONE

        binding.tvPrivacyTitleText.visibility = View.GONE
        binding.homeTopLeftLayout.visibility = View.VISIBLE
    }

    /**
     * 私密模式UI
     */
    private fun initPrivacyUI() {
        binding.ivHealthStatus.background = resources.getDrawable(R.drawable.health_status_disable)
        binding.tvPrivacyTitleText.visibility = View.VISIBLE
        binding.hsView.visibility = View.GONE // 事实健康数据得分视图
        binding.homeTopLeftLayout.visibility = View.GONE
        binding.ivSettings.visibility = View.GONE
        binding.btnHealthReport.visibility = View.GONE
//        binding.tvHomeScore.visibility = View.GONE
        binding.btnToLogin.visibility = View.GONE
        binding.btnToAuthorization.visibility = View.GONE
    }

    /**
     * 无网络状态
     */
    private fun initNoNetUI() {
        binding.ivHealthStatus.background = resources.getDrawable(R.drawable.health_status_disable)
        binding.hsView.visibility = View.GONE // 事实健康数据得分视图
        // 隐藏登录按钮
        binding.btnToLogin.visibility = View.GONE
        // 健康周报
        binding.btnHealthReport.visibility = View.GONE
        // 去授权
        binding.btnToAuthorization.visibility = View.GONE
        // 更新时间
        binding.tvDataUpdateTime.visibility = View.GONE
        // 设置按钮
        binding.ivSettings.visibility = View.GONE
    }

    private fun initNormalUI() {
        if (HmsApplication.isNetworkConn()) {
            (activity as? MainActivity)?.let { mainActivity ->
                if (mainActivity.isCanRefresh) {
                    //如果是默认值，蔡显示正在加载数据中
                    if (binding.tvHealthStatus.text == context?.resources?.getString(R.string.hms_widget_tip_for_no_login)){
                        binding.tvHealthStatus.text = context?.resources?.getString(R.string.loading_for_health_data)
                    }
                }
            }
        } else {
            // 无网络时，显示无网络提示、无分数图片
            binding.tvHealthStatus.text = context?.resources?.getString(R.string.network_error_tips)
            binding.hsView.visibility = View.GONE
            binding.tvDataUpdateTime.visibility = View.GONE
            binding.btnHealthReport.visibility = View.GONE
            binding.ivHealthStatus.background = resources.getDrawable(R.drawable.health_status_disable)
        }
        // 登录模式不显示私密模式提示语
        binding.tvPrivacyTitleText.visibility = View.GONE
        binding.homeTopLeftLayout.visibility = View.VISIBLE
        // 显示设置
        binding.ivSettings.visibility = View.VISIBLE
        // 隐藏登录按钮
        binding.btnToLogin.visibility = View.GONE

        //  如果所有主权限已授权（心率、血氧、血压、压力、睡眠、体温）
        if (AuthorizationUtil.hasMainPrivillegesAuth()) {
            binding.btnToAuthorization.visibility = View.GONE
            // 默认不可见；获取实时健康数据时才设置健康周报按钮可见
//            binding.btnHealthReport.visibility=View.GONE // 健康周报

        } else {
            binding.btnToAuthorization.visibility = View.VISIBLE
//            binding.btnHealthReport.visibility = View.GONE // 健康周报
        }
    }

    private fun registerBroadcast() {
        val intentFilter = IntentFilter().apply {
            addAction(HMSAction.ACTION_HMS_UPDATE_VEHICLE_SERVICE)
            addAction(HMSAction.ACTION_CANCEL_PLAY_HEALTH_TIPS)
        }
        requireActivity()?.registerReceiver(reciever, intentFilter)
    }

    private fun unregisterBroadcast() {
        activity?.unregisterReceiver(reciever)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding.recyclerView.adapter = null
        backgroundThread?.stopThread()
        backgroundThread = null
//        ToastUtil.cancelToast()
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterBroadcast()
        mainLooperHandler.removeCallbacksAndMessages(null)
        // 系统TTS转语音功能
        TTSHelper.release()

    }

}