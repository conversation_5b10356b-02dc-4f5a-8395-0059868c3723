package com.healthlink.hms.fragment


import android.app.Dialog
import android.graphics.Typeface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.blankj.utilcode.util.ClickUtils
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.R
import com.healthlink.hms.activity.card.HMSCardFragmentInteractWithAcInterface
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.databinding.FragmentTempTimeBinding
import com.healthlink.hms.fragment.viewmodel.TempFragmentModel
import com.healthlink.hms.ktExt.dp
import com.healthlink.hms.mvvm.model.BaseResponse
import com.healthlink.hms.server.data.dto.HealthDataStatusDTO
import com.healthlink.hms.server.data.dto.HealthTempSummaryDTO
import com.healthlink.hms.server.data.dto.TempCard1DTO
import com.healthlink.hms.server.data.dto.TempCard2DTO
import com.healthlink.hms.server.data.dto.TempItemDTO
import com.healthlink.hms.server.data.dto.TempItemResponseDTO
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.HMSDialogUtils
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.TimeUtils
import com.healthlink.hms.utils.TimeUtils.resetWMDateTime
import com.healthlink.hms.utils.TimeUtils.resetYDateTime
import com.healthlink.hms.utils.getPrivacyModeDate
import com.healthlink.hms.viewmodels.MainViewModel
import com.healthlink.hms.views.ImmersiveDialog
import com.healthlink.hms.views.MiddleEllipsesTextView
import java.lang.ref.WeakReference
import java.util.Calendar

/**
 *@Author: 付仁秀
 *@Description：
 **/
class TempTimeFragment : BaseCardFragment<FragmentTempTimeBinding, MainViewModel>(
    MainViewModel::class.java,
    R.layout.fragment_temp_time
), MiddleEllipsesTextView.UpdateSeeMore {
    private var chartDayList = arrayListOf<TempItemDTO>()
    private var chartWeekList = arrayListOf<TempItemDTO>()
    private var chartMonthList = arrayListOf<TempItemDTO>()
    private var chartYearList = arrayListOf<TempItemDTO>()
    private var chartWeekHealthList: HealthTempSummaryDTO? = null
    private var chartMonthHealthList: HealthTempSummaryDTO? = null
    private var chartYearHealthList: HealthTempSummaryDTO? = null
    private var reqMap = mapOf<String, String>()
    private var healthAdviceStr = ""
    private var introString = ""
    private var isNodataMode = false
    private var currentDateStr = ""
    private var isProvacyMode = false

    private var fragmentDataModel = TempFragmentModel()



    companion object {
        private const val TAG = "TempTimeFragment"
        private const val ARG_PARAM_TYPE = "ARG_PARAM_TYPE"
        private val fragmentInteractWithAC
            get() = _fragmentInteractWithAC?.get()
        private var _fragmentInteractWithAC: WeakReference<HMSCardFragmentInteractWithAcInterface>? =
            null
        private lateinit var mUserId: String

        fun newInstance(
            cartTimeType: TimeCode,
            userId: String,
            interact: HMSCardFragmentInteractWithAcInterface
        ): TempTimeFragment {
            val fragment = TempTimeFragment()
            val args = Bundle()
            args.putString(ARG_PARAM_TYPE, cartTimeType.timeCode)
            fragment.arguments = args
            _fragmentInteractWithAC = WeakReference(interact)
            mUserId = userId
            return fragment
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            cardTimeType = it.getString(ARG_PARAM_TYPE)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initUI()
        initData()
        // 恢复数据
        if(savedInstanceState!=null){
            restoreDataIfPossible(savedInstanceState)
        }
    }

    private fun restoreDataIfPossible(savedInstanceState: Bundle) {
        try {
            var fragmentSavedData = savedInstanceState.getString(KEY_SAVED_DATA_SUMMARY)
            if (fragmentSavedData != null) {
                var fragmentDataModel =
                    gson.fromJson(fragmentSavedData, TempFragmentModel::class.java)
                if(fragmentDataModel!=null){
                    this.fragmentDataModel = fragmentDataModel
                    binding.svContainer.scrollY = this.fragmentDataModel.scollY
                    Log.i(TAG, "init TempTimeFragmnet data from saved fragment success.")
                }
            }

            if(savedInstanceState.getBoolean(KEY_SHOW_DIALOG_FLAG)) {
                showExplainDialog()
            }
        }catch (ex: Exception){
            Log.i(TAG, "init TempTimeFragmnet data from saved fragment fail. error : ${ex.message}")
        }
    }

    override fun sendRequest(userId: String, timeCode: String) {
        reqMap = mapOf(
            "userId" to userId,
            "unit" to timeCode
        )
        if (!HmsApplication.isPrivacyModeEnabled()) {
            resetData()
            if (!HmsApplication.isNetworkConn()) {
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            } else {
                if (isDataReady)
                    showLoading()


                if(this.fragmentDataModel!=null
                    && this.fragmentDataModel.healthData!=null
                    && this.fragmentDataModel.healthData!!.code == "0"){
                    when (cardTimeType) {

                        TimeCode.TIME_CODE_DAY.timeCode -> {
                            processHealthDataDay(fragmentDataModel.healthData!!)
                        }

                        TimeCode.TIME_CODE_WEEK.timeCode -> {
                            processHealthDataWeek(fragmentDataModel.healthData!!)
                        }

                        TimeCode.TIME_CODE_MONTH.timeCode -> {
                            processHealthDataMonth(fragmentDataModel.healthData!!)
                        }

                        TimeCode.TIME_CODE_YEAR.timeCode -> {
                            processHealthDataYear(fragmentDataModel.healthData!!)
                        }
                    }
                }else {
                    viewModel.getTempDetailData(reqMap)
                }
            }
        } else initPrivacyUI(timeCode)
    }

    override fun sendDataReadyRequest(userId: String, timeCode: String) {
        when (timeCode) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Bodytemperaturereports_Daytab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
                binding.cTempWmy.setXData(
                    null,
                    TimeCode.TIME_CODE_DAY.timeCode
                )
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Bodytemperaturereports_Weektab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
                binding.cTempWmy.setXData(
                    TimeUtils.getWeekListStr(),
                    TimeCode.TIME_CODE_WEEK.timeCode
                )
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Bodytemperaturereports_Mouthtab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
                binding.cTempWmy.setXData(
                    TimeUtils.getMonthListStr(),
                    TimeCode.TIME_CODE_MONTH.timeCode
                )
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                DataTrackUtil.dtClick(
                    "Health_Bodytemperaturereports_Yeartab_Click",
                    DataTrackUtil.userIDMap(userId)
                )
                binding.cTempWmy.setXData(
                    null,
                    TimeCode.TIME_CODE_YEAR.timeCode
                )
            }
        }
        if (!HmsApplication.isPrivacyModeEnabled()) {
            if (!HmsApplication.isNetworkConn()) {
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            } else {
                resetData()
                showLoading()
                setScrollEnable(binding.svContainer, false)
                viewModel.getHistoryStatusData(userId)
            }
        } else initPrivacyUI(timeCode)
    }

    private fun resetData() {
        moveToBottomRelative(binding.sleepHourValue)
        moveToBottomRelative(binding.privacyPlaceholderC12)
        moveToBottomRelative(binding.tvSpo2MinValue)
        moveToBottomRelative(binding.privacyPlaceholderC22)
        binding.sleepHourValue.text = "--"
        binding.tvSpo2MinValue.text = "--"
        binding.privacyPlaceholderC11.visibility = View.GONE
        binding.privacyPlaceholderC12.text = ""
        binding.privacyPlaceholderC21.visibility = View.GONE
        binding.privacyPlaceholderC22.text = ""
        binding.tvNormalPer.text = "0%"
        binding.tvLowPer.text = "0%"
        binding.tvHighPer.text = "0%"
    }

    override fun onResume() {
        super.onResume()
        isProvacyMode = HmsApplication.isPrivacyModeEnabled()
        if (!isProvacyMode) setScrollEnable(binding.svContainer)
        if (isDataReady)
            sendRequest(mUserId, cardTimeType!!)
        else
            handler.post(runnable)
    }

    override fun onPause() {
        super.onPause()
        handler.removeCallbacks(runnable)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)

        // 保存数据
        if(this.fragmentDataModel!=null
            && this.fragmentDataModel.healthData!=null
            && this.fragmentDataModel.healthData!!.code == "0"
            && fragmentDataModel.healthSummaryResponse!=null
            && fragmentDataModel.healthSummaryResponse!!.code == "0"
        ){
            fragmentDataModel.scollY = binding.svContainer.scrollY
            Log.i(TAG, "fragmentDataModel.isShowExplainDialog = ${fragmentDataModel.isShowExplainDialog}")
            outState.putString(KEY_SAVED_DATA_SUMMARY, gson.toJson(fragmentDataModel));
        }

        outState.putBoolean(KEY_SHOW_DIALOG_FLAG, fragmentDataModel.isShowExplainDialog)

    }

    private val runnable = UpdateRunnable(this)
    private class UpdateRunnable(private val fragment: TempTimeFragment) : Runnable {
        private val weakFragment = WeakReference(fragment)

        override fun run() {
            val fragment = weakFragment.get()
            if (fragment != null && !fragment.isDetached) {
                if (!fragment.isDataReady) {
                    fragment.sendDataReadyRequest(fragment.mUserId, fragment.cardTimeType!!)
                    // 15 秒后再次调用
                    if (HmsApplication.isNetworkConn())
                        fragment.handler.postDelayed(this, 15000)
                }
            }
        }
    }

//    private val runnable = object : Runnable {
//        private val weekFragment = WeakReference(this@TempTimeFragment)
//        override fun run() {
//            val fragment = weekFragment.get()
//            if (fragment != null && !fragment.isDetached) {
//                if (!fragment.isDataReady) {
//                    fragment.sendDataReadyRequest(fragment.mUserId, fragment.cardTimeType!!)
//                    // 15 秒后再次调用
//                    if (HmsApplication.isNetworkConn())
//                        fragment.handler.postDelayed(this, 15000)
//                }
//            }
//        }
//    }

    fun getDataReady() {
        isDataReady = true
        handler.removeCallbacks(runnable)
        sendRequest(mUserId, cardTimeType!!)
    }

    fun readyDataNoAuth() {
        handler.removeCallbacks(runnable)
        showNoAuthView()
        fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
    }

    private fun initData() {
        viewModel.healthHistoryData.observe(viewLifecycleOwner) {
            val notReadyText =
                "体温${requireContext().resources.getString(R.string.text_data_not_ready)}"
            if (it.code == "0" && it.data != null) {
                val statusList = it.data?.dataStatusList
                if (statusList.isNullOrEmpty()) //如果是空的 也认为是有数据的
                {
                    getDataReady()
                    return@observe
                }
                var status: HealthDataStatusDTO? = null
                if (!statusList.isNullOrEmpty()) {
                    val statusArray = statusList.filter { it.dataType == "temperatureRead" }
                    if (!statusArray.isNullOrEmpty()) {
                        status = statusArray[0]
                    } else {
                        getDataReady()
                        return@observe
                    }
                }
                if (status != null) {
                    when (cardTimeType) {
                        TimeCode.TIME_CODE_DAY.timeCode -> {
                            if (status.dayDataStatus == null || status.dayDataStatus == 2) {
                                //如果返回值是null  也认为是有值的
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_WEEK.timeCode -> {
                            if (status.weekDataStatus == null || status.weekDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_MONTH.timeCode -> {
                            if (status.monthDataStatus == null || status.monthDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }

                        TimeCode.TIME_CODE_YEAR.timeCode -> {
                            if (status.yearDataStatus == null || status.yearDataStatus == 2) {
                                getDataReady()
                            } else {
                                showNoAuthView(notReadyText)
                            }
                        }
                    }
                } else {
                    showNetErrorOrSettingView()
                    fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
                }
            } else if (it.code == "5") {
                showNoAuthView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            } else {
                // 无网络或者刷新失败处理
                showNetErrorOrSettingView()
                fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            }
        }


        viewModel.tempSpO2SummeryData.observe(requireActivity()) {
            this.fragmentDataModel.healthSummaryResponse = it
            processSummaryData(it)
        }

        viewModel.tempDayChartData.observe(requireActivity()) {
            this.fragmentDataModel.healthData = it
            processHealthDataDay(it)
        }

        viewModel.tempWeekChartData.observe(requireActivity()) {
            this.fragmentDataModel.healthData = it
            processHealthDataWeek(it)
        }

        viewModel.tempMonthChartData.observe(requireActivity()) {
            this.fragmentDataModel.healthData = it
            processHealthDataMonth(it)
        }

        viewModel.tempYearChartData.observe(requireActivity()) {
            this.fragmentDataModel.healthData = it
            processHealthDataYear(it)
        }

        registScrollListener()
    }

    private fun processHealthDataYear(it: BaseResponse<TempItemResponseDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            chartYearList = it.data!!.nodeList
            if (!chartYearList.isNullOrEmpty() && isDataListNotEmpty(chartYearList)) {
                //   this.type = getBarType(chartYearList)
                var extreme = calExtremePair(chartYearList)
                val showCardDTO = TempCard1DTO(
                    getFetchtime(chartYearList.last().createTime),//startTime 有空值
                    getAvgStr(chartYearList.last().avg),
                    getMinMaxStr(chartYearList.last().min, chartYearList.last().max),
                    getMinMaxStr(extreme.first, extreme.second)
                )
                val tempRangeDTO = TempCard1DTO(
                    getFetchtime(chartYearList.last().createTime),//startTime 有空值
                    getAvgStr(chartYearList.last().avg),
                    getMinMaxStr(chartYearList.last().min, chartYearList.last().max),
                    getMinMaxStr(extreme.first, extreme.second)
                )
                binding.cardShowInfo = showCardDTO
                binding.tempRangeDTO = tempRangeDTO
                val map = mutableMapOf<String, String>()
                map.putAll(reqMap)
                map.put("startTime", it.data!!.startTime)
                map.put("endTime", it.data!!.endTime)

                initChartData()

                processLoadingSummaryData(map)
            } else {
                initChartNoData(TimeCode.TIME_CODE_YEAR.timeCode)
            }
        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            setScrollEnable(binding.svContainer)
        }, 500)
    }

    private fun processHealthDataMonth(it: BaseResponse<TempItemResponseDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            chartMonthList = it.data!!.nodeList
            if (!chartMonthList.isNullOrEmpty() && isDataListNotEmpty(chartMonthList)) {
                var extreme = calExtremePair(chartMonthList)
                val showCardDTO = TempCard1DTO(
                    getFetchtime(chartMonthList.last().createTime),//startTime 有空值
                    getAvgStr(chartMonthList.last().avg),
                    getMinMaxStr(chartMonthList.last().min, chartMonthList.last().max),
                    getMinMaxStr(extreme.first, extreme.second)
                )
                val tempRangeDTO = TempCard1DTO(
                    getFetchtime(chartMonthList.last().createTime),//startTime 有空值
                    getAvgStr(chartMonthList.last().avg),
                    getMinMaxStr(chartMonthList.last().min, chartMonthList.last().max),
                    getMinMaxStr(extreme.first, extreme.second)
                )
                binding.cardShowInfo = showCardDTO
                binding.tempRangeDTO = tempRangeDTO
                val map = mutableMapOf<String, String>()
                map.putAll(reqMap)
                map.put("startTime", it.data!!.startTime)
                map.put("endTime", it.data!!.endTime)
                initChartData()

                processLoadingSummaryData(map)

            } else {
                initChartNoData(TimeCode.TIME_CODE_MONTH.timeCode)
            }
        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            setScrollEnable(binding.svContainer)
        }, 400)
    }

    private fun processHealthDataWeek(it: BaseResponse<TempItemResponseDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            chartWeekList = it.data!!.nodeList
            if (!chartWeekList.isNullOrEmpty() && isDataListNotEmpty(chartWeekList)) {
                //  this.type = getBarType(chartWeekList)
                var extreme = calExtremePair(chartWeekList)
                val showCardDTO = TempCard1DTO(
                    getFetchtime(chartWeekList.last().createTime),//startTime 有空值
                    getAvgStr(chartWeekList.last().avg),
                    getMinMaxStr(chartWeekList.last().min, chartWeekList.last().max),
                    getMinMaxStr(extreme.first, extreme.second)
                )
                val tempRangeDTO = TempCard1DTO(
                    getFetchtime(chartWeekList.last().createTime),//startTime 有空值
                    getAvgStr(chartWeekList.last().avg),
                    getMinMaxStr(chartWeekList.last().min, chartWeekList.last().max),
                    getMinMaxStr(extreme.first, extreme.second)
                )
                binding.cardShowInfo = showCardDTO
                binding.tempRangeDTO = tempRangeDTO
                val map = mutableMapOf<String, String>()
                map.putAll(reqMap)
                map.put("startTime", it.data!!.startTime)
                map.put("endTime", it.data!!.endTime)
                initChartData()

                processLoadingSummaryData(map)
            } else {
                initChartNoData(TimeCode.TIME_CODE_WEEK.timeCode)
            }
        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            setScrollEnable(binding.svContainer)
        }, 400)
    }

    private fun processHealthDataDay(it: BaseResponse<TempItemResponseDTO>) {
        if (it.code == "0" && it.data != null) {
            binding.svContainer.visibility = View.VISIBLE
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            chartDayList = it.data!!.nodeList
            if (!chartDayList.isNullOrEmpty() && isDataListNotEmpty(chartDayList)) {
                var extreme = calExtremePair(chartDayList)
                val showCardDTO = TempCard1DTO(
                    getFetchtime(chartDayList.last().createTime),//startTime 有空值
                    getAvgStr(chartDayList.last().avg),
                    getMinMaxStr(chartDayList.last().min, chartDayList.last().max),
                    getMinMaxStr(extreme.first, extreme.second)
                )
                val tempRangDTO = TempCard1DTO(
                    getFetchtime(chartDayList.last().createTime),//startTime 有空值
                    getAvgStr(chartDayList.last().avg),
                    getMinMaxStr(chartDayList.last().min, chartDayList.last().max),
                    getMinMaxStr(extreme.first, extreme.second)
                )
                binding.cardShowInfo = showCardDTO
                binding.tempRangeDTO = tempRangDTO
                val map = mutableMapOf<String, String>()
                map.putAll(reqMap)
                val dto = it.data!!
                map.put("startTime", it.data!!.startTime)
                map.put("endTime", it.data!!.endTime)

                initChartData()

                processLoadingSummaryData(map)
            } else {
                initChartNoData(TimeCode.TIME_CODE_DAY.timeCode)
            }
        } else if (it.code == "5") {
            showNoAuthView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        } else {
            showNetErrorOrSettingView()
            fragmentInteractWithAC?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
        }
        Handler(Looper.getMainLooper()).postDelayed({
            hideLoading()
            setScrollEnable(binding.svContainer)
        }, 400)
    }

    /**
     * 判断是否有合理的健康建议数据。如果有，则直接使用，否则去服务器获取
     */
    private fun processLoadingSummaryData(map: MutableMap<String, String>) {
        if(fragmentDataModel==null
            || fragmentDataModel.healthSummaryResponse == null
            || fragmentDataModel.healthSummaryResponse!!.code != "0") {
            viewModel.getHealthTempSummery(map)
        }else{
            processSummaryData(fragmentDataModel.healthSummaryResponse!!)
        }
    }

    private fun processSummaryData(it:BaseResponse<HealthTempSummaryDTO>){
        if (it.code == "0" && it.data != null) {
            binding.healthSpO2SummeryVo = it.data
            binding.svContainer.post {
                binding.svContainer.requestLayout()
            }
            Handler(Looper.getMainLooper()).postDelayed({
                hideLoading()
                setScrollEnable(binding.svContainer)
            }, 100)
            // 重置查看更多按钮为不可见
            binding.healthRiskAll.tvHealthAdviceContent.setIsEllipsized(false)
            it.data!!.healthAdvice?.let { item ->
                healthAdviceStr = item
            }
//                it.data!!.nounExplain?.let { explain ->
//                    if (explain.isNotEmpty()) {
//                        this.introString = explain
//                        binding.ivIntroTips.visibility = View.VISIBLE
//                    }
//                }
            when (cardTimeType) {
                TimeCode.TIME_CODE_DAY.timeCode -> {
                    buildCard3Info(it)
                }

                TimeCode.TIME_CODE_WEEK.timeCode -> {
                    chartWeekHealthList = it.data
                    buildCard3Info(it)
                }

                TimeCode.TIME_CODE_MONTH.timeCode -> {
                    chartMonthHealthList = it.data
                    buildCard3Info(it)
                }

                TimeCode.TIME_CODE_YEAR.timeCode -> {
                    chartYearHealthList = it.data
                    buildCard3Info(it)
                }
            }

//                if (cardTimeType != TimeCode.TIME_CODE_DAY.timeCode) {
//                    var tempH = getLastAdvice(it.data!!)
//                    tempH?.let {
//                        var tempCard2DTO = TempCard2DTO(
//                            it.createTime,
//                            it.normalProp,
//                            it.lowProp,
//                            it.mediumProp
//                        )
//                        binding.cardShowInfo2 = tempCard2DTO
//                    }
//                }

        }
    }

    private fun buildCard3Info(it: BaseResponse<HealthTempSummaryDTO>) {
        var tempCard2DTO = TempCard2DTO(
            it.data!!.createTime,
            it.data!!.normalProp,
            it.data!!.lowProp,
            it.data!!.mediumProp
        )
        binding.cardShowInfo2 = tempCard2DTO
    }

    private fun initUI() {
        initExplain()
        ClickUtils.applySingleDebouncing(binding.ivIntroTips, 1000) {
            showExplainDialog()
        }
        binding.healthRiskAll.tvHealthAdviceContent.setCallback(this)
        binding.healthRiskAll.tvHealthAdviceContent.setEndPercentage(80)
        binding.healthRiskAll.tvSeeMore.text = Html.fromHtml("<u>查看更多</u>")
        val scrollView = binding.svContainer
        scrollView.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY ->
            val height = scrollView.getChildAt(0).height // 获取ScrollView内容的总高度
            val scrollViewHeight = scrollView.height // 获取ScrollView的可见高度
            val diff = (height - scrollViewHeight) * 0.75f //scrollview中判定的距离 动画view位置底部约为总长度的的75%
            if (scrollY >= diff) {
                MMKVUtil.getUserId()?.let {
                    DataTrackUtil.dtScroll(
                        "Health_Bodytemperaturereports_Bottom_Show",
                        DataTrackUtil.userIDMap(it)
                    )
                }
            }
        }
        applyPrivacyStyleChanged(HmsApplication.isPrivacyModeEnabled())

    }

    fun initChartData() {
        isNodataMode = false
        setUnitVisiable(true)
        // 根据不同的cardTimeType 初始化不同的view
        binding.sPrivacyText.visibility = View.GONE



        when (cardTimeType) {
            // 时间类型日 - 折线图
            TimeCode.TIME_CODE_DAY.timeCode -> {
                if (chartDayList.isEmpty()) {
                    binding.cTempWmy.visibility = View.GONE
                    binding.tempNoDataText.visibility = View.VISIBLE
                    return
                }
                binding.cTempWmy.visibility = View.VISIBLE
                binding.tempNoDataText.visibility = View.GONE
                initDayChart()
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                if (chartWeekList.isEmpty()) {
                    binding.cTempWmy.visibility = View.GONE
                    binding.tempNoDataText.visibility = View.VISIBLE
                    return
                }
                binding.cTempWmy.visibility = View.VISIBLE
                binding.tempNoDataText.visibility = View.GONE
                initWeekChart()
            }
//
            TimeCode.TIME_CODE_MONTH.timeCode -> {
                if (chartMonthList.isEmpty()) {
                    binding.cTempWmy.visibility = View.GONE
                    binding.tempNoDataText.visibility = View.VISIBLE
                    return
                }
                binding.cTempWmy.visibility = View.VISIBLE
                binding.tempNoDataText.visibility = View.GONE
                initMonthChart()
            }
//
            TimeCode.TIME_CODE_YEAR.timeCode -> {
                if (chartYearList.isEmpty()) {
                    binding.cTempWmy.visibility = View.GONE
                    binding.tempNoDataText.visibility = View.VISIBLE
                    return
                }
                binding.cTempWmy.visibility = View.VISIBLE
                binding.tempNoDataText.visibility = View.GONE
                initYearChart()
            }

        }

    }

    fun registScrollListener() {
        //无数据部分
        when (cardTimeType) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                currentDateStr = getDayFetchTime()
                binding.cTempWmy.setOnXTextSelectListener { index, xText ->
                    if (isNodataMode)
                        binding.tvSleepDate.text = currentDateStr + " " + xText
                }
            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {
                binding.cTempWmy.setOnXTextSelectListener { index, xText ->
                    if (isNodataMode || (!chartWeekList.isEmpty() && index >= chartWeekList.size)) {
                        var showCardDTO = TempCard1DTO(
                            resetWMDateTime(xText),//startTime 有空值
                            "--",
                            "--",
                            "--"
                        )
                        binding.cardShowInfo = showCardDTO
                    }
                }
            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {
                val monthDayList = TimeUtils.getMonthListStr()
                binding.cTempWmy.setOnXTextSelectListener { index, xText ->
                    try {
                        if (isNodataMode || (!chartMonthList.isEmpty() && index >= chartMonthList.size)) {
                            var showCardDTO = TempCard1DTO(
                                resetWMDateTime(monthDayList[index]),//startTime 有空值
                                "--",
                                "--",
                                "--"
                            )
                            binding.cardShowInfo = showCardDTO
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }

                }
            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                binding.cTempWmy.setOnXTextSelectListener { index, xText ->
                    if (isNodataMode || (!chartYearList.isEmpty() && index >= chartYearList.size)) {
                        var showCardDTO = TempCard1DTO(
                            resetYDateTime(xText),//startTime 有空值
                            "--",
                            "--",
                            "--"
                        )
                        binding.cardShowInfo = showCardDTO
                    } else if (chartYearList.isNotEmpty() && index < chartYearList.size) {
                        val dto = chartYearList[index]
                        if (dto != null && dto.avg == 0F) {
                            var showCardDTO = TempCard1DTO(
                                resetYDateTime(xText),//startTime 有空值
                                "--",
                                "--",
                                "--"
                            )
                            binding.cardShowInfo = showCardDTO
                        }
                    }
                }
            }

        }
        // 有数据部分
        when (cardTimeType) {
            TimeCode.TIME_CODE_DAY.timeCode -> {
                binding.cTempWmy.setOnDaySelectListener { index, item ->
                    var showCardDTO = TempCard1DTO(
                        null,//startTime 有空值
                        "--",
                        "--",
                        "--"
                    )
                    var tempRangedDTO = TempCard1DTO(
                        null,//startTime 有空值
                        "--",
                        "--",
                        "--"
                    )
                    if (item != null) {
                        if (!chartDayList.isNullOrEmpty()) {
                            var extreme = calExtremePair(chartDayList)
                            showCardDTO = TempCard1DTO(
                                getFetchtime(chartDayList[index].createTime),//startTime 有空值
                                getAvgStr(chartDayList[index].avg),
                                getMinMaxStr(chartDayList[index].min, chartDayList[index].max),
                                getMinMaxStr(extreme.first, extreme.second)
                            )
                            tempRangedDTO = TempCard1DTO(
//                                getFetchtime(chartDayList[index].createTime),//startTime 有空值
//                                getAvgStr(chartDayList[index].avg),
                                getMinMaxStr(chartDayList[index].min, chartDayList[index].max),
                                getMinMaxStr(extreme.first, extreme.second)
                            )
                        }
                    } else {
                        var fetchTime = ""
                        if(index>=0&&index<chartDayList.size && chartDayList[index].createTime!=null){
                            fetchTime = chartDayList[index].createTime!!
                        }
                        showCardDTO = TempCard1DTO(
                            fetchTime,//startTime 有空值
                            "--",
                            "--",
                            "--"
                        )
                        tempRangedDTO = TempCard1DTO(
                            null,//startTime 有空值
                            "--",
                            "--",
                            "--"
                        )
                    }
                    binding.cardShowInfo = showCardDTO
//                    binding.tempRangeDTO = tempRangedDTO
                }

            }

            TimeCode.TIME_CODE_WEEK.timeCode -> {

                binding.cTempWmy.setOnDaySelectListener { index, item ->
                    if (chartWeekList.isEmpty() || index >= chartWeekList.size) return@setOnDaySelectListener
                    var showCardDTO = TempCard1DTO(
                        null,//startTime 有空值
                        "--",
                        "--",
                        "--"
                    )
                    var tempCard2DTO = TempCard2DTO(
                        "",
                        "0",
                        "0",
                        "0"
                    )
                    if (item != null) {
                        if (!chartWeekList.isNullOrEmpty()) {
                            var extreme = calExtremePair(chartWeekList)
                            showCardDTO = TempCard1DTO(
                                getFetchtime(chartWeekList[index].createTime),//startTime 有空值
                                getAvgStr(chartWeekList[index].avg),
                                getMinMaxStr(
                                    chartWeekList[index].min,
                                    chartWeekList[index].max
                                ),
                                getMinMaxStr(extreme.first, extreme.second)
                            )

                        }

                    } else {
                        showCardDTO = TempCard1DTO(
                            getFetchtime(chartWeekList[index].createTime),//startTime 有空值
                            "--",
                            "--",
                            "--"
                        )
                    }
                    if (!chartWeekHealthList?.valueList.isNullOrEmpty() && index < chartWeekHealthList?.valueList!!.size) {
                        var tempH = chartWeekHealthList!!.valueList[index]
                        tempCard2DTO = TempCard2DTO(
                            tempH.createTime,
                            tempH.normalProp,
                            tempH.lowProp,
                            tempH.mediumProp
                        )
                    } else {
                        tempCard2DTO = TempCard2DTO(
                            "",
                            "0",
                            "0",
                            "0"
                        )
                    }
//                    binding.cardShowInfo2 = tempCard2DTO
                    binding.cardShowInfo = showCardDTO
                }

            }

            TimeCode.TIME_CODE_MONTH.timeCode -> {

                binding.cTempWmy.setOnDaySelectListener { index, item ->
                    if (chartMonthList.isEmpty() || index >= chartMonthList.size) return@setOnDaySelectListener
                    var showCardDTO = TempCard1DTO(
                        null,//startTime 有空值
                        "--",
                        "--",
                        "--"
                    )
                    var tempCard2DTO = TempCard2DTO(
                        "",
                        "0",
                        "0",
                        "0"
                    )
                    if (item != null) {
                        if (!chartMonthList.isNullOrEmpty()) {
                            var extreme = calExtremePair(chartMonthList)
                            showCardDTO = TempCard1DTO(
                                getFetchtime(chartMonthList[index].createTime),//startTime 有空值
                                getAvgStr(chartMonthList[index].avg),
                                getMinMaxStr(
                                    chartMonthList[index].min,
                                    chartMonthList[index].max
                                ),
                                getMinMaxStr(extreme.first, extreme.second)
                            )

                        }

                    } else {
                        showCardDTO = TempCard1DTO(
                            getFetchtime(chartMonthList[index].createTime),//startTime 有空值
                            "--",
                            "--",
                            "--"
                        )
                    }
                    if (!chartMonthHealthList?.valueList.isNullOrEmpty() && index < chartMonthHealthList?.valueList!!.size) {
                        var tempH = chartMonthHealthList!!.valueList[index]
                        tempCard2DTO = TempCard2DTO(
                            tempH.createTime,
                            tempH.normalProp,
                            tempH.lowProp,
                            tempH.mediumProp
                        )
                    } else {
                        tempCard2DTO = TempCard2DTO(
                            "",
                            "0",
                            "0",
                            "0"
                        )
                    }
//                    binding.cardShowInfo2 = tempCard2DTO
                    binding.cardShowInfo = showCardDTO
                }

            }

            TimeCode.TIME_CODE_YEAR.timeCode -> {
                binding.cTempWmy.setOnDaySelectListener { index, item ->
                    if (chartYearList.isEmpty() || index >= chartYearList.size) return@setOnDaySelectListener
                    var showCardDTO = TempCard1DTO(
                        null,//startTime 有空值
                        "--",
                        "--",
                        "--"
                    )
                    var tempCard2DTO = TempCard2DTO(
                        "",
                        "0",
                        "0",
                        "0"
                    )
                    if (item != null) {
                        if (!chartYearList.isNullOrEmpty()) {
                            var extreme = calExtremePair(chartYearList)
                            showCardDTO = TempCard1DTO(
                                getFetchtime(chartYearList[index].createTime),//startTime 有空值
                                getAvgStr(chartYearList[index].avg),
                                getMinMaxStr(
                                    chartYearList[index].min,
                                    chartYearList[index].max
                                ),
                                getMinMaxStr(extreme.first, extreme.second)
                            )

                        }
                    } else {
                        showCardDTO = TempCard1DTO(
                            getFetchtime(chartYearList[index].createTime),//startTime 有空值
                            "--",
                            "--",
                            "--"
                        )
                    }
                    if (!chartYearHealthList?.valueList.isNullOrEmpty() && index < chartYearHealthList?.valueList!!.size) {
                        val tempH = chartYearHealthList!!.valueList[index]
                        tempCard2DTO = TempCard2DTO(
                            tempH.createTime,
                            tempH.normalProp,
                            tempH.lowProp,
                            tempH.mediumProp
                        )
                    } else {
                        tempCard2DTO = TempCard2DTO(
                            "",
                            "0",
                            "0",
                            "0"
                        )
                    }
//                    binding.cardShowInfo2 = tempCard2DTO
                    binding.cardShowInfo = showCardDTO
                }

            }
        }
    }

    fun initChartNoData(timeCode: String) {
        isNodataMode = true
        setUnitVisiable(false)
        binding.cTempWmy.visibility = View.VISIBLE
        binding.sPrivacyText.visibility = View.GONE
        if (timeCode == TimeCode.TIME_CODE_WEEK.timeCode) {
            binding.cTempWmy.setValue(
                null,
                TimeUtils.getWeekListStr(),
                1,
                timeCode,
            )
        } else if (timeCode == TimeCode.TIME_CODE_MONTH.timeCode) {
            binding.cTempWmy.setValue(
                null,
                TimeUtils.getMonthListStr(),
                1,
                timeCode,
            )
        } else {
            binding.cTempWmy.setValue(
                null,
                null,
                1,
                timeCode,
            )
        }

        if (cardTimeType == TimeCode.TIME_CODE_YEAR.timeCode) {
            binding.tvSleepDate.text = "${Calendar.getInstance()[Calendar.YEAR]}年7月"
        }


    }


    private fun initDayChart() {
        binding.cTempWmy.setValue(
            chartDayList,
            null,
            getBarType(chartDayList),
            TimeCode.TIME_CODE_DAY.timeCode,
        )
    }


    private fun initWeekChart() {
        binding.cTempWmy.setValue(
            chartWeekList,
            TimeUtils.getWeekListStr(),
            getBarType(chartWeekList),
            TimeCode.TIME_CODE_WEEK.timeCode
        )

    }

    private fun initMonthChart() {
        binding.cTempWmy.setValue(
            chartMonthList,
            TimeUtils.getMonthListStr(),
            getBarType(chartMonthList),
            TimeCode.TIME_CODE_MONTH.timeCode
        )

    }

    private fun initYearChart() {
        binding.cTempWmy.setValue(
            chartYearList,
            null,
            getBarType(chartYearList),
            TimeCode.TIME_CODE_YEAR.timeCode
        )

    }

    private fun showExplainDialog() {
        showHmsDialog(R.layout.hms_dialog_tips_small, "体温说明", introString)
    }

    private var dialog: Dialog? = null

    /**
     * 显示自定义对话框
     */
    fun showHmsDialog(layoutId: Int, title: String, message: String?) {
        // 创建自定义视图
        val view = LayoutInflater.from(requireActivity()).inflate(layoutId, null)
        val contentView = view.findViewById<RelativeLayout>(R.id.dialog_content)
        var btnPositive = view.findViewById<Button>(R.id.positiveButton)
        var tvMessage = view.findViewById<TextView>(R.id.textView)
        var tvTitle = view.findViewById<TextView>(R.id.tv_tips_title_small)
        tvTitle.text = title
        if (message != null && !message.isNullOrBlank()) {
            tvMessage.setText(message)
        }
        // 创建并显示对话框
        dialog = ImmersiveDialog(
            requireContext(),
            R.style.MyDialogStyle
        )
        //dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog!!.setContentView(view)
        dialog!!.setOnShowListener {
            HMSDialogUtils.doDialogAnimateEnter(dialog!!)
            view.setOnTouchListener { v: View?, event: MotionEvent ->
                if (event.action == MotionEvent.ACTION_DOWN && ((!(event.y.toInt() in contentView.top..contentView.bottom)) || (!(event.x.toInt() in contentView.left..contentView.right)))) {
                    fragmentDataModel.isShowExplainDialog = false
                    dialog?.dismiss()
                }
                false
            }
        }
        // 设置按钮点击事件
        btnPositive.setOnClickListener {
            fragmentDataModel.isShowExplainDialog = false
            dialog?.dismiss()
        }
        dialog!!.show()
        fragmentDataModel.isShowExplainDialog = true
    }

    fun calExtremePair(ori: ArrayList<TempItemDTO>): Pair<Float, Float> {
        var maxTemp = ori[0].avg
        var minTemp = ori[0].avg
        var needInit = true
        ori.forEach {
            if (it.avg == 0f) return@forEach
            if (needInit) {
                maxTemp = it.max
                minTemp = it.min
                needInit = false
            } else {
                if (it.max > maxTemp)
                    maxTemp = it.max
                if (it.min < minTemp)
                    minTemp = it.min
            }
        }
        return Pair(minTemp, maxTemp)
    }

    fun getBarType(oriList: ArrayList<TempItemDTO>): Int {
        var max = 0f
        var min = 0f
        var needInit = true
        oriList.forEachIndexed { index, item ->
            if (item.avg == 0f) return@forEachIndexed
            if (needInit) {
                max = item.max
                min = item.min
                needInit = false
            } else {
                if (item.max > max)
                    max = item.max
                if (item.min < min)
                    min = item.min
            }
        }
        //最值修正 如果最值超过范围  则修正至35f-45f
        if (max in 35f..39f) {
            if (min in 35f..39f)
                return 1
            else
                return 2
        } else {
            return 2
        }
    }

    override fun update(isEllipsized: Boolean, viewID: Int) {
        if (isEllipsized) {
            binding.healthRiskAll.cardAdviceMore.visibility = View.VISIBLE
            binding.healthRiskAll.cardAdviceMore.setOnClickListener { v ->
                HMSDialogUtils.showHMSNotiDialog(
                    requireContext(),
                    R.layout.hms_dialog_see_more,
                    "健康建议",
                    healthAdviceStr,
                    "知道了"
                ) { isPositive ->
                }
            }
        } else {
            binding.healthRiskAll.cardAdviceMore.visibility = View.GONE
        }
    }

    fun getMinMaxStr(min: Float, max: Float): String {
        val minStr = String.format("%.1f", min)
        val maxStr = String.format("%.1f", max)
        return "${minStr}-${maxStr}"
    }

    fun getAvgStr(avg: Float): String {
        return String.format("%.1f", avg)
    }

    fun getLastAdvice(advice: HealthTempSummaryDTO): HealthTempSummaryDTO? {
        for (i in advice.valueList.size - 1 downTo 0) {
            if (advice.valueList[i].temperatureAvg != null)
                return advice.valueList[i]
        }
        return null
    }

    fun initPrivacyUI(timeCode: String) {
        setScrollEnable(binding.svContainer, false)
        binding.sPrivacyText.visibility = View.VISIBLE
        binding.tempNoDataText.visibility = View.GONE
        binding.cTempWmy.visibility = View.GONE
        binding.tvSleepDate.text = getPrivacyModeDate(timeCode)
        binding.sleepHourValue.text = "***"
        binding.privacyPlaceholderC11.visibility = View.VISIBLE
        binding.privacyPlaceholderC11.text = "-"
        binding.sleepHourUnit.visibility = View.INVISIBLE
        binding.privacyPlaceholderC12.visibility = View.VISIBLE
        binding.privacyPlaceholderC12.text = "***"
        binding.tvSpo2MinValue.text = "***"
        binding.privacyPlaceholderC21.visibility = View.VISIBLE
        binding.privacyPlaceholderC21.text = "-"
        binding.tvSpo2MinUnit.visibility = View.INVISIBLE
        binding.privacyPlaceholderC22.visibility = View.VISIBLE
        binding.privacyPlaceholderC22.text = "***"
        binding.tvNormalPer.text = "***%"
        binding.tvLowPer.text = "***%"
        binding.tvHighPer.text = "***%"
        moveToTopRelative(binding.sleepHourValue, 9)
        moveToTopRelative(binding.privacyPlaceholderC12, 9)
        moveToTopRelative(binding.tvSpo2MinValue, 9)
        moveToTopRelative(binding.privacyPlaceholderC22, 9)
        // 健康建议在私密模式下不展示
        binding.healthSpO2SummeryVo = null
        binding.svContainer.visibility = View.VISIBLE

    }

    private fun applyPrivacyStyleChanged(isPrivacyMode: Boolean) {
        binding.llIntroContainer.apply {
            val params = layoutParams as ViewGroup.MarginLayoutParams
            params.bottomMargin = if (isPrivacyMode) 30f.dp.toInt() else 0f.dp.toInt()
            layoutParams = params
        }
        binding.sleepHourUnit.visibility = if (isPrivacyMode) View.INVISIBLE else View.VISIBLE
        binding.tvSpo2MinUnit.visibility = if (isPrivacyMode) View.INVISIBLE else View.VISIBLE
        binding.privacyPlaceholderC11.visibility = if (isPrivacyMode) View.VISIBLE else View.GONE
        binding.privacyPlaceholderC12.visibility = if (isPrivacyMode) View.VISIBLE else View.GONE
        binding.privacyPlaceholderC21.visibility = if (isPrivacyMode) View.VISIBLE else View.GONE
        binding.privacyPlaceholderC22.visibility = if (isPrivacyMode) View.VISIBLE else View.GONE
        listOf(binding.tvNormalPer, binding.tvLowPer, binding.tvHighPer).forEach {
            it.setTypeface(it.typeface, if (isPrivacyMode) Typeface.BOLD else Typeface.NORMAL)
        }
        if (!isPrivacyMode) {
            binding.tvSleepDate.text = ""
            binding.sleepHourValue.text = "--"
            binding.tvSpo2MinValue.text = "--"
        }
    }

    fun getFetchtime(time: String?): String {
        return time ?: ""
    }

    fun isDataListNotEmpty(dataList: ArrayList<TempItemDTO>): Boolean {
        dataList.forEach {
            if (it.avg != 0f) return true
        }
        return false
    }

    fun setUnitVisiable(sw: Boolean) {
        if (sw) {
            binding.sleepHourUnit.visibility = View.VISIBLE
            binding.tvSpo2MinUnit.visibility = View.VISIBLE
        } else {
            binding.sleepHourUnit.visibility = View.GONE
            binding.tvSpo2MinUnit.visibility = View.GONE
        }
    }

    fun getDayFetchTime(): String {
        val calendar = Calendar.getInstance()
        val month = calendar[Calendar.MONTH] + 1 // 月份是从0开始的，需要加1
        val day = calendar[Calendar.DAY_OF_MONTH]
        return "${month}月${day}日"
    }

    override fun onPrivacyModeChange(provacyMode: Boolean) {
        applyPrivacyStyleChanged(provacyMode)
        if (isProvacyMode != provacyMode) {
            //当前模式与变化模式不同
            binding.svContainer.scrollY = 0
            setScrollEnable(binding.svContainer)
            sendRequest(mUserId, cardTimeType!!)
            isProvacyMode = provacyMode
        } else {
            setScrollEnable(binding.svContainer, false)
        }
    }

    fun initExplain() {
        val tips = binding.ivIntroTips
        tips.visibility = View.VISIBLE
//        tips.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener{
//            override fun onGlobalLayout() {
//                tips.viewTreeObserver.removeGlobalOnLayoutListener { this }
//                val parentView=tips.parent as View
//                val rect = Rect()
//                tips.getHitRect(rect)
//                rect.top-=24
//                rect.bottom+=100
//                rect.left-=100
//                rect.right+=24
//                parentView.touchDelegate = TouchDelegate(rect, tips)
//            }
//        })
        viewAddOnGlobalLayoutListener(tips)
        this.introString = getString(R.string.description_temperature)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        try {
            dialog?.dismiss()
            dialog = null
            viewRemoveOnGlobalLayoutListener(binding.ivIntroTips)
            binding.healthRiskAll.tvHealthAdviceContent.setCallback(null)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}