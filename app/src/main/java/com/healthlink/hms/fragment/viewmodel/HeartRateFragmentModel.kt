package com.healthlink.hms.fragment.viewmodel

/**
 * Created by imaginedays on 2024/6/25
 * 心率二级页面的ViewModel
 */
import com.healthlink.hms.mvvm.model.BaseResponse
import com.healthlink.hms.server.data.dto.charts.HealthSummeryDTO
import com.healthlink.hms.server.data.dto.charts.heartrate.HeartRateStatDTO

class HeartRateFragmentModel{

    /**
     * 页面滚动的Y值
     */
    var scollY : Int = 0

    /**
     * 心率数据
     */
    var healthData : BaseResponse<HeartRateStatDTO>? = null

    /**
     * 健康建议响应对象
     */
    var healthSummaryResponse : BaseResponse<HealthSummeryDTO>? = null

    var isShowExplainDialog : Boolean = false
}


