package com.healthlink.hms.fragment

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Rect
import android.graphics.drawable.Animatable2
import android.graphics.drawable.AnimatedVectorDrawable
import android.graphics.drawable.Drawable
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.TouchDelegate
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.viewpager2.widget.ViewPager2
import com.google.gson.Gson
import com.gwm.widget.GwmButton
import com.healthlink.hms.R
import com.healthlink.hms.activity.HMSBaseCardDetailActivity
import com.healthlink.hms.activity.card.HMSCardFragmentInteractWithAcInterface
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.server.data.dto.charts.HealthSummeryBaseDTO
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.views.StretchScrollView

abstract class BaseCardFragment<VB : ViewDataBinding, VM : ViewModel>(
    private val viewModelClass: Class<VM>, private val layoutId: Int
) : Fragment() {
    abstract fun sendRequest(userId: String, timeCode: String)
    abstract fun sendDataReadyRequest(userId: String, timeCode: String)
    protected lateinit var binding: VB
    protected lateinit var viewModel: VM
    private var mContainer: ViewGroup? = null
    private var mContainerParent: ViewGroup? = null
    protected var mUserId: String = ""
    protected var cardTimeType: String? = null

    // 无网络或者设置网络
    private var netErrorView: View? = null
    private var noAuthView: View? = null

    /**
     * 服务器端的数据状态是否已经完成。
     */
    protected var isDataReady = false
    protected val handler = Handler(Looper.getMainLooper())

    // loading 动画
    private var loadingView: View? = null
    private var drawable: AnimatedVectorDrawable? = null
    private var animationCallback: Animatable2.AnimationCallback? = null

    private var myReceiver: BroadcastReceiver? = null
    private var globalLayoutListener: ViewTreeObserver.OnGlobalLayoutListener? = null

    /**
     * 序列化使用
     */
    var gson = Gson()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DataBindingUtil.inflate(inflater, layoutId, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        viewModel = ViewModelProvider(this)[viewModelClass]
//        receiveBroadcast()
        mUserId = MMKVUtil.getUserId()!!
        return binding.root
    }

    companion object {
        const val KEY_SAVED_DATA_SUMMARY = "KEY_SAVED_DATA_SUMMARY"
        const val KEY_SHOW_DIALOG_FLAG = "key_show_dialog_flag"
        fun isHealthAdviceVisible(baseDTO: HealthSummeryBaseDTO?): Boolean {
            if (baseDTO == null) {
                return false
            }
            if (baseDTO.singleDeclaration == null && baseDTO.riskDescription == null && baseDTO.healthAdvice == null) {
                return false
            }
            return true
        }
    }

    protected fun moveToTop(view: TextView, marginTop: Int = 2) {
        if (view.layoutParams !is LinearLayout.LayoutParams) {
            return
        }
        val layoutParams = view.layoutParams as LinearLayout.LayoutParams
        layoutParams.topMargin = marginTop
        layoutParams.gravity = Gravity.TOP
        view.layoutParams = layoutParams
    }

    protected fun moveToTopRelative(view: TextView, marginTop: Int = 5) {
        if (view.layoutParams !is RelativeLayout.LayoutParams) {
            return
        }
        val layoutParams = view.layoutParams as RelativeLayout.LayoutParams
        layoutParams.addRule(RelativeLayout.ALIGN_PARENT_TOP, RelativeLayout.TRUE)
        layoutParams.topMargin = marginTop
        view.layoutParams = layoutParams
    }

    protected fun moveToBottomRelative(view: TextView) {
        if (view.layoutParams !is RelativeLayout.LayoutParams) {
            return
        }
        val layoutParams = view.layoutParams as RelativeLayout.LayoutParams
        layoutParams.addRule(RelativeLayout.ALIGN_PARENT_TOP, RelativeLayout.TRUE)
        view.layoutParams = layoutParams
    }

    protected fun moveToBottom(view: TextView, marginBottom: Int = 2) {
        if (view.layoutParams !is LinearLayout.LayoutParams) {
            return
        }
        val layoutParams = view.layoutParams as LinearLayout.LayoutParams
        layoutParams.bottomMargin = marginBottom
        layoutParams.gravity = Gravity.BOTTOM
        view.layoutParams = layoutParams
    }

    /**
     * 打开网络异常重新组件
     */
    protected fun showNetErrorOrSettingView() {

        Log.d("BaseCardFragment", "showNetErrorOrSettingView by $this")

//        if (netErrorView == null) {
//            initNetErrorView()
//        }
        if(netErrorView !=null){
            netErrorView?.visibility = View.VISIBLE
        }


        Handler(Looper.getMainLooper()).postDelayed({
            if (netErrorView == null) {
                initNetErrorView()
            }
            //增加延时是因为在viewpager快速滑动时  会出现加载不成功
            hideLoading()
            setViewPagerScrollEnable(false)
        }, 100)

    }

    /**
     * 关闭网络异常重新组件
     */
    private fun hideNetErrorOrSettingView() {
        Log.d("BaseCardFragment", "hideNetErrorOrSettingView by $this")
        if (netErrorView != null) {
            netErrorView?.visibility = View.GONE
            // 如果隐藏，则显示Tab
            if(HmsApplication.isNetworkConn()) {
                (requireActivity() as HMSCardFragmentInteractWithAcInterface)?.setTabVisibilityforNetErrorOrSettingView(View.VISIBLE)
            } else {
                (requireActivity() as HMSCardFragmentInteractWithAcInterface)?.setTabVisibilityforNetErrorOrSettingView(View.INVISIBLE)
            }
            
            // 设置容器可以滑动
            setViewPagerScrollEnable(true)
//            mContainerParent =
//                requireActivity().findViewById<ViewPager2>(R.id.contentViewPager).parent as ViewGroup
//            mContainerParent?.removeView(netErrorView)
//            netErrorView = null
        }
    }

    /**
     * 初始化网络异常重新组件
     */
    // 加入200毫秒防抖
    val debounceDelay: Long = 500 // 防抖延迟时间（毫秒）
    var lastClickTime: Long = 0

    private fun initNetErrorView(){
        Log.d("BaseCardFragment", "initNetErrorView by $this")
        val mContainerParent =
            requireActivity().findViewById<ViewPager2>(R.id.contentViewPager).parent
        if (mContainerParent != null) {
            mContainerParent as ViewGroup
            // 动态加载自定义布局文件
            val inflater = LayoutInflater.from(requireContext())
            netErrorView =
                inflater.inflate(R.layout.activity_detail_no_network, mContainerParent, false)
            // 创建 layoutparam 并设置位置
            val params = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )

            mContainerParent?.addView(netErrorView, params)
//            mContainerParent?.addView(netErrorView, 0, params)
            // 设置netErrorView的点击事件
            // 刷新重试点击事件
            netErrorView?.findViewById<GwmButton>(R.id.btn_no_net_refresh)?.setOnClickListener {
                val currentTime = System.currentTimeMillis()
                if (currentTime - lastClickTime > debounceDelay) {
                    lastClickTime = currentTime
                    hideNetErrorOrSettingView()

                    //先判断是否i私密模式，如果是则直接初始化UI，不刷新
                    if (HmsApplication.isPrivacyModeEnabled()) {
                        return@setOnClickListener
                    }

                    showLoading()

                    // 无网延迟200毫秒重新展示
                    if (!HmsApplication.isNetworkConn()) {
                        handler.postDelayed({
                            showNetErrorOrSettingView()
                        },200)
                    }else {
                        if (isDataReady) {
                            sendRequest(mUserId, cardTimeType!!)
                        } else {
                            sendDataReadyRequest(mUserId, cardTimeType!!)
                        }
                    }
                    return@setOnClickListener
                }
            }
            // 触摸事件直接消费掉。
            netErrorView?.setOnTouchListener(object: View.OnTouchListener{
                override fun onTouch(v: View?, event: MotionEvent?): Boolean {
                    return true;
                }

            })
        }
    }

    protected fun showNoAuthView(text: String = requireContext().resources.getString(R.string.text_auth_default)) {
        hideLoading()
        mContainer = binding.root as ViewGroup
        val inflater = LayoutInflater.from(requireContext())
        noAuthView = inflater.inflate(R.layout.activity_detail_no_auth, mContainer, false)
        // 创建 layoutparam 并设置位置
        val params = ConstraintLayout.LayoutParams(
            ConstraintLayout.LayoutParams.MATCH_PARENT,
            ConstraintLayout.LayoutParams.MATCH_PARENT
        )
        noAuthView?.findViewById<TextView>(R.id.tv_auth)?.text = text
        mContainer?.addView(noAuthView, params)

    }

    private fun hideNoAuthView() {
        if (noAuthView != null) {
            mContainer?.removeView(noAuthView)
            noAuthView = null
        }
    }


    //endregion

    //region loading
    protected fun showLoading() {
        if (loadingView != null) {
            return
        }
        hideNetErrorOrSettingView()
        hideNoAuthView()
        setViewPagerScrollEnable(true)
        mContainer = binding.root as ViewGroup

        // 动态加载自定义布局文件
        val inflater = LayoutInflater.from(requireContext())
        loadingView = inflater.inflate(R.layout.activity_detail_loading_180, mContainer, false)
        // 创建 layoutparam 并设置位置
        val params = ConstraintLayout.LayoutParams(
            ConstraintLayout.LayoutParams.MATCH_PARENT,
            ConstraintLayout.LayoutParams.MATCH_PARENT
        )

        mContainer?.addView(loadingView, params)

        // 给ImageView设置动画
        val imageView = loadingView?.findViewById<ImageView>(R.id.iv_loading_amin)!!
        imageView.setImageResource(R.drawable.loading_80x80)
        drawable = imageView.drawable as AnimatedVectorDrawable
        animationCallback = object : Animatable2.AnimationCallback() {
            override fun onAnimationEnd(drawable: Drawable) {
                super.onAnimationEnd(drawable)
                (drawable as AnimatedVectorDrawable).start()
            }
        }
        drawable?.registerAnimationCallback(animationCallback!!)
        drawable?.start()

//        val drawable = imageView.drawable as AnimatedVectorDrawable
//        drawable.registerAnimationCallback(object : Animatable2.AnimationCallback() {
//            override fun onAnimationEnd(drawable: Drawable) {
//                super.onAnimationEnd(drawable)
//                (drawable as AnimatedVectorDrawable).start()
//            }
//        })
//        if (drawable != null) {
//            drawable.start()
//        }
    }

    protected fun hideLoading() {
        if (loadingView != null) {
            mContainer?.removeView(loadingView)
            loadingView = null;
        }
    }

    fun receiveBroadcast() {
        myReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                val value = intent.getBooleanExtra("pm", false)
                onPrivacyModeChange(value)
            }
        }
        val intentFilter = IntentFilter("com.healthlink.hms.privacymode")
        LocalBroadcastManager.getInstance(requireContext())
            .registerReceiver(myReceiver!!, intentFilter)
    }

    open fun onPrivacyModeChange(provacyMode: Boolean) {}

    fun viewAddOnGlobalLayoutListener(view: View) {
        // 先移除之前的监听
        if (view.viewTreeObserver.isAlive) {
            view.viewTreeObserver.removeOnGlobalLayoutListener { this }
            globalLayoutListener = null
        }

        if (globalLayoutListener == null) {
            globalLayoutListener = ViewTreeObserver.OnGlobalLayoutListener {
                val parentView = view.parent as View
                val rect = Rect()
                view.getHitRect(rect)
                rect.top -= 24
                rect.bottom += 100
                rect.left -= 100
                rect.right += 24
                parentView.touchDelegate = TouchDelegate(rect, view)
            }
        }
    }

    fun viewRemoveOnGlobalLayoutListener(view: View) {
        if (view.viewTreeObserver.isAlive && globalLayoutListener != null) {
            view.viewTreeObserver.removeOnGlobalLayoutListener(globalLayoutListener)
            globalLayoutListener = null
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // 移除loading动画回调
        drawable?.unregisterAnimationCallback(animationCallback!!)
        drawable = null
        animationCallback = null

        myReceiver?.let {
            LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(it)
            myReceiver = null
        }

        handler.removeCallbacksAndMessages(null)
    }

    fun setScrollEnable(scrollView: StretchScrollView, enable: Boolean = true) {
        if (enable) {
            scrollView.setOnTouchListener(null)
        } else {
            scrollView.setOnTouchListener { _, _ ->
                true
            }
        }
    }
    //endregion

    override fun onPause() {
        super.onPause()
        // 取消注册监听
        myReceiver?.let {
            LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(it)
            myReceiver = null
        }
        //如果是私密模式，关闭loading
        if (HmsApplication.isPrivacyModeEnabled()) {
            return
        }
        //  showLoading()
    }

    override fun onResume() {
        super.onResume()
        // 注册监听
        receiveBroadcast()
        //如果是私密模式，关闭loading
        if (HmsApplication.isPrivacyModeEnabled()) {
            hideLoading()
        }
    }


    fun setViewPagerScrollEnable(enable: Boolean) {
        val viewPager = requireActivity().findViewById<ViewPager2>(R.id.contentViewPager)
        if (viewPager != null) {
            if (enable)
                viewPager.isUserInputEnabled = true
            else
                viewPager.isUserInputEnabled = false
        }

    }
}