package com.healthlink.hms.fragment.viewmodel

/**
 * Created by imaginedays on 2024/6/25
 * 睡眠二级页面的ViewModel
 */
import com.healthlink.hms.mvvm.model.BaseResponse
import com.healthlink.hms.server.data.dto.SleepDayResponseDTO
import com.healthlink.hms.server.data.dto.SleepMonthResponseDTO
import com.healthlink.hms.server.data.dto.SleepWeekResponseDTO
import com.healthlink.hms.server.data.dto.SleepYearResponseDTO
import com.healthlink.hms.server.data.dto.charts.HealthSleepSummeryDTO
import com.healthlink.hms.server.data.dto.charts.HealthSummeryDTO
import com.healthlink.hms.server.data.dto.charts.heartrate.HeartRateStatDTO

class SleepFragmentModel{

    /**
     * 页面滚动的Y值
     */
    var scollY : Int = 0

    /**
     * 健康数据 - 日
     */
    var healthDataDay : BaseResponse<SleepDayResponseDTO>? = null
    /**
     * 健康数据 - 周
     */
    var healthDataWeek : BaseResponse<SleepWeekResponseDTO>? = null
    /**
     * 健康数据 - 月
     */
    var healthDataMonth : BaseResponse<SleepMonthResponseDTO>? = null
    /**
     * 健康数据 - 年
     */
    var healthDataYear : BaseResponse<SleepYearResponseDTO>? = null

    /**
     * 健康建议响应对象
     */
    var healthSummaryResponse : BaseResponse<HealthSleepSummeryDTO>? = null

    var isShowExplainDialog : Boolean = false
}


