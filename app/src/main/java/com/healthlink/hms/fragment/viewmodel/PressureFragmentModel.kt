package com.healthlink.hms.fragment.viewmodel

/**
 * Created by imaginedays on 2024/6/25
 * 压力二级页面的DataModel
 */
import com.healthlink.hms.mvvm.model.BaseResponse
import com.healthlink.hms.server.data.dto.charts.pressure.PressureDetailRespDTO
import com.healthlink.hms.server.data.dto.charts.pressure.PressureSummaryDTO

class PressureFragmentModel{

    /**
     * 页面滚动的Y值
     */
    var scollY : Int = 0

    /**
     * 健康数据
     */
    var healthData : BaseResponse<PressureDetailRespDTO>? = null

    /**
     * 健康建议响应对象
     */
    var healthSummaryResponse : BaseResponse<PressureSummaryDTO>? = null

    var isShowExplainDialog : Boolean = false
}


