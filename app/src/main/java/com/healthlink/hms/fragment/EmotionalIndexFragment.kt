package com.healthlink.hms.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.R
import com.healthlink.hms.activity.card.HMSCardFragmentInteractWithAcInterface
import com.healthlink.hms.databinding.FragmentCardEmotionalBinding
import com.healthlink.hms.viewmodels.MainViewModel
import java.lang.ref.WeakReference

class EmotionalIndexFragment: BaseCardFragment<FragmentCardEmotionalBinding, MainViewModel>(
    MainViewModel::class.java,
    R.layout.fragment_card_emotional
) {

    override fun sendRequest(userId: String, timeCode: String) {
    }

    override fun sendDataReadyRequest(userId: String, timeCode: String) {
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            cardTimeType = it.getString(ARG_PARAM_TYPE)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
    }

    companion object {
        private const val TAG = "EmotionalIndexFragment"
        private const val ARG_PARAM_TYPE = "ARG_PARAM_TYPE"
        private val fragmentInteractWithAC
            get() = _fragmentInteractWithAC?.get()
        private var _fragmentInteractWithAC: WeakReference<HMSCardFragmentInteractWithAcInterface>? =
            null
        private lateinit var mUserId: String

        fun newInstance(
            cartTimeType: TimeCode,
            userId: String,
            interact: HMSCardFragmentInteractWithAcInterface
        ): EmotionalIndexFragment {
            val fragment = EmotionalIndexFragment()
            val args = Bundle()
            args.putString(ARG_PARAM_TYPE, cartTimeType.timeCode)
            fragment.arguments = args
            _fragmentInteractWithAC = WeakReference(interact)
            mUserId = userId
            return fragment
        }
    }
}