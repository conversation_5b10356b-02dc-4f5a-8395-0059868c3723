package com.healthlink.hms.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.healthlink.hms.databinding.ItemPlaylistBinding
import com.healthlink.hms.model.VideoItem

class PlaylistAdapter(private val onItemClick: (VideoItem) -> Unit) :
    ListAdapter<VideoItem, PlaylistAdapter.VideoViewHolder>(VideoDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VideoViewHolder {
        val binding = ItemPlaylistBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return VideoViewHolder(binding, onItemClick)
    }

    override fun onBindViewHolder(holder: VideoViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class VideoViewHolder(
        private val binding: ItemPlaylistBinding,
        private val onItemClick: (VideoItem) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: VideoItem) {
            binding.apply {
                tvVideoTitle.text = item.title
                tvDuration.text = formatDuration(item.duration)
                
                // 加载缩略图 无服务器暂时无法加载缩略图
                Glide.with(ivVideoThumbnail)
                    .load(item.thumbnailUrl)
                    .centerCrop()
                    .into(ivVideoThumbnail)

                // 设置点击事件
                root.setOnClickListener { onItemClick(item) }
            }
        }

        private fun formatDuration(seconds: Int): String {
            val minutes = seconds / 60
            val remainingSeconds = seconds % 60
            return String.format("%02d:%02d", minutes, remainingSeconds)
        }
    }

    private class VideoDiffCallback : DiffUtil.ItemCallback<VideoItem>() {
        override fun areItemsTheSame(oldItem: VideoItem, newItem: VideoItem): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: VideoItem, newItem: VideoItem): Boolean {
            return oldItem == newItem
        }
    }
} 