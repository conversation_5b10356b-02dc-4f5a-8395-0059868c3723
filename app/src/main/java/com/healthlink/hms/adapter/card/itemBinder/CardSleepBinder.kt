package com.healthlink.hms.adapter.card.itemBinder

import android.app.Activity
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ClickUtils
import com.drakeet.multitype.ItemViewBinder
import com.healthlink.hms.R
import com.healthlink.hms.adapter.OnItemClickListener
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.ktExt.addClickScale
import com.healthlink.hms.server.data.bean.HomeCardDTO
import com.healthlink.hms.server.data.dto.Sleep
import com.healthlink.hms.utils.TimeUtils
import com.healthlink.hms.views.SegmentedBarView.Segment
import com.healthlink.hms.views.SegmentedBarView.SegmentedBarView
import java.lang.ref.WeakReference
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date

/**
 * Created by imaginedays on 2024/5/24
 *
 *
 */
class CardSleepBinder(private var onItemClick: ((cardType: Int, cardDTO: HomeCardDTO<*>) -> Unit)?) :
    ItemViewBinder<HomeCardDTO<*>, CardSleepBinder.ViewHolder>() {
    private var currentHolder: WeakReference<CardSleepBinder.ViewHolder>? = null
    private var cardDTO: HomeCardDTO<*>? = null

    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): ViewHolder {
        val view = inflater.inflate(R.layout.card_sleep, parent, false)
        return ViewHolder(view,onItemClick)
    }

    override fun onBindViewHolder(holder: CardSleepBinder.ViewHolder, item: HomeCardDTO<*>) {
        holder.itemView.addClickScale()

        cardDTO = item
        currentHolder = WeakReference(holder) // 缓存 holder
        holder.itemView.tag = item

        if (item.cardType == 6) {
            // 睡眠
            //私密模式
            if (HmsApplication.isPrivacyModeEnabled()) {
                holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                    View.VISIBLE
                holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                    View.GONE
                holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility = View.GONE
                initSleepUI(item, holder)

            } else {

                if (item.data != null
                        // 睡眠小时数与分钟数都为0的反例
                        &&!((item.data as Sleep).sleepTimeHour==0 && (item.data as Sleep).sleepTimeMin==0)
                    ) {
                    holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                        View.GONE
                    holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                        View.VISIBLE
                    holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility =
                        View.GONE
                    val lastUpdateTime =
                        holder.itemView.findViewById<TextView>(R.id.tv_data_year_or_day)
                    val timeStr = (item.data as Sleep).createTime
                    lastUpdateTime.text = TimeUtils.getLastUpdateDateText(timeStr)
                    setDataGray(timeStr,holder.itemView)
                    initSleepUI(item, holder)
                } else {
                    holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                        View.GONE
                    holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                        View.GONE
                    holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility =
                        View.VISIBLE
                }
            }
        }
    }

    private fun initSleepUI(cardDTO: HomeCardDTO<*>, holder: CardSleepBinder.ViewHolder) {
        val aColor = HmsApplication.appContext.getString(R.string.card_sleep_bar_bg_color_left)
        val aStartColor = HmsApplication.appContext.getString(R.string.card_sleep_bar_bg_color_left_gr_start)
        val aEndColor = HmsApplication.appContext.getString(R.string.card_sleep_bar_bg_color_left_gr_end)

        val bColor = HmsApplication.appContext.getString(R.string.card_sleep_bar_bg_color_middle)
        val bStartColor = HmsApplication.appContext.getString(R.string.card_sleep_bar_bg_color_middle_gr_start)
        val bEndColor = HmsApplication.appContext.getString(R.string.card_sleep_bar_bg_color_middle_gr_end)

        val cColor = HmsApplication.appContext.getString(R.string.card_sleep_bar_bg_color_right)
        val cStartColor = HmsApplication.appContext.getString(R.string.card_sleep_bar_bg_color_right_gr_start)
        val cEndColor = HmsApplication.appContext.getString(R.string.card_sleep_bar_bg_color_right_gr_end)
        if (HmsApplication.isPrivacyModeEnabled()){
            val segmentBarView =
                holder.itemView.findViewById<SegmentedBarView>(R.id.iv_sleeping_chart_privacy)
            segmentBarView.visibility = View.VISIBLE
            segmentBarView.setSegments(
                listOf(Segment(0f,
                    100f,
                    "",
                    HmsApplication.appContext.getColor(R.color.card_sleep_char_privacy_standard),
                    HmsApplication.appContext.getColor(R.color.card_sleep_char_privacy_shadow_s),
                    HmsApplication.appContext.getColor(R.color.card_sleep_char_privacy_shadow_e))) )
            return
        }
        val model = cardDTO.data as Sleep
        // 睡眠 - 小时
        val mTvSleepHour = holder.itemView.findViewById<TextView>(R.id.tv_main_body_sleep_hour)
        if (model.sleepTimeHour != null) {
            mTvSleepHour.text = model!!.sleepTimeHour.toString()
        }

        // 睡眠 - 分钟
        val mTvSleepMin = holder.itemView.findViewById<TextView>(R.id.tv_main_body_sleep_minute)
        if (model.sleepTimeMin != null) {
            mTvSleepMin.text = model.sleepTimeMin.toString()
        }

        // 睡眠状态
        val mTvSleepStatus = holder.itemView.findViewById<TextView>(R.id.tv_main_body_sleep_status)
        if (model.sleepType != null && !model.sleepType.isNullOrBlank()) {
            mTvSleepStatus.text = model.sleepType
        } else {
            mTvSleepStatus.text = "较好"
        }

        // 睡眠状态代码 0：较差， 1：一般，2：较好，3:正常， 4：较长
        model.sleepCode?.let {
            when (it) {
                0 -> {
                    mTvSleepStatus.setBackgroundResource(R.drawable.health_index_status_danger_bg_fill)
                    changeCardToException(true, holder)
                }
                1 -> {
                    mTvSleepStatus.setBackgroundResource(R.drawable.health_index_status_warning_bg_fill)
                    changeCardToException(false, holder)
                }
                2 -> {
                    mTvSleepStatus.setBackgroundResource(R.drawable.health_index_status_nice_bg_fill)
                    changeCardToException(false, holder)
                }
                3 -> {
                    mTvSleepStatus.setBackgroundResource(R.drawable.health_index_status_nice_bg_fill)
                    changeCardToException(false, holder)
                }
                4 -> {
                    mTvSleepStatus.setBackgroundResource(R.drawable.health_index_status_nice_bg_fill)
                    changeCardToException(false, holder)
                }
            }
        }


        // 睡眠时长 图表 深睡(deepSleepTime)、浅睡(lightSleepTime)、快速动眼(dreamTime)
        if (model.deepSleepTime != null && model.lightSleepTime != null && model.dreamTime != null) {
            var deepSleepTime = model.deepSleepTime!!.toFloat()
            var lightSleepTime = model.lightSleepTime!!.toFloat()
            var dreamTime = model.dreamTime!!.toFloat()

            val (percentageA, percentageB, percentageC) = calculatePercentages(
                deepSleepTime,
                lightSleepTime,
                dreamTime
            )
            val segments = ArrayList<Segment>()


            val aMix = 0F
            val aMax = percentageA

            val bMix = percentageA
            val bMax = percentageA + percentageB

            val cMix = percentageA + percentageB
            val cMax = percentageA + percentageB + percentageC

            // 深睡
            segments.add(
                Segment(
                    aMix,
                    aMax,
                    "",
                    Color.parseColor(aColor),
                    Color.parseColor(aStartColor),
                    Color.parseColor(aEndColor)
                )
            )
            // 浅睡
            segments.add(
                Segment(
                    bMix,
                    bMax,
                    "",
                    Color.parseColor(bColor),
                    Color.parseColor(bStartColor),
                    Color.parseColor(bEndColor)
                )
            )
            // 快速眼动
            segments.add(
                Segment(
                    cMix,
                    cMax,
                    "",
                    Color.parseColor(cColor),
                    Color.parseColor(cStartColor),
                    Color.parseColor(cEndColor)
                )
            )
            val segmentBarView =
                holder.itemView.findViewById<SegmentedBarView>(R.id.iv_sleeping_chart)
            segmentBarView.visibility = View.VISIBLE
            segmentBarView.setSegments(segments)
        }
    }

    private fun calculatePercentages(a: Float, b: Float, c: Float): Triple<Float, Float, Float> {
        val total = a + b + c
        val percentageA = (a / total) * 100
        val percentageB = (b / total) * 100
        val percentageC = (c / total) * 100
        return Triple(percentageA, percentageB, percentageC)
    }


    class ViewHolder(view: View,onItemClick: ((cardType: Int, cardDTO: HomeCardDTO<*>) -> Unit)?) : RecyclerView.ViewHolder(view) {
        val normalDataLayout: FrameLayout
        val noDataLayout: FrameLayout

        init {
            normalDataLayout = view.findViewById(R.id.card_normal_data)
            noDataLayout = view.findViewById(R.id.card_no_data)

            // 使用 ClickUtils 进行防抖处理，同时仅在点击时通过 view.tag 获取最新数据
            ClickUtils.applySingleDebouncing(view, 1000) {
                // 从 itemView 的 tag 中获取当前数据
                val cardDTO = view.tag as? HomeCardDTO<*>
                if (cardDTO != null) {
                    // 调用外部传入的 onItemClick lambda，避免直接引用外部对象
                    onItemClick?.invoke(cardDTO.cardType, cardDTO)
                }
            }
        }
    }

    fun setDataGray(time: String?, view: View) {
        val text = view.findViewById<TextView>(R.id.tv_data_year_or_day)
        text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_40))
        if (time != null) {
            try {
                val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                val time = sdf.parse(time)
                val calendar: Calendar = Calendar.getInstance()
                calendar.add(Calendar.DATE, 0) // 将当前时间回退一天
                val now: Date = calendar.getTime()
                val isBefore24Hours = (now.time - time.time) < 24 * 60 * 60 * 1000

                if (isBefore24Hours) {
                    text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_100))
                } else {
                    text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_40))
                }
            } catch (e: ParseException) {
                e.printStackTrace()
            }
        }
    }

    fun privacyUi() {
        currentHolder?.let { holder ->
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_privacy_data)?.visibility = View.GONE
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_normal_data)?.visibility = View.GONE
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_no_data)?.visibility = View.VISIBLE
        }
    }
}