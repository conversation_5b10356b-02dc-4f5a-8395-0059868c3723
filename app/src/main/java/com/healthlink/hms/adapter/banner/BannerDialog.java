package com.healthlink.hms.adapter.banner;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.res.Configuration;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStoreOwner;

import com.healthlink.hms.R;
import com.healthlink.hms.application.HmsApplication;
import com.healthlink.hms.utils.MMKVUtil;
import com.healthlink.hms.views.dialog.HmsDialog;
import com.youth.banner.indicator.CircleIndicator;
import com.youth.banner.listener.OnPageChangeListener;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

import me.jessyan.autosize.AutoSizeCompat;

/**
 * <AUTHOR>
 * @data 2024/9/24
 * @function bannerDialog
 */
public class BannerDialog implements OnClickListener {


    private final WeakReference<Context> context;
    private HmsDialog dialog;
    private ImageView noTips;
    private LinearLayout noTipsContainer;
    // ViewModel 变量
    private BannerViewModel bannerViewModel;

    public BannerDialog(Context context) {
        this.context = new WeakReference(context);

        // 获取 BannerViewModel 实例
        bannerViewModel = new ViewModelProvider((ViewModelStoreOwner) this.context.get()).get(BannerViewModel.class);

        WindowManager windowManager = (WindowManager) this.context.get().getSystemService(Context.WINDOW_SERVICE);
        assert windowManager != null;
    }

    public BannerDialogDismissListener bannerDialogDismissListener;

    public interface BannerDialogDismissListener{
        void bannerDialogDismiss();
    }

    public BannerDialog setBannerDialogDismissListener(BannerDialogDismissListener bannerDialogDismissListener){
        this.bannerDialogDismissListener = bannerDialogDismissListener;
        return this;
    }

    @SuppressLint("InflateParams")
    public BannerDialog builder() {
        View view = LayoutInflater.from(context.get()).inflate(R.layout.hms_dialog_banner, null, false);
        ConstraintLayout container = view.findViewById(R.id.container);
        CustomBanner banner = view.findViewById(R.id.banner);
        CircleIndicator indicator = view.findViewById(R.id.indicator);
        noTipsContainer = view.findViewById(R.id.noTipsContainer);
        noTips = view.findViewById(R.id.noTips);
        TextView know = view.findViewById(R.id.know);

        container.setOnClickListener(this);
        know.setOnClickListener(this);
        noTipsContainer.setOnClickListener(this);

        // 定义Dialog布局和参数
        dialog = new HmsDialog(context.get(), R.style.MyDialogStyle);
        dialog.setContentView(view);
        //点击空白处是否dismiss
        dialog.setCancelable(true);

        AutoSizeCompat.autoConvertDensityOfGlobal(context.get().getResources());

        // 使用 ViewModel 来恢复 noTips 的选中状态
        boolean isNoTipsSelected = bannerViewModel.getNoTipsSelected();
        noTips.setSelected(isNoTipsSelected);

        dialog.setOnDismissListener(dialogInterface -> {
            if (noTips.isSelected()){
                //选中了不再提醒
                MMKVUtil.INSTANCE.storeBannerTips(false);
                MMKVUtil.INSTANCE.doSync();
            } else {
                //未选中不再提醒，还需要提醒
                MMKVUtil.INSTANCE.storeBannerTips(true);
                MMKVUtil.INSTANCE.doSync();
            }

            // 重置默认选中状态
            MMKVUtil.INSTANCE.storeLastBannerPosition(0);

            if (null != bannerDialogDismissListener){
                bannerDialogDismissListener.bannerDialogDismiss();
            }
        });

        List<BannerBean> bannerData = new ArrayList<>();
        bannerData.add(new BannerBean(R.drawable.dialog_banner_1));
        bannerData.add(new BannerBean(R.drawable.dialog_banner_2));
        bannerData.add(new BannerBean(R.drawable.dialog_banner_3));
        bannerData.add(new BannerBean(R.drawable.dialog_banner_4));
        bannerData.add(new BannerBean(R.drawable.dialog_banner_5));
        bannerData.add(new BannerBean(R.drawable.dialog_banner_6));

        banner.addOnPageChangeListener(new OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
//                bannerViewModel.setBannerPosition(position);
                MMKVUtil.INSTANCE.storeLastBannerPosition(position);

            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });

        banner.addBannerLifecycleObserver((LifecycleOwner) context.get())//添加生命周期观察者
                .setAdapter(new ImageAdapter(bannerData, context.get()))
                .setIndicator(indicator, false);

        // 恢复 Banner 的位置
        if(HmsApplication.Companion.isChangeTheme()) {
            banner.setCurrentItem(MMKVUtil.INSTANCE.getLastBannerPosition(), false);
            banner.setIndicatorPageChange();
        } else {
            banner.setCurrentItem(0);
        }

//        banner.setCurrentItem(bannerViewModel.getBannerPosition());
        return this;
    }


    public void show() {
        try {
            if (dialog != null && !dialog.isShowing()) {
                dialog.show();
            }
        } catch (Exception e) {
            Log.i("BannerDialog", "show: " + e.getMessage());
        }
    }

    public void dismiss() {
        if (context.get() instanceof Activity) {
            if (!((Activity) context.get()).isFinishing()) {
                if (!((Activity) context.get()).isDestroyed()) {
                    if (dialog != null && dialog.isShowing()) {
                        dialog.dismiss();
                    }
                }
            }
        } else if (dialog != null && dialog.isShowing()) {
            dialog.dismiss();
        }
    }


    @SuppressLint("NonConstantResourceId")
    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.know || id == R.id.container) {
            dismiss();
        } else if (id == R.id.noTipsContainer) {
            noTips.setSelected(!noTips.isSelected());
            // 保存 noTips 的选中状态到 ViewModel
            bannerViewModel.setNoTipsSelected(noTips.isSelected());

            if (noTips.isSelected()){
                //选中了不再提醒
                MMKVUtil.INSTANCE.storeBannerTips(false);
                MMKVUtil.INSTANCE.doSync();
            } else {
                //未选中不再提醒，还需要提醒
                MMKVUtil.INSTANCE.storeBannerTips(true);
                MMKVUtil.INSTANCE.doSync();
            }
        }
    }

    /**
     * 对话框是否展示中。
     * @return
     */
    public boolean isShowing(){
        return dialog!=null && dialog.isShowing();
    }
}
