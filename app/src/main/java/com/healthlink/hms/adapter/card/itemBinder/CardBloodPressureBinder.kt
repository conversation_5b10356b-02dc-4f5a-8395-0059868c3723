package com.healthlink.hms.adapter.card.itemBinder

import android.app.Activity
import android.graphics.Color
import android.graphics.PorterDuff
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ClickUtils
import com.drakeet.multitype.ItemViewBinder
import com.healthlink.hms.R
import com.healthlink.hms.adapter.OnItemClickListener
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.ktExt.addClickScale
import com.healthlink.hms.server.data.BloodPressure
import com.healthlink.hms.server.data.bean.HomeCardDTO
import com.healthlink.hms.server.data.dto.Temperature
import com.healthlink.hms.utils.TimeUtils
import com.healthlink.hms.views.CustomProgressBar
import java.lang.ref.WeakReference
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date

/**
 * Created by imaginedays on 2024/6/23
 * 血压
 */
class CardBloodPressureBinder(private var onItemClick: ((cardType: Int, cardDTO: HomeCardDTO<*>) -> Unit)?) :
    ItemViewBinder<HomeCardDTO<*>, CardBloodPressureBinder.ViewHolder>() {
    private var currentHolder: WeakReference<CardBloodPressureBinder.ViewHolder>? = null
    private var cardDTO: HomeCardDTO<*>? = null
    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): ViewHolder {
        val view = inflater.inflate(R.layout.card_blood_pressure, parent, false)
        return ViewHolder(view, onItemClick)
    }

    override fun onBindViewHolder(
        holder: CardBloodPressureBinder.ViewHolder,
        item: HomeCardDTO<*>
    ) {
        holder.itemView.addClickScale()

        cardDTO = item
        currentHolder = WeakReference(holder)
        holder.itemView.tag = item

        if (HmsApplication.isPrivacyModeEnabled()) {
            holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                View.VISIBLE
            holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                View.GONE
            holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility = View.GONE
        } else {

            if (item.data != null && (item.data as BloodPressure).systolicPressure != null) {
                val bloodPressure = item.data as BloodPressure
                holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                    View.VISIBLE
                holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility = View.GONE
                holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                    View.GONE
                val lastUpdateTime =
                    holder.itemView.findViewById<TextView>(R.id.tv_data_year_or_day)
                val timeStr = bloodPressure.createTime
                setDataGray(timeStr, holder.itemView)
                lastUpdateTime.text = TimeUtils.getLastUpdateDateText(timeStr)
                initBloodPressureUI(bloodPressure, holder)
            } else {
                holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                    View.GONE
                holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility =
                    View.VISIBLE
                holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                    View.GONE
            }
        }
    }


    private fun initBloodPressureUI(
        model: BloodPressure,
        holder: CardBloodPressureBinder.ViewHolder
    ) {
        val tv_value = holder.itemView.findViewById<TextView>(R.id.tv_main_blood_pressure)
        if (model.systolicPressure != null) {
            tv_value.text = "${model.systolicPressure}/${model.diastolicPressure}"
        } else {
            tv_value.text = "--/--"
        }

        // 体温状态
        val tv_status = holder.itemView.findViewById<TextView>(R.id.tv_main_blood_pressure_status)
        if (model.systolicPressure != null && !model.level.isNullOrBlank()) {
            tv_status.text = model.level
        } else {
            tv_status.text = "待测量"
        }

        // 血压  1：正常 0：疑似低血压 2：疑似正常高值 3：一级高血压 4：二级高血压 5：三级高血压
        if (model.systolicPressure != null && model.levelCode == 1) {
            tv_status.setBackgroundResource(R.drawable.health_index_status_nice_bg_fill)
            changeCardToException(false, holder)
        } else if (model.systolicPressure != null && (model.levelCode == 0 || model.levelCode == 2)) {
            tv_status.setBackgroundResource(R.drawable.health_index_status_warning_bg_fill)
            changeCardToException(false, holder)
        } else if (model.systolicPressure != null && (model.levelCode == 3 || model.levelCode == 4 || model.levelCode == 5)) {
            tv_status.setBackgroundResource(R.drawable.health_index_status_danger_bg_fill)
            changeCardToException(true, holder)
        }

        val progressBar1 = holder.itemView.findViewById<ProgressBar>(R.id.card_progress_1) as CustomProgressBar
        val progressBar2 = holder.itemView.findViewById<ProgressBar>(R.id.card_progress_2) as CustomProgressBar
        //以25%为正常值
        if (model.systolicPressure == null) {

        } else {
            var percent1 = 0f
            var percent2 = 0f

            percent1 = model.systolicPressure!! / 310f
            percent2 = model.diastolicPressure!! / 310f

            if (percent1 != 0f ) {
                progressBar1.setProgressAnimation((percent1*100).toInt())
            }

            if ( percent2 != 0f) {
                progressBar2.setProgressAnimation((percent2*100).toInt())
            }
        }
    }


    class ViewHolder(view: View,private var onItemClick: ((cardType: Int, cardDTO: HomeCardDTO<*>) -> Unit)?) : RecyclerView.ViewHolder(view) {
        val normalDataLayout: FrameLayout
        val noDataLayout: FrameLayout

        init {
            normalDataLayout = view.findViewById(R.id.card_normal_data)
            noDataLayout = view.findViewById(R.id.card_no_data)

            // 使用 ClickUtils 进行防抖处理，同时仅在点击时通过 view.tag 获取最新数据
            ClickUtils.applySingleDebouncing(view, 1000) {
                // 从 itemView 的 tag 中获取当前数据
                val cardDTO = view.tag as? HomeCardDTO<*>
                if (cardDTO != null) {
                    // 调用外部传入的 onItemClick lambda，避免直接引用外部对象
                    onItemClick?.invoke(cardDTO.cardType, cardDTO)
                }
            }
        }
    }

    fun setDataGray(time: String?, view: View) {
        val text = view.findViewById<TextView>(R.id.tv_data_year_or_day)
        text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_40))
        if (time != null) {
            try {
                val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                val time = sdf.parse(time)
                val calendar: Calendar = Calendar.getInstance()
                calendar.add(Calendar.DATE, 0) // 将当前时间回退一天
                val now: Date = calendar.getTime()
                val isBefore24Hours = (now.time - time.time) < 24 * 60 * 60 * 1000

                if (isBefore24Hours) {
                    text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_100))
                } else {
                    text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_40))
                }
            } catch (e: ParseException) {
                e.printStackTrace()
            }
        }
    }

    fun privacyUi() {
        currentHolder?.let { holder ->
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_privacy_data)?.visibility = View.GONE
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_normal_data)?.visibility = View.GONE
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_no_data)?.visibility = View.VISIBLE
        }
    }
}