package com.healthlink.hms.adapter.card.itemBinder

import android.animation.AnimatorInflater
import android.animation.AnimatorSet
import android.app.Activity
import android.graphics.Color
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ClickUtils
import com.drakeet.multitype.ItemViewBinder
import com.github.mikephil.charting.animation.Easing
import com.github.mikephil.charting.charts.BarChart
import com.github.mikephil.charting.components.Description
import com.github.mikephil.charting.data.BarData
import com.github.mikephil.charting.data.BarDataSet
import com.github.mikephil.charting.data.BarEntry
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet
import com.healthlink.hms.R
import com.healthlink.hms.adapter.OnItemClickListener
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.charts.mpandroidchart.render.RoundedBarChartRenderer
import com.healthlink.hms.ktExt.addClickScale
import com.healthlink.hms.server.data.bean.HomeCardDTO
import com.healthlink.hms.server.data.dto.BloodOxygen
import com.healthlink.hms.server.data.dto.Pressure
import com.healthlink.hms.server.data.dto.charts.CDPressureDTO
import com.healthlink.hms.utils.TimeUtils
import java.lang.ref.WeakReference
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import kotlin.random.Random

/**
 * Created by imaginedays on 2024/5/24
 *
 *
 */
class CardPressureBinder(private var onItemClick: ((cardType: Int, cardDTO: HomeCardDTO<*>) -> Unit)?) :
    ItemViewBinder<HomeCardDTO<*>, CardPressureBinder.ViewHolder>() {
    private var currentHolder: WeakReference<CardPressureBinder.ViewHolder>? = null
    private var cardDTO: HomeCardDTO<*>? = null
    override fun onCreateViewHolder(inflater: LayoutInflater, parent: ViewGroup): ViewHolder {
        val view = inflater.inflate(R.layout.card_pressure, parent, false)
        return ViewHolder(view,onItemClick)
    }

    override fun onBindViewHolder(holder: CardPressureBinder.ViewHolder, item: HomeCardDTO<*>) {
        holder.itemView.addClickScale()

        cardDTO = item
        currentHolder = WeakReference(holder) // 缓存 holder
        holder.itemView.tag = item
        // 压力
        //私密模式
        if (HmsApplication.isPrivacyModeEnabled()) {
            holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                View.VISIBLE
            holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                View.GONE
            holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility = View.GONE
            val privacyChart =
                holder.itemView.findViewById<BarChart>(R.id.chart_pressure_bar_privacy)
            initPressurePrivacyChart(privacyChart)
//            doPressureAnim(privacyChart)

        } else {
            if (item.data != null && item.dataList != null
                //压力值大于0
                &&((item.data as Pressure).pressure!=null && (item.data as Pressure).pressure!!.toInt()!!>0)
                ) {
                holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                    View.GONE
                holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                    View.VISIBLE
                holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility = View.GONE
                val lastUpdateTime =
                    holder.itemView.findViewById<TextView>(R.id.tv_data_year_or_day)
                val timeStr = (item.data as Pressure).createTime
                lastUpdateTime.text = TimeUtils.getLastUpdateDateText(timeStr)
                setDataGray(timeStr, holder.itemView)
                initPressureUI(item, holder)
            } else {
                holder.itemView.findViewById<FrameLayout>(R.id.card_privacy_data).visibility =
                    View.GONE
                holder.itemView.findViewById<FrameLayout>(R.id.card_normal_data).visibility =
                    View.GONE
                holder.itemView.findViewById<FrameLayout>(R.id.card_no_data).visibility =
                    View.VISIBLE
            }
        }
    }

    private fun initPressureUI(dto: HomeCardDTO<*>, holder: CardPressureBinder.ViewHolder) {
        val model = dto.data as Pressure
        // 压力值
        val tvPressureValue = holder.itemView.findViewById<TextView>(R.id.tv_main_body_pressure)
        if (model.pressure != null) {
            tvPressureValue.text = model.pressure.toString()
        } else {
            tvPressureValue.text = "未知"
        }
        // 压力状态
        val tvPressureValueStatus =
            holder.itemView.findViewById<TextView>(R.id.tv_main_body_pressure_status)
        if (model.pressure != null && !model.pressureType.isNullOrBlank()) {
            tvPressureValueStatus.text = model.pressureType
        } else {
            tvPressureValueStatus.text = "放松"
        }

        // 压力状态背景颜色 压力类型代码，0：正常 1：放松 2：中等 3：偏高
        if (model.pressure != null &&
            (model.pressureCode == 0 || model.pressureCode == 1)
        ) {
            tvPressureValueStatus.setBackgroundResource(R.drawable.health_index_status_nice_bg_fill)
            // 关闭异常背景
            changeCardToException(false, holder)
        } else if (model.pressure != null && model.pressureCode == 2) {
            tvPressureValueStatus.setBackgroundResource(R.drawable.health_index_status_warning_bg_fill)
            // 关闭异常背景
            changeCardToException(false, holder)
        } else if (model.pressure != null && model.pressureCode == 3) {
            // 偏高
            tvPressureValueStatus.setBackgroundResource(R.drawable.health_index_status_danger_bg_fill)
            // 打开异常背景
            changeCardToException(true, holder)
        }

        // 压力图表组件
        val chart = holder.itemView.findViewById<BarChart>(R.id.chart_pressure_bar)
        if (dto.dataList != null) {
            var dataList = dto.dataList?.mapNotNull { it as? CDPressureDTO }?.toMutableList() ?: mutableListOf()
            //dto.dataList as MutableList<CDPressureDTO>
            initPressureChart(chart, dataList)
            doPressureAnim(chart)
        }
    }


    /**
     * 根据压力数据画压力图
     * 1、放松，分值（1-29）
     * 2、正常，分值（30-59）
     * 3、中等，分值（60-79）
     * 4、偏高，分值（80-99）
     */
    private fun initPressureChart(chart: BarChart, dataList: MutableList<CDPressureDTO>) {
        val maxCount = 20

        var allDataList = mutableListOf<CDPressureDTO>()
        val size = maxCount - dataList.size
        if (size > 0) {
            var addedDataList = ArrayList<CDPressureDTO>()
            for (i in 0 until size) {
                addedDataList.add(CDPressureDTO(30))
            }
            allDataList.addAll(dataList)
            allDataList.addAll(addedDataList)

        } else {
            val fromIndex = maxOf(0, dataList.size - maxCount)
            allDataList.addAll(dataList.subList(fromIndex, dataList.size))
        }

        // 服务端返回的数据添加颜色（分集）
        val barDataSetList = ArrayList<IBarDataSet>()
        for ((index, value) in allDataList.withIndex()) {
            val entries = ArrayList<BarEntry>()
            entries.add(BarEntry(index.toFloat(), value.pressure!!.toFloat()))
            val barDataSet = BarDataSet(entries, "压力趋势图${index + 1}");
            when (value.pressure) {
                in 1..29 -> {
                    barDataSet.color =
                        Color.parseColor(HmsApplication.appContext.getString(R.string.card_press_bar_bg_color_relax))
                }

                in 30..59 -> {
                    // 如果是添加进来的默认数据，设置默认颜色
                    if (value.pressureCode == null) {
                        barDataSet.color = HmsApplication.appContext.getColor(R.color.card_press_bar_bg_color_default)
                    } else {
                        barDataSet.color =
                            Color.parseColor(HmsApplication.appContext.getString(R.string.card_press_bar_bg_color_normal))
                    }
                }

                in 60..79 -> {
                    barDataSet.color =
                        Color.parseColor(HmsApplication.appContext.getString(R.string.card_press_bar_bg_color_middle))
                }

                in 80..100 -> {
                    barDataSet.color =
                        Color.parseColor(HmsApplication.appContext.getString(R.string.card_press_bar_bg_color_high))
                }
            }
            barDataSetList.add(barDataSet)
        }
        // 数据不足，补充数据
//        if (dataList.size < maxCount) {
//            val size = maxCount - dataList.size
//            for (i in 0 until size) {
//                val entries = ArrayList<BarEntry>()
//                entries.add(BarEntry(i.toFloat(), 30f))
//                val barDataSet = BarDataSet(entries, "压力趋势图${dataList.size + i + 1}");
//                barDataSet.color = Color.parseColor(mContext.getString(R.string.card_press_bar_bg_color_default))
//                barDataSetList.add(barDataSet)
//            }
//        }

        // 设置柱状图的宽度和高度
        val barData = BarData(barDataSetList);
        barData.setDrawValues(false)
        barData.barWidth = 0.5f
        val barSpace = 0.501f // 设置柱状图之间的间距
        barData.groupBars(0F, 0F, barSpace)

        chart.setNoDataText("")
        chart.renderer = RoundedBarChartRenderer(chart, chart.animator, chart.viewPortHandler, 0.5F)

        chart.setBackgroundColor(Color.TRANSPARENT)
        chart.setBorderColor(Color.TRANSPARENT); //是否展示网格线
        chart.setDrawBorders(false); //是否显示边界
        chart.setScaleEnabled(false); // 是否可以缩放
        chart.setTouchEnabled(false); //是否有触摸事件
        chart.legend.isEnabled = false
        //设置XY轴描述
        var description = Description();
        description.isEnabled = false;
        description.text = "s";
        chart.description = description;
        var yAxisLeft = chart.axisLeft
        yAxisLeft.setDrawGridLines(false)
        yAxisLeft.isEnabled = false
        yAxisLeft.axisMinimum = 0F
        yAxisLeft.axisMaximum = 100F

        chart.axisRight.setDrawGridLines(false)
        chart.xAxis.setDrawGridLines(false)
        chart.axisRight.isEnabled = false
        chart.xAxis.isEnabled = false
        chart.xAxis.axisMinimum = 0f
        chart.xAxis.axisMaximum = 20f

        chart.data = barData
        chart.minOffset = 0f
        chart.notifyDataSetChanged()
    }

    private fun initPressurePrivacyChart(chart: BarChart) {
        val maxCount = 14

        val barDataSetList = ArrayList<IBarDataSet>()
        for (i in 0 until maxCount) {
            val entries = ArrayList<BarEntry>()
            entries.add(BarEntry(i.toFloat(), 15f))
            val barDataSet = BarDataSet(entries, "");
            barDataSet.color = HmsApplication.appContext.getColor(R.color.card_press_bar_bg_color_default)
            barDataSetList.add(barDataSet)
        }

        // 设置柱状图的宽度和高度
        val barData = BarData(barDataSetList);
        barData.setDrawValues(false)
        barData.barWidth = 0.5f
        val barSpace = 0.51f // 设置柱状图之间的间距
        barData.groupBars(0F, 0F, barSpace)

        chart.setNoDataText("")
        chart.renderer = RoundedBarChartRenderer(chart, chart.animator, chart.viewPortHandler, 0.5F)
        chart.setBackgroundColor(Color.TRANSPARENT)
        chart.setBorderColor(Color.TRANSPARENT); //是否展示网格线
        chart.setDrawBorders(false); //是否显示边界
        chart.setScaleEnabled(false); // 是否可以缩放
        chart.setTouchEnabled(false); //是否有触摸事件
        chart.legend.isEnabled = false
        //设置XY轴描述
        var description = Description();
        description.isEnabled = false;
        description.text = "s";
        chart.description = description;
        var yAxisLeft = chart.axisLeft
        yAxisLeft.setDrawGridLines(false)
        yAxisLeft.isEnabled = false
        yAxisLeft.axisMinimum = 0F
        yAxisLeft.axisMaximum = 15f

        chart.axisRight.setDrawGridLines(false)
        chart.xAxis.setDrawGridLines(false)
        chart.axisRight.isEnabled = false
        chart.xAxis.isEnabled = false
        chart.xAxis.axisMinimum = 0f
        chart.xAxis.axisMaximum = 14f

        chart.data = barData
        chart.minOffset = 0f
        chart.notifyDataSetChanged()
    }

    private fun doPressureAnim(chart: BarChart) {
        // 获取 ChartAnimator
        val animator = chart.animator
        animator.animateY(500, Easing.Linear)
        chart.invalidate()
    }

    class ViewHolder(view: View, onItemClick: ((cardType: Int, cardDTO: HomeCardDTO<*>) -> Unit)?) : RecyclerView.ViewHolder(view) {
        var normalDataLayout: FrameLayout
        var noDataLayout: FrameLayout

        init {
            normalDataLayout = view.findViewById(R.id.card_normal_data)
            noDataLayout = view.findViewById(R.id.card_no_data)
            // 使用 ClickUtils 进行防抖处理，同时仅在点击时通过 view.tag 获取最新数据
            ClickUtils.applySingleDebouncing(view, 1000) {
                // 从 itemView 的 tag 中获取当前数据
                val cardDTO = view.tag as? HomeCardDTO<*>
                if (cardDTO != null) {
                    // 调用外部传入的 onItemClick lambda，避免直接引用外部对象
                    onItemClick?.invoke(cardDTO.cardType, cardDTO)
                }
            }
        }
    }

    fun setDataGray(time: String?, view: View) {
        val text = view.findViewById<TextView>(R.id.tv_data_year_or_day)
        text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_40))
        if (time != null) {
            try {
                val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                val time = sdf.parse(time)
                val calendar: Calendar = Calendar.getInstance()
                calendar.add(Calendar.DATE, 0) // 将当前时间回退一天
                val now: Date = calendar.getTime()
                val isBefore24Hours = (now.time - time.time) < 24 * 60 * 60 * 1000

                if (isBefore24Hours) {
                    text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_100))
                } else {
                    text.setTextColor(HmsApplication.appContext.getColor(R.color.text_color_fc_40))
                }
            } catch (e: ParseException) {
                e.printStackTrace()
            }
        }
    }

    fun privacyUi() {
        currentHolder?.let { holder ->
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_privacy_data)?.visibility = View.GONE
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_normal_data)?.visibility = View.GONE
            holder.get()?.itemView?.findViewById<FrameLayout>(R.id.card_no_data)?.visibility = View.VISIBLE
        }
    }
}