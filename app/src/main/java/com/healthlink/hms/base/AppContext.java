package com.healthlink.hms.base;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Application;

import com.healthlink.hms.BuildConfig;
import com.healthlink.hms.api.AppExecutors;
import com.healthlink.hms.utils.ListUtil;
import com.healthlink.hms.utils.LogUtil;

import java.util.LinkedList;

public class AppContext extends BaseContext {

    public static boolean mock = false;
    /**
     * 客户端类型
     */
    public final static String CLIENT_NAME = "android";

    /**
     * 渠道（非walle打包渠道，如debug安装）
     */
    private final static String CHANNEL_NAME = "INSTALL_CHANNEL";

    public static String channel = "official";

    public static String app_version = BuildConfig.VERSION_NAME;

    public static AppExecutors executors;

    /**
     * 手势密码开启状态
     */
    private static boolean isOpenGestures;

    public static boolean isNeedGestures = false;

    public static String mPushToken;

    /**
     * 账户信息锁
     */
    private final static byte[] sLoginInfoLock = new byte[0];

    /**
     * 用户信息锁
     */
    private final static byte[] sUserInfoLock = new byte[0];

    /**
     * 历史账户信息锁
     */
    private static byte[] sHistoryLock = new byte[0];

    private static int foregroundCount;

    private static long backgroundTime;

    private static LinkedList<Activity> activityList;

    /**
     * 初始化
     */
    @SuppressLint("NewApi")
    public static void init(Application application) {
        BaseContext.init(application);
//        JniUtils jniUtils = JniUtils.getInstance();
//        jniUtils.setJNIDebug(BuildConfig.DEBUG);
//        jniUtils.initJNI(app.getApplicationContext());
//        ApplicationInfo appInfo;
//        try {
//            appInfo = sAppContext.getPackageManager().getApplicationInfo(sAppContext.getPackageName(), PackageManager.GET_META_DATA);
//            app_version = appInfo.metaData.getString("VERSIONNAME");
//        } catch (PackageManager.NameNotFoundException e) {
//            e.printStackTrace();
//        }
//        try {
//            channel = WalleChannelReader.getChannel(sAppContext);
//            if (TextUtils.isEmpty(channel)) {
//                appInfo = sAppContext.getPackageManager().getApplicationInfo(sAppContext.getPackageName(), PackageManager.GET_META_DATA);
//                channel = appInfo.metaData.getString(CHANNEL_NAME);
//            }
//        } catch (PackageManager.NameNotFoundException e) {
//            channel = "unknown";
//        }
//        Thread.setDefaultUncaughtExceptionHandler(new AppExceptionHandler());
//
//        activityList = new LinkedList<>();
//        AppComponent appComponent = DaggerAppComponent.builder().appModule(new AppModule(app)).httpModule(new HttpModule(BuildConfig.BASE_URL)).build();
//        appComponent.inject(app);
//        backgroundTime = System.currentTimeMillis();
//        app.registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
//            @Override
//            public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
//                activityList.addFirst(activity);
//                handleActivity(activity);
//            }
//
//            @Override
//            public void onActivityStarted(Activity activity) {
//                if (foregroundCount == 0 && (System.currentTimeMillis() - backgroundTime) / 1000 > 120 && isLogin()) {
//                    SecurityAssistant.getInstance().resumeAuth(activity);
//                }
//                foregroundCount++;
//            }
//
//            @Override
//            public void onActivityResumed(Activity activity) {
//
//            }
//
//            @Override
//            public void onActivityPaused(Activity activity) {
//
//            }
//
//            @Override
//            public void onActivityStopped(Activity activity) {
//                foregroundCount--;
//                backgroundTime = System.currentTimeMillis();
//            }
//
//            @Override
//            public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
//
//            }
//
//            @Override
//            public void onActivityDestroyed(Activity activity) {
//                activityList.remove(activity);
//            }
//        });
    }

    /**
     * 关闭打开的Activity
     */
    public static void clearActivity() {
        if (ListUtil.isEmpty(activityList)) {
            return;
        }
        for (Activity activity : activityList) {
            activity.finish();
        }
    }

    /**
     * 获取顶部的Activity
     */
    public static Activity getTopActivity() {
        if (ListUtil.isEmpty(activityList)) {
            return null;
        }
        return activityList.peek();
    }

    /**
     * 回到APP首页
     */
//    public static void toMainActivity(Activity activity, @AppConstant.LoginAction int loginAction) {
//        if (activity != null) {
//            Intent intent = new Intent(activity, MainActivity.class);
//            intent.putExtra("login_action", loginAction);
//            activity.startActivity(intent);
////            activity.finish();
//        }
//    }

//    private static void handleActivity(Activity activity) {
//        if (activity instanceof HasSupportFragmentInjector || activity instanceof Injectable) {
//            AndroidInjection.inject(activity);
//        }
//        if (activity instanceof FragmentActivity) {
//            ((FragmentActivity) activity).getSupportFragmentManager().registerFragmentLifecycleCallbacks(
//                    new FragmentManager.FragmentLifecycleCallbacks() {
//                        @Override
//                        public void onFragmentPreCreated(@NonNull FragmentManager fm, @NonNull Fragment f, @Nullable Bundle savedInstanceState) {
//                            if (f instanceof Injectable) {
//                                AndroidSupportInjection.inject(f);
//                            }
//                        }
//
//                        @Override
//                        public void onFragmentResumed(@NonNull FragmentManager fm, @NonNull Fragment f) {
//                            if (f instanceof PageUri) {
//                                BuryUtils.buryBeginPage(activity, TextUtils.isEmpty(((PageUri) f).pageName()) ? f.getClass().getName() : ((PageUri) f).pageName());
//                            }
//                            LogUtil.d("onResume:" + f.getClass().getName());
//                        }
//
//                        @Override
//                        public void onFragmentPaused(@NonNull FragmentManager fm, @NonNull Fragment f) {
//                            if (f instanceof PageUri) {
//                                BuryUtils.buryEndPage(activity, TextUtils.isEmpty(((PageUri) f).pageName()) ? f.getClass().getName() : ((PageUri) f).pageName());
//                            }
//                            LogUtil.d("onPause:" + f.getClass().getName());
//                        }
//                    }, true);
//        }
//    }

    /**
     * 检测网络是否畅通
     *
     * @return true:畅通，false:不畅通
     */
    public static boolean checkNetWork() {
//        if (mock) {
//            return true;
//        }
        return BaseContext.checkNetWork();
    }

    /**
     * @param
     */
//    public static void setLoginInfo(UserLoginInfo loginInfo) {
//        synchronized (sLoginInfoLock) {
//            AccountInfoConfig.setLoginInfo(loginInfo);
//        }
//    }

//    public static UserLoginInfo getLoginInfo() {
//        synchronized (sLoginInfoLock) {
//            return AccountInfoConfig.getLoginInfo();
//        }
//    }



//    public static boolean isLogin() {
//        UserLoginInfo loginInfo = getLoginInfo();
//        //认证登录信息
//        if (!TextUtils.isEmpty(loginInfo.token)) {
//            return true;
//        }
//        return false;
//    }

    /**
     * @param
     */
//    public static void setUserInfo(UserInfo userInfo) {
//        synchronized (sUserInfoLock) {
//            AccountInfoConfig.setAccount(userInfo);
//        }
//    }

//    public static UserInfo getUserInfo() {
//        synchronized (sUserInfoLock) {
//            UserInfo userInfo = AccountInfoConfig.getAccount();
//            if (userInfo == null) {
//                userInfo = new UserInfo();
//            }
//            return userInfo;
//        }
//    }

    /**
     * 清空账户信息
     */
//    public static void clearUserInfo() {
//        setLoginInfo(null);
//        setUserInfo(null);
//    }

//    public static void gotoLogin() {
//        Activity activity = getTopActivity();
//        if (activity != null && !(activity instanceof LoginActivity)) {
//            LoginHelper.getInstance().setLogin(activity);
//        }
//    }

//    public static void needLogin() {
//        clearUserInfo();
//        Activity activity = getTopActivity();
//        if (activity != null && !(activity instanceof LoginActivity)) {
//            LoginHelper.getInstance().reLogin(activity);
//        }
//    }

//    public static void needLogin(String messageTip) {
//        UserLoginInfo loginInfo = getLoginInfo();
//        clearUserInfo();
//        Activity activity = getTopActivity();
//        if (activity != null && !(activity instanceof LoginActivity) && activity instanceof FragmentActivity) {
//            LoginHelper.getInstance().reLogin(activity, loginInfo.userId, messageTip);
//        }
//    }
}
