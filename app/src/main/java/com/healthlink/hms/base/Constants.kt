package com.healthlink.hms.base

object Constants {

    const val USER_AGREEMENT_H5_URL = "https://pages.healthlinkiot.com/doc/UserAgreement1.1.16.htm"
    const val PRIVACY_STATEMENT_H5_URL = "https://pages.healthlinkiot.com/doc/PrivacyStatement1.1.16.htm"

    const val ECAllViewTag: Int = 2000

    //智能腰托系统消息的通知ID
    const val NOTIFICATION_ID_SEAT_WAIST_DIRECTION = "NOTIFICATION_ID_SEAT_WAIST_DIRECTION"

    // 座椅通风系统消息的通知ID
    const val NOTIFICATION_ID_SEAT_VENTILATION = "NOTIFICATION_ID_SEAT_VENTILATION"


    //本地信息存储
    const val SHARE_HMS_INFO = "hms_info"
    //系统启动时间
    const val SHARE_HMS_INFO_BOOT_TIME = "boot_time"

    //用户信息存储
    const val SHARE_USER_INFO = "user_info"
    //用户ID的存储Key
    const val SHARE_USER_INFO_USER_ID = "userId"

    //Widget 存储信息Key
    const val SHARE_WIDGET_INFO = "widget_info"
    const val SHARE_WIDGET_INFO_LAST_PLAY_TTS_TIME = "widget.info.last_play_tts_time"
    const val SHARE_WIDGET_INFO_IS_FIRST_LOADING= "widget.info.is_first_loading"

    // 记录用户回首页
    const val SHARE_IS_USER_RETURN_TO_MAIN = "is_user_return_to_main"
    const val BACK_TO_MAIN = "back_to_main"
    // 接收到widget数据
    const val SHARE_RECEIVE_WIDGET_DATA = "share_receive_widget_data"
    const val RECEIVE_WIDGET_DATA = "RECEIVE_WIDGET_DATA"
    // 场景引擎 关联功能
    const val END_TAG_FUNC_DOCTOR = "&HMS_FUNC_DOCTOR"
    const val NOTIFICATION_ID_PHONE_DOCTOR_SERVICE = "NOTIFICATION_ID_PHONE_DOCTOR_SERVICE"
    // 打开放松模式通知ID
    const val NOTIFICATION_ID_OPEN_RELAX_MODE = "NOTIFICATION_ID_OPEN_RELAX_MODE"
    // 打开导航App搜索服务区通知ID
    const val NOTIFICATION_ID_NAVIGATION_SEARCH_SERVICE = "NOTIFICATION_ID_NAVIGATION_SEARCH_SERVICE"

    // 用户Token
    const val SHARE_USER_TOKEN = "share_user_token"
    // 用户权限
    const val SHARE_USER_AUTHORITY = "share_user_authority"
    // 用户ID
    const val SHARE_USER_ID = "share_user_id"
    // 用户协议与隐私政策
    const val USER_PRIVACY_AGREEMENT = "user_privacy_agreement"
    // 用户协议与隐私政策 TOAST
    const val USER_PRIVACY_AGREEMENT_TOAST = "show_user_agreement_toast"

    const val FROM_TYPE_CARD_OPEN_CHAT = "card"
    const val FROM_TYPE_TOAST_OPEN_CHAT = "toast"
}