package com.healthlink.hms.sceneEngine

import android.util.Log
import com.healthlink.hms.mvvm.network.RetrofitClient
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.StringUtil

/**
 * Created by imaginedays on 2024/7/14
 * 场景抽象类
 */

/**
 * 场景名枚举
 */
enum class SceneNameEnum(val sceneName: String) {
    HEALTH_SEVERE_EXCEPTION("健康严重异常关怀"),
    HIGH_REACTION_CARE_1("高反关怀1"),
    HIGH_REACTION_CARE_2("高反关怀2"),
    HEALTH_WEEK_REPORT("健康周报"),
    WELCOME_AFTER_BOOT("开机健康迎宾"),
    AFTER_WORK_CARE_1("下班关怀1"),
    AFTER_WORK_CARE_2("下班关怀2"),
    LONG_DRIVE_CARE_1("长途驾驶关怀1"),
    LONG_DRIVE_CARE_2("长途驾驶关怀2"),
}

/**
 * 场景类型枚举
 */
enum class SceneType(val sceneTypeName: String) {
    Care("关怀类"),
    Intervention("干预类"),
}

abstract class Scene(
    val sceneManager: SceneManager,
    val sceneName: String,
    val sceneNameCode: String,
    val scenePriority: Int,
    val sceneTypeName: String,
    val sceneTypeCode: Int,
    val sceneTypePriority: Int,
    val scenePrecondition: Map<String, Any>,
    val sceneExecuteCondition: Map<String, Any>
) {
    val TAG = "SceneManager"
    abstract suspend fun scenePrecondition(): Boolean
    abstract suspend fun sceneExecuteConditionForResult() : String?
    abstract fun scenePostCondition(result: String? = null)

    fun isLogin(): Boolean {
        return !MMKVUtil.isVisitorMode()
    }

    suspend fun lonAndLatConvertElevation(lat : Double?, lon : Double?): Double? {
        if (lat == null || lon == null) {
            return null
        }
        if (!(lat > 0) || !(lon > 0)) {
            return null
        }
        RetrofitClient.apiService.getElevationByGps("${StringUtil.formatDouble(lat)},${StringUtil.formatDouble(lon)}").let { dto ->
            Log.i(TAG, "getElevationByGps $dto")
            val okRes = dto.code == "0" && dto.data != null && dto.data!!.elevation != null && dto.data!!.elevation!! > 0
            if (okRes) {
                return dto.data!!.elevation
            }
        }
        return null
    }
}