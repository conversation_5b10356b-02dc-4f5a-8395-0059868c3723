package com.healthlink.hms.sceneEngine.scenes

import android.util.Log
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.sceneEngine.Scene
import com.healthlink.hms.sceneEngine.SceneManager
import com.healthlink.hms.sceneEngine.dto.SceneWelcomeDTO
import com.healthlink.hms.sceneEngine.SceneWelcomeScript
import com.healthlink.hms.base.Constants
import com.healthlink.hms.mvvm.network.RetrofitClient
import com.healthlink.hms.sdks.gwmadapter.GwmAdapterManagerKotCoroutines
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.NotificationUtil
import com.healthlink.hms.utils.TimeUtils
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.withTimeoutOrNull

/**
 * Created by imaginedays on 2024/7/14
 * 开机迎宾
 */
class Scene1WelcomeAfterBootImpl(
    sceneManager: SceneManager,
    sceneName: String,
    sceneNameCode: String,
    scenePriority: Int,
    sceneTypeName: String,
    sceneTypeCode: Int,
    sceneTypePriority: Int,
    scenePrecondition: Map<String, Any>,
    sceneExecuteCondition: Map<String, Any>
) : Scene(sceneManager,sceneName,sceneNameCode, scenePriority, sceneTypeName, sceneTypeCode, sceneTypePriority, scenePrecondition, sceneExecuteCondition) {
    override suspend fun scenePrecondition(): Boolean {
        // 1、当天是否播报过迎宾场景
        val welcomeTime = MMKVUtil.getWelcomeTime()
        val today = TimeUtils.formatDateString(System.currentTimeMillis(), "yyyy-MM-dd")
        if (welcomeTime != null && welcomeTime == today) {
            Log.i(TAG, "$sceneName 当天已经播报过迎宾")
            return false
        }

        // 2、车载健康App通知设置为打开状态
        if (!MMKVUtil.getNotificationOpen()) {
            Log.i(TAG, "$sceneName 通知设置未打开")
            return false
        }
        return true
    }

    override suspend fun sceneExecuteConditionForResult() : String? {
        var liveIndexRes : MutableMap<String, String?>? = mutableMapOf()
        var airCurrentRes: MutableMap<String,String?>? = mutableMapOf()
        var result: String? = "" // 用于接收脚本执行结果
        try {
            coroutineScope {
                if (!isLogin()) {
                    throw Exception("非App用户")
                }
                Log.i(TAG, "$sceneName 开始执行")

                // 1.获取生活指数
                try {
                    withTimeoutOrNull(2000) {
                        liveIndexRes = GwmAdapterManagerKotCoroutines.sendWeatherLiveIndexReq(sceneManager)
                        Log.i(TAG, "$sceneName $liveIndexRes")
                    }
                }catch (ex:Exception){
                    Log.i(TAG,"Scene1WelcomeAfterBootImpl: 获取天气信息失败，不考虑天气信息")
                }

                // 2. 获取当前空气质量
                try {
                    withTimeoutOrNull(2000) {
                        airCurrentRes = GwmAdapterManagerKotCoroutines.sendWeatherAirCurrentReq()
                        if (airCurrentRes!!.isNotEmpty()) {
                            liveIndexRes!![GwmAdapterManagerKotCoroutines.KEY_TYPE_AIR] = airCurrentRes!![GwmAdapterManagerKotCoroutines.KEY_TYPE_AIR]
                        }
                        Log.i(TAG, "$sceneName airCurrentRes $airCurrentRes")
                    }
                }catch (ex:Exception){
                    Log.i(TAG,"$sceneName: 获取当前空气质量失败")
                }

                Log.i(TAG, "$sceneName end")

                // 3.获取健康评分数据
                withTimeoutOrNull(1000) {

                    var reqMap = hashMapOf(
                        "userId" to MMKVUtil.getUserId()!!
                    )
                    // 健康得分
                    val liveHealthStatusDTO = RetrofitClient.apiService.getLiveHealthStatus(reqMap)
                    Log.i(TAG, "$sceneName $liveHealthStatusDTO")
                    val healthScore =  liveHealthStatusDTO.data?.score?.toInt()
                    // 加入未测量任何参数的实时健康状态判断
                    if ((healthScore == 0 || liveHealthStatusDTO.data?.riskLevel.isNullOrEmpty()) && (liveIndexRes == null || liveIndexRes?.size!! <= 0)) {
                        Log.i(TAG,"健康得分没有数据&天气指数无数据")
                        throw Exception("健康得分没有数据&天气指数无数据")
                    }

                    val welComeDTO =  SceneWelcomeDTO(
                        healthScore,
                        liveIndexRes!![GwmAdapterManagerKotCoroutines.KEY_TYPE_UV],
                        liveIndexRes!![GwmAdapterManagerKotCoroutines.KEY_TYPE_FLU],
                        liveIndexRes!![GwmAdapterManagerKotCoroutines.KEY_TYPE_AIR])
                    Log.i(TAG, "$sceneName welComeDTO $welComeDTO")
                    result =  SceneWelcomeScript().gernarateWelcomeTip(welComeDTO)
                    Log.i(TAG, "$sceneName RESULT $result")

                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Log.i(TAG, "Scene1WelcomeAfterBootImpl $sceneName 异常 ${e.message}")
            return ""
        }
        return result
    }

    override fun scenePostCondition(result: String?) {
        result?.let {
            DataTrackUtil.dtTrigger("Health_CaringScenes_Open_Trigger")
            if (it.endsWith(Constants.END_TAG_FUNC_DOCTOR)) {
                // 开机迎宾普通通知 关联功能 左侧 电话医生，右侧 查看详情
                val msg = it.substring(0, result.length - Constants.END_TAG_FUNC_DOCTOR.length)
                NotificationUtil(HmsApplication.appContext).sendCallDoctorNotification(msg,"需要为您联系在线医生吗?")
            } else {
                // 开机迎宾普通通知 不需要action
                NotificationUtil(HmsApplication.appContext).sendWelcomeNotification(it,it)
            }
        }
    }

}