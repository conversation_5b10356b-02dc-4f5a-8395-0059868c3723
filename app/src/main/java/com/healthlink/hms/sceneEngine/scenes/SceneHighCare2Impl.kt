package com.healthlink.hms.sceneEngine.scenes

import android.util.Log
import com.healthlink.hms.HmsSettings
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.sceneEngine.Scene
import com.healthlink.hms.sceneEngine.SceneManager
import com.healthlink.hms.mvvm.model.request.HealthInfoRequestParam
import com.healthlink.hms.mvvm.network.RetrofitClient
import com.healthlink.hms.sdks.map.gwm.GWMMapLocDTO
import com.healthlink.hms.sdks.map.gwm.GWMMapManagerKotCoroutines
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.NotificationUtil
import com.healthlink.hms.utils.map.AltitudeManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch

/**
 * Created by imaginedays on 2024/7/23
 * 高反关怀2 干预类
 * 3、前置条件：
 * 3.1、当前位置海拔：高于2500米
 * 3.2、血氧含量低于90%
 */
class SceneHighCare2Impl(sceneManager: SceneManager,
                         sceneName: String,
                         sceneNameCode: String,
                         scenePriority: Int,
                         sceneTypeName: String,
                         sceneTypeCode: Int,
                         sceneTypePriority: Int,
                         scenePrecondition: Map<String, Any>,
                         sceneExecuteCondition: Map<String, Any>
) : Scene(sceneManager, sceneName,sceneNameCode, scenePriority, sceneTypeName, sceneTypeCode, sceneTypePriority, scenePrecondition, sceneExecuteCondition) {

    override suspend fun scenePrecondition(): Boolean {
        var desLocation : GWMMapLocDTO? = null
        try {
            coroutineScope {
            // 0 非注册用户
            if (!isLogin()) {
                throw Exception("高反关怀2：非注册用户")
            }

            launch(Dispatchers.IO) {
                // 2、当前位置海拔：高于2500米
                val destinationLocation = GWMMapManagerKotCoroutines.sendCurrentLocationAsyncReq()
//                    destinationLocation?.data?.let {
//                        it.lon = 91.117212
//                        it.lat = 29.646922
//                    }

                Log.i(TAG, "$sceneName $destinationLocation")
                if (destinationLocation?.data != null && destinationLocation.data.lat != null && destinationLocation.data.lon != null) {
                    desLocation = destinationLocation.data
                } else {
                    throw Exception("高反关怀2：未获取到当前地位置数据")
                }
            } .join()

            /**
             * 经纬度转海拔换算
             */
            launch(Dispatchers.IO) {
//                val isAltitudeLevelOver2500 = AltitudeManager.isSameAltitude(desLocation!!.city ?:"",desLocation!!.district ?:"", 2,"$sceneName 当前位置海拔是否大于2500")
                val isAltitudeLevelOver2500 = AltitudeManager.isHighAltitudeArea(
                    desLocation!!.city ?:"",
                    desLocation!!.district ?:"",
                    desLocation!!.adcode ?:"")
                if(!isAltitudeLevelOver2500) {
                    throw Exception("高反关怀2：当前位置海拔不高于2500米")
                }
            }.join()

            launch(Dispatchers.IO) {
                // 3、血氧含量低于90%
                val healthInfoRequestParam = HealthInfoRequestParam()
                healthInfoRequestParam.userId = MMKVUtil.getUserId()
                val healthInfoDTO = RetrofitClient.apiService.newHealthInfo(healthInfoRequestParam)
                Log.i(TAG, "$sceneName $healthInfoDTO")
                healthInfoDTO.let { dto ->
                    val bloodOxygenValue = dto.data?.bloodOxygen?.bloodOxygen
                    var okRes = dto.code == "0" && bloodOxygenValue != null && bloodOxygenValue < 90
                    if (!okRes) {
                        throw Exception("高反关怀2：当前血氧含量血氧含量${bloodOxygenValue}%不低于90%")
                    }
                }

                // 如果5分内有拨打过电话医生（正在通话中或是已挂断）
                val lastDoctorCallTime = MMKVUtil.getLastDoctorCallTime()
                val intervalSinceLastDoctorCall = System.currentTimeMillis() - lastDoctorCallTime
                if(intervalSinceLastDoctorCall < HmsSettings.INTERVAL_FOR_CANCEL_SCENE_AFTER_DOCTOR_CALL){
                    SceneManager.resetSceneHighReact2CountAfterCallDoctor()
                    throw Exception("高反关怀2： 5分钟内拨打过电话医生，不再播报，且本次行程取消播报")
                }
            }.join()
            }
        } catch (e: Exception) {
            Log.i(TAG, "$sceneName ${e.message}")
            return false
        }
        return true
    }

    override suspend fun sceneExecuteConditionForResult(): String? {
        return null
    }

    override fun scenePostCondition(result: String?) {
        DataTrackUtil.dtTrigger("Health_CaringScenes_Highanti2_Trigger")
        // 高反关怀2通知 左侧 在线医生 右侧 不需要
        NotificationUtil(HmsApplication.appContext).sendCallDoctorOrEmergencyNotification(HmsApplication.appContext.getString(R.string.notification_play_tts_highCarePrecondition2),
            "您的血氧含量偏低，需要为您联系在线医生吗？",HmsApplication.appContext.getString(R.string.doctor_service))
    }
}