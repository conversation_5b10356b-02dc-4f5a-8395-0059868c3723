package com.healthlink.hms.sceneEngine.scenes

import android.util.Log
import com.healthlink.hms.HmsSettings
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.sceneEngine.Scene
import com.healthlink.hms.sceneEngine.SceneManager
import com.healthlink.hms.mvvm.network.RetrofitClient
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.NotificationUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch

/**
 * Created by imaginedays on 2024/7/23
 *  健康严重异常
 */
class SceneHealthAbnormalImpl(sceneManager: SceneManager,
                              sceneName: String,
                              sceneNameCode: String,
                              scenePriority: Int,
                              sceneTypeName: String,
                              sceneTypeCode: Int,
                              sceneTypePriority: Int,
                              scenePrecondition: Map<String, Any>,
                              sceneExecuteCondition: Map<String, Any>
) : Scene(sceneManager, sceneName,sceneNameCode, scenePriority, sceneTypeName, sceneTypeCode, sceneTypePriority, scenePrecondition, sceneExecuteCondition) {
    override suspend fun scenePrecondition(): Boolean {
        try {
            coroutineScope {
                if (!isLogin()) {
                    throw Exception("健康严重异常：非注册用户")
                }

                launch(Dispatchers.IO){
                    var reqMap = mutableMapOf<String, String>(
                        "userId" to MMKVUtil.getUserId()!!
                    )

                    // 健康等级
                    val liveHealthStatusDTO = RetrofitClient.apiService.getLiveHealthStatus(reqMap)
                    liveHealthStatusDTO.let { dto ->
                        var okRes = dto.code == "0" && dto.data != null && (dto.data!!.riskCode == 1)
                        if(!okRes) {
                            throw Exception("健康严重异常： ${dto.data!!.riskLevel} 健康等级不为较差")
                        }
                    }

                    // 如果5分内有拨打过电话医生（正在通话中或是已挂断）
                    val lastDoctorCallTime = MMKVUtil.getLastDoctorCallTime()
                    val intervalSinceLastDoctorCall = System.currentTimeMillis() - lastDoctorCallTime
                    if(intervalSinceLastDoctorCall < HmsSettings.INTERVAL_FOR_CANCEL_SCENE_AFTER_DOCTOR_CALL){
                        SceneManager.resetSceneHealthAbnormalCountAfterCallDoctor()
                        throw Exception("健康严重异常： 5分钟内拨打过电话医生，不再播报，且本次行程取消播报")
                    }
                }
            }
        } catch (e: Exception) {
            Log.i(TAG, "$sceneName $e")
            return false
        }
        return true
    }

    override suspend fun sceneExecuteConditionForResult(): String? {
        return null
    }

    override fun scenePostCondition(result: String?) {
        DataTrackUtil.dtTrigger("Health_CaringScenes_Abnormalhealth_Trigger")
        NotificationUtil(HmsApplication.appContext).sendHealthAbnormalNotification(
            HmsApplication.appContext.getString(R.string.notification_play_tts_healthAbnormalPrecondition),
            "需要为您联系在线医生吗?",
            HmsApplication.appContext.getString(R.string.doctor_service))
    }
}