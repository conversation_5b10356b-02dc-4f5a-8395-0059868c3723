package com.healthlink.hms.sceneEngine.scenes

import android.util.Log
import com.google.gson.Gson
import com.gwm.android.adapter.client.GwmAdapterClient
import com.gwm.map.sdk.MapServiceProxy
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.sceneEngine.Scene
import com.healthlink.hms.sceneEngine.SceneManager
import com.healthlink.hms.sceneEngine.dto.SceneGearStatusInfoDTO
import com.healthlink.hms.sceneEngine.dto.SceneNavigationInfoDTO
import com.healthlink.hms.sdks.gwmadapter.GwmAdapterManager
import com.healthlink.hms.sdks.gwmadapter.GwmAdapterManagerKotCoroutines
import com.healthlink.hms.sdks.map.gwm.GWMMapManagerKotCoroutines
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.NotificationUtil
import com.healthlink.hms.utils.TimeUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch

/**
 * Created by imaginedays on 2024/7/23
 * 长途驾驶关怀2
 */
class SceneLongDriveCare2Impl(sceneManager: SceneManager,
                              sceneName: String,
                              sceneNameCode: String,
                              scenePriority: Int,
                              sceneTypeName: String,
                              sceneTypeCode: Int,
                              sceneTypePriority: Int,
                              scenePrecondition: Map<String, Any>,
                              sceneExecuteCondition: Map<String, Any>
) : Scene(sceneManager,sceneName,sceneNameCode, scenePriority, sceneTypeName, sceneTypeCode, sceneTypePriority, scenePrecondition, sceneExecuteCondition) {
    override suspend fun scenePrecondition(): Boolean {
        try {
            coroutineScope {
                // 0 非注册用户
                if (!isLogin()) {
                    throw Exception("长途驾驶关怀2：非注册用户")
                }

                launch {
                    var gearStatus = GwmAdapterManagerKotCoroutines.getGearStatus()
                    if (gearStatus == null || (gearStatus != "1" && gearStatus != "2")) {
                        throw Exception("当前档位是$gearStatus = ${mapGearStatusDes(gearStatus!!)} , 不触发长途驾驶关怀2")
                    }
                }.join()

                launch {
                    // 1、导航：到达目的地剩余时间大于1小时
                    val isEstimateToArrival = GWMMapManagerKotCoroutines.sendQueryEsitmateToArrivalAsyncReq()
                    if (!isEstimateToArrival) {
                        throw Exception("长途驾驶关怀2：导航目的地剩余时间小于1小时")
                    } }.join()

                launch { // 2、连续驾驶时长：大于2小时
                    val isDrivingDuration2Hour =  GWMMapManagerKotCoroutines.isDrivingDurationOver2Hours()
                    if (!isDrivingDuration2Hour) {
                        throw Exception("长途驾驶关怀2：连续驾驶时长小于2小时")
                    } }.join()

                launch { // 3、道路类型：高速
                    val isHighway =  GWMMapManagerKotCoroutines.sendQueryRoadLevelAsyncReq()
                    if (!isHighway) {
                        throw Exception("长途驾驶关怀2：当前道路类型不是高速")
                    }
                }.join()
            }
        } catch (e: Exception) {
            Log.i(TAG, "$sceneName $e")
            return false
        }
        return true
    }

    private fun mapGearStatusDes(gearStatus: String): String {
        return when (gearStatus) {
            "0" -> "N挡"
            "3" -> "P挡"
            "4" -> "R挡"
            "1" -> "S挡"
            "2" -> "D挡"
            "5" -> "M挡"
            else -> "未知挡位"
        }
    }

    override suspend fun sceneExecuteConditionForResult(): String? {
        return null
    }

    override fun scenePostCondition(result: String?) {
        DataTrackUtil.dtTrigger("Health_CaringScenes_Longdistance2_Trigger")
        NotificationUtil(HmsApplication.appContext).sendOpenNavigationAppNotification(HmsApplication.appContext.getString(R.string.notification_play_tts_longDrive2),true)
    }



    companion object {
        /**
         * 重置长途驾驶关怀2的相关参数
         */
        fun resetJourney(){
            // 重置档位信息（时间和休息时长）
            resetGearStatusForLongDriveCare2()
            // 重置导航信息与导航状态
            resetNavigateInfo()
        }

        /**
         * 连接导航服务后是否需要重置行程
         * 本地 有导航数据 和 无导航数据
         * 获取导航接口： 导航和非导航
         * 本地导航状态：是， 地图导航：是 不处理，
         * 本地导航状态：是， 地图导航：否 重置，
         * 本地导航状态：否， 地图导航：是 重置，
         * 本地导航状态：否， 地图导航：否 不处理，
         */
        fun resetJourneyAfterConnectedMapService() {
            CoroutineScope(Dispatchers.IO).launch {
                runCatching {
                    val isNaviMode = GWMMapManagerKotCoroutines.sendQueryGuideStatusAsyncReq()
                    val localNavigateInfo = MMKVUtil.getNavigationInfo()

                    if (localNavigateInfo.isNullOrEmpty()) {
                        if (isNaviMode) resetJourney()
                    } else {
                        val localNavigationInfoDTO = Gson().fromJson(localNavigateInfo, SceneNavigationInfoDTO::class.java)
                        val shouldReset = (localNavigationInfoDTO.status == 0 && !isNaviMode) ||
                                (localNavigationInfoDTO.status == 1 && isNaviMode)
                        if (shouldReset) resetJourney()
                    }
                }.onFailure { e ->
                    Log.i(SceneManager.TAG, "resetJourneyAfterConnectedMapService: ${e.message}")
                }
            }
        }

        /**
         * 重置导航信息
         * TODO: 重置导航信息的各种case
         */
        @OptIn(DelicateCoroutinesApi::class)
        private fun resetNavigateInfo() {
            GlobalScope.launch(Dispatchers.IO) {
                launch {
                    // 获取之前存储的导航信息
                    val navigateInfo = MMKVUtil.getNavigationInfo()
                    // 获取当前导航状态
                    Log.i(SceneManager.TAG, "begin sendQueryGuideStatusAsyncReq")
                    val isNaviMode = GWMMapManagerKotCoroutines.sendQueryGuideStatusAsyncReq()
                    Log.i(SceneManager.TAG, "end sendQueryGuideStatusAsyncReq isNaviMode $isNaviMode")
                    val gson = Gson()
                    if (navigateInfo != null && navigateInfo != "") {
                        try {
                            val currentNavigationInfoDTO =
                                gson.fromJson(navigateInfo, SceneNavigationInfoDTO::class.java)
                            currentNavigationInfoDTO.status = if(isNaviMode) 0 else 1
                            currentNavigationInfoDTO.timestamp = System.currentTimeMillis()

                            val navigateInfoStr = gson.toJson(currentNavigationInfoDTO)
                            MMKVUtil.storeNavigationInfo(navigateInfoStr)
                        } catch (e: Exception) {
                            Log.i(SceneManager.TAG, "currentNavigationInfoDTO fromJson error $e")
                        }
                    } else {
                        // 没有存储过导航信息
                        Log.i(SceneManager.TAG, "getNavigationInfo null new SceneNavigationInfoDTO")
                        val newNavigationInfoDTO = SceneNavigationInfoDTO().apply {
                            status = if(isNaviMode) 0 else 1
                            timestamp = System.currentTimeMillis()
                        }
                        val navigateInfoStr = gson.toJson(newNavigationInfoDTO)
                        MMKVUtil.storeNavigationInfo(navigateInfoStr)
                    }
                    resetNavigateStatus(isNaviMode)

                }.join()
            }
        }

        /**
         * 重置导航状态数据
         */
        private fun resetNavigateStatus(isNaviMode: Boolean) {
            MMKVUtil.storeNavigationGuideStatus(isNaviMode)
        }

        /**
         * 重置档位数据
         */
        private fun resetGearStatusForLongDriveCare2() {
            val lastGearStatusInfoStr = MMKVUtil.getLastGearStatus()
            lastGearStatusInfoStr?.let {
                try {
                    val gson = Gson()
                    val lastGearStatusInfoDTO =
                        gson.fromJson(lastGearStatusInfoStr, SceneGearStatusInfoDTO::class.java)
                    lastGearStatusInfoDTO?.let {
                        val newGearStatus = GwmAdapterManagerKotCoroutines.getGearStatus() ?: "-1"
                        Log.i(SceneManager.TAG,"get newGearStatus = $newGearStatus")
                        it.status = newGearStatus
                        it.timestamp = System.currentTimeMillis()
                        it.totalRestTimeInJourney = 0L
                        MMKVUtil.storeLastGearStatus(gson.toJson(lastGearStatusInfoDTO))
                    } ?: Log.i(SceneManager.TAG,"lastGearStatusInfoDTO null")
                } catch (e : Exception) {
                    Log.i(SceneManager.TAG,"e.message = ${e.message}")
                }
            } ?: Log.i(SceneManager.TAG,"getLastGearStatus null")
        }
    }
}