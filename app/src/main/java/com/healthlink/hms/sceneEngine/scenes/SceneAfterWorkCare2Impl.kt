package com.healthlink.hms.sceneEngine.scenes

import android.util.Log
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.sceneEngine.Scene
import com.healthlink.hms.sceneEngine.SceneManager
import com.healthlink.hms.mvvm.model.request.HealthInfoRequestParam
import com.healthlink.hms.mvvm.network.RetrofitClient
import com.healthlink.hms.sdks.gwmadapter.GwmAdapterManagerKotCoroutines
import com.healthlink.hms.sdks.map.gwm.GWMMapManagerKotCoroutines
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.NotificationUtil
import com.healthlink.hms.utils.TimeUtils
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch

/**
 * Created by imaginedays on 2024/7/23
 * 下班关怀2 压力
 */
class SceneAfterWorkCare2Impl(sceneManager: SceneManager,
                              sceneName: String,
                              sceneNameCode: String,
                              scenePriority: Int,
                              sceneTypeName: String,
                              sceneTypeCode: Int,
                              sceneTypePriority: Int,
                              scenePrecondition: Map<String, Any>,
                              sceneExecuteCondition: Map<String, Any>
) : Scene(sceneManager, sceneName,sceneNameCode, scenePriority, sceneTypeName, sceneTypeCode, sceneTypePriority, scenePrecondition, sceneExecuteCondition) {
    /**
    3、前置条件：
    3.1、地点：公司附近
    3.2、时间：晚上8点-凌晨2点；
    3.3、华为健康数据：最近1小时内出现压力“中等”及以上，当前压力“正常”及以上
    3.5、放松模式未打开（条件与放松模式一致）
    3.6、触发时机：开机首次启动应用服务
     */
    override suspend fun scenePrecondition(): Boolean {
        try {
            coroutineScope {
                // 0 非注册用户
                if (!isLogin()) {
                    throw Exception("下班关怀2场景：非注册用户")
                }

                // 6、触发时机：开机首次启动应用服务
//                if (!MMKVUtil.getIsFirstLaunchAppAfterBoot()) {
//                    throw Exception("下班关怀2场景：非开机首次启动App")
//                }

//                // 4、人数：独自驾车
//                val isSelfDriver = GwmAdapterManagerKotCoroutines.isSelfDrive()
//                if (!isSelfDriver) {
//                    throw Exception("不是独自驾车")
//                }

                // 5、放松模式未打开（条件与放松模式一致）
                val isRelaxModeOpen = GwmAdapterManagerKotCoroutines.isRelaxModeOpen()
                if(isRelaxModeOpen) {
                    throw Exception("下班关怀2场景：放松模式已打开")
                }


                // 2、时间：晚上8点-凌晨2点 时间段不在中间返回false, 在中间返回true
                val isInMiddleNight = TimeUtils.isCurrentTimeInRange(20, 0, 2, 0)
                if (!isInMiddleNight) {
                    throw Exception("下班关怀2场景：时间段不在中间")
                }

                launch {
                    // 1、地点：公司附近 不在公司附近返回false, 在公司附近返回true
                    val isLocationCompany = GWMMapManagerKotCoroutines.sendQueryCompanyAsyncReq()
                    if (!isLocationCompany) {
                        throw Exception("下班关怀2场景：地点不在公司附近")
                    }
                }.join()

                // 3、华为健康数据：当前压力“正常”及以上 压力类型代码，0：正常 1：放松 2：中等 3：偏高 最近1小时内出现压力“中等”及以上，
                // 正常及以上 指的是 正常、中等、偏高
                // 中等及以上 指的是 中等、偏高
                launch { // 当前压力“正常”及以上
                    val healthInfoRequestParam = HealthInfoRequestParam()
                    healthInfoRequestParam.userId = MMKVUtil.getUserId()
                    val healthInfoDTO = RetrofitClient.apiService.newHealthInfo(healthInfoRequestParam)
                    healthInfoDTO.let { dto ->
                        var pressureCode = dto.data?.pressure?.pressureCode
                        var okRes = (dto.code == "0" && pressureCode != null) && (pressureCode == 0 || pressureCode == 2 || pressureCode == 3)
                        if (!okRes) {
                            throw Exception("下班关怀2场景：当前压力值不为“正常”及以上")
                        }
                    }
                }.join()

                launch { //  最近1小时内出现压力“中等”及以上
                    val baseResponse = RetrofitClient.apiService.pressure1HourSummary(MMKVUtil.getUserId())
                    Log.i(TAG,"下班关怀2场景：最近1小时内出现压力值: $baseResponse")
                    baseResponse.let { dto ->
                        val middleCount = dto.data?.middleCount ?: 0
                        val highCount = dto.data?.highCount ?: 0
                        var okRes = dto.code == "0" && (middleCount > 0 || highCount > 0)
                        if (!okRes) {
                            throw Exception("下班关怀2场景：最近1小时内出现压力值正常")
                        }
                    }
                }.join()
            }
        } catch (e: Exception) {
            Log.i(TAG, "$sceneName $e")
            return false
        }
        return true
    }

    override suspend fun sceneExecuteConditionForResult(): String? {
        return null
    }


    override fun scenePostCondition(result: String?) {
        DataTrackUtil.dtTrigger("Health_CaringScenes_Gooffwork2_Trigger")
        NotificationUtil(HmsApplication.appContext).sendOpenRelaxModeNotification(
            HmsApplication.appContext.getString(
                R.string.notification_play_tts_offwork_2_pressure
            ))
    }
}