package com.healthlink.hms.sceneEngine.scenes

import android.util.Log
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.sceneEngine.Scene
import com.healthlink.hms.sceneEngine.SceneManager
import com.healthlink.hms.mvvm.network.RetrofitClient
import com.healthlink.hms.sdks.gwmadapter.GwmAdapterManagerKotCoroutines
import com.healthlink.hms.sdks.map.gwm.GWMMapManagerKotCoroutines
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.NotificationUtil
import com.healthlink.hms.utils.TimeUtils
import kotlinx.coroutines.coroutineScope

/**
 * Created by imaginedays on 2024/7/23
 * 下班关怀1
 */
class SceneAfterWorkCare1Impl(
    sceneManager: SceneManager,
    sceneName: String,
    sceneNameCode: String,
    scenePriority: Int,
    sceneTypeName: String,
    sceneTypeCode: Int,
    sceneTypePriority: Int,
    scenePrecondition: Map<String, Any>,
    sceneExecuteCondition: Map<String, Any>
) : Scene(sceneManager,sceneName,sceneNameCode, scenePriority, sceneTypeName, sceneTypeCode, sceneTypePriority, scenePrecondition, sceneExecuteCondition) {

    /**
     * 场景2：下班关怀1 前置条件
     * 1、地点：公司附近
     * 2、时间：晚上8点-凌晨2点；
     * 3、华为健康数据：过去4个小时，出现过未活跃数据
     * 4、腰托：未打开腰托
     * 5、触发时机：开机首次启动应用服务
     */
    override suspend fun scenePrecondition(): Boolean {
        try {
            coroutineScope {
                // 0 非注册用户
                if (!isLogin()) {
                    throw Exception("非注册用户")
                }

                // 5、触发时机：开机首次启动应用服务
//                if (!MMKVUtil.getIsFirstLaunchAppAfterBoot()) {
//                    throw Exception("不是开机首次启动")
//                }

                // 4、腰托：未打开腰托 打开返回false， 为打开返回true
                val isOpenWaist = GwmAdapterManagerKotCoroutines.sceneIsOpenWaist()
                if (isOpenWaist) {
                    throw Exception("腰托已打开")
                }

                // 2、时间：晚上8点-凌晨2点 时间段不在中间返回false, 在中间返回true
                val isInMiddleNight = TimeUtils.isCurrentTimeInRange(20, 0, 2, 0)
                if (!isInMiddleNight) {
                    throw Exception("时间段不在中间")
                }
//
                //1、地点：公司附近 不在公司附近返回false, 在公司附近返回true
                val isLocationCompany = GWMMapManagerKotCoroutines.sendQueryCompanyAsyncReq()
                if (!isLocationCompany) {
                    throw Exception("未在公司附近")
                }
//
                // 3、华为健康数据：过去4个小时，出现过未活跃数据
                val activity4HourSummaryDTO =  RetrofitClient.apiService.activity4HourSummary(MMKVUtil.getUserId())
//                Log.i(TAG, "过去4个小时，出现过未活跃数据 $activity4HourSummaryDTO")
                val okRes = activity4HourSummaryDTO != null && activity4HourSummaryDTO.code == "0" && activity4HourSummaryDTO.data != null && activity4HourSummaryDTO.data == true
                if (!okRes) {
                    throw Exception("过去4个小时，出现过未活跃数据")
                }
            }

        } catch (e: Exception) {
            Log.i(TAG, "$sceneName $e")
            return false
        }
        return true
    }

    override suspend fun sceneExecuteConditionForResult(): String? {
        return null
    }


    override fun scenePostCondition(result: String?) {
        DataTrackUtil.dtTrigger("Health_CaringScenes_Gooffwork1_Trigger")
        NotificationUtil(HmsApplication.appContext).sendNoSeatWaistDirectionNotification(HmsApplication.appContext.getString(R.string.notification_play_tts_offwork_1))
    }
}