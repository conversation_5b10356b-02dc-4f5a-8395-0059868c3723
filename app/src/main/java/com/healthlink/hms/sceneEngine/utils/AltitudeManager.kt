package com.healthlink.hms.utils.map

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.mvvm.network.RetrofitClient
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.files.AssetsUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.Dispatcher

/**
 * 高原地区管理类
 */
object AltitudeManager {

    const val mTAG = "SceneManager"
    const val ALTITUDE_DATA_KEY = "altitude_data"
    const val AREA_HIGH_ALTITUDE_VERSON = "area_high_altitude_v1.json"

    /**
     * 海拔数据是否初始化完成
     */
    private var isInitialized = false
    /**
     * 高海拔的行政区域数据
     */
    private var highAltitudeDatas = mutableMapOf<String,ArrayList<AltitudeDataItem>>()

    /**
     * 低海拔的行政区域数据
     */
    private var lowAltitudeDatas = mutableMapOf<String,ArrayList<AltitudeDataItem>>()


    /**
     * 初始化海拔数据，确保调用后数据已经初始化
     */
    suspend fun initAltitudeData() {
        // Check outside synchronized block
        if (isInitialized) return
        Log.i(mTAG,"Loading altitude data...")
        var altitudeJson: String? = null

        // Load data outside synchronized block
        try {
            // 先从网络或缓存中获取数据
            altitudeJson = loadAltitudeData() ?: MMKVUtil.getData(ALTITUDE_DATA_KEY)

            // 如果缓存为空，则从 assets 中读取
            if (altitudeJson.isNullOrEmpty()) {
                altitudeJson = try {
                    Log.i(mTAG, "Loading altitude data from assets version: $AREA_HIGH_ALTITUDE_VERSON")
                    AssetsUtil.readTextFromAsset(HmsApplication.appContext, AREA_HIGH_ALTITUDE_VERSON)
                } catch (ex: Exception) {
                    Log.d(mTAG, "Failed to read altitude data from asset", ex)
                    null
                }
            }
        } catch (ex: Exception) {
            Log.d(mTAG, "Error loading altitude data: ${ex.message}", ex)
        }

        // Synchronized block to safely set up data
        synchronized(this) {
            if (!isInitialized) {
                altitudeJson?.let {
                    val altitudeDatasTemp = Gson().fromJson(it, AltitudeDatas::class.java)
                    if (highAltitudeDatas.isNotEmpty()) {
                        highAltitudeDatas.clear()
                    }
                    if (lowAltitudeDatas.isEmpty()) {
                        lowAltitudeDatas.clear()
                    }
                    
                    altitudeDatasTemp?.items?.forEach { item ->
                        // 清洗数据
                        var districtId = item.a //cleanAreaName(item.d)
                        // 预处理数据
                        if(!districtId.isNullOrEmpty()) {

                            if (item.l == 2) {
                                // 处理高海拔地区
                                addItemToMapList(highAltitudeDatas, districtId, item)
                            }
                            if (item.l == 0) {
                                // 处理低海拔地区
                                addItemToMapList(lowAltitudeDatas, districtId, item)
                            }
                        }
                    }

                    Log.i(mTAG, "Loading altitude data finished, high altitude data size ${highAltitudeDatas.size}")
                    Log.i(mTAG, "Loading altitude data finished, low altitude data size ${lowAltitudeDatas.size}")
                    isInitialized = highAltitudeDatas.isNotEmpty() && lowAltitudeDatas.isNotEmpty()
                    Log.i(mTAG,"initAltitudeData isInitialized = $isInitialized")
                } ?: Log.i(mTAG, "Failed to initialize altitude data")
            }
        }
    }

    /**
     * 将数据对象加入到指定的列表集合中
     */
    private fun addItemToMapList(
        altitudeDatas:  MutableMap<String, ArrayList<AltitudeManager.AltitudeDataItem>>,
        districtId: String,
        item: AltitudeDataItem
    ) {
        var listTemp = altitudeDatas.get(districtId);
        if (listTemp == null) {
            listTemp = ArrayList()
        }
        listTemp.add(item)
        altitudeDatas.put(districtId, listTemp)
    }

    /**
     * 从网络加载海拔数据，并缓存
     */
    private suspend fun loadAltitudeData():String? = withContext(Dispatchers.IO) {
        var altitudeJson: String? = null

        // TODO load altitude from server
        // MainRepository.get....
        if(HmsApplication.isNetworkConn()) {
            val areaAltitudeResp =  RetrofitClient.apiService.getAreaHighAltitudeJson()
            areaAltitudeResp.data?.let {
                altitudeJson = Gson().toJson(it)
            }
        }
        if (!altitudeJson.isNullOrEmpty()) {
            MMKVUtil.storeData(ALTITUDE_DATA_KEY , altitudeJson!!)
        }

        return@withContext altitudeJson
    }

    /**
     * 判断区县是否在高海拔
     */
    suspend fun isHighAltitudeArea(city : String,district: String, districtId: String): Boolean{

        ensureInitialized()

//        var districtNameCleaned = cleanAreaName(district)
        if(districtId!=null){
            var listDistrict = highAltitudeDatas.get(districtId)
            if(listDistrict!=null&&listDistrict.size>0){
                Log.d(mTAG, " city[$city],district[$district] is high altitude")
                return true
            }
        }
        // TODO 如果行政区域在地海拔中也存在，则需要判断城市是否一致
//        var cityNameCleaned = cleanAreaName(city)

        return false
    }

    /**
     * 判断区县是否在低海拔
     */
    suspend fun isLowAltitudeArea(city : String,district: String, districtId: String): Boolean{

        ensureInitialized()

//        var districtNameCleaned = cleanAreaName(district)
        if(districtId!=null){
            var listDistrict = lowAltitudeDatas.get(districtId)
            if(listDistrict!=null&&listDistrict.size>0){
                Log.d(mTAG, " city[$city],district[$district] is low altitude")
                return true
            }
        }
        // TODO 如果行政区域在高地海拔中也存在，则需要判断城市是否一致
//        var cityNameCleaned = cleanAreaName(city)

        return false
    }

    /**
     * 清理数据，简化地名，便于匹配
     */
    private fun cleanAreaName(areaName : String?): String?{
        var districtName = areaName
        if(districtName !=null ) {
            districtName = districtName.replace("市", "")
            districtName = districtName.replace("县", "")
            districtName = districtName.replace("区", "")
        }
        return districtName
    }

    /**
     * 判断当前地区是否为高海等级是否为指定等级
     * @param areaName 指定地区
     * @param altitudeLevel 高海拔等级 0: 低海拔 < 1500 1: 中海拔 [1500, 2500] 2: 高海拔 > 2500
     */
//   suspend fun isSameAltitude(city : String,district: String, altitudeLevel: Int = 0,from: String): Boolean{
//        Log.i(mTAG , "$from isSameAltitude 初始化：$isInitialized 城市：$city 地区：$district 海拔等级是否为：$altitudeLevel ")
//        ensureInitialized()
//        Log.i(mTAG, "altitudeDatas size ：${altitudeDatas.size}")
//        val altitudeData = altitudeDatas[district]
//        Log.i(mTAG, "$district altitudeLevel：$altitudeData")
//        return altitudeData?.l == altitudeLevel
//    }



    /**
     * 确保海拔数据已初始化（双重检查锁机制）
     */
    private suspend fun ensureInitialized() {
        if (!isInitialized) {
            initAltitudeData()
        }
    }


    data class AltitudeDatas(val items: List<AltitudeDataItem>)

    /**
     * altitudeLevel
     * 0: 低海拔 < 1500
     * 1: 中海拔 [1500, 2500]
     * 2: 高海拔 > 2500
     * @param c 城市
     * @param d 区
     * @param a 区域代码
     * @param l 高拔
     */
    data class AltitudeDataItem(val c: String, val d: String, val a:String, val l : Int)
}