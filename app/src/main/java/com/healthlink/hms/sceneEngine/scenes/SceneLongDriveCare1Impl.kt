package com.healthlink.hms.sceneEngine.scenes

import android.util.Log
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.sceneEngine.Scene
import com.healthlink.hms.sceneEngine.SceneManager
import com.healthlink.hms.mvvm.model.BaseResponse
import com.healthlink.hms.mvvm.network.RetrofitClient
import com.healthlink.hms.sdks.gwmadapter.GwmAdapterManagerKotCoroutines
import com.healthlink.hms.sdks.map.gwm.GWMMapManagerKotCoroutines
import com.healthlink.hms.server.data.dto.HolidayDTO
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.NotificationUtil
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch

/**
 * Created by imaginedays on 2024/7/23
 * 长途驾驶关怀1
 */
class SceneLongDriveCare1Impl(
    sceneManager: SceneManager,
    sceneName: String,
    sceneNameCode: String,
    scenePriority: Int,
    sceneTypeName: String,
    sceneTypeCode: Int,
    sceneTypePriority: Int,
    scenePrecondition: Map<String, Any>,
    sceneExecuteCondition: Map<String, Any>
) : Scene(sceneManager,sceneName,sceneNameCode, scenePriority, sceneTypeName, sceneTypeCode, sceneTypePriority, scenePrecondition, sceneExecuteCondition) {
    /**
     * 场景4：长途驾驶关怀1
     * 3.1、导航：到达目的地剩余时间大于1小时
     * 3.2、座椅通风：主驾未打开座椅通风；
     * 3.3、时间：节假日（周六日+法定）
     * 3.4、车外温度：38°以上
     */
    override suspend fun scenePrecondition(): Boolean {
        try {
            coroutineScope {
                // 0 非注册用户
                if (!isLogin()) {
                    throw Exception("长途驾驶关怀1:非注册用户")
                }

                // 2、座椅通风：主驾未打开座椅通风
                val isOpenSeatVentilation = GwmAdapterManagerKotCoroutines.isOpenSeatVentilation()
                if (isOpenSeatVentilation) {
                    throw Exception("长途驾驶关怀1:不支持主驾座椅通风或已经打开")
                }

                // 3、车外温度：38°以上
                val isOver38 = GwmAdapterManagerKotCoroutines.isOver38()
                if (!isOver38) {
                    throw Exception("长途驾驶关怀1:车外温度不超过38°C")
                }

                launch {
                    // 1、导航：到达目的地剩余时间大于1小时
                    val isEstimateToArrival = GWMMapManagerKotCoroutines.sendQueryEsitmateToArrivalAsyncReq()
                    if (!isEstimateToArrival) {
                        throw Exception("长途驾驶关怀1:导航目的地剩余时间小于1小时")
                    } }.join()

                launch { // 3、时间：节假日（周六日+法定）
                    var resp : BaseResponse<HolidayDTO> = RetrofitClient.apiService.todayIsHoliday()
                    if (resp == null && resp.code != "0") {
                        throw Exception("长途驾驶关怀1:未获取到节假日数据")
                    } else {
                        resp.let { dto ->
                            var isHoliday = dto.data?.isHoliday
                            var okRes = dto.code == "0" && isHoliday != null && isHoliday
                            if(!okRes) {
                                throw Exception("长途驾驶关怀1:当天不是节假日")
                            }
                        }
                    } }.join()
            }
        } catch (e: Exception) {
            Log.i(TAG, "${sceneName}: ${e.message}")
            return false
        }
        return true
    }

    override suspend fun sceneExecuteConditionForResult(): String? {
        return null
    }

    override fun scenePostCondition(result: String?) {
        DataTrackUtil.dtTrigger("Health_CaringScenes_Longdistance1_Trigger")
        NotificationUtil(HmsApplication.appContext).sendNoSeatWaistDirectionNotification(HmsApplication.appContext.getString(R.string.notification_play_tts_longDrive1))
    }
}