package com.healthlink.hms.sceneEngine.scenes

import android.util.Log
import com.healthlink.hms.Contants.TimeCode
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.sceneEngine.Scene
import com.healthlink.hms.sceneEngine.SceneManager
import com.healthlink.hms.mvvm.network.RetrofitClient
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.NotificationUtil
import com.healthlink.hms.utils.TimeUtils
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch

/**
 * Created by imaginedays on 2024/9/9
 * 查看健康周报场景
 */
class SceneHealthWeekReportImpl(sceneManager: SceneManager,
                                sceneName: String,
                                sceneNameCode: String,
                                scenePriority: Int,
                                sceneTypeName: String,
                                sceneTypeCode: Int,
                                sceneTypePriority: Int,
                                scenePrecondition: Map<String, Any>,
                                sceneExecuteCondition: Map<String, Any>
) : Scene(sceneManager, sceneName,sceneNameCode, scenePriority, sceneTypeName, sceneTypeCode, sceneTypePriority, scenePrecondition, sceneExecuteCondition) {
    /**
    3、前置条件：
    3.1、时间：每周五下午4点-晚上21点
    3.2、用户上周有健康评分数据
    3.3、用户本周健康评分数据与上周相比分数差大于5
    3.4、触发时机：开机首次启动应用服务
     */
    override suspend fun scenePrecondition(): Boolean {
        try {
            coroutineScope {
                // 0 非注册用户
                if (!isLogin()) {
                    throw Exception("本周健康周报场景：非注册用户")
                }

                // 3.4、触发时机：开机首次启动应用服务
//                if (!MMKVUtil.getIsFirstLaunchAppAfterBoot()) {
//                    throw Exception("本周健康周报场景：非开机首次启动App")
//                }

                // 3.1、时间：每周五 && 下午4点-晚上21点 时间段不在中间返回false, 在中间返回true
                val isInMiddleNight = TimeUtils.isTodayFriday() && TimeUtils.isCurrentTimeInTodayRange()
                if (!isInMiddleNight) {
                    throw Exception("本周健康周报场景：时间段不在中间 是否是周五：${TimeUtils.isTodayFriday()} 时间段是否在中间：${TimeUtils.isCurrentTimeInTodayRange()}")
                }

                // 3.2、用户上周有健康评分数据 3.3、用户本周健康评分数据与上周相比分数差大于5
                launch {
                   var reqMap = hashMapOf<String,String>().apply {
                       put("userId", MMKVUtil.getUserId() ?: "")
                       put("unit", TimeCode.TIME_CODE_WEEK.timeCode)
                       put("vin", MMKVUtil.getVinCode() ?: "")
                   }

                    val dto = RetrofitClient.apiService.getHealthReportInfo(reqMap)
                    var scoreLastCycle = dto.data?.healthReport?.scoreLastCycle
                    if(scoreLastCycle.isNullOrEmpty()) {
                        throw Exception("本周健康周报场景：用户上周没有健康评分数据")
                    }

                    // 判断用户本周健康评分数据与上周相比分数差大于5
                    var scoreChangeValue = dto.data?.healthReport?.scoreChangeValue
                    var strategyScore = dto.data?.healthReport?.swithValue ?: "5"
                    if(scoreChangeValue.isNullOrEmpty()) {
                        throw Exception("本周健康周报场景：用户本周没有健康评分数据")
                    }else if(scoreChangeValue.toInt() == 0) {
                        // 持平
                        throw Exception("本周健康周报场景：分数持平")
                    } else if(scoreChangeValue.toInt() > 0 && kotlin.math.abs(scoreChangeValue.toInt()) > strategyScore.toInt()) {
                        // 分数上升
                        MMKVUtil.storeHealthReportScoreChangeIsUp(true)
                        Log.i(TAG, "$sceneName 分数上升")
                    } else if (scoreChangeValue.toInt() < 0 && kotlin.math.abs(scoreChangeValue.toInt()) > strategyScore.toInt()) {
                        // 分数下降
                        MMKVUtil.storeHealthReportScoreChangeIsUp(false)
                        Log.i(TAG, "$sceneName 分数下降")
                    } else {
                        throw Exception("本周健康周报场景：${scoreChangeValue.toInt()}没有超过指定策略值$strategyScore")
                    }
                }.join()

            }
        } catch (e: Exception) {
            Log.i(TAG, "$sceneName $e")
            return false
        }
        return true
    }

    override suspend fun sceneExecuteConditionForResult(): String? {
        return null
    }

    override fun scenePostCondition(result: String?) {
        DataTrackUtil.dtTrigger("Health_CaringScenes_Healthreport_Trigger")
        val isUp = MMKVUtil.getHealthReportScoreChangeIsUp()
        val contentTextStrId = R.string.notification_play_tts_healthweekreport_ok_action_title
        val ttsStrId = if (isUp) R.string.notification_play_tts_healthweekreport_up else R.string.notification_play_tts_healthweekreport_down
        NotificationUtil(HmsApplication.appContext).sendOpenHealthReportNotification(
            HmsApplication.appContext.getString(
                ttsStrId
            ),HmsApplication.appContext.getString(contentTextStrId))
    }
}