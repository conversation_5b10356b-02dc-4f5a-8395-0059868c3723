package com.healthlink.hms.sceneEngine

import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.gwm.android.adapter.client.GwmAdapterClient
import com.healthlink.hms.sceneEngine.dto.SceneRemainRunCountDTO
import com.healthlink.hms.sceneEngine.scenes.Scene1WelcomeAfterBootImpl
import com.healthlink.hms.sceneEngine.scenes.SceneAfterWorkCare2Impl
import com.healthlink.hms.sceneEngine.scenes.SceneHealthAbnormalImpl
import com.healthlink.hms.sceneEngine.scenes.SceneHealthWeekReportImpl
import com.healthlink.hms.sceneEngine.scenes.SceneHighCare1Impl
import com.healthlink.hms.sceneEngine.scenes.SceneHighCare2Impl
import com.healthlink.hms.sceneEngine.scenes.SceneLongDriveCare1Impl
import com.healthlink.hms.sceneEngine.scenes.SceneLongDriveCare2Impl
import com.healthlink.hms.sdks.gwmadapter.IVIPowerMode
import com.healthlink.hms.service.WidgetHmsDataWorker
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.TimeUtils
import com.healthlink.hms.utils.map.AltitudeManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.supervisorScope

/**
 * Created by imaginedays on 2024/7/14
 * 场景管理类
 */
class SceneManager() {
    companion object{
        const val TAG = "SceneManager"
        const val POWER_MODE = "car.basic.power_mode";
        const val IVI_POWER_MODE = "sys.basic.ivi_power_mode"

        fun resetJourneySceneTriggerCount() {
            MMKVUtil.storeSceneRemainRunCountInfo(Gson().toJson(SceneRemainRunCountDTO()))

//            //清除迎宾时间
//            MMKVUtil.storeWelcomeTime("")
//            MMKVUtil.setBindDoctorService(false)
//            var json = MMKVUtil.getSceneRemainRunCountInfo() as String
//            val gson = Gson()
//            val listType = object : TypeToken<SceneRemainRunCountDTO>() {}.type
//            var o = gson.fromJson(json, listType) as SceneRemainRunCountDTO
//            o.interventionTriggerCount = 0
//            MMKVUtil.storeSceneRemainRunCountInfo(gson.toJson(o))
//            MMKVUtil.storeTime(WidgetHmsDataWorker.WORKER_TASK_SCENE_ENGINE_RUN_SUCCESS_TIME,0)
        }

        /**
         * 拨打电话医生后重置行程内场景次数
         * 场景包含 健康严重异常 和 高反关怀2
         */
        fun resetSceneCountAfterCallDoctorService() {
            val runCountDTO = getSceneRemainRunCountDTO()
            runCountDTO?.let {
                // 健康严重异常，如果播报过则不播报
                if (it.sceneRunCountMap[SceneNameEnum.HEALTH_SEVERE_EXCEPTION.name] != 3) {
                    it.sceneRunCountMap[SceneNameEnum.HEALTH_SEVERE_EXCEPTION.name] = 0
                    MMKVUtil.storeSceneRemainRunCountInfo(Gson().toJson(it))
                }
                // 高反关怀2，如果播报过则不播报
                if (it.sceneRunCountMap[SceneNameEnum.HIGH_REACTION_CARE_2.name] != 3) {
                    it.sceneRunCountMap[SceneNameEnum.HIGH_REACTION_CARE_2.name] = 0
                    MMKVUtil.storeSceneRemainRunCountInfo(Gson().toJson(it))
                }
            }
        }

        /**
         * 重置严重健康关怀的次数
         */
        fun resetSceneHealthAbnormalCountAfterCallDoctor() {
            val runCountDTO = getSceneRemainRunCountDTO()
            runCountDTO?.let {
                // 健康严重异常，如果播报过则不播报
                it.sceneRunCountMap[SceneNameEnum.HEALTH_SEVERE_EXCEPTION.name] = 0
                MMKVUtil.storeSceneRemainRunCountInfo(Gson().toJson(it))
            }
        }

        /**
         * 重置高反关怀的次数
         */
        fun resetSceneHighReact2CountAfterCallDoctor() {
            val runCountDTO = getSceneRemainRunCountDTO()
            runCountDTO?.let {
                // 高反关怀2，如果播报过则不播报
                it.sceneRunCountMap[SceneNameEnum.HIGH_REACTION_CARE_2.name] = 0
                MMKVUtil.storeSceneRemainRunCountInfo(Gson().toJson(it))
            }
        }

        /**
         * 重置行程内场景触发次数
         */
        fun getSceneRemainRunCountDTO(): SceneRemainRunCountDTO? {
            val json = MMKVUtil.getSceneRemainRunCountInfo()
            if (json.isNullOrEmpty()) {
                return null
            }
            return parseJson(json)
        }

        fun parseJson(json: String): SceneRemainRunCountDTO {
            val gson = Gson()
            val listType = object : TypeToken<SceneRemainRunCountDTO>() {}.type
            return gson.fromJson(json, listType)
        }
    }
    private val scenesList = mutableListOf<Scene>()
    private val scenesOkList = mutableListOf<Scene>()
    private var remainCountDTO: SceneRemainRunCountDTO? = null
    private var mGwmAdapterClient: GwmAdapterClient =  GwmAdapterClient.getInstance()
    // 汽车电源状态

    /**
     * 初始化添加场景
     */
    fun initScenes() : SceneManager {

        Log.i(TAG, "SceneManager initScene")
        initScenesRunCount()
        Log.i(TAG, "SceneManager initScene ${MMKVUtil.getSceneRemainRunCountInfo()}")
        Log.i(TAG, "SceneManager initScene 播报开机迎宾日期 = ${MMKVUtil.getWelcomeTime()}")

        GlobalScope.launch {
            // 初始化海拔数据
            AltitudeManager.initAltitudeData()
        }

        if (scenesList.isNotEmpty()) {
            scenesList.clear()
            scenesOkList.clear()
        }
        // 干预类
        scenesList.add(SceneHealthAbnormalImpl(this,SceneNameEnum.HEALTH_SEVERE_EXCEPTION.sceneName,"HEALTH_SEVERE_EXCEPTION", 1, SceneType.Intervention.sceneTypeName, 2, 1, mapOf(), mapOf()))
        scenesList.add(SceneHighCare2Impl(this,SceneNameEnum.HIGH_REACTION_CARE_2.sceneName, "HIGH_REACTION_CARE_2",2, SceneType.Intervention.sceneTypeName, 2, 1, mapOf(), mapOf()))
        // 关怀类
        scenesList.add(SceneHealthWeekReportImpl(this,SceneNameEnum.HEALTH_WEEK_REPORT.sceneName, "HEALTH_WEEK_REPORT",1, SceneType.Care.sceneTypeName, 1, 2, mapOf(), mapOf()))
        scenesList.add(Scene1WelcomeAfterBootImpl(this,SceneNameEnum.WELCOME_AFTER_BOOT.sceneName, "WELCOME_AFTER_BOOT",2, SceneType.Care.sceneTypeName, 1, 2, mapOf(), mapOf()))
//        scenesList.add(SceneAfterWorkCare1Impl(this,SceneNameEnum.AFTER_WORK_CARE_1.sceneName,"AFTER_WORK_CARE_1", 6, SceneType.Care.sceneTypeName, 1, 2, mapOf(), mapOf()))
        scenesList.add(SceneAfterWorkCare2Impl(this,SceneNameEnum.AFTER_WORK_CARE_2.sceneName,"AFTER_WORK_CARE_2",6, SceneType.Care.sceneTypeName, 1, 2, mapOf(), mapOf()))
        scenesList.add(SceneLongDriveCare1Impl(this,SceneNameEnum.LONG_DRIVE_CARE_1.sceneName,"LONG_DRIVE_CARE_1", 4, SceneType.Care.sceneTypeName, 1, 2, mapOf(), mapOf()))
        scenesList.add(SceneLongDriveCare2Impl(this,SceneNameEnum.LONG_DRIVE_CARE_2.sceneName,"LONG_DRIVE_CARE_2", 5, SceneType.Care.sceneTypeName, 1, 2, mapOf(), mapOf()))
        scenesList.add(SceneHighCare1Impl(this,SceneNameEnum.HIGH_REACTION_CARE_1.sceneName, "HIGH_REACTION_CARE_1",3, SceneType.Care.sceneTypeName, 1, 2, mapOf(), mapOf()))

        Log.i(TAG, "SceneManager initScene ended")

        return this
    }


    /**
     * 是否完成了行程内场景引擎通知
     */
//    private fun isFinishJourneySceneNotify() : Boolean {
//        remainCountDTO = getSceneRemainRunCountDTO() ?: SceneRemainRunCountDTO()
//        Log.i(TAG, "isFinishJourneySceneNotify 播报开机迎宾日期 = ${MMKVUtil.getWelcomeTime()}")
//        val isFinished = remainCountDTO!!.sceneRunCountMap.all {
//            it.value == 0
//        }
//        return isFinished
//    }

    /**
     * 初始化场景触发次数
     */
    private fun initScenesRunCount() {
        remainCountDTO = getSceneRemainRunCountDTO()
        if (remainCountDTO == null) {
            remainCountDTO =  SceneRemainRunCountDTO()
            MMKVUtil.storeSceneRemainRunCountInfo(Gson().toJson(remainCountDTO))
        }
    }


    suspend fun doScenesFilterMethod() : SceneManager {
        Log.i(TAG, "doScenesFilterMethod")

        if (scenesList.isEmpty()) {
            scenesOkList.clear()
            Log.d(TAG, "doScenesFilterMethod: no scenes , exit scene manager")
            return this
        }

        // 场景预处理
        // 1、行程内到达通知数量
//        val isFinished = isFinishJourneySceneNotify()
//        if (isFinished) {
//            Log.i(TAG, "doScenesFilterMethod: isFinished 行程内所有场景完毕返回")
//            scenesOkList.clear()
//            return this
//        }

        // 4、如果电源状态不为大电2、待机模式则不进行场景引擎
        val powerMode  = "2"
//        val isServiceConnected = mGwmAdapterClient.isServiceConnected
//        Log.i(TAG, "${mGwmAdapterClient.getData(POWER_MODE)}")
//        if (isServiceConnected) {
            val res = mGwmAdapterClient.getData(POWER_MODE) ?: return this
            if (res != powerMode) {
                Log.i(TAG, "doScenesFilterMethod: 电源状态不为大电2、待机模式则不进行场景引擎")
                scenesOkList.clear()
                return this
            }
            // 处理远程启动模式等
            val iviPowerMode = mGwmAdapterClient.getData(IVI_POWER_MODE) ?: return this
            if (iviPowerMode == IVIPowerMode.POWER_MODE_STATIC_SAVING_POWER_1 // 静态节电等级一
                ||iviPowerMode == IVIPowerMode.POWER_MODE_STATIC_SAVING_POWER_2 // 静态节电等级二
                ||iviPowerMode == IVIPowerMode.POWER_MODE_REMOTE   //远程模式
                ||iviPowerMode == IVIPowerMode.POWER_MODE_AWAKE_HOLD  //AwakeHold 模式
                ) {
                Log.i(TAG, "doScenesFilterMethod: iviPowerMode = $iviPowerMode，不进行场景引擎")
                scenesOkList.clear()
                return this
            }
//        } else {
//            Log.i(TAG, "doScenesFilterMethod: 车机服务未连接。")
//        }

        // 移除行程内执行次数为0的场景
        remainCountDTO?.let { outterIt ->
//            if(it.careTriggerCount == 0) {
//                Log.i(TAG, "doScenesFilterMethod: 2、行程内执行了一次关怀类场景、其余关怀场景移除")
//                scenesList.removeIf { it.sceneTypeName == SceneType.Care.sceneTypeName }
//            }
//
//            if(it.interventionTriggerCount == 0) {
//
            //                scenesList.removeIf { it.sceneTypeName == SceneType.Intervention.sceneTypeName }
//                Log.i(TAG, "doScenesFilterMethod: 3. 行程内干预类执行次数不能超过三次")
//            }
            scenesList.removeIf { it ->
                outterIt.sceneRunCountMap[it.sceneNameCode] == 0
            }
        }

        val power2ModeStartTime = MMKVUtil.getPowerMode2StartTime()
        val curTime = System.currentTimeMillis()
        if (curTime - power2ModeStartTime > 4 * 60 * 1000) {
            scenesList.removeIf { it.sceneNameCode == SceneNameEnum.WELCOME_AFTER_BOOT.name }
            scenesList.removeIf { it.sceneNameCode == SceneNameEnum.AFTER_WORK_CARE_2.name }
            Log.i(TAG,"doScenesFilterMethod: 开机4分钟后不再执行开机迎宾和下班关怀场景")
        }

        // 一天只播放一次开机迎宾
        val welcomeDate = MMKVUtil.getWelcomeTime()
        welcomeDate?.let {
            val today = TimeUtils.formatDateString(System.currentTimeMillis(), "yyyy-MM-dd")
            if (today == it) {
                Log.i(TAG, "doScenesFilterMethod: 一天只播放一次开机迎宾,已经播放迎宾模式，不再播放，移除场景")
                scenesList.removeIf { it.sceneNameCode == SceneNameEnum.WELCOME_AFTER_BOOT.name }
            }
        }

        // 3、时间段不在 20:00 - 2:00 之间，移除下班场景
        val isInMiddleNight = TimeUtils.isCurrentTimeInRange(20, 0, 2, 0)
        Log.i(TAG, "doScenesFilterMethod: 时间段是否在 20:00 - 2:00 之间 isInMiddleNight $isInMiddleNight")
        if(!isInMiddleNight) {
            Log.i(TAG, "doScenesFilterMethod: 时间段不在 20:00 - 2:00 之间，移除下班场景")
            scenesList.removeIf { it.sceneNameCode == SceneNameEnum.AFTER_WORK_CARE_2.name }
        }

        // 4、如果是周五、且执行了健康周报场景则移除健康周报场景
        val isFriday = TimeUtils.isTodayFriday()
        if (isFriday) {
            if (MMKVUtil.isNotifyHealthWeekReport()) {
                Log.i(TAG, "doScenesFilterMethod: 移除健康周报场景")
                scenesList.removeIf { it.sceneNameCode == SceneNameEnum.HEALTH_WEEK_REPORT.name }
            }
        } else {
            MMKVUtil.storeIsNotifyHealthWeekReport(false)
        }

        // 4、如果没开启导航，移除高反关怀、下班场景
//        coroutineScope {
//            withTimeoutOrNull(10000) {
//                try {
//                    val isOpenNavigation = MMKVUtil.getNavigationGuideStatus() || mapManager.sendQueryGuideStatusAsyncReq(this@SceneManager)
//                    Log.i(TAG, "导航状态值：isOpenNavigation $isOpenNavigation")
//                    if (!isOpenNavigation) {
//                        Log.d(TAG,"导航未打开，过滤3个场景")
//                        scenesList.removeIf {
//                            it.sceneNameCode == SceneNameEnum.LONG_DRIVE_CARE_1.name ||
//                                    it.sceneNameCode == SceneNameEnum.LONG_DRIVE_CARE_2.name ||
//                                    it.sceneNameCode == SceneNameEnum.HIGH_REACTION_CARE_1.name
//                        }
//                    }else{
//                        Log.d(TAG,"导航已打开")
//                    }
//                } catch (e: Exception) {
//                    Log.e(TAG,"导航发现异常，${e.message}，未获得地图服务")
//                    //return this
//                }
//            }
//        }

        Log.i(TAG, "doScenesFilterMethod: preFilter doScenesFilterMethod ${scenesList.size} ended")

        // 使用 CoroutineScope 和 async 来并发地调用 scenePrecondition 函数
        val preconditionResults = mutableListOf<Pair<Scene, Boolean>>()

        supervisorScope {
            for (scene in scenesList) {
                val job = launch(Dispatchers.IO) {

                    val precondition = try {
                        scene.scenePrecondition()
                    } catch (e: Exception) {
                        Log.i(TAG, "${scene.sceneName} 前置条件异常: ${e.message}")
                        false
                    }
                    Log.i(TAG, "${scene.sceneName} 前置条件 $precondition")
                    Pair(scene, precondition)
                    preconditionResults.add(Pair(scene, precondition))
                }
                job.join()
            }
        }

        // 根据 preconditionResults 过滤并将符合条件的场景添加到 scenesOkList
        for ((scene, precondition) in preconditionResults) {
            if (precondition) {
                scenesOkList.add(scene)
            }
        }

        // 排序规则 先按场景类型优先级排序，再按场景优先级排序 多级排序
        scenesOkList.sortWith(compareBy({ it.sceneTypePriority }, { it.scenePriority }))

        // 遍历 scenesOkList
        for (scene in scenesOkList) {
            Log.i(TAG, "doScenesFilterMethod: 按优先级排序后的场景 ${scene.sceneName}")
        }
        return this
    }



    suspend fun doScenesExecuteMethod() : SceneManager {
        Log.i(TAG, "doScenesExecuteMethod ${scenesOkList.size} starting")
        if (scenesOkList.isNotEmpty()) {
            val scene = scenesOkList[0]
            if (scene.sceneNameCode == SceneNameEnum.WELCOME_AFTER_BOOT.name) {
                // 判断是开机迎宾
                val executeResult =  scene.sceneExecuteConditionForResult()
                if (executeResult != null && !executeResult.isNullOrBlank()) {
                    runScene(scene, executeResult)
                } else {
                    // 执行欢迎场景失败时，查看剩余场景里是否还有需要执行的场景
                    if (scenesOkList.size > 1) {
                        runScene(scenesOkList[1])
                    }
                }
            } else {
                runScene(scene)
            }
        }
        Log.i(TAG, "doScenesExecuteMethod ${scenesOkList.size} end")
        return this
    }

    private fun runScene(scene: Scene, result: String? = null) {
        Log.i(TAG, "manager runScene ${scene.sceneName}")
        synchronized(this) {
            scene.scenePostCondition(result)
            modifyRemainRunCountInfo(scene.sceneNameCode)
            if (scene.sceneNameCode != SceneNameEnum.WELCOME_AFTER_BOOT.name) {
                Log.i(TAG, "manager runScene ${scene.sceneName} 剩余次数 ${remainCountDTO?.sceneRunCountMap?.get(scene.sceneNameCode)}")
            }
        }
        MMKVUtil.storeTime(WidgetHmsDataWorker.WORKER_TASK_SCENE_ENGINE_RUN_SUCCESS_TIME,System.currentTimeMillis())
    }

    private fun modifyRemainRunCountInfo(sceneNameCode: String) {
        // 开机迎宾
        if (sceneNameCode == SceneNameEnum.WELCOME_AFTER_BOOT.name) {
            MMKVUtil.storeWelcomeTime(TimeUtils.formatDateString(System.currentTimeMillis(), "yyyy-MM-dd"))
        } else {
            // 其他场景
            remainCountDTO?.let { it ->
                try {
                    if  (it.sceneRunCountMap[sceneNameCode]!! > 0) {
                        it.sceneRunCountMap[sceneNameCode] = it.sceneRunCountMap[sceneNameCode]!! - 1
                    }

                    // 记录执行了健康报告场景
                    if (sceneNameCode == SceneNameEnum.HEALTH_WEEK_REPORT.name && it.sceneRunCountMap[sceneNameCode]!! == 0) {
                        MMKVUtil.storeIsNotifyHealthWeekReport(true)
                    }

                    val json = Gson().toJson(it)
                    Log.i(TAG, "manager modifyRemainRunCountInfo $json")
                    MMKVUtil.storeSceneRemainRunCountInfo(json)
                } catch (e: Exception) {
                    Log.i(TAG, "manager modifyRemainRunCountInfo ${e.message}")
                }
            }
        }
    }
}