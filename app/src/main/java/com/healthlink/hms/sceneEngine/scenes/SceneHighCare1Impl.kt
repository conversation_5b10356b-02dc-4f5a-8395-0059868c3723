package com.healthlink.hms.sceneEngine.scenes

import android.util.Log
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.sceneEngine.Scene
import com.healthlink.hms.sceneEngine.SceneManager
import com.healthlink.hms.sdks.map.gwm.GWMMapLocDTO
import com.healthlink.hms.sdks.map.gwm.GWMMapManagerKotCoroutines
import com.healthlink.hms.sdks.map.gwm.baseDTO.GWMMapRespBaseDTO
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.utils.NotificationUtil
import com.healthlink.hms.utils.map.AltitudeManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch

/**
 * Created by imaginedays on 2024/7/23
 * 高反关怀1
 */
class SceneHighCare1Impl(sceneManager: SceneManager,
                         sceneName: String,
                         sceneNameCode: String,
                         scenePriority: Int,
                         sceneTypeName: String,
                         sceneTypeCode: Int,
                         sceneTypePriority: Int,
                         scenePrecondition: Map<String, Any>,
                         sceneExecuteCondition: Map<String, Any>
) : Scene(sceneManager, sceneName,sceneNameCode, scenePriority, sceneTypeName, sceneTypeCode, sceneTypePriority, scenePrecondition, sceneExecuteCondition) {

    /**
     * 场景6： 高反关怀
     * 3.1、当前位置海拔：低于1500米
     * 3.2、导航目的地海拔：高于2500米
     */
    override suspend fun scenePrecondition(): Boolean {
        var  curLocation : GWMMapLocDTO? = null // 当前位置LocationInfo信息
        var  desLocation : GWMMapLocDTO? = null // 目的地位置LocationInfo信息
        try {
            coroutineScope {
                // 0 非注册用户
                if (!isLogin()) {
                    throw Exception("高反关怀1：非注册用户")
                }

                launch(Dispatchers.IO) {
                    // 1、当前位置海拔：低于1500米
                    val currentLocationDTO: GWMMapRespBaseDTO<GWMMapLocDTO>? = GWMMapManagerKotCoroutines.sendCurrentLocationAsyncReq()
                    Log.i(TAG, "$sceneName $currentLocationDTO")
                    if (currentLocationDTO?.data != null && currentLocationDTO.data.lat != null && currentLocationDTO.data.lon != null) {
                        curLocation = currentLocationDTO.data
                    } else {
                        throw Exception("高反关怀1：未获取到当前位置信息")
                    }
                }.join()

                // 当前位置是否小于1500
                launch(Dispatchers.IO) {
                    // 1、当前位置海拔：低于1500米
//                    val isAltitudeLowerThan1500 = AltitudeManager.isSameAltitude(curLocation!!.city ?: "",curLocation!!.district ?: "", 0,"$sceneName 当前位置是否小于1500")
                    val isAltitudeLowerThan1500 = AltitudeManager.isLowAltitudeArea(
                        curLocation!!.city ?: "",
                        curLocation!!.district ?: "",
                        curLocation!!.adcode ?: "")
                    if (!isAltitudeLowerThan1500) {
                        throw Exception("高反关怀1：当前位置[${curLocation!!.city},${curLocation!!.district}]不低于1500米")
                    }else{
                        Log.d(TAG,"高反关怀1：目的地[[${curLocation!!.city},${curLocation!!.district}]]海拔低于1500米\"")
                    }
                }.join()

                /**
                 * 1、查询目的地经纬度
                 */
                launch(Dispatchers.IO) {
                    // 1、查询目的地经纬度,仅包含经纬度
                    val destinationLocDTO = GWMMapManagerKotCoroutines.sendQueryDestinationAsyncReq()
                    if (destinationLocDTO?.data != null && destinationLocDTO.data.lat != null && destinationLocDTO.data.lon != null) {
                        desLocation = destinationLocDTO.data
                    } else {
                        throw Exception("高反关怀1：未获取到目的地位置信息")
                    }
                }.join()

                /**
                 * 2、根据目的地经纬度，获取LocationInfo,
                 * 3、判断LocationInfo的海拔
                 */
                launch(Dispatchers.IO) {
                    val lat = desLocation!!.lat
                    val lon = desLocation!!.lon

                    // 2、根据目的地经纬度，获取LocationInfo
                    val locationInfo =  GWMMapManagerKotCoroutines.sendCustomLocationAsyncReq(lon, lat)
                    if (locationInfo?.data == null) {
                        throw Exception("高反关怀1：未获取到目的地locationInfo信息")
                    }

                    // 3、判断LocationInfo的海拔 是否为高海拔
//                    val isAltitudeLevelOver2500 = AltitudeManager.isSameAltitude(locationInfo.data.city ?:"",locationInfo.data.district ?:"", 2,"$sceneName 目的地海拔是否高于2500")
                    val isAltitudeLevelOver2500 = AltitudeManager.isHighAltitudeArea(
                        locationInfo.data.city ?:"",
                        locationInfo.data.district ?:"",
                        locationInfo.data.adcode ?:"")

                    if(!isAltitudeLevelOver2500) {
                        throw Exception("高反关怀1：目的地[[${locationInfo.data.city},${locationInfo.data.district}]]海拔不高于2500米")
                    }else{
                        Log.d(TAG,"高反关怀1：目的地[[${locationInfo.data.city},${locationInfo.data.district}]]海拔高于2500米\"")
                    }

                }.join()
            }
        } catch (e: Exception) {
            Log.i(TAG, "$sceneName ${e.message}")
            return false
        }
        return true
    }

    override suspend fun sceneExecuteConditionForResult(): String? {
        return null
    }

    override fun scenePostCondition(result: String?) {
        DataTrackUtil.dtTrigger("Health_CaringScenes_Highanti1_Trigger")
//        var elevation = "高于2500"
//        MMKVUtil.getDestinationElevation()?.let { if(it.length > 0) elevation = it }
        NotificationUtil(HmsApplication.appContext).sendOpenNavigationAppNotification(
            HmsApplication.appContext.getString(
                R.string.notification_play_tts_highCarePrecondition1,
            ), false
        )
    }
}