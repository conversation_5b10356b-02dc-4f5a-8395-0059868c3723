package com.healthlink.hms.mvvm.network;

import android.app.Application;

public interface INetworkRequiredInfo {
    /**
     * 客户端类型
     */
    String getClientType();
    /**
     * 客户端操作系统类型
     */
    String getOSName();
    /**
     * 获取App版本名
     */
    String getAppVersionName();

    /**
     * 获取App版本号
     */
    String getAppVersionCode();

    /**
     * 设备唯一标识
     */
    String getDeviceId();
    /**
     * uuid
     */
    String getUUID();
    /**
     * 屏幕高
     */
    int getScreenHeight();
    /**
     * 屏幕宽
     */
    int getScreenWidth();
    /**
     * 屏幕密度dpi
     */
    int getScreenDpi();
    /**
     * 设备（手机或者车机）版本号（Android版本号）
     */
    String getOSVersion();
    /**
     * 设备品牌
     */
    String getDeviceBrand();
    /**
     * 设备型号
     */
    String getDeviceModel();
    /**
     * 渠道
     */
    String getChannel();

    /**
     * token
     */
    String getToken();
    /**
     * 判断是否为Debug模式
     */
    boolean isDebug();
    /**
     * coffeeOS版本 310 默认值
     */
    String getCoffeeOSVersion();
    /**
     * 获取全局上下文参数
     */
    Application getApplicationContext();

}
