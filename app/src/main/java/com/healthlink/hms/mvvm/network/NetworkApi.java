package com.healthlink.hms.mvvm.network;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import com.healthlink.hms.BuildConfig;
import com.healthlink.hms.R;
import com.healthlink.hms.application.HmsApplication;
import com.healthlink.hms.mvvm.model.BaseResponse;
import com.healthlink.hms.mvvm.model.BusinessRespCode;
import com.healthlink.hms.mvvm.network.errorhandler.ExceptionHandle;
import com.healthlink.hms.mvvm.network.errorhandler.HttpErrorHandler;
import com.healthlink.hms.mvvm.network.interceptor.LogInterceptor;
import com.healthlink.hms.mvvm.network.interceptor.NetworkInterceptor;
import com.healthlink.hms.mvvm.network.interceptor.RequestInterceptor;
import com.healthlink.hms.mvvm.network.interceptor.ResponseInterceptor;
import com.healthlink.hms.utils.ToastUtil;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.ObservableTransformer;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;
import okhttp3.Dns;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;

public class NetworkApi {
    /**
     * 获取APP运行状态及版本信息，用于日志打印
     */
    private static INetworkRequiredInfo iNetworkRequiredInfo;
    /**
     * API访问地址
     */
    private static final String BASE_URL = BuildConfig.BASE_URL;

    private static OkHttpClient okHttpClient;

    private static final HashMap<String, Retrofit> retrofitHashMap = new HashMap<>();

    /**
     * 初始化
     */
    public static void init(INetworkRequiredInfo networkRequiredInfo) {
        iNetworkRequiredInfo = networkRequiredInfo;
    }

    /**
     * 创建serviceClass的实例
     */
    public static <T> T createService(Class<T> serviceClass) {
        return getRetrofit(serviceClass).create(serviceClass);
    }

    /**
     * 配置OkHttp
     *
     * @return OkHttpClient
     */
    private static OkHttpClient getOkHttpClient() {
        //不为空则说明已经配置过了，直接返回即可。
        if (okHttpClient == null) {
            //OkHttp构建器
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            //设置缓存大小
            int cacheSize = 100 * 1024 * 1024;
            //设置网络请求超时时长，这里设置为8s
            builder.connectTimeout(8, TimeUnit.SECONDS);
            builder.addInterceptor(new NetworkInterceptor());
            //添加请求拦截器，如果接口有请求头的话，可以放在这个拦截器里面
            builder.addInterceptor(new RequestInterceptor(iNetworkRequiredInfo));
            //添加返回拦截器，可用于查看接口的请求耗时，对于网络优化有帮助
            builder.addInterceptor(new ResponseInterceptor());
            // 添加日志拦截器
            if (BuildConfig.DEBUG){builder.addInterceptor(new LogInterceptor());}
            //当程序在debug过程中则打印数据日志，方便调试用。
            if (iNetworkRequiredInfo != null && iNetworkRequiredInfo.isDebug()) {
                //iNetworkRequiredInfo不为空且处于debug状态下则初始化日志拦截器
                HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
                //设置要打印日志的内容等级，BODY为主要内容，还有BASIC、HEADERS、NONE。
                httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
                //将拦截器添加到OkHttp构建器中
//                builder.addInterceptor(httpLoggingInterceptor);
            }
            builder.dns(new Dns(){
                @NonNull
                @Override
                public List<InetAddress> lookup(@NonNull String hostname) {
                    try {
                        return Dns.SYSTEM.lookup(hostname);
                    } catch (Exception e) {
//                        Log.i("NetworkApi", "lookup() called with: hostname = [" + hostname + "]", e);
                        return Collections.emptyList();
                    }
                }
            });
            //OkHttp配置完成
            okHttpClient = builder.build();
        }
        return okHttpClient;
    }

    /**
     * 配置Retrofit
     *
     * @param serviceClass 服务类
     * @return Retrofit
     */
    private static Retrofit getRetrofit(Class serviceClass) {
        if (retrofitHashMap.get(BASE_URL + serviceClass.getName()) != null) {
            //刚才上面定义的Map中键是String，值是Retrofit，当键不为空时，必然有值，有值则直接返回。
            return retrofitHashMap.get(BASE_URL + serviceClass.getName());
        }
        //初始化Retrofit  Retrofit是对OKHttp的封装，通常是对网络请求做处理，也可以处理返回数据。
        //Retrofit构建器
        Retrofit.Builder builder = new Retrofit.Builder();
        //设置访问地址
        builder.baseUrl(BASE_URL);
        //设置OkHttp客户端，传入上面写好的方法即可获得配置后的OkHttp客户端。
        builder.client(getOkHttpClient());
        //设置数据解析器 会自动把请求返回的结果（json字符串）通过Gson转化工厂自动转化成与其结构相符的实体Bean
        builder.addConverterFactory(GsonConverterFactory.create());
        //设置请求回调，使用RxJava 对网络返回进行处理
        builder.addCallAdapterFactory(RxJava2CallAdapterFactory.create());
        //retrofit配置完成
        Retrofit retrofit = builder.build();
        //放入Map中
        retrofitHashMap.put(BASE_URL + serviceClass.getName(), retrofit);
        //最后返回即可
        return retrofit;
    }

    /**
     * 配置RxJava 完成线程的切换
     *
     * @param observer 这个observer要注意不要使用lifecycle中的Observer
     * @return Observable
     */
    public static <T> ObservableTransformer<T, T> applySchedulers(final Observer<T> observer) {
        return upstream -> {
            if (HmsApplication.Companion.isNetworkConn()) {
                Observable<T> observable = upstream
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .map(NetworkApi.getAppErrorHandler()) // 判断有没有500的错误
                        .onErrorResumeNext(new HttpErrorHandler<>()); // 判断有没有400的错误
                observable.subscribe(observer);
                return observable; // 返回处理后的 Observable
            } else {
                Log.i("NetworkApi", "检测不到网络-不请求接口");
                Observable<T> observable = Observable.error(ExceptionHandle.handleException(new Throwable("666")));
                upstream.subscribe(observer);
                return observable; // 抛出无网络异常
            }
        };
    }

    /**
     * 错误码处理
     */
    protected static <T> Function<T, T> getAppErrorHandler() {
        return response -> {
            //当response返回出现500之类的错误时
            if (response!=null && response instanceof BaseResponse){
                Context context = HmsApplication.Companion.getAppContext();

                BaseResponse respTemp= (BaseResponse) response;
                // 华为运动健康App上的【华为运动健康服务】开关【关闭】，需要弹窗提醒用户【打开】，但不退出登录
                if(respTemp.getCode().equals(BusinessRespCode.HUAWEI_APP_CLOSE_SERVICE)){
//                    if(!HmsApplication.Companion.isDialogShow() && !ToastUtil.INSTANCE.isToastShowing()) {
//                        HmsApplication.Companion.showGlobalDialog("", context.getString(R.string.noti_open_data_auth), false);
//                    }
                }
                // 登录超时或者用户取消授权操作，前端需要提醒用户重新登录【弹窗】，并退出登录
                else if(respTemp.getCode().equals(BusinessRespCode.USER_LOGIN_TIME_OUT_OR_CANCEL_AUTH)){
//                    HmsApplication.Companion.showGlobalDialog("", context.getString(R.string.noti_huawei_oauth_changed_or_timeout),true);
                }
                // 登录时效，请重新登录
                else if(respTemp.getCode().equals(BusinessRespCode.LOGIN_EXPIRE)){
//                    HmsApplication.Companion.showGlobalDialog("",context.getString(R.string.noti_login_invalid),true);
                }
            }

            return response;
        };
    }
}
