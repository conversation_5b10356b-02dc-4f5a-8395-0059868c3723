package com.healthlink.hms.mvvm.network.interceptor;

import android.util.Log;

import com.healthlink.hms.base.AppContext;
import com.healthlink.hms.mvvm.model.request.BaseRequestParam;
import com.healthlink.hms.mvvm.network.INetworkRequiredInfo;
import com.healthlink.hms.mvvm.network.utils.DateUtil;
import com.healthlink.hms.mvvm.network.utils.KLog;
import com.healthlink.hms.utils.JsonUtil;
import com.healthlink.hms.utils.MMKVUtil;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;

/**
 * 请求拦截器
 */
public class RequestInterceptor implements Interceptor {
    private static final String TAG = "ResponseInterceptor";
    /**
     * 网络请求信息
     */
    private INetworkRequiredInfo iNetworkRequiredInfo;

    public RequestInterceptor(INetworkRequiredInfo iNetworkRequiredInfo){
        this.iNetworkRequiredInfo = iNetworkRequiredInfo;
    }

    /**
     * 拦截
     */
    @Override
    public Response intercept(Chain chain) throws IOException {

        try {
            String url = chain.request()!=null? chain.request().url().toString() : "";
            KLog.d(TAG, "do request for " + url );
            //构建器
            Request.Builder builder = chain.request().newBuilder();
            batchAddHeader(builder);
            // get 请求的 body为空
            if (chain.request().body() != null) {
                okio.Buffer buffer = new okio.Buffer();
                chain.request().body().writeTo(buffer);
                String bodyString = buffer.readUtf8();
                // 将基础参数包装到BaseRequestParam里
                BaseRequestParam baseRequestParam = new BaseRequestParam(bodyString);
                String allbodyString = JsonUtil.objectToJson(baseRequestParam);
                //        RequestBody body = RequestBody.create(allbodyString,MediaType.parse("application/json; charset=utf-8"));
                //        builder.post(body);
                Log.i("RequestInterceptor", allbodyString);
            }


            return chain.proceed(builder.build());
        }catch(Throwable ex){
            Log.i(TAG,"network exception. "+ex.getMessage());
            // 构建一个有效的 Response.Builder 对象
            return new Response.Builder()
                    .request(chain.request())           // 必须包含原始请求
                    .protocol(Protocol.HTTP_1_1)        // 设置协议版本
                    .code(500)                          // 设置有效的 HTTP 状态码，500 表示服务器错误
                    .message("Internal Server Error")   // 设置错误消息
                    .body(ResponseBody.create("", MediaType.parse("text/json")))  // 空的响应体
                    .build();
        }
    }

    /**
     * 批量添加请求头
     */
    public void batchAddHeader(okhttp3.Request. Builder builder) {
        if (builder != null) {
            //添加使用环境
            builder.addHeader("clientType", this.iNetworkRequiredInfo.getClientType());
            //添加版本号
            builder.addHeader("appVersionCode",this.iNetworkRequiredInfo.getAppVersionCode());
            //添加版本名
            builder.addHeader("appVersionName",this.iNetworkRequiredInfo.getAppVersionName());
            //添加设备唯一标识
            builder.addHeader("deviceId",this.iNetworkRequiredInfo.getDeviceId());
            //添加uuid
            builder.addHeader("uuid",this.iNetworkRequiredInfo.getUUID());
            //添加设备品牌
            builder.addHeader("deviceBrand",this.iNetworkRequiredInfo.getDeviceBrand());
            //添加设备型号
            builder.addHeader("deviceModel",this.iNetworkRequiredInfo.getDeviceModel());
            //添加渠道
            builder.addHeader("channel",this.iNetworkRequiredInfo.getChannel());
            // 添加token
            if (this.iNetworkRequiredInfo.getToken() != null) {
                builder.addHeader("token",this.iNetworkRequiredInfo.getToken());
            }
            // 添加coffeeOS版本
            builder.addHeader("coffeeOSVersion", this.iNetworkRequiredInfo.getCoffeeOSVersion());
            //添加操作系统
            builder.addHeader("osName",this.iNetworkRequiredInfo.getOSName());
            //添加操作系统版本
            builder.addHeader("osVersion",this.iNetworkRequiredInfo.getOSVersion());
            //添加屏幕宽
            builder.addHeader("screenWidth",String.valueOf(this.iNetworkRequiredInfo.getScreenWidth()));
            //添加屏幕高
            builder.addHeader("screenHeight",String.valueOf(this.iNetworkRequiredInfo.getScreenHeight()));
            //添加屏幕密度dpi
            builder.addHeader("screenDpi",String.valueOf(this.iNetworkRequiredInfo.getScreenDpi()));
            //添加VIN
            builder.addHeader("vin", MMKVUtil.INSTANCE.getVinCode());
        }
    }
}
