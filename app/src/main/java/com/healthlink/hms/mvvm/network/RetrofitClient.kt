package com.healthlink.hms.mvvm.network

import com.healthlink.hms.BuildConfig
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.mvvm.NetworkRequiredInfo
import com.healthlink.hms.mvvm.api.ApiServiceKot
import com.healthlink.hms.mvvm.network.interceptor.RequestInterceptor
import com.jakewharton.retrofit2.adapter.kotlin.coroutines.CoroutineCallAdapterFactory
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory

/**
 * Created by imaginedays on 2024/7/19
 *
 *
 */
object RetrofitClient {
    private const val BASE_URL = BuildConfig.BASE_URL
    // 创建一个 OkHttpClient.Builder
    //        okHttpClientBuilder.addInterceptor(RequestInterceptor(NetworkRequiredInfo(HmsApplication.instance))
    var apiService: ApiServiceKot = NetworkApi.createService(ApiServiceKot::class.java)
//    private val retrofit: Retrofit = Retrofit.Builder()
//        .baseUrl(BASE_URL)
//        .client(okHttpClient)
//        .addConverterFactory(GsonConverterFactory.create())
//        .addCallAdapterFactory(CoroutineCallAdapterFactory())
//        .build()



//    val apiService: ApiServiceKot = retrofit.create(ApiServiceKot::class.java)
}