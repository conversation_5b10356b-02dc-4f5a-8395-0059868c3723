package com.healthlink.hms.mvvm.model.request

import com.healthlink.hms.base.AppContext
import com.healthlink.hms.utils.DeviceInfo
import com.healthlink.hms.utils.JsonUtil

class BaseRequestParam(reqData: String?) {
    /**
     * 客户端类型
     */
    var clientType: String = AppContext.CLIENT_NAME

    var osName: String = AppContext.CLIENT_NAME

    /**
     * 客户端版本
     */
    var clientVersion: String = AppContext.app_version

    /**
     * 设备唯一标识
     */
    var deviceId: String = AppContext.deviceId

    var deviceInfo: String = JsonUtil.objectToJson(DeviceInfo())

    /**
     * 手机版本号
     */
    var osVersion: String = AppContext.getOSVersion()

    /**
     * 屏幕高幕
     */
    var sScreenHeight = AppContext.sScreenWidth

    /**
     * 屏幕宽
     */
    var sScreenWidth = AppContext.sScreenHeight

    /**
     * 屏幕密度dpi
     */
    var sScreenDpi = AppContext.sScreenDpi

    /**
     * 渠道
     */
    var src: String = AppContext.channel

    /**
     * 接口版本号
     */
    var version: String? = null

    var deviceName: String = AppContext.deviceName

    var uuid: String = AppContext.uuid

    var uuid2: String = AppContext.getIMEI()

    /**
     * coffee os 版本
     */
    var coffeeOSVersion: String = AppContext.coffeeOSVersion

//    /**
//     * token
//     */
//    var token: String? = null
//
//    /**
//     * 业务数据
//     */
    var reqData: String? = null

    init {
        // 业务数据
        this.reqData = reqData
        // 判断是否登录
        //        val info: UserLoginInfo = AppContext.getLoginInfo()
//        if (AppContext.isLogin()) {
//            token = info.token
//        }
    }

//    fun getMap(): Map<String, String> {
//        var accessKey = ""
////        val info: UserLoginInfo = AppContext.getLoginInfo()
////        if (AppContext.isLogin()) {
////            accessKey = info.token
////        }
//        val paramMap: MutableMap<String, String> = HashMap()
//        paramMap["clientType"] = AppContext.CLIENT_NAME
//        paramMap["osName"] = AppContext.CLIENT_NAME
//        paramMap["clientVersion"] = AppContext.app_version
//        paramMap["deviceId"] = AppContext.deviceId
//        paramMap["uuid"] = AppContext.uuid
//        paramMap["uuid2"] = AppContext.getIMEI()
//        paramMap["deviceInfo"] = JsonUtil.objectToJson(DeviceInfo())
//        paramMap["osVersion"] = AppContext.getOSVersion()
//        paramMap["src"] = AppContext.channel
//        paramMap["accessKey"] = accessKey
//        return paramMap
//    }
}