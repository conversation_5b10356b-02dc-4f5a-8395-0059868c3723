package com.healthlink.hms.mvvm;

import android.app.Application;

import com.healthlink.hms.BuildConfig;
import com.healthlink.hms.base.AppContext;
import com.healthlink.hms.mvvm.network.INetworkRequiredInfo;
import com.healthlink.hms.utils.MMKVUtil;

public class NetworkRequiredInfo implements INetworkRequiredInfo {
    private final Application application;

    public NetworkRequiredInfo(Application application){
        this.application = application;
    }

    /**
     * 客户端类型
     */
    @Override
    public String getClientType() {
        return AppContext.CLIENT_NAME;
    }

    /**
     * 客户端操作系统类型
     */
    @Override
    public String getOSName() {
        return AppContext.CLIENT_NAME;
    }

    @Override
    public String getAppVersionName() {
        return BuildConfig.VERSION_NAME;
    }

    @Override
    public String getAppVersionCode() {
        return String.valueOf(BuildConfig.VERSION_CODE);
    }

    /**
     * 设备唯一标识
     */
    @Override
    public String getDeviceId() {
        return AppContext.deviceId;
    }

    /**
     * uuid
     */
    @Override
    public String getUUID() {
        return AppContext.uuid;
    }

    /**
     * 屏幕高
     */
    @Override
    public int getScreenHeight() {
        return AppContext.sScreenHeight;
    }

    /**
     * 屏幕宽
     */
    @Override
    public int getScreenWidth() {
        return AppContext.sScreenWidth;
    }

    /**
     * 屏幕密度dpi
     */
    @Override
    public int getScreenDpi() {
        return AppContext.sScreenDpi;
    }

    /**
     * 设备（手机或者车机）版本号（Android版本号）
     */
    @Override
    public String getOSVersion() {
        return AppContext.getOSVersion();
    }

    /**
     * 设备品牌
     */
    @Override
    public String getDeviceBrand() {
        return AppContext.deviceBrand;
    }

    /**
     * 设备型号
     */
    @Override
    public String getDeviceModel() {
        return AppContext.deviceName;
    }

    /**
     * 渠道
     */
    @Override
    public String getChannel() {
        return "official";
    }

    /**
     * token
     */
    @Override
    public String getToken() {
        return MMKVUtil.INSTANCE.getUserToken();
    }

    @Override
    public boolean isDebug() {
        return BuildConfig.DEBUG;
    }

    /**
     * @return
     */
    @Override
    public String getCoffeeOSVersion() {
        return AppContext.coffeeOSVersion;
    }

    @Override
    public Application getApplicationContext() {
        return application;
    }

}
