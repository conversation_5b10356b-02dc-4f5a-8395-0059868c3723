package com.healthlink.hms.mvvm.network.interceptor;

import android.util.Log;

import com.healthlink.hms.mvvm.network.utils.KLog;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Protocol;
import okhttp3.Response;
import okhttp3.ResponseBody;

public class ResponseInterceptor implements Interceptor {
    private static final String TAG = "ResponseInterceptor";

    /**
     * 拦截
     */
    @Override
    public Response intercept(Chain chain) throws IOException {
        try {
            long requestTime = System.currentTimeMillis();
            Response response = chain.proceed(chain.request());

            String url = chain.request()!=null? chain.request().url().toString() : "";
            KLog.i(TAG, url + " , requestSpendTime=" + (System.currentTimeMillis() - requestTime) + "ms");
            return response;
        }catch(Throwable ex){
            Log.i(TAG,"network exception. "+ex.getMessage());
            return new Response.Builder()
                    .request(chain.request())           // 必须包含原始请求
                    .protocol(Protocol.HTTP_1_1)        // 设置协议版本
                    .code(500)                          // 设置有效的 HTTP 状态码，500 表示服务器错误
                    .message("Internal Server Error")   // 设置错误消息
                    .body(ResponseBody.create("", MediaType.parse("text/json")))  // 空的响应体
                    .build();
        }
    }
}
