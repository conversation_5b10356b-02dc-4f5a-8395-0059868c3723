package com.healthlink.hms.mvvm.api

import com.healthlink.hms.mvvm.model.BaseResponse
import com.healthlink.hms.mvvm.model.HealthInfoDTO
import com.healthlink.hms.mvvm.model.request.HealthInfoRequestParam
import com.healthlink.hms.server.data.dto.GPSDTO
import com.healthlink.hms.server.data.dto.HealthReportDTO
import com.healthlink.hms.server.data.dto.HolidayDTO
import com.healthlink.hms.server.data.dto.LiveHealthStatusDTO
import com.healthlink.hms.server.data.dto.Pressure1HourSummaryDTO
import com.healthlink.hms.utils.map.AltitudeManager
import io.reactivex.Observable
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path

/**
 * Created by imaginedays on 2024/7/19
 *
 *
 */
interface ApiServiceKot {
    companion object {
        const val GERNERAL_URL: String =  "/app-api/gwm-cockpit"
        const val URL_API_HEALTH_DATA: String = "/healthdata/detail"
        const val URL_API_HEALTH_DETAIL: String =  GERNERAL_URL + URL_API_HEALTH_DATA
    }

    // 实时体征参数综合分析
    @POST("$URL_API_HEALTH_DETAIL/result")
    suspend fun getLiveHealthStatus(@Body param: Map<String,String>?): BaseResponse<LiveHealthStatusDTO>

    // 当前日期是不是节假日
    @GET("$GERNERAL_URL/service/holiday")
    suspend fun todayIsHoliday(): BaseResponse<HolidayDTO>

    // 根据经纬度获取海拔 gps
    @GET("$GERNERAL_URL/service/elevation/{gps}")
    suspend fun getElevationByGps(@Path("gps") gps: String?): BaseResponse<GPSDTO>

    // 最近一小时压力状态统计
    @GET("$URL_API_HEALTH_DETAIL/stress/count/{userId}")
    suspend fun pressure1HourSummary(@Path("userId") userId: String?): BaseResponse<Pressure1HourSummaryDTO>

    // 最近4小时未活动
    @GET("$URL_API_HEALTH_DETAIL/activity/{userId}")
    suspend fun activity4HourSummary(@Path("userId") userId: String?): BaseResponse<*>

    // 健康数据心率、血氧
    @POST("${GERNERAL_URL}/index/info")
    suspend fun newHealthInfo(@Body param: HealthInfoRequestParam): BaseResponse<HealthInfoDTO?>
    // 保存用户个人信息
    @POST("${GERNERAL_URL}/user/info")
    @JvmSuppressWildcards
    suspend fun saveUserInfo(@Body param: Map<String,Any>): BaseResponse<Boolean?>

    //获取健康报告页面信息
    @POST("$URL_API_HEALTH_DETAIL/report")
    suspend fun getHealthReportInfo(@Body param: Map<String, String>): BaseResponse<HealthReportDTO>

    // 获取地理位置海拔等级 /app-api/gwm-cockpit/service/areaHighAltitudeJson
    @GET("$GERNERAL_URL/service/areaHighAltitudeJson")
    suspend fun getAreaHighAltitudeJson(): BaseResponse<AltitudeManager.AltitudeDatas>


}