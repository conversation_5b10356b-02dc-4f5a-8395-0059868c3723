package com.healthlink.hms.mvvm.network.interceptor;

import android.util.Log;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;

public class LogInterceptor implements Interceptor {
    String mTag = "LogInterceptor";
    @Override
    public Response intercept(Chain chain) throws IOException {
        try {
            Request request = chain.request();
            Log.d(mTag, String.format("请求开始:%1$s ->%2$s", request.method(), request.url()));
            if (request.headers() != null) {
                Log.d(mTag, "Headers:" + request.headers());
            }
            long requestTime = System.currentTimeMillis();
            if (request.body() != null) {
                String requestBody = bodyToString(request.body());
                Log.d(mTag, "RequestBody:" + (requestBody.length() > 2048 ? "数据太长..." : requestBody));
            }

            Response response = chain.proceed(chain.request());
            MediaType mediaType = response.body().contentType();
            String responseBody = response.body().string();
            Log.d(mTag, String.format("请求结束:%1$s->%2$s ->耗时:%3$s毫秒", request.method(), request.url(), System.currentTimeMillis() - requestTime));
            Log.d(mTag, "ResponseBody:" + responseBody);

            return response.newBuilder()
                    .body(ResponseBody.create(mediaType, responseBody))
                    .build();
        }catch(Throwable ex){
            Log.i(mTag,"network exception. "+ex.getMessage());
            // 构建一个有效的 Response.Builder 对象
            return new Response.Builder()
                    .request(chain.request())           // 必须包含原始请求
                    .protocol(Protocol.HTTP_1_1)        // 设置协议版本
                    .code(500)                          // 设置有效的 HTTP 状态码，500 表示服务器错误
                    .message("Internal Server Error")   // 设置错误消息
                    .body(ResponseBody.create("", MediaType.parse("text/json")))  // 空的响应体
                    .build();
        }
    }

    private String bodyToString(final RequestBody request) {
        if (request != null) {
            try {
                final RequestBody copy = request;
                final Buffer buffer = new Buffer();
                copy.writeTo(buffer);
                return buffer.readUtf8();
            } catch (final IOException e) {
                Log.i(mTag, "Did not work.");
            }
        }
        return null;
    }
}
