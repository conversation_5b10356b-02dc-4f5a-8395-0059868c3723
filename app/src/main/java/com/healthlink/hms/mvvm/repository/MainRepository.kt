package com.healthlink.hms.mvvm.repository

/**
 * Created by imaginedays on 2024/6/25
 *
 *
 */
import android.annotation.SuppressLint
import android.util.Log
import android.widget.Toast
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.base.AppContext
import com.healthlink.hms.mvvm.api.ApiService
import com.healthlink.hms.mvvm.model.BaseResponse
import com.healthlink.hms.mvvm.model.BaseResponseCallback
import com.healthlink.hms.mvvm.model.HealthInfoDTO
import com.healthlink.hms.mvvm.model.request.HealthInfoRequestParam
import com.healthlink.hms.mvvm.network.BaseObserver
import com.healthlink.hms.mvvm.network.NetworkApi
import com.healthlink.hms.mvvm.network.utils.KLog
import com.healthlink.hms.server.data.bean.BaseDTO
import com.healthlink.hms.server.data.dto.BloodPressureResponseDTO
import com.healthlink.hms.server.data.dto.ChartDataDTO
import com.healthlink.hms.server.data.dto.HWAuthStatusDTO
import com.healthlink.hms.server.data.dto.HealthBloodpressureSummaryDTO
import com.healthlink.hms.server.data.dto.HealthHistoryDataStatusDTO
import com.healthlink.hms.server.data.dto.HealthReportDTO
import com.healthlink.hms.server.data.dto.HealthSpO2SummaryDTO
import com.healthlink.hms.server.data.dto.HealthSummarizeDTO
import com.healthlink.hms.server.data.dto.HealthTempSummaryDTO
import com.healthlink.hms.server.data.dto.HealthTipsDTO
import com.healthlink.hms.server.data.dto.HolidayDTO
import com.healthlink.hms.server.data.dto.LiveHealthStatusDTO
import com.healthlink.hms.server.data.dto.Pressure1HourSummaryDTO
import com.healthlink.hms.server.data.dto.SleepDayResponseDTO
import com.healthlink.hms.server.data.dto.charts.HealthSleepSummeryDTO
import com.healthlink.hms.server.data.dto.SleepMonthResponseDTO
import com.healthlink.hms.server.data.dto.SleepWeekResponseDTO
import com.healthlink.hms.server.data.dto.SleepYearResponseDTO
import com.healthlink.hms.server.data.dto.SpO2ItemResponseDTO
import com.healthlink.hms.server.data.dto.TempItemResponseDTO
import com.healthlink.hms.server.data.dto.UpgradeVersionDTO
import com.healthlink.hms.server.data.dto.UserInfoDTO
import com.healthlink.hms.server.data.dto.charts.HealthSummeryDTO
import com.healthlink.hms.server.data.dto.charts.heartrate.HeartRateStatDTO
import com.healthlink.hms.server.data.dto.charts.pressure.PressureDetailRespDTO
import com.healthlink.hms.server.data.dto.charts.pressure.PressureSummaryDTO
import com.healthlink.hms.server.data.dto.init.CallDoctorPhoneDTO
import com.healthlink.hms.server.data.dto.init.DoctorServiceDTO
import com.healthlink.hms.server.data.dto.init.InitInfoDTO
import com.healthlink.hms.server.data.dto.init.VehicleCapacityDTO
import com.healthlink.hms.utils.ToastUtil
import com.healthlink.hms.utils.MMKVUtil

/**
 * Main存储库 用于对数据进行处理
 */
class MainRepository {
    private val TAG = "MainRepository"
    @SuppressLint("CheckResult")
    fun getInitInfo(parames: Map<*, *>, callback: BaseResponseCallback<InitInfoDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getInitInfo(parames).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<InitInfoDTO>>() {
            override fun onSuccess(initInfo: BaseResponse<InitInfoDTO>) {
                if (callback != null) {
                    callback.onSuccess(initInfo)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getInitInfo Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     * 实时体征参数综合分析
     */
    @SuppressLint("CheckResult")
    fun getLiveHealthStatus(parameters: Map<*, *>, callback: BaseResponseCallback<LiveHealthStatusDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getLiveHealthStatus(parameters).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<LiveHealthStatusDTO>>() {
            override fun onSuccess(liveHealthStatusDTO: BaseResponse<LiveHealthStatusDTO>) {
                if (callback != null) {
                    callback.onSuccess(liveHealthStatusDTO)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getInitInfo Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     * 发送车机服务功能请求
     * 车辆ID vehicleModeId
     * version 版本号
     */
    @SuppressLint("CheckResult")
    fun getVehicleServiceList(map: Map<*, *>, callback: BaseResponseCallback<VehicleCapacityDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getVehicleServiceList(map).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<VehicleCapacityDTO>>() {
            override fun onSuccess(capacityDTO: BaseResponse<VehicleCapacityDTO>) {
                if (callback != null) {
                    callback.onSuccess(capacityDTO)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getInitInfo Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     * 电话医生、紧急救援服务号码
     */
    @SuppressLint("CheckResult")
    fun getDoctorService(callback: BaseResponseCallback<DoctorServiceDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getDoctorService().compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<DoctorServiceDTO>>() {
            override fun onSuccess(doctorServiceDTO: BaseResponse<DoctorServiceDTO>) {
                if (callback != null) {
                    callback.onSuccess(doctorServiceDTO)
                    if (doctorServiceDTO.code == "0" && doctorServiceDTO.data != null) {
                        if (doctorServiceDTO.data!!.phoneDoctorNumber.isNotEmpty()) {
                            MMKVUtil.storePhoneDoctorNumber(doctorServiceDTO.data!!.phoneDoctorNumber)
                        }
                    }
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getInitInfo Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }


    @SuppressLint("CheckResult")
    fun getHealthTips(callback: BaseResponseCallback<HealthTipsDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getHealthTips().compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<HealthTipsDTO>>() {
            override fun onSuccess(tipsInfoDataResponse: BaseResponse<HealthTipsDTO>) {
                if (callback != null) {
                    callback.onSuccess(tipsInfoDataResponse)
                }
            }

            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getHealthTips Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    @SuppressLint("CheckResult")
    fun getHealthTipsWithVin(vin : String,callback: BaseResponseCallback<HealthTipsDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getHealthTipsWithVin(vin).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<HealthTipsDTO>>() {
            override fun onSuccess(tipsInfoDataResponse: BaseResponse<HealthTipsDTO>) {
                if (callback != null) {
                    callback.onSuccess(tipsInfoDataResponse)
                }
            }

            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getHealthTipsWithVin Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    @SuppressLint("CheckResult")
    fun getHomeNewHealthInfo(requestParam: HealthInfoRequestParam, callback: BaseResponseCallback<HealthInfoDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.newHealthInfo(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<HealthInfoDTO>>() {
            override fun onSuccess(healthInfoResponse: BaseResponse<HealthInfoDTO>) {
                if (callback != null) {
                    callback.onSuccess(healthInfoResponse)
                }
            }

            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getHomeNewHealthInfo Error: " + e.toString())
                if(HmsApplication.isInForeground) {
                    ToastUtil.makeText(AppContext.sAppContext, "更新失败，请重试", Toast.LENGTH_SHORT).show()
                }
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    @SuppressLint("CheckResult")
    fun getNewHealthInfoDetail(requestParam: HealthInfoRequestParam, callback: BaseResponseCallback<ChartDataDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.newHealthInfoDetail(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<ChartDataDTO>>() {
            override fun onSuccess(healthInfoResponse: BaseResponse<ChartDataDTO>) {
                if (callback != null) {
                    callback.onSuccess(healthInfoResponse)
                }
            }

            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getNewHealthInfoDetail Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    @SuppressLint("CheckResult")
    fun getHeartrateDetail(requestParam: Map<*, *>, callback: BaseResponseCallback<HeartRateStatDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getHeartrateDetail(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<HeartRateStatDTO>>() {
            override fun onSuccess(healthInfoResponse: BaseResponse<HeartRateStatDTO>) {
                if (callback != null) {
                    callback.onSuccess(healthInfoResponse)
                }
            }

            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getHeartrateDetail Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    @SuppressLint("CheckResult")
    fun getHealthSummery(requestParam: Map<*, *>, callback: BaseResponseCallback<HealthSummeryDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getHealthSummery(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<HealthSummeryDTO>>() {
            override fun onSuccess(healthInfoResponse: BaseResponse<HealthSummeryDTO>) {
                if (callback != null) {
                    callback.onSuccess(healthInfoResponse)
                }
            }

            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getHealthSummery Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    @SuppressLint("CheckResult")
    fun getSleepDayDetail(requestParam: Map<*, *>, callback: BaseResponseCallback<SleepDayResponseDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getSleepDayDetail(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<SleepDayResponseDTO>>() {
            override fun onSuccess(resp: BaseResponse<SleepDayResponseDTO>) {
                if (callback != null) {
                    callback.onSuccess(resp)
                }
            }

            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getSleepDayDetail Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    @SuppressLint("CheckResult")
    fun getSleepWeekDetail(requestParam: Map<*, *>, callback: BaseResponseCallback<SleepWeekResponseDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getSleepWeekDetail(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<SleepWeekResponseDTO>>() {
            override fun onSuccess(resp: BaseResponse<SleepWeekResponseDTO>) {
                if (callback != null) {
                    callback.onSuccess(resp)
                }
            }

            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getSleepWeekDetail Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    @SuppressLint("CheckResult")
    fun getSleepMonthDetail(requestParam: Map<*, *>, callback: BaseResponseCallback<SleepMonthResponseDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getSleepMonthDetail(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<SleepMonthResponseDTO>>() {
            override fun onSuccess(resp: BaseResponse<SleepMonthResponseDTO>) {
                if (callback != null) {
                    callback.onSuccess(resp)
                }
            }

            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getSleepMonthDetail Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    @SuppressLint("CheckResult")
    fun getSleepYearDetail(requestParam: Map<*, *>, callback: BaseResponseCallback<SleepYearResponseDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getSleepYearDetail(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<SleepYearResponseDTO>>() {
            override fun onSuccess(resp: BaseResponse<SleepYearResponseDTO>) {
                if (callback != null) {
                    callback.onSuccess(resp)
                }
            }

            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getSleepYearDetail Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    @SuppressLint("CheckResult")
    fun getHealthSleepSummery(requestParam: Map<*, *>, callback: BaseResponseCallback<HealthSleepSummeryDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getHealthSleepSummery(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<HealthSleepSummeryDTO>>() {
            override fun onSuccess(healthInfoResponse: BaseResponse<HealthSleepSummeryDTO>) {
                if (callback != null) {
                    callback.onSuccess(healthInfoResponse)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getHealthSleepSummery Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     * 压力详情页接口数据
     */
    @SuppressLint("CheckResult")
    fun getPressureDetail(requestParam: Map<*, *>, callback: BaseResponseCallback<PressureDetailRespDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)

        apiService.getPressureDetail(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<PressureDetailRespDTO>>() {
            override fun onSuccess(resp: BaseResponse<PressureDetailRespDTO>) {
                if (callback != null) {
                    callback.onSuccess(resp)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getPressureDetail Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    @SuppressLint("CheckResult")
    fun getPressureSummary(requestParam: Map<*, *>, callback: BaseResponseCallback<PressureSummaryDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getPressureSummary(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<PressureSummaryDTO>>() {
            override fun onSuccess(resp: BaseResponse<PressureSummaryDTO>) {
                if (callback != null) {
                    callback.onSuccess(resp)
                }
            }
            override fun onFailure(e: Throwable) {
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
                Log.d(TAG,"getPressureSummary Error: " + e.toString())
            }
        }))
    }
    @SuppressLint("CheckResult")
    fun getSpO2Summary(requestParam: Map<*, *>, callback: BaseResponseCallback<HealthSpO2SummaryDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getSpO2Summary(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<HealthSpO2SummaryDTO>>() {
            override fun onSuccess(resp: BaseResponse<HealthSpO2SummaryDTO>) {
                if (callback != null) {
                    callback.onSuccess(resp)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getSpO2Summary Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    @SuppressLint("CheckResult")
    fun getTempSummary(requestParam: Map<*, *>, callback: BaseResponseCallback<HealthTempSummaryDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getTempSummary(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<HealthTempSummaryDTO>>() {
            override fun onSuccess(resp: BaseResponse<HealthTempSummaryDTO>) {
                if (callback != null) {
                    callback.onSuccess(resp)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getTempSummary Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }



    @SuppressLint("CheckResult")
    fun getSpO2DayDetail(requestParam: Map<*, *>, callback: BaseResponseCallback<SpO2ItemResponseDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getSpO2DayDetail(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<SpO2ItemResponseDTO>>() {
            override fun onSuccess(resp: BaseResponse<SpO2ItemResponseDTO>) {
                if (callback != null) {
                    callback.onSuccess(resp)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getSpO2DayDetail Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    @SuppressLint("CheckResult")
    fun getTempDetail(requestParam: Map<*, *>, callback: BaseResponseCallback<TempItemResponseDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getTempDetail(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<TempItemResponseDTO>>() {
            override fun onSuccess(resp: BaseResponse<TempItemResponseDTO>) {
                if (callback != null) {
                    callback.onSuccess(resp)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getSpO2DayDetail Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     * 血压数据
     */
    @SuppressLint("CheckResult")
    fun getBloodPressureDetail(requestParam: Map<*, *>, callback: BaseResponseCallback<BloodPressureResponseDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getBloodPressureDetail(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<BloodPressureResponseDTO>>() {
            override fun onSuccess(resp: BaseResponse<BloodPressureResponseDTO>) {
                if (callback != null) {
                    callback.onSuccess(resp)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getBloodPressureDetail Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     * 血压建议
     */
    @SuppressLint("CheckResult")
    fun getBloodpressureSummary(requestParam: Map<*, *>, callback: BaseResponseCallback<HealthBloodpressureSummaryDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getBloodpressureSummary(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<HealthBloodpressureSummaryDTO>>() {
            override fun onSuccess(resp: BaseResponse<HealthBloodpressureSummaryDTO>) {
                if (callback != null) {
                    callback.onSuccess(resp)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getBloodpressureSummary Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     * 当天是不是节假日
     */
    @SuppressLint("CheckResult")
    fun getTodayIsHoliday(callback: BaseResponseCallback<HolidayDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.todayIsHoliday().compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<HolidayDTO>>() {
            override fun onSuccess(resp: BaseResponse<HolidayDTO>) {
                if (callback != null) {
                    callback.onSuccess(resp)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getTodayIsHoliday Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     * 最近一小时压力状态统计
     */
    @SuppressLint("CheckResult")
    fun getPressure1HourSummary(userId: String,callback: BaseResponseCallback<Pressure1HourSummaryDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.pressure1HourSummary(userId).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<Pressure1HourSummaryDTO>>() {
            override fun onSuccess(resp: BaseResponse<Pressure1HourSummaryDTO>) {
                if (callback != null) {
                    callback.onSuccess(resp)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getHealthTips Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     *用户注销
     */
    @SuppressLint("CheckResult")
    fun getDeleteUser(userId: String, callback: BaseResponseCallback<Boolean>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.deleteUser(userId).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<Boolean>>() {
            override fun onSuccess(capacityDTO: BaseResponse<Boolean>) {
                if (callback != null) {
                    callback.onSuccess(capacityDTO)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"deleteUser Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     *关联账户解绑
     */
    @SuppressLint("CheckResult")
    fun getUnbindAccount(userId: String, callback: BaseResponseCallback<Boolean>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.unbindAccount(userId).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<Boolean>>() {
            override fun onSuccess(capacityDTO: BaseResponse<Boolean>) {
                if (callback != null) {
                    callback.onSuccess(capacityDTO)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"unbindAccount Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     *获取用户信息
     */
    @SuppressLint("CheckResult")
    fun getUserInfo(userId: String, callback: BaseResponseCallback<UserInfoDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.userInfo(userId).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<UserInfoDTO>>() {
            override fun onSuccess(capacityDTO: BaseResponse<UserInfoDTO>) {
                if (callback != null) {
                    callback.onSuccess(capacityDTO)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"userInfo Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    @SuppressLint("CheckResult")
    fun getHistoryStatus(userId : String,callback: BaseResponseCallback<HealthHistoryDataStatusDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getDataReadyStatus(userId).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<HealthHistoryDataStatusDTO>>() {
            override fun onSuccess(dataReadyResponse: BaseResponse<HealthHistoryDataStatusDTO>) {
                if (callback != null) {
                    callback.onSuccess(dataReadyResponse)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getDataReadyStatus Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     *获取用户信息
     */
    @SuppressLint("CheckResult")
    fun getAuthStatus(userId: String, callback: BaseResponseCallback<HWAuthStatusDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getAuthStatus(userId).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<HWAuthStatusDTO>>() {
            override fun onSuccess(capacityDTO: BaseResponse<HWAuthStatusDTO>) {
                if (callback != null) {
                    callback.onSuccess(capacityDTO)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getAuthStatus Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     *保存用户信息
     */
    @SuppressLint("CheckResult")
    fun saveUserInfoData(requestParam: Map<*, *>, callback: BaseResponseCallback<Boolean>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.saveUserInfo(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<Boolean>>() {
            override fun onSuccess(capacityDTO: BaseResponse<Boolean>) {
                if (callback != null) {
                    callback.onSuccess(capacityDTO)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"saveUserInfo Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     *获取健康报告数据
     */
    @SuppressLint("CheckResult")
    fun getHealthRerortInfoData(requestParam: Map<*, *>, callback: BaseResponseCallback<HealthReportDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getHealthReportInfo(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<HealthReportDTO>>() {
            override fun onSuccess(capacityDTO: BaseResponse<HealthReportDTO>) {
                if (callback != null) {
                    callback.onSuccess(capacityDTO)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"unbindAccount Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     *获取健康总结数据
     */
    @SuppressLint("CheckResult")
    fun getHealthSummarizeInfoData(requestParam: Map<*, *>, callback: BaseResponseCallback<HealthSummarizeDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getHealthSummarizeInfo(requestParam).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<HealthSummarizeDTO>>() {
            override fun onSuccess(capacityDTO: BaseResponse<HealthSummarizeDTO>) {
                if (callback != null) {
                    callback.onSuccess(capacityDTO)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getHealthSummarizeInfo Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     * 数据使用说明
     */
    @SuppressLint("CheckResult")
    fun sendDataUsage(baseResponseCallback: BaseResponseCallback<String>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.dataUsageIntro().compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<String>>() {
            override fun onSuccess(dataUsageResp: BaseResponse<String>) {
                if (baseResponseCallback != null) {
                    baseResponseCallback.onSuccess(dataUsageResp)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"sendDataUsage Error: " + e.toString())
                if (baseResponseCallback != null) {
                    baseResponseCallback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     * 电话医生 - 车辆备案信息查询
     */
    @SuppressLint("CheckResult")
    fun sendQueryDoctorServicePhoneReq(params : Map<*,*>, callback: BaseResponseCallback<CallDoctorPhoneDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.queryDoctorServicePhone(params).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<CallDoctorPhoneDTO>>() {
            override fun onSuccess(doctorServicePhoneDTO: BaseResponse<CallDoctorPhoneDTO>) {
                if (callback != null) {
                    val phoneGroup = doctorServicePhoneDTO?.data?.phoneGroup
                    val isBindDoctorService = phoneGroup != null && phoneGroup.size > 0
                    MMKVUtil.setBindDoctorService(isBindDoctorService)
                    callback.onSuccess(doctorServicePhoneDTO)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getDoctorServicePhone Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }

    /**
     * 电话医生 - 车辆备案更新手机号
     */
    @SuppressLint("CheckResult")
    fun sendUpdateDoctorServicePhoneReq(params: Map<*, *>, callback: BaseResponseCallback<Boolean>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.updateDoctorServicePhone(params).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<Boolean>>() {
            override fun onSuccess(doctorServicePhoneDTO: BaseResponse<Boolean>) {
                if (callback != null) {
                    if (doctorServicePhoneDTO?.data == true) {
                        MMKVUtil.setBindDoctorService(true)
                    }
                    callback.onSuccess(doctorServicePhoneDTO)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getDoctorServicePhone Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))

    }

    @SuppressLint("CheckResult")
    fun getVersionInfo(versionCode:String,callback: BaseResponseCallback<UpgradeVersionDTO>) {
        val apiService = NetworkApi.createService(ApiService::class.java)
        apiService.getVersionStatus(versionCode).compose(NetworkApi.applySchedulers(object : BaseObserver<BaseResponse<UpgradeVersionDTO>>() {
            override fun onSuccess(resp: BaseResponse<UpgradeVersionDTO>) {
                if (callback != null) {
                    callback.onSuccess(resp)
                }
            }
            override fun onFailure(e: Throwable) {
                Log.d(TAG,"getVersionInfo Error: " + e.toString())
                if (callback != null) {
                    callback.onFailed(BaseResponse())
                }
            }
        }))
    }
}


