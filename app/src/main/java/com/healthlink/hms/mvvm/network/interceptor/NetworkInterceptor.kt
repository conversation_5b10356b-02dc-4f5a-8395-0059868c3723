package com.healthlink.hms.mvvm.network.interceptor

import android.content.Context
import com.healthlink.hms.application.HmsApplication
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException

/**
 * Created by imaginedays on 2024/9/14
 *
 *
 */
class NetworkInterceptor() : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        if (!HmsApplication.isNetworkConn()) {
            throw IOException("No internet connection")
        }
        return chain.proceed(chain.request())
    }
}