package com.healthlink.hms.mvvm.model

import com.healthlink.hms.server.data.BloodPressure
import com.healthlink.hms.server.data.dto.BloodOxygen
import com.healthlink.hms.server.data.dto.HeartRate
import com.healthlink.hms.server.data.dto.PhysiologicalPeriod
import com.healthlink.hms.server.data.dto.Pressure
import com.healthlink.hms.server.data.dto.Sleep
import com.healthlink.hms.server.data.dto.Temperature

data class HealthInfoDTO(
    var extenalId: String? = null,
    var name: String? = null,
    var height: String? = null,
    var age: String? = null,
    var weight: String? = null,
    var bodyFat: String? = null,
    var altitude: String? = null,
    var carInteriorTemperatur: String? = null,
    var heartRate: HeartRate? = null,
    var bloodOxygen: BloodOxygen? = null,
    var pressure: Pressure? = null,
    var temperature: Temperature? = null,
    var sleep: Sleep? = null,
    var physiologicalPeriod: PhysiologicalPeriod? = null,
    var bloodPressure: BloodPressure? = null
) {
}