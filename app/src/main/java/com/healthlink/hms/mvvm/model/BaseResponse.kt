package com.healthlink.hms.mvvm.model

/**
 *
 */

/**
 * 业务CODE码和网关CODE码
 */
object BusinessRespCode {
    const val DATA_ACCESS_NO_AUTH = "5" // 数据访问没有权限
    const val HUAWEI_APP_CLOSE_SERVICE = "4" // 华为运动健康App关闭
    const val LOGIN_EXPIRE = "2" // 登录失效
    const val USER_LOGIN_TIME_OUT_OR_CANCEL_AUTH = "7"  // 登录超时或取消授权操作
    const val ACCESS_SUCCESS = "0"
    const val ACCESS_FAILED_NO_INTERNET = "666"
}

data class BaseResponse<T> (
    /**
     * 处理码
     * code = 0, 表示成功
     * code = 2,数据校验失败，请求的参数不合法，描述请见“处理结果描述”
     * code = 3, 失败，失败的描述请见“处理结果描述”
     * code = 4, 请求超时失败
     */
    var code: String? = "3",
    /**
     * 处理结果描述 message
     */
    var msg: String? = "数据获取失败！请重试~",
    /**
     * 数据集
     */
    var data: T? = null)
{
}