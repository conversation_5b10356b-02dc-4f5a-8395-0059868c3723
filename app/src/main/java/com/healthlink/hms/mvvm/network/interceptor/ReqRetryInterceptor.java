package com.healthlink.hms.mvvm.network.interceptor;

import com.healthlink.hms.mvvm.network.utils.KLog;

import java.io.IOException;
import java.io.InterruptedIOException;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * Created by imaginedays on 2024/5/29
 * 网络请求重试拦截器
 */
public class ReqRetryInterceptor implements Interceptor {
    public int executionCount;//最大重试次数
    private long retryInterval;//重试的间隔
    ReqRetryInterceptor(Builder builder) {
        this.executionCount = builder.executionCount;
        this.retryInterval = builder.retryInterval;
    }



    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Response response = doRequest(chain, request);
        int retryNum = 0;
        while ((response == null || !response.isSuccessful()) && retryNum <= executionCount) {
            KLog.i("intercept Request is not successful - {}", String.valueOf(retryNum));
            final long nextInterval = getRetryInterval();
            try {
                KLog.i("Wait for {}",String.valueOf(nextInterval));
                Thread.sleep(nextInterval);
            } catch (final InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new InterruptedIOException();
            }
            retryNum++;
            // retry the request
            response = doRequest(chain, request);
        }
        return response;
    }

    private Response doRequest(Chain chain, Request request) {
        Response response = null;
        try {
            response = chain.proceed(request);
        } catch (Exception e) {
        }
        return response;
    }

    /**
     * retry间隔时间
     */
    public long getRetryInterval() {
        return this.retryInterval;
    }

    public static final class Builder {
        private int executionCount;
        private long retryInterval;
        public Builder() {
            executionCount = 3;
            retryInterval = 1000;
        }

        public ReqRetryInterceptor.Builder executionCount(int executionCount){
            this.executionCount =executionCount;
            return this;
        }

        public ReqRetryInterceptor.Builder retryInterval(long retryInterval){
            this.retryInterval =retryInterval;
            return this;
        }
        public ReqRetryInterceptor build() {
            return new ReqRetryInterceptor(this);
        }
    }
}
