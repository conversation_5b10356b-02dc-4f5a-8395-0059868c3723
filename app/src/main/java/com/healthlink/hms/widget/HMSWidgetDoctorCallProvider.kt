package com.healthlink.hms.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.SystemClock
import android.util.Log
import android.util.TypedValue.COMPLEX_UNIT_PX
import android.view.View
import android.widget.RemoteViews
import androidx.appcompat.app.AppCompatActivity
import com.gwm.tts.service.client.GwmTTSManager
import com.gwm.tts.service.request.Priority
import com.gwm.tts.service.request.StreamChannel
import com.gwm.tts.service.request.TTSRequest
import com.healthlink.hms.BuildConfig
import com.healthlink.hms.R
import com.healthlink.hms.activity.MainActivity
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.base.Constants
import com.healthlink.hms.reciever.HMSAction
import com.healthlink.hms.sdks.gwmadapter.GwmAdapterManagerKotCoroutines
import com.healthlink.hms.service.HmsDataWorker
import com.healthlink.hms.utils.AuthorizationUtil
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.HMSDialogUtils
import com.healthlink.hms.utils.MMKVUtil
import com.healthlink.hms.widget.HMSWidgetProvider.Companion
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

class HMSWidgetDoctorCallProvider : AppWidgetProvider() {
    override fun onReceive(context: Context, intent: Intent) {
        super.onReceive(context, intent)
        Log.d(mTag, "onReceive: starting ... , $intent  ")
        when (intent.action) {
            CLICK_ACTION -> {
                launchDoctorCallUI(context)
            }
        }
    }

    /**
     * 当widget第一次添加到桌面的时候回调，可添加多次widget，但该方法只回调一次
     */
    override fun onEnabled(context: Context) {
        Log.i(mTag, "onEnable")
    }

    /**
     * 当widget更新时回调
     *
     * @param context
     * @param appWidgetManager
     * @param appWidgetIds      这个数组使用用来存储已经创建的widget的id，因为可能创建了多个widget
     */
    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        Log.d(mTag, "onUpdate")
        updateHmsWidget(context, appWidgetIds)
        Log.d(mTag,"invoke updateHmsWidget() From $mTag")
        DataTrackUtil.dtClick("Health_Card_PV", DataTrackUtil.userIDMap())
    }


    //当 widget 被删除时回调
    override fun onDeleted(context: Context, appWidgetIds: IntArray) {
        super.onDeleted(context, appWidgetIds)
        Log.i(mTag, "桌面卡片被删除（onDeleted)")
        DataTrackUtil.dtClick("Health_Card_Close", DataTrackUtil.userIDMap())
    }

    //当最后一个widget实例被删除时回调.
    override fun onDisabled(context: Context) {
        super.onDisabled(context)
        Log.i(mTag, "桌面卡片被禁用（onDisabled)")
    }

    companion object {
        const val mTag = "HMSWidgetDoctorCallProvider"
        const val CLICK_ACTION = "com.healthlink.hms.widgetapp.doctor.CLICK"
        const val HEALTH_STATUS_UPDATE = "com.healthlink.hms.widgetapp.HEALTH_STATUS_UPDATE"

        const val DATA_KEY_RISK_LEVEL = "widget_info_health_status_risk_level"
        const val DATA_KEY_SCORE = "widget_info_health_status_score"
        const val DATA_KEY_RESULT = "widget_info_health_status_result"
    }

    /**
     * 根据数据更新Widget
     */
    private fun updateHmsWidget(context: Context, appWidgetIds:IntArray) {

        // TODO 私密模式 是否直接进入App
        updateHmsWidgetForLogin(context, appWidgetIds)
    }

    private fun updateHmsWidgetForLogin(context: Context, appWidgetIds:IntArray){
        Log.d(mTag, "updateHmsWidget : widget updating ...")

        //因为点击按钮后要对布局中的文本进行更新，所以需要创建一个远程view
        val remoteViews = createBaseHmsHealthStatusWidget(context)
        val appWidgetManager = AppWidgetManager.getInstance(context)
//        updateWidgetStyle(remoteViews)
        appWidgetManager.updateAppWidget(appWidgetIds, remoteViews)
    }

    private fun updateWidgetStyle(remoteViews: RemoteViews) {
        var scale = 1F

        if(BuildConfig.PLATFORM_CODE == "V4") {
            scale = 1.33F
        }
        Log.d(mTag,"doing updateWidgetStyle, scale=${scale}")
        remoteViews.setTextViewTextSize(
            R.id.widget_tv_health_status_label,
            COMPLEX_UNIT_PX,
            22*scale
        )

        remoteViews.setTextViewTextSize(
            R.id.widget_tv_health_status,
            COMPLEX_UNIT_PX,
            18f*scale
        )
    }

    /**
     * 创建基础的健康状态卡片，绑定单击进入主App的事件
     */
    private fun createBaseHmsHealthStatusWidget(context: Context): RemoteViews {
        var layoutId = getLayoutResource()
        val remoteViews = RemoteViews(context.packageName, layoutId)
//        val pendingIntent = openActivityPendingIntent(context)
        val intentClick = Intent()
        intentClick.setClass(context, HMSWidgetDoctorCallProvider::class.java)
        intentClick.action = CLICK_ACTION
        //PendingIntent表示的是一种即将发生的意图，区别于Intent它不是立即会发生的
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            0,
            intentClick,
            PendingIntent.FLAG_IMMUTABLE
        )
        // 为布局文件中的按钮设置点击监听
        remoteViews.setOnClickPendingIntent(R.id.widget_card_container_doctor,pendingIntent);
        return remoteViews
    }

    /**
     * 打开主App
     */
    private fun openActivityPendingIntent(context: Context) : PendingIntent {
        // 创建要启动的 Activity 的 Intent
        val intent: Intent = Intent(context, MainActivity::class.java)
        // 添加FLAG防止启动多余的实例 // or Intent.FLAG_ACTIVITY_CLEAR_TASK
//        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

        intent.action = HMSAction.ACTION_OPEN_PHONE_DOCTOR_SERVICE

        // 创建 PendingIntent
        val pendingIntent = PendingIntent.getActivity(
            context,
            1,
            intent,
            PendingIntent.FLAG_MUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )
        return pendingIntent
    }

    private fun launchDoctorCallUI(context: Context) {
        val intent: Intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK // 添加FLAG防止启动多余的实例 or Intent.FLAG_ACTIVITY_CLEAR_TASK
            action = HMSAction.ACTION_OPEN_PHONE_DOCTOR_SERVICE
        }

        // 如果主界面有弹框，先取消弹框
        HMSDialogUtils.clearDialog()

        if (!HmsApplication.isAgreePrivacy()) {
            Log.i(mTag, "用户未同意隐私政策，设置没有显示过 toast")
            HmsApplication.storeShowUserPrivacyToast(false)
        } else {
            Log.i(mTag, "用户同意隐私政策，不做任何操作")
        }
        context.startActivity(intent)
    }

    /**
     * 根据不同的平台选择不同的layout
     */
    private fun getLayoutResource():Int{
//        var layoutId = R.layout.layout_widget_doctor
//        if(BuildConfig.PLATFORM_CODE == "V4"){
//            Log.d(mTag , "using V4 layout_widget resource：R.layout.layout_widget_doctor")
//            layoutId = R.layout.layout_widget_doctor
//        }else if(BuildConfig.PLATFORM_CODE == "V35"){
//            Log.d(mTag , "using V35 layout_widget resource：R.layout.layout_widget_doctor_v35")
//            layoutId = R.layout.layout_widget_doctor_v35
//        }
//        return layoutId
        return R.layout.layout_widget_doctor
    }
}