package com.healthlink.hms.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.res.Resources
import android.os.SystemClock
import android.util.Log
import android.util.TypedValue.COMPLEX_UNIT_PX
import android.view.View
import android.widget.RemoteViews
import androidx.appcompat.app.AppCompatActivity
import com.gwm.tts.service.client.GwmTTSManager
import com.gwm.tts.service.request.Priority
import com.gwm.tts.service.request.StreamChannel
import com.gwm.tts.service.request.TTSRequest
import com.healthlink.hms.BuildConfig
import com.healthlink.hms.R
import com.healthlink.hms.application.HmsApplication
import com.healthlink.hms.base.Constants
import com.healthlink.hms.sdks.gwmadapter.GwmAdapterManagerKotCoroutines
import com.healthlink.hms.service.HmsDataWorker
import com.healthlink.hms.service.WidgetHmsDataWorker
import com.healthlink.hms.utils.AuthorizationUtil
import com.healthlink.hms.utils.DataTrackUtil
import com.healthlink.hms.utils.MMKVUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch


class HMSWidgetProvider : AppWidgetProvider() {

    override fun onReceive(context: Context, intent: Intent) {
        super.onReceive(context, intent)
        Log.d(mTag, "onReceive: starting ... , $intent  ")

        when (intent.action) {
            // 当Widget可用
            AppWidgetManager.ACTION_APPWIDGET_ENABLED -> {
                //显示加载数据
//                showLoading(context)
            }
            // 数据更新事件，获取数据，更新Widget
            HEALTH_STATUS_UPDATE -> {
                getDataFromIntent(context, intent)
            }
            // 点击事件，启动主App
            CLICK_ACTION -> {
                launchAppByPackageName(context,context.packageName)
                DataTrackUtil.dtClick("Health_Card_PV", DataTrackUtil.userIDMap())
            }
        }
    }

    /**
     * 当widget第一次添加到桌面的时候回调，可添加多次widget，但该方法只回调一次
     */
    override fun onEnabled(context: Context) {
        Log.i(mTag, "onEnable")
        //清楚卡片的历史数据信息
//        clearHistoryWidgetData(context)
        //立即加载
        loadHealthStatus(context)
        //初始化TT
//        initTTS(context)
        //调度任务
        HMSWidgetManager.scheduleTask(context, mTag)
        //启动行程记录程序
//        JourneyManager().startJournaryCollectService(context)
    }

    /**
     * 当widget更新时回调
     *
     * @param context
     * @param appWidgetManager
     * @param appWidgetIds      这个数组使用用来存储已经创建的widget的id，因为可能创建了多个widget
     */
    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        Log.d(mTag, "onUpdate")
        updateHmsWidget(context, appWidgetIds)
        Log.d(mTag,"invoke updateHmsWidget() From $mTag")
        //调度任务
        HMSWidgetManager.scheduleTask(context, mTag)
        DataTrackUtil.dtClick("Health_Card_PV", DataTrackUtil.userIDMap())
        //绑定服务（保证服务活跃） //启动行程记录程序
//        JourneyManager().startJournaryCollectService(context)
    }

    private fun showLoading(context: Context){
        val remoteViews = createBaseHmsHealthStatusWidget(context)
        //为对应的TextView设置文本
        remoteViews.setImageViewResource(
            R.id.widget_card_health_status,
            R.drawable.widget_health_status_disable
        )
//        remoteViews.set
        remoteViews.setTextViewText(
            R.id.widget_tv_health_status, "正在健康检测中..."
        )
        remoteViews.setTextColor(R.id.widget_tv_health_status,context.getColor(R.color.text_color_fc_100))
        remoteViews.setViewVisibility(R.id.widget_tv_health_status_summary, View.GONE)
        val appWidgetManager = AppWidgetManager.getInstance(context)
        //更新widget
        Log.i(mTag,"update hms widget of loading heath data. $remoteViews")
        appWidgetManager.updateAppWidget(
            ComponentName(context, HMSWidgetProvider::class.java),
            remoteViews
        )
    }


    /**
     * 判断用户是否已经登陆， TODO 未来抽取到全局判断中
     */
    private fun isLogin(context: Context): Boolean {
        return !MMKVUtil.isVisitorMode()
    }

    //当 widget 被删除时回调
    override fun onDeleted(context: Context, appWidgetIds: IntArray) {
        super.onDeleted(context, appWidgetIds)
        Log.i(mTag, "桌面卡片被删除（onDeleted)")
        DataTrackUtil.dtClick("Health_Card_Close", DataTrackUtil.userIDMap())
    }

    //当最后一个widget实例被删除时回调.
    override fun onDisabled(context: Context) {
        super.onDisabled(context)
        Log.i(mTag, "桌面卡片被禁用（onDisabled)")
    }

    companion object {
        const val mTag = "HMSWidgetProvider"
        const val CLICK_ACTION = "com.healthlink.hms.widgetapp.CLICK"
        const val HEALTH_STATUS_UPDATE = "com.healthlink.hms.widgetapp.HEALTH_STATUS_UPDATE"

        const val DATA_KEY_RISK_LEVEL = "widget_info_health_status_risk_level"
        const val DATA_KEY_SCORE = "widget_info_health_status_score"
        const val DATA_KEY_RESULT = "widget_info_health_status_result"
    }

    /**
     * 根据数据更新Widget
     */
    fun updateHmsWidget(context: Context,appWidgetIds:IntArray) {

        // 私密模式
        // 优先考虑私密模式
        if(HmsApplication.isPrivacyModeEnabled()){
            updateHmsWidgetForPrivacyMode(context)
        }else if (isLogin(context)) {
            // 无任何主生理指标的授权
            if(!AuthorizationUtil.hasMainPrivillegesAuth()) {
                updateHmsWidgetForNoEnoughAuthorization(context)
            }
            // 正常展示
            else{
                updateHmsWidgetForLogin(context, appWidgetIds)
            }
        }
        // 未登录样式
        else {
            updateHmsWidgetForNoLogin(context)
        }
    }

    private fun updateHmsWidgetForLogin(context: Context,appWidgetIds:IntArray){
        Log.d(mTag, "updateHmsWidget : widget updating ...")

        val  riskLevel= getData(context, DATA_KEY_RISK_LEVEL, "0")
        val  scoreStr= getData(context, DATA_KEY_SCORE, "0")
        val  result= getData(context, DATA_KEY_RESULT, "0")
        Log.d(mTag, "widget value: riskLevel=$riskLevel,score =$scoreStr, tips= $result")
        var score = 0

        if(scoreStr==null){
            score = 0
        }else{
            score = scoreStr.toInt()
        }

        //因为点击按钮后要对布局中的文本进行更新，所以需要创建一个远程view
        val remoteViews = createBaseHmsHealthStatusWidget(context)
        val appWidgetManager = AppWidgetManager.getInstance(context)
        when (riskLevel) {
            "很棒" -> {
                remoteViews.setImageViewResource(
                    R.id.widget_iv_health_status,
                    R.drawable.widget_health_status_bg_normal
                )
                remoteViews.setTextViewText(
                    R.id.widget_tv_health_status,
                    result
                )
                remoteViews.setTextViewText(
                    R.id.widget_health_score_data,
                    scoreStr
                )

                var bitmap = WidgetScoreBitmapFactory.drawScoreBitmap(context,score,R.drawable.widget_health_status_basic_normal,"normal")
                if(bitmap!=null){
                    val res = WidgetScoreBitmapFactory.basicBitmapMap["normal"]
                    res?.let { remoteViews.setImageViewResource(R.id.widget_health_status_image_bg, it) }
                    remoteViews.setImageViewBitmap(R.id.widget_health_status_image,bitmap)
                }

                remoteViews.setTextViewText(R.id.widget_tv_health_status_code_name, "很棒")
                remoteViews.setViewVisibility(R.id.widget_tv_health_status_summary, View.VISIBLE)
                remoteViews.setViewVisibility(R.id.widget_heath_score_ll, View.VISIBLE)
                remoteViews.setViewVisibility(R.id.widget_health_status_image, View.VISIBLE)
                remoteViews.setViewVisibility(R.id.widget_health_status_image_bg, View.VISIBLE)
                remoteViews.setViewVisibility(R.id.widget_tv_login_status, View.GONE)
                remoteViews.setViewVisibility(R.id.widget_tv_health_status, View.VISIBLE)
                remoteViews.setTextColor(R.id.widget_tv_health_status,context.getColor(R.color.text_color_fc_60))
            }
            "良好" -> {
                remoteViews.setImageViewResource( R.id.widget_iv_health_status,R.drawable.widget_health_status_bg_low)
                remoteViews.setTextViewText(
                    R.id.widget_tv_health_status,
                    result
                )
                remoteViews.setTextViewText(
                    R.id.widget_health_score_data,
                    scoreStr
                )
                var bitmap = WidgetScoreBitmapFactory.drawScoreBitmap(context,score.toInt(),R.drawable.widget_health_status_basic_normal,"low")
                if(bitmap!=null){
                    val res = WidgetScoreBitmapFactory.basicBitmapMap["low"]
                    res?.let { remoteViews.setImageViewResource(R.id.widget_health_status_image_bg, it) }
                    remoteViews.setImageViewBitmap(R.id.widget_health_status_image,bitmap)
                }

                remoteViews.setTextViewText(R.id.widget_tv_health_status_code_name, "良好")
                remoteViews.setViewVisibility(R.id.widget_tv_health_status_summary, View.VISIBLE)
                remoteViews.setViewVisibility(R.id.widget_heath_score_ll, View.VISIBLE)
                remoteViews.setViewVisibility(R.id.widget_health_status_image, View.VISIBLE)
                remoteViews.setViewVisibility(R.id.widget_health_status_image_bg, View.VISIBLE)
                remoteViews.setViewVisibility(R.id.widget_tv_login_status, View.GONE)
                remoteViews.setViewVisibility(R.id.widget_tv_health_status, View.VISIBLE)
                remoteViews.setTextColor(R.id.widget_tv_health_status,context.getColor(R.color.text_color_fc_60))
            }
            "不佳" -> {
                remoteViews.setImageViewResource(
                    R.id.widget_iv_health_status,
                    R.drawable.widget_health_status_bg_middle
                )
                remoteViews.setTextViewText(
                    R.id.widget_tv_health_status,
                    result
                )
                remoteViews.setTextViewText(
                    R.id.widget_health_score_data,
                    scoreStr
                )
                var bitmap = WidgetScoreBitmapFactory.drawScoreBitmap(context,score.toInt(),R.drawable.widget_health_status_basic_normal,"middle")
                if(bitmap!=null){
                    val res = WidgetScoreBitmapFactory.basicBitmapMap["middle"]
                    res?.let { remoteViews.setImageViewResource(R.id.widget_health_status_image_bg, it) }
                    remoteViews.setImageViewBitmap(R.id.widget_health_status_image,bitmap)
                }

                remoteViews.setTextViewText(R.id.widget_tv_health_status_code_name, "不佳")
                remoteViews.setViewVisibility(R.id.widget_tv_health_status_summary, View.VISIBLE)
                remoteViews.setViewVisibility(R.id.widget_heath_score_ll, View.VISIBLE)
                remoteViews.setViewVisibility(R.id.widget_health_status_image, View.VISIBLE)
                remoteViews.setViewVisibility(R.id.widget_health_status_image_bg, View.VISIBLE)
                remoteViews.setViewVisibility(R.id.widget_tv_login_status, View.GONE)
                remoteViews.setViewVisibility(R.id.widget_tv_health_status, View.VISIBLE)
                remoteViews.setTextColor(R.id.widget_tv_health_status,context.getColor(R.color.text_color_fc_60))
            }
            "较差" -> {
                remoteViews.setImageViewResource(
                    R.id.widget_iv_health_status,
                    R.drawable.widget_health_status_bg_high
                )
                remoteViews.setTextViewText(
                    R.id.widget_tv_health_status,
                    result
                )
                remoteViews.setTextViewText(
                    R.id.widget_health_score_data,
                    scoreStr
                )
                var bitmap = WidgetScoreBitmapFactory.drawScoreBitmap(context,score.toInt(),R.drawable.widget_health_status_basic_normal,"high")
                if(bitmap!=null){
                    val res = WidgetScoreBitmapFactory.basicBitmapMap["high"]
                    res?.let { remoteViews.setImageViewResource(R.id.widget_health_status_image_bg, it) }
                    remoteViews.setImageViewBitmap(R.id.widget_health_status_image,bitmap)
                }
                remoteViews.setTextViewText(R.id.widget_tv_health_status_code_name, "较差")
                remoteViews.setViewVisibility(R.id.widget_tv_health_status_summary, View.VISIBLE)
                remoteViews.setViewVisibility(R.id.widget_heath_score_ll, View.VISIBLE)
                remoteViews.setViewVisibility(R.id.widget_health_status_image, View.VISIBLE)
                remoteViews.setViewVisibility(R.id.widget_health_status_image_bg, View.VISIBLE)
                remoteViews.setViewVisibility(R.id.widget_tv_login_status, View.GONE)
                remoteViews.setViewVisibility(R.id.widget_tv_health_status, View.VISIBLE)
                remoteViews.setTextColor(R.id.widget_tv_health_status,context.getColor(R.color.text_color_fc_60))
            }
            else -> {
                remoteViews.setImageViewResource(
                    R.id.widget_iv_health_status,
                    R.drawable.widget_health_status_disable
                )
                remoteViews.setTextViewText(
                    R.id.widget_tv_health_status,
                    context.resources.getString(R.string.hms_widget_tip_no_data)
                )
                remoteViews.setViewVisibility(R.id.widget_tv_health_status_summary, View.GONE)
                remoteViews.setViewVisibility(R.id.widget_tv_login_status, View.GONE)
                remoteViews.setViewVisibility(R.id.widget_tv_health_status, View.VISIBLE)
                //隐藏分数
                remoteViews.setViewVisibility(R.id.widget_heath_score_ll, View.GONE)
                remoteViews.setViewVisibility(R.id.widget_health_status_image, View.GONE)
                remoteViews.setViewVisibility(R.id.widget_health_status_image_bg, View.GONE)
                remoteViews.setTextColor(R.id.widget_tv_health_status,context.getColor(R.color.text_color_fc_100))
            }


        }
        updateWidgetStyle(remoteViews)
        Log.i(mTag,"update hms widget of health data. $remoteViews")
        appWidgetManager.updateAppWidget(appWidgetIds, remoteViews)
        Log.d(mTag, "updateHmsWidget : widget view update end .")
    }

    private fun updateWidgetStyle(remoteViews: RemoteViews) {
        if(remoteViews!=null){
            val densityDpi = Resources.getSystem().displayMetrics.densityDpi
            var scale = 1F
            if(BuildConfig.PLATFORM_CODE == "V4") {
                scale = 1.33F
            } else if(BuildConfig.PLATFORM_CODE == "V35" && densityDpi == 320) {
                scale = 1.33F
            }
            Log.d(mTag,"doing updateWidgetStyle, scale=${scale} densityDpi: $densityDpi")

            remoteViews.setTextViewTextSize(
                R.id.widget_tv_health_status_label,
                COMPLEX_UNIT_PX,
                22*scale
            )
            remoteViews.setTextViewTextSize(
                R.id.widget_tv_health_status_code_name,
                COMPLEX_UNIT_PX,
                26*scale
            )
            remoteViews.setTextViewTextSize(
                R.id.widget_tv_health_status,
                COMPLEX_UNIT_PX,
                18f*scale
            )
            remoteViews.setTextViewTextSize(
                R.id.widget_health_score_data,
                COMPLEX_UNIT_PX,
                24.7F*scale
            )
//            remoteViews.setViewVisibility(R.id.widget_health_score_data, View.GONE)
//            remoteViews.setViewVisibility(R.id.widget_health_score_label, View.GONE)
        }
    }


    /**
     * 全部无主生理指标权限时
     */
    public fun updateHmsWidgetForNoEnoughAuthorization(context: Context) {
        val remoteViews = createBaseHmsHealthStatusWidget(context)
        //为对应的TextView设置文本
        remoteViews.setImageViewResource(
            R.id.widget_iv_health_status,
            R.drawable.widget_health_status_disable
        )
        remoteViews.setTextViewText(
            R.id.widget_tv_login_status,
            context.resources.getString(R.string.no_enogh_authorizatoin_tips)
        )
        remoteViews.setTextColor(R.id.widget_tv_login_status,context.getColor(R.color.text_color_fc_100))
        remoteViews.setViewVisibility(R.id.widget_tv_health_status_summary, View.GONE)
        remoteViews.setViewVisibility(R.id.widget_tv_health_status, View.GONE)
        remoteViews.setViewVisibility(R.id.widget_heath_score_ll, View.GONE)
        remoteViews.setViewVisibility(R.id.widget_health_status_image, View.GONE)
        remoteViews.setViewVisibility(R.id.widget_health_status_image_bg, View.GONE)

        val appWidgetManager = AppWidgetManager.getInstance(context)
        //更新widget
        Log.i(mTag,"update hms widget of no auth. $remoteViews")
        appWidgetManager.updateAppWidget(
            ComponentName(context, HMSWidgetProvider::class.java),
            remoteViews
        )
    }

    /**
     * 未登录时的Widget状态
     */
    public fun updateHmsWidgetForPrivacyMode(context: Context) {
        val remoteViews = createBaseHmsHealthStatusWidget(context)
        //为对应的TextView设置文本
        remoteViews.setImageViewResource(
            R.id.widget_iv_health_status,
            R.drawable.widget_health_status_disable
        )
        remoteViews.setTextViewText(
            R.id.widget_tv_login_status,
            context.resources.getString(R.string.hms_widget_tip_for_privacy_mode)
        )
        remoteViews.setTextColor(R.id.widget_tv_login_status,context.getColor(R.color.text_color_fc_100))
        remoteViews.setViewVisibility(R.id.widget_tv_health_status_summary, View.GONE)
        remoteViews.setViewVisibility(R.id.widget_tv_health_status, View.GONE)
        remoteViews.setViewVisibility(R.id.widget_heath_score_ll, View.GONE)
        remoteViews.setViewVisibility(R.id.widget_health_status_image, View.GONE)
        remoteViews.setViewVisibility(R.id.widget_health_status_image_bg, View.GONE)

        val appWidgetManager = AppWidgetManager.getInstance(context)
        //更新widget
        Log.i(mTag,"update hms widget of privacy mode. $remoteViews")
        appWidgetManager.updateAppWidget(
            ComponentName(context, HMSWidgetProvider::class.java),
            remoteViews
        )
    }

    /**
     * 未登录时的Widget状态
     */
    public fun updateHmsWidgetForNoLogin(context: Context) {
        val remoteViews = createBaseHmsHealthStatusWidget(context)
        //为对应的TextView设置文本
        remoteViews.setImageViewResource(
            R.id.widget_iv_health_status,
            R.drawable.widget_health_status_disable
        )
        remoteViews.setTextViewText(
            R.id.widget_tv_login_status,
            context.resources.getString(R.string.hms_widget_tip_for_no_login)
        )
        remoteViews.setTextColor(R.id.widget_tv_login_status,context.getColor(R.color.text_color_fc_100))
        remoteViews.setViewVisibility(R.id.widget_tv_health_status_summary, View.GONE)
        remoteViews.setViewVisibility(R.id.widget_tv_health_status,View.GONE)
        remoteViews.setViewVisibility(R.id.widget_heath_score_ll, View.GONE)
        remoteViews.setViewVisibility(R.id.widget_health_status_image, View.GONE)
        remoteViews.setViewVisibility(R.id.widget_health_status_image_bg, View.GONE)

        val appWidgetManager = AppWidgetManager.getInstance(context)
        //更新widget
        Log.i(mTag,"update hms widget of no login. $remoteViews")
        appWidgetManager.updateAppWidget(
            ComponentName(context, HMSWidgetProvider::class.java),
            remoteViews
        )
    }


    /**
     * 创建基础的健康状态卡片，绑定单击进入主App的事件
     */
    private fun createBaseHmsHealthStatusWidget(context: Context): RemoteViews{
        var layoutId = getLayoutResource()
        val remoteViews = RemoteViews(context.packageName, layoutId)
        val intentClick = Intent()
        intentClick.setClass(context, HMSWidgetProvider::class.java)
        intentClick.action = CLICK_ACTION
        //PendingIntent表示的是一种即将发生的意图，区别于Intent它不是立即会发生的
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            0,
            intentClick,
            PendingIntent.FLAG_IMMUTABLE
        )
        //为布局文件中的按钮设置点击监听
        remoteViews.setOnClickPendingIntent(R.id.widget_card_health_status,pendingIntent);
        return remoteViews
    }

    private fun getLayoutResource():Int{
        var layoutId = R.layout.layout_widget
        if(BuildConfig.PLATFORM_CODE == "V4"){
            Log.d(mTag , "using V4 layout_widget resource：R.layout.layout_widget")
            layoutId = R.layout.layout_widget
        }else if(BuildConfig.PLATFORM_CODE == "V35"){
            Log.d(mTag , "using V4 layout_widget resource：R.layout.layout_widget_v35")
            layoutId = R.layout.layout_widget_v35
        }
        return layoutId
    }

    /**
     * 初始化语音播报模块
     */
    public fun initTTS(context: Context){
//        //TTS 连接监听
//        GwmTTSManager.getInstance().init(context
//        ) { connect ->
//            Log.i(mTag, " TTS connect status : $connect")
//            try {
////                    mIsTTSConnect = connect
////                    notifyInitAbilityCallback(isConnect)
//            } catch (e: RemoteException) {
//                Log.e(mTag, "onConnectState(),connect error:" + e.message)
//            }
//        }
//        //TTS监听
//        GwmTTSManager.getInstance().setTTSListener(object:TTSListener{
//            override fun onStart(p0: String?) {
//                Log.i(mTag, "TTS Start")
//            }
//
//            override fun onCancel(p0: String?, p1: Int) {
//                Log.i(mTag, "TTS Cancel")
//            }
//
//            override fun onFinish(p0: String?) {
//                Log.i(mTag, "TTS Finish")
//            }
//        })
    }

    /**
     * 开始播报
     */
    private fun playTts(context:Context ,msg: String, stopCurrent: Boolean = true){
        //最新播放迎宾语时间
        var mSharePreference =
            context.getSharedPreferences(Constants.SHARE_WIDGET_INFO, AppCompatActivity.MODE_PRIVATE)
        var lastPlayTtsTime = mSharePreference.getLong(Constants.SHARE_WIDGET_INFO_LAST_PLAY_TTS_TIME, 0)
        //系统启动时间
        var bootTime = context.getSharedPreferences(Constants.SHARE_HMS_INFO, AppCompatActivity.MODE_PRIVATE)
            .getLong(Constants.SHARE_HMS_INFO_BOOT_TIME,0L)

        var elapsedRealtime = SystemClock.elapsedRealtime()
        var currentTimeMillis = System.currentTimeMillis()
        var lastPlayPeroid = currentTimeMillis - lastPlayTtsTime
//
        Log.d(mTag,"迎宾模式：lastPlayTtsTime=$lastPlayTtsTime , lastPlayPeroid=$lastPlayPeroid, elapsedRealtime=$elapsedRealtime, bootTIme={$bootTime}")
        //(lastPlayTtsTime == 0L || lastPlayPeroid > 1000 * 60 * 1)||
        // 迎宾模式 如果TTS播放的文字是 您的健康状态正常，请保持就在后面拼接 祝您旅途愉快 播报
        if(lastPlayPeroid > elapsedRealtime){
            // 此处确定是迎宾模式？
            var tempMsg = msg
            if (tempMsg.startsWith("您的状态健康")){
                tempMsg += ",今天紫外线辐射强，注意防晒，祝您旅途愉快。"
            }
            Log.d(mTag,"迎宾模式：to play tts ... ， 内容：$tempMsg")
            val ttsId = TTSRequest.createRequestId()
            val ttsRequest = TTSRequest(tempMsg, ttsId, -1, Priority.T1, StreamChannel.INTERACTIVE)
            GwmTTSManager.getInstance().playTTS(ttsRequest)

            mSharePreference.edit().putLong(Constants.SHARE_WIDGET_INFO_LAST_PLAY_TTS_TIME, System.currentTimeMillis()).commit()
            //开启氛围灯
            GwmAdapterManagerKotCoroutines.setCarLightModeEnable(context)

            Log.d(mTag,"迎宾模式：finished play tts ...")
        }

    }

    /**
     * 向SharePreference中存储数据
     */
    public fun saveData(context:Context, key:String, value: String?){
        value?.let {
            var mSharePreference =
                context.getSharedPreferences(Constants.SHARE_WIDGET_INFO, AppCompatActivity.MODE_PRIVATE)
            mSharePreference.edit().putString(key, value).commit()
        }
    }

    /**
     * 向SharePreference中存储数据
     */
    private fun saveData(context:Context, key:String, value: Int?){
        value?.let {
            var mSharePreference =
                context.getSharedPreferences(Constants.SHARE_WIDGET_INFO, AppCompatActivity.MODE_PRIVATE)
            mSharePreference.edit().putInt(key, value).commit()
        }
    }

    private fun removeData(context:Context, key:String){
        var mSharePreference =
            context.getSharedPreferences(Constants.SHARE_WIDGET_INFO, AppCompatActivity.MODE_PRIVATE)
        mSharePreference.edit().remove(key).commit()
    }

    /**
     * 从SharePreference中读取数据
     */
    private fun getData(context:Context, key:String, defauleValue: Int): Int?{
        var mSharePreference =
            context.getSharedPreferences(Constants.SHARE_WIDGET_INFO, AppCompatActivity.MODE_PRIVATE)
        return mSharePreference.getInt(key, 0)
    }

    /**
     * 从SharePreference中读取数据
     */
    private fun getData(context:Context, key:String, defauleValue: String): String?{
        var mSharePreference =
            context.getSharedPreferences(Constants.SHARE_WIDGET_INFO, AppCompatActivity.MODE_PRIVATE)
        return mSharePreference.getString(key, defauleValue)
    }

    /**
     * 从广播中获取健康状态和建议，并存储到本地。
     */
    private fun getDataFromIntent(context:Context ,intent: Intent) {
        var healthStatusCode = intent.getStringExtra("HEALTH_STATUS_CODE")
        if(healthStatusCode!=null){
            saveData(context, "widget_info_health_status_code", healthStatusCode)
        }

        var ttsText = intent.getStringExtra("HEALTH_STATUS_TTS_TEXT")
        if(ttsText!=null){
            saveData(context, "widget_info_health_status_tts_text", ttsText)
        }

        var exceptionCount = intent.getIntExtra("HEALTH_STATUS_EXCEPTION_COUNT",0)
        if(exceptionCount!=null){
            saveData(context, "HEALTH_STATUS_EXCEPTION_COUNT", exceptionCount)
        }else{
            removeData(context,"HEALTH_STATUS_EXCEPTION_COUNT" )
        }

        var from = intent.getStringExtra("HEALTH_STATUS_FROM")

        Log.i(mTag,"从[$from]获取到健康卡片数据，健康状态代码：{$healthStatusCode},提示语：{$ttsText}")
    }

    /**
     * 打开主App
     */
    private fun launchAppByPackageName(context:Context,packageName: String) {
        val launchIntent = context.packageManager.getLaunchIntentForPackage(packageName)
        Log.i(mTag,"launchIntent {$launchIntent}")
        if (launchIntent != null) {
            context.startActivity(launchIntent)
        } else {
            Log.i(mTag, "App {$packageName} is not installed")
        }
    }

    /**
     * 清楚卡片的历史数据信息
     */
    public fun clearHistoryWidgetData(context: Context) {
        context.getSharedPreferences(Constants.SHARE_WIDGET_INFO, AppCompatActivity.MODE_PRIVATE)
            .edit()
            .clear()
            .commit()
    }

    /**
     * 通过Worker去获取最新健康数据
     */
    public fun loadHealthStatus(context: Context){
//        //定义回调广播
//        val workManager = WorkManager.getInstance(context!!)
//        // 创建WorkRequest
//        val hmsHealthDataWorker = OneTimeWorkRequest.Builder(WidgetHmsDataWorker::class.java).build()
//        // 调度WorkRequest
//        workManager.enqueue(hmsHealthDataWorker)

        GlobalScope.launch(Dispatchers.IO) {
            var worker = HmsDataWorker(context)
            worker.doWork()
        }
    }

}
