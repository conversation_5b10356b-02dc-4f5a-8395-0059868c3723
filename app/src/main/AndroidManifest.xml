<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingLeanbackLauncher">

    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" />
    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="com.gwm.permission.SUPPORT_VR_SPEAK" />
    <uses-permission android:name="com.gwm.android.adapter.permission.SET_DATA" />
    <uses-permission android:name="com.gwm.android.adapter.permission.GET_DATA" />
    <uses-permission android:name="com.gwm.media.permission.SERVER_SET_DATA" />
    <uses-permission android:name="com.gwm.media.permission.SERVER_GET_DATA" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CAPTURE_AUDIO_OUTPUT" />

    <queries>
        <intent>
            <action android:name="android.intent.action.TTS_SERVICE" />
        </intent>
    </queries>

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />

    <application
        android:name=".application.HmsApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_config"
        android:supportsRtl="true"
        android:theme="@style/Theme.HMS">
        <activity
            android:name=".activity.HMSUpgradeDialogActivity"
            android:theme="@style/TransparentDialogActivity"
            android:exported="false" />
        <activity
            android:name=".activity.DialogTransparentActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/TransparentDialogActivity"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".activity.BindPhoneCallDoctorActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/ActivityTranslucent" />

        <activity
            android:name=".activity.PlaylistActivity"
            android:screenOrientation="landscape"
            android:theme="@style/Theme.HMS" />
        <activity
            android:name=".activity.VideoPlayerActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:screenOrientation="landscape"
            android:theme="@style/Theme.HMS" />
        <activity
            android:name="com.healthlink.hms.business.car_seat.ui.spinecare.SpineCareActivity"
            android:exported="true"
            android:theme="@style/Theme.HMS"
            android:screenOrientation="landscape" />

        <activity
            android:name=".activity.AromatherapyActivity"
            android:exported="true"
            android:theme="@style/Theme.HMS"
            android:screenOrientation="landscape" />

        <activity
            android:name=".activity.card.HMSCardEmotionalIndexDetailActivity"
            android:exported="true"
            android:theme="@style/Theme.HMS"/>

        <meta-data
            android:name="show_in_display"
            android:value="central_screen" />
        <meta-data
            android:name="VERSIONNAME"
            android:value="1.0.0" />
        <meta-data
            android:name="design_width_in_dp"
            android:value="1920" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="1128" />

        <activity
            android:name=".activity.BlueToothManagerActivity"
            android:exported="true" />
        <activity
            android:name=".activity.SplashActivity"
            android:configChanges="uiMode|locale|layoutDirection"
            android:exported="true"
            android:theme="@style/SplashUITheme"
            >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activity.HuaweiOAuthActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/TransparentDialogActivity" />
        <activity
            android:name=".activity.MainActivity"
            android:exported="true"
            android:label="@string/title_activity_main"
            android:launchMode="singleTask"
            android:theme="@style/Theme.HMS" />
        <activity
            android:name=".activity.WeekReportActivity"
            android:exported="true"
            android:theme="@style/Theme.HMS" />
        <activity
            android:name=".activity.HMSBaseCardDetailActivity"
            android:exported="true"
            android:theme="@style/Theme.HMS" />
        <activity
            android:name=".activity.card.HMSCardHeartRateDetailActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.HMS" />
        <activity
            android:name=".activity.card.HMSCardSleepDetailActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.HMS" />
        <activity
            android:name=".activity.card.HMSCardPressureDetailActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.HMS" />
        <activity
            android:name=".activity.card.HMSCardSpO2DetailActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.HMS" />
        <activity
            android:name=".activity.card.HMSCardTempDetailActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.HMS" />
        <activity
            android:name=".activity.card.HMSCardBloodPressureDetailActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.HMS" />
        <activity
            android:name=".activity.HealthReportActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.HMS" />
        <activity
            android:name=".activity.HMSPersonalActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.HMS" />
        <activity
            android:name=".activity.CallActivity"
            android:theme="@style/TransparentTheme" />

        <service
            android:name=".journey.JourneyCollectService"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.FOREGROUND_SERVICE">
            <intent-filter>
                <action android:name="com.healthlink.hms.START_JOURNEY_COLLECT_SERVICE" />
            </intent-filter>
        </service>
        <service
            android:name=".journey.JourneyCollectProtectService"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.FOREGROUND_SERVICE"
            android:process=":JOURNEY_COLLECT_PROTECT_SERVICE" />
        <service
            android:name="com.cincc.siphone.core.SipCoreService"
            android:label="CinSipCoreService"
            android:stopWithTask="false" />
        <service
            android:name=".business.doctorcall.DoctorCallService"
            android:foregroundServiceType="microphone"
            tools:ignore="ForegroundServicePermission"
            android:enabled="true"
            android:exported="false" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <receiver
            android:name=".widget.HMSWidgetProvider"
            android:label="数字健康"
            android:exported="true">
            <intent-filter>

                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="com.healthlink.hms.widgetapp.CLICK" />
                <action android:name="com.healthlink.hms.widgetapp.HEALTH_STATUS_UPDATE" />
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/app_widget" />

            <meta-data
                android:name="appwidget_show_condition"
                android:value="central_screen" />
        </receiver>

        <receiver
            android:name=".widget.HMSWidgetDoctorCallProvider"
            android:label="电话医生"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="com.healthlink.hms.widgetapp.doctor.CLICK" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/app_widget_doctor" />

            <meta-data
                android:name="appwidget_show_condition"
                android:value="central_screen" />
        </receiver>

        <receiver
            android:name=".reciever.BootCompleteReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".reciever.StrExitReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.gwm.action.str.exit" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".reciever.HMSBroadcastReciever"
            android:exported="true">
            <intent-filter>
                <action android:name="com.hl.hms.action.OPEN_SEAT_WAIST_DIRECTION_MODE" />
                <action android:name="com.hl.hms.action.OPEN_SEAT_WAIST_DIRECTION_MODE_CANCEL" />
                <action android:name="com.hl.hms.action.OPEN_SEAT_VENTILATION_MODE" />
                <action android:name="com.hl.hms.action.OPEN_SEAT_VENTILATION_MODE_CANCEL" />
                <action android:name="com.hl.hms.action.OPEN_CALL_DOCTOR_NOTIFICATION" />
                <action android:name="com.hl.hms.action.CLOSE_CALL_DOCTOR_NOTIFICATION" />
                <action android:name="com.hl.hms.action.OPEN_RELAX_MODE" />
                <action android:name="com.hl.hms.action.CLOSE_RELAX_MODE" />
                <action android:name="com.hl.hms.action.OPEN_NAVIGATION_SEARCH_SERVICE" />
                <action android:name="com.hl.hms.action.CLOSE_NAVIGATION_SEARCH_SERVICE" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".reciever.HMSWidgetUpdateDataReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.healthlink.hms.action.ACTION_HMS_WIDGET_UPDATE_DATE" />
                <action android:name="com.healthlink.hms.action.ACTION_HMS_USER_STATUS_UPDATE" />
            </intent-filter>
        </receiver>
    </application>

</manifest>