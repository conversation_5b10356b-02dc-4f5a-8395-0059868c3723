<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:visibility="visible"
    android:id="@+id/card_no_data"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:showIn="@layout/card_blood_pressure">

    <ImageView
        style="@style/hms_card_no_data_image"
        android:src="@drawable/card_icon_blood_pressure_default"
        android:contentDescription="@string/card_bg_blood_oxygen_no_data" />

    <LinearLayout
        style="@style/hms_card_container_bound"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                style="@style/hms_card_icon"
                android:id="@+id/icon_no_data"
                android:src="@drawable/card_icon_blood_pressure" />

            <TextView
                style="@style/hms_card_title"
                android:layout_toEndOf="@+id/icon_no_data"
                android:text="血压" />
        </RelativeLayout>

        <TextView
            style="@style/hms_card_no_data_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="血压检测 血压分析" />

    </LinearLayout>

</FrameLayout>