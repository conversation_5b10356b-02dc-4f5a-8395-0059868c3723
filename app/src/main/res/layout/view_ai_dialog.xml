<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:gravity="center_vertical"
    android:layout_height="match_parent">

    <cn.enjoytoday.shadow.ShadowLayout
        android:orientation="vertical"
        android:id="@+id/shadowLayout"
        android:gravity="center"
        app:shadowRadius="16dp"
        app:shadowColor="#26000000"
        app:bgColor="#E8F2FD"
        android:background="#E8F2FD"
        app:xOffset="0dp"
        app:yOffset="0dp"
        app:blurRadius="40dp"
        android:layout_marginHorizontal="12dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:layout_width="560dp"
            android:layout_height="876dp"
            android:gravity="center_horizontal"
            android:layout_gravity="top"
            android:elevation="3dp"
            android:background="@drawable/bg_ai_chat"
            android:orientation="vertical">
<!--header-->
            <FrameLayout
                android:orientation="horizontal"
                android:id="@+id/ll_ai_chat_view_header"
                android:layout_width="match_parent"
                android:layout_height="100dp">
                <ImageView
                    android:id="@+id/iv_close"
                    android:layout_gravity="center_vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:src="@drawable/icon_close"
                    />

                <ImageView
                    android:layout_gravity="center_vertical|center_horizontal"
                    android:id="@+id/iv_ai_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="100dp"
                    android:src="@drawable/icon_ai"
                    />
            </FrameLayout>
<!--content-->
            <FrameLayout
                android:id="@+id/ll_ai_chat_view_content"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:layout_marginStart="@dimen/space_24_dp"
                android:layout_marginEnd="@dimen/space_24_dp">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <ImageView
                        android:id="@+id/iv_ai_chat"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/img_ai_chat"
                        android:scaleType="fitStart"
                        />

                </LinearLayout>
            </FrameLayout>

<!-- 底部输入区域 -->
            <LinearLayout
                android:id="@+id/ll_input_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginHorizontal="25dp"
                android:layout_marginTop="14dp"
                android:layout_marginBottom="25dp"
                android:gravity="center_vertical">
                <FrameLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    >
                    <!-- 输入框 -->
                    <EditText
                        android:id="@+id/et_message_input"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_ai_chat_input_field"
                        android:hint="请输入问题关键字"
                        android:padding="12dp"
                        android:textSize="20sp"
                        android:maxLines="3"
                        android:inputType="textMultiLine" />
                    <!-- 发送按钮 -->
                    <ImageView
                        android:id="@+id/btn_send"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginEnd="9dp"
                        android:layout_gravity="end|center_vertical"
                        android:src="@drawable/icon_send_message"/>
                </FrameLayout>

                <!-- 更多选项按钮 -->
                <ImageView
                    android:id="@+id/btn_more_options"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="14dp"
                    android:background="@android:color/transparent"
                    android:src="@drawable/icon_more_options"/>
            </LinearLayout>
        </LinearLayout>
    </cn.enjoytoday.shadow.ShadowLayout>
</FrameLayout>

