<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_health_tips"
    android:layout_width="416dp"
    android:layout_height="152dp"
    tools:showIn="@layout/fragment_service_list">

    <include layout="@layout/include_bg_card_blur"/>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:layout_marginTop="23dp"
            android:lineHeight="0dp"
            android:text="@string/title_health_tips"
            android:textColor="@color/text_color_333"
            android:textSize="22sp" />

        <ImageView
            android:id="@+id/iv_health_play_icon"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginTop="23dp"
            android:layout_marginEnd="30dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:src="@drawable/ic_volumn_03" />

        <TextView
            android:id="@+id/tv_health_tips_title"
            android:layout_marginTop="14dp"
            android:layout_below="@+id/tv_title"
            android:layout_alignStart="@+id/tv_title"
            android:layout_alignEnd="@+id/iv_health_play_icon"
            android:layout_alignParentBottom="true"
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tip_title_default"
            android:textColor="@color/text_color_666"
            android:textSize="20sp" />
    </RelativeLayout>

</FrameLayout>