<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/main_data_card_width"
    android:layout_height="@dimen/main_data_card_height"
    android:orientation="vertical">
    <!--    高斯模糊背景-->
    <include layout="@layout/include_bg_card_blur" />

    <!--    卡片正常数据界面-->
    <FrameLayout
        android:visibility="gone"
        android:id="@+id/card_normal_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <View
            android:id="@+id/iv_bg_card_exception"
            style="@style/car_exception_style"
            android:visibility="gone"/>

        <LinearLayout
            style="@style/hms_card_container_bound"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            >
            <ImageView
                style="@style/hms_card_icon"
                android:id="@+id/icon"
                android:src="@drawable/card_icon_sleep"
                />

            <TextView
                style="@style/hms_card_title"
                android:layout_toEndOf="@+id/icon"
                android:text="睡眠"/>

            <TextView
                style="@style/hms_card_title_status"
                android:id="@+id/tv_main_body_sleep_status"
                android:text="较好"/>
        </RelativeLayout>
        <LinearLayout
            style="@style/hms_card_value_container">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >
            <TextView
                android:id="@+id/tv_main_body_sleep_hour"
                style="@style/hms_data_card_main_data"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="--"
                />

            <TextView
                android:id="@+id/tv_main_body_sleep_hour_unit"
                style="@style/hms_data_car_data_unit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="小时"
                android:layout_toEndOf="@+id/tv_main_body_sleep_hour"
                android:layout_alignBaseline="@+id/tv_main_body_sleep_hour"
                />

            <TextView
                style="@style/hms_data_card_main_data"
                android:id="@+id/tv_main_body_sleep_minute"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="--"
                android:layout_toEndOf="@+id/tv_main_body_sleep_hour_unit"
                android:layout_alignBaseline="@+id/tv_main_body_sleep_hour_unit"

                />

            <TextView
                style="@style/hms_data_car_data_unit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="分钟"
                android:layout_toEndOf="@+id/tv_main_body_sleep_minute"
                android:layout_alignBaseline="@+id/tv_main_body_sleep_minute"
                />
            </RelativeLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="14dp"
            android:orientation="vertical"
            >
            <com.healthlink.hms.views.SegmentedBarView.SegmentedBarView
                android:visibility="visible"
                android:id="@+id/iv_sleeping_chart"
                android:layout_width="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_height="64dp"
                android:elevation="10dp"
                android:translationZ="10dp"
                app:sbv_bar_height="7dp"
                app:sbv_empty_segment_text="No segments"
                app:sbv_segment_gap_width="2dp"
                app:sbv_segment_text_size="0sp"
                app:sbv_show_segment_text="false"
                app:sbv_show_description_text="true"
                app:sbv_side_style="normal"
                app:sbv_value_sign_round="8dp"
                app:sbv_value_sign_height="10dp"
                app:sbv_value_sign_width="30dp"
                app:sbv_segment_rule="scale"
                app:sbv_segment_bg = "false"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                />
        </LinearLayout>
        </LinearLayout>
        <TextView
            style="@style/hms_card_last_update_text"
            android:id="@+id/tv_data_year_or_day"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:text="@string/card_last_update_time_str"
            />
    </FrameLayout>

    <!--    卡片无数据界面-->
    <FrameLayout
        android:visibility="visible"
        android:id="@+id/card_no_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            style="@style/hms_card_no_data_image"
            android:src="@drawable/card_icon_sleep_default"
            android:contentDescription="@string/card_bg_sleep_no_data" />

        <LinearLayout
            style="@style/hms_card_container_bound"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >
                <ImageView
                    style="@style/hms_card_icon"
                    android:id="@+id/icon_no_data"
                    android:src="@drawable/card_icon_sleep"
                    />

                <TextView
                    style="@style/hms_card_title"
                    android:layout_toEndOf="@+id/icon_no_data"
                    android:text="睡眠" />
            </RelativeLayout>

            <TextView
                style="@style/hms_card_no_data_desc"
                android:text="疏压减压 监测睡眠"/>

        </LinearLayout>

    </FrameLayout>

    <!--    卡片私密模式界面-->
    <FrameLayout
        android:visibility="gone"
        android:id="@+id/card_privacy_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            style="@style/hms_card_container_bound"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >
                <TextView
                    style="@style/hms_card_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:gravity="center_vertical"
                    android:text="睡眠"/>

            </RelativeLayout>
            <LinearLayout  android:layout_marginTop="20dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    >
                    <TextView
                        android:id="@+id/tv_main_body_sleep_hour_privacy"
                        style="@style/hms_data_card_main_data_privacy"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="***"
                        />

                    <TextView
                        android:id="@+id/tv_main_body_sleep_hour_unit_privacy"
                        style="@style/hms_data_car_data_unit_privacy"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="小时"
                        android:layout_toEndOf="@+id/tv_main_body_sleep_hour_privacy"
                        />

                    <TextView
                        style="@style/hms_data_card_main_data_privacy"
                        android:id="@+id/tv_main_body_sleep_minute_privacy"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="***"
                        android:layout_toEndOf="@+id/tv_main_body_sleep_hour_unit_privacy"
                        />

                    <TextView
                        style="@style/hms_data_car_data_unit_privacy"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="分钟"
                        android:layout_toEndOf="@+id/tv_main_body_sleep_minute_privacy"
                        />
                </RelativeLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="20dp"
                android:orientation="vertical"
                >
                <com.healthlink.hms.views.SegmentedBarView.SegmentedBarView
                    android:visibility="visible"
                    android:id="@+id/iv_sleeping_chart_privacy"
                    android:layout_width="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_height="64dp"
                    android:elevation="10dp"
                    android:translationZ="10dp"
                    app:sbv_bar_height="7dp"
                    app:sbv_empty_segment_text="No segments"
                    app:sbv_segment_gap_width="2dp"
                    app:sbv_segment_text_size="0sp"
                    app:sbv_show_segment_text="false"
                    app:sbv_show_description_text="true"
                    app:sbv_side_style="normal"
                    app:sbv_value_sign_round="8dp"
                    app:sbv_value_sign_height="10dp"
                    app:sbv_value_sign_width="30dp"
                    app:sbv_segment_rule="scale"
                    app:sbv_segment_bg = "false"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    />
            </LinearLayout>
        </LinearLayout>
    </FrameLayout>
</FrameLayout>