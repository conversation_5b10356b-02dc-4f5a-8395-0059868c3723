<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <data>
        <import type="android.view.View"/>
        <import type="com.healthlink.hms.utils.TimeUtils"/>
        <variable
            name="baseFragment"
            type="com.healthlink.hms.fragment.BaseCardFragment" />
        <variable
            name="cardShowInfo"
            type="com.healthlink.hms.server.data.dto.BloodPressureCard1DTO" />
        <variable
            name="cardShowInfo2"
            type="com.healthlink.hms.server.data.dto.BloodPressureCard2DTO" />
        <variable
            name="healthBloodpressureSummeryVo"
            type="com.healthlink.hms.server.data.dto.HealthBloodpressureSummaryDTO" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout

        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >
        <com.healthlink.hms.views.CustomBarStretchScrollView
            android:visibility="invisible"
            android:id="@+id/sv_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="10dp"
            android:scrollbarThumbVertical="@drawable/scrollbar_ver_thumb"
            android:scrollbars="none"
            >
            <LinearLayout
                style="@style/fragment_card_container_style">
                >
                <!--    图表容器 -->
                <!--    图表解释容器 -->
                <LinearLayout
                    android:id="@+id/sleep_chart_container"
                    android:layout_width="match_parent"
                    android:layout_height="420dp"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toTopOf="@+id/ll_intro_container"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">


                    <com.healthlink.hms.views.charts.BloodPressureChart
                        android:id="@+id/c_temp_wmy"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:visibility="gone"
                        />
                    <TextView
                        android:id="@+id/temp_no_data_text"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text=""
                        android:textSize="26sp"
                        android:textAlignment="center"
                        android:gravity="center"
                        android:visibility="gone"
                        android:textColor="@color/text_color_fc_80"
                        />

                    <TextView
                        android:id="@+id/s_privacy_text"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="系统已开启私密模式"
                        android:textAlignment="center"
                        android:textColor="@color/text_color_fc_80"
                        android:textSize="26sp"
                        android:visibility="gone" />


                </LinearLayout>
                <!--        体温颜色图例-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="horizontal">
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        >
                        <com.healthlink.hms.views.HMSCircleView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            app:circleColor="@color/high_pressure_100"
                            />
                        <TextView
                            android:layout_marginStart="8dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="22sp"
                            android:layout_gravity="center"
                            android:textColor="@color/text_color_fc_80"
                            android:text="高压（收缩压）"/>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_marginStart="200dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        >
                        <com.healthlink.hms.views.HMSCircleView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            app:circleColor="@color/low_pressure_100"/>
                        <TextView
                            android:layout_marginStart="8dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="22sp"
                            android:layout_gravity="center"
                            android:textColor="@color/text_color_fc_80"
                            android:text="低压（舒张压）"/>
                    </LinearLayout>
                </LinearLayout>
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ll_intro_container"
                    android:layout_marginTop="40dp"
                    style="@style/fragment_card_metrics_intro_style"
                    >
                    <LinearLayout
                        android:id="@+id/sleep_day_data_center"
                        android:orientation="vertical"
                        style="@style/card_3_style"
                        android:gravity="center"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/sleep_score_layout"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        >
                        <TextView
                            android:id="@+id/tv_sleep_date"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{cardShowInfo.fetchTime}"
                            android:textColor="@color/text_color_fc_60"
                            android:lineHeight="42dp"
                            android:textSize="@dimen/card_title_text_size" />
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="24dp"
                            android:orientation="horizontal"
                            android:gravity="center"
                            >
                                <TextView
                                    android:id="@+id/tv_pre_blood_pressure"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text=""
                                    android:textColor="@color/text_color_fc_60"
                                    android:textSize="24sp" />

                                <TextView
                                    android:id="@+id/sleep_hour_value"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_color_fc_80"
                                    android:textSize="@dimen/card_content_value_text_size"
                                    android:textStyle="bold"
                                    android:text="@{cardShowInfo.currentString != null ? cardShowInfo.currentString : `--/--`}"
                                    />


                                <TextView
                                    android:id="@+id/sleep_hour_unit"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_color_fc_60"
                                    android:textSize="24sp"
                                    android:text="@string/unit_blood_pressure"
                                    />
                        </LinearLayout>
                    </LinearLayout>
                    <LinearLayout
                        android:id="@+id/sleep_score_layout"
                        android:orientation="vertical"
                        style="@style/card_3_style"
                        android:gravity="center"
                        app:layout_constraintStart_toEndOf="@+id/sleep_day_data_center"
                        app:layout_constraintEnd_toStartOf="@+id/lo_bl_count"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintHorizontal_bias="0.5">
                        <TextView
                            android:id="@+id/tv_sleep_intro_range"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_color_fc_60"
                            android:textSize="@dimen/card_title_text_size"
                            android:lineHeight="42dp"
                            android:text="血压平均值"
                            />
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="24dp"
                            android:orientation="horizontal"
                            android:gravity="center"
                            >
                                <TextView
                                    android:id="@+id/tv_spo2_min_value"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_color_fc_80"
                                    android:textSize="@dimen/card_content_value_text_size"
                                    android:textStyle="bold"
                                    android:text="--/--"
                                    />

                                <TextView
                                    android:id="@+id/tv_spo2_min_unit"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_color_fc_60"
                                    android:textSize="24sp"
                                    android:text="@string/unit_blood_pressure"
                                    />
                        </LinearLayout>
                    </LinearLayout>
                    <LinearLayout
                        android:id="@+id/lo_bl_count"
                        android:orientation="vertical"
                        style="@style/card_3_style"
                        android:gravity="center"
                        app:layout_constraintStart_toEndOf="@+id/sleep_score_layout"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        >
                        <FrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">
                            <LinearLayout
                                android:orientation="vertical"
                                android:layout_width="match_parent"
                                android:gravity="center_vertical"
                                android:layout_marginStart="88dp"
                                android:layout_height="match_parent">
                                <LinearLayout
                                    android:id="@+id/lo_normal_per"
                                    android:orientation="horizontal"
                                    android:layout_width="match_parent"
                                    android:gravity="end|bottom"
                                    android:layout_weight="1"
                                    android:layout_height="0dp">
                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="0.5"
                                        android:layout_marginEnd="20dp"
                                        android:textColor="@color/text_color_fc_60"
                                        android:gravity="end"
                                        android:textSize="22sp"
                                        android:text="正常"
                                        />
                                    <TextView
                                        android:id="@+id/tv_c41_per"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="0.5"
                                        android:textSize="22sp"
                                        android:layout_marginStart="20dp"
                                        android:textColor="@color/text_color_fc_100"
                                        android:gravity="start"
                                        android:text="@{(cardShowInfo2.normalPer!=null?cardShowInfo2.normalPer:`0`)+`%`}"
                                        />
                                </LinearLayout>
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_weight="1"
                                    android:layout_height="0dp"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">
                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_weight="0.5"
                                        android:layout_height="wrap_content"
                                        android:layout_marginEnd="20dp"
                                        android:gravity="end"
                                        android:text="疑似正常高值"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="22sp" />

                                    <TextView
                                        android:id="@+id/tv_c42_per"
                                        android:layout_width="0dp"
                                        android:layout_weight="0.5"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="20dp"
                                        android:gravity="start"
                                        android:textSize="22sp"
                                        android:textColor="@color/text_color_fc_100"
                                        android:text="@{(cardShowInfo2.normalHighProportion!=null?cardShowInfo2.normalHighProportion:`0`)+`%`}"/>
                                </LinearLayout>
                                <LinearLayout
                                    android:id="@+id/lo_bl_low"
                                    android:layout_width="match_parent"
                                    android:layout_weight="1"
                                    android:layout_height="0dp"
                                    android:gravity="start"
                                    android:orientation="horizontal">
                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_weight="0.5"
                                        android:layout_height="wrap_content"
                                        android:layout_marginEnd="20dp"
                                        android:gravity="end"
                                        android:text="疑似低血压"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="22sp" />

                                    <TextView
                                        android:id="@+id/tv_c43_per"
                                        android:layout_width="0dp"
                                        android:layout_weight="0.5"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="20dp"
                                        android:gravity="start"
                                        android:textSize="22sp"
                                        android:textColor="@color/text_color_fc_100"
                                        android:text="@{(cardShowInfo2.lowProportion!=null?cardShowInfo2.lowProportion:`0`)+`%`}"/>
                                </LinearLayout>
                                <LinearLayout
                                    android:id="@+id/lo_high_bp_l1"
                                    android:layout_width="match_parent"
                                    android:layout_weight="1"
                                    android:layout_height="0dp"
                                    android:orientation="horizontal"
                                    android:gravity="start"
                                    android:visibility="gone">
                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_weight="0.5"
                                        android:layout_height="wrap_content"
                                        android:layout_marginEnd="20dp"
                                        android:gravity="end"
                                        android:text="一级高血压"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="22sp" />

                                    <TextView
                                        android:id="@+id/tv_c44_per"
                                        android:layout_width="0dp"
                                        android:layout_weight="0.5"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="20dp"
                                        android:gravity="start"
                                        android:textSize="22sp"
                                        android:textColor="@color/text_color_fc_100"
                                        android:text="@{(cardShowInfo2.primaryProportion!=null?cardShowInfo2.primaryProportion:`0`)+`%`}"/>
                                </LinearLayout>
                                <LinearLayout
                                    android:id="@+id/lo_high_bp_l2"
                                    android:layout_width="match_parent"
                                    android:layout_weight="1"
                                    android:layout_height="0dp"
                                    android:orientation="horizontal"
                                    android:gravity="start"
                                    android:visibility="gone">
                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_weight="0.5"
                                        android:layout_height="wrap_content"
                                        android:layout_marginEnd="20dp"
                                        android:gravity="end"
                                        android:text="二级高血压"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="22sp" />

                                    <TextView
                                        android:id="@+id/tv_c45_per"
                                        android:layout_width="0dp"
                                        android:layout_weight="0.5"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="20dp"
                                        android:gravity="start"
                                        android:textSize="22sp"
                                        android:textColor="@color/text_color_fc_100"
                                        android:text="@{(cardShowInfo2.secondaryProportion!=null?cardShowInfo2.secondaryProportion:`0`)+`%`}"/>
                                </LinearLayout>
                                <LinearLayout
                                    android:id="@+id/lo_high_bp_l3"
                                    android:layout_width="match_parent"
                                    android:layout_weight="1"
                                    android:layout_height="0dp"
                                    android:orientation="horizontal"
                                    android:gravity="start"
                                    android:visibility="gone">
                                    <TextView
                                        android:layout_width="0dp"
                                        android:layout_weight="0.5"
                                        android:layout_height="wrap_content"
                                        android:layout_marginEnd="20dp"
                                        android:gravity="end"
                                        android:text="三级高血压"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="22sp" />

                                    <TextView
                                        android:id="@+id/tv_c46_per"
                                        android:layout_width="0dp"
                                        android:layout_weight="0.5"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="20dp"
                                        android:gravity="start"
                                        android:textSize="22sp"
                                        android:textColor="@color/text_color_fc_100"
                                        android:text="@{(cardShowInfo2.tertiaryHighProportion!=null?cardShowInfo2.tertiaryHighProportion:`0`)+`%`}"/>
                                </LinearLayout>
                            </LinearLayout>

                            <com.gwm.widget.GwmButton
                                app:btnStyle="flat_forward"
                                app:iconSize="32dp"
                                android:id="@+id/iv_intro_tips"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="top|end"
                                android:layout_marginTop="24dp"
                                android:layout_marginEnd="24dp"
                                app:icon="@drawable/ic_introduce_circle"
                                android:visibility="visible" />
                        </FrameLayout>

                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!--风险建议提示容器-->
                <include
                    android:visibility="@{baseFragment.Companion.isHealthAdviceVisible(healthBloodpressureSummeryVo) == true ? View.VISIBLE : View.GONE}"
                    layout="@layout/layout_card_detail_risk_advice"
                    android:id="@+id/health_risk_all"
                    app:baseDTO="@{healthBloodpressureSummeryVo}"/>

                <include layout="@layout/fragment_card_footer" />
            </LinearLayout>

        </com.healthlink.hms.views.CustomBarStretchScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>