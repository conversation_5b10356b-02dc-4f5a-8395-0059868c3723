<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/main_data_card_width"
    android:layout_height="@dimen/main_data_card_height"
    android:orientation="vertical">

    <!--    高斯模糊背景-->
    <include layout="@layout/include_bg_card_blur" />

    <!--    卡片正常数据界面-->
    <FrameLayout
        android:visibility="gone"
        android:id="@+id/card_normal_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <!--异常卡片背景-->
        <View
            android:id="@+id/iv_bg_card_exception"
            style="@style/car_exception_style"
            android:visibility="gone"/>

        <LinearLayout
            style="@style/hms_card_container_bound"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                style="@style/hms_card_icon"
                android:id="@+id/icon"
                android:src="@drawable/card_icon_pressure"
                />

            <TextView
                style="@style/hms_card_title"
                android:layout_toEndOf="@+id/icon"
                android:text="压力" />

            <TextView
                style="@style/hms_card_title_status"
                android:id="@+id/tv_main_body_pressure_status"
                android:background="@drawable/health_index_status_nice_bg_fill"
                android:gravity="center"
                android:text="正常" />
        </RelativeLayout>

        <LinearLayout
            style="@style/hms_card_value_container">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >
            <TextView
                android:id="@+id/tv_main_body_pressure"
                style="@style/hms_data_card_main_data"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="--"/>

            <TextView
                style="@style/hms_data_car_data_unit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toEndOf="@+id/tv_main_body_pressure"
                android:layout_alignBaseline="@+id/tv_main_body_pressure"
                android:text="中等"
                android:visibility="gone"/>
            </RelativeLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="70dp"
            android:layout_marginTop="0dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@mipmap/img_pressure_line_data"
                android:visibility="gone"/>

            <com.github.mikephil.charting.charts.BarChart
                android:id="@+id/chart_pressure_bar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="visible" />

        </LinearLayout>
        </LinearLayout>
        <TextView
            style="@style/hms_card_last_update_text"
            android:id="@+id/tv_data_year_or_day"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:text="2024-05-11"
            />
    </FrameLayout>

    <!--    卡片无数据界面-->
    <FrameLayout
        android:visibility="visible"
        android:id="@+id/card_no_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            style="@style/hms_card_no_data_image"
            android:src="@drawable/card_icon_pressure_default"
            android:contentDescription="@string/card_bg_pressure_no_data" />

        <LinearLayout
            style="@style/hms_card_container_bound"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >
                <ImageView
                    style="@style/hms_card_icon"
                    android:id="@+id/icon_no_data"
                    android:src="@drawable/card_icon_pressure"
                    />

                <TextView
                    style="@style/hms_card_title"
                    android:layout_toEndOf="@+id/icon_no_data"
                    android:text="压力" />
            </RelativeLayout>

            <TextView
                style="@style/hms_card_no_data_desc"
                android:text="压力检测 减压放松"/>

        </LinearLayout>

    </FrameLayout>

    <!--    卡片私密模式界面-->
    <FrameLayout
        android:visibility="gone"
        android:id="@+id/card_privacy_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            style="@style/hms_card_container_bound"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    style="@style/hms_card_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:gravity="center_vertical"
                    android:text="压力" />
            </RelativeLayout>

            <LinearLayout
                style="@style/hms_card_value_container_secrect_mode"
                >
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    >
                    <TextView
                        android:id="@+id/tv_main_body_pressure_privacy"
                        style="@style/hms_data_card_main_data_privacy"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="***"/>
                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:layout_marginTop="39dp"
                android:orientation="horizontal">
                <com.github.mikephil.charting.charts.BarChart
                    android:id="@+id/chart_pressure_bar_privacy"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="visible" />
            </LinearLayout>
        </LinearLayout>
    </FrameLayout>
    </FrameLayout>
