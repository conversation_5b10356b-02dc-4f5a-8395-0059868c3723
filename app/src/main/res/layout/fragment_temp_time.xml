<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <data>
        <import type="android.view.View"/>
        <import type="com.healthlink.hms.utils.TimeUtils"/>
        <variable
            name="baseFragment"
            type="com.healthlink.hms.fragment.BaseCardFragment" />
        <variable
            name="cardShowInfo"
            type="com.healthlink.hms.server.data.dto.TempCard1DTO" />
        <variable
            name="tempRangeDTO"
            type="com.healthlink.hms.server.data.dto.TempCard1DTO" />
        <variable
            name="cardShowInfo2"
            type="com.healthlink.hms.server.data.dto.TempCard2DTO" />
        <variable
            name="healthSpO2SummeryVo"
            type="com.healthlink.hms.server.data.dto.HealthTempSummaryDTO" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout

        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingBottom="10dp"
        >
        <com.healthlink.hms.views.CustomBarStretchScrollView
            android:visibility="invisible"
            android:id="@+id/sv_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="10dp"
            android:scrollbars="none"
            >
            <LinearLayout
                style="@style/fragment_card_container_style">
                <!--    图表容器 -->
                <!--    图表解释容器 -->
                <LinearLayout
                    android:id="@+id/sleep_chart_container"
                    android:layout_width="match_parent"
                    android:layout_height="420dp"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toTopOf="@+id/ll_intro_container"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">


                    <com.healthlink.hms.views.charts.TempBarChart
                        android:id="@+id/c_temp_wmy"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:visibility="gone"
                        />
                    <TextView
                        android:id="@+id/temp_no_data_text"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:text="暂无体温数据"
                        android:textSize="26sp"
                        android:textAlignment="center"
                        android:gravity="center"
                        android:visibility="gone"
                        android:textColor="@color/text_color_fc_80"
                        />
                    <TextView
                        android:id="@+id/s_privacy_text"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="系统已开启私密模式"
                        android:textAlignment="center"
                        android:textColor="@color/text_color_fc_80"
                        android:textSize="26sp"
                        android:visibility="gone" />

                </LinearLayout>
                <!--        体温颜色图例-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="horizontal">
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        >
                        <com.healthlink.hms.views.HMSCircleView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            app:circleColor="@color/bodytemp_normal"
                            />
                        <TextView
                            android:layout_marginStart="8dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="22sp"
                            android:layout_gravity="center"
                            android:textColor="@color/text_color_fc_80"
                            android:text="&lt;37.3℃"/>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_marginStart="200dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        >
                        <com.healthlink.hms.views.HMSCircleView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            app:circleColor="@color/bodytemp_low_grade_fever"/>
                        <TextView
                            android:layout_marginStart="8dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="22sp"
                            android:layout_gravity="center"
                            android:textColor="@color/text_color_fc_80"
                            android:text="37.3℃-38℃"/>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_marginStart="200dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        >
                        <com.healthlink.hms.views.HMSCircleView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            app:circleColor="@color/bodytemp_high_ferver"
                            />
                        <TextView
                            android:layout_marginStart="8dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="22sp"
                            android:layout_gravity="center"
                            android:textColor="@color/text_color_fc_80"
                            android:text="≥38.1℃"/>
                    </LinearLayout>

                </LinearLayout>
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ll_intro_container"
                    android:layout_marginTop="40dp"
                    style="@style/fragment_card_metrics_intro_style"
                    >
                    <LinearLayout
                        android:id="@+id/sleep_day_data_center"
                        android:orientation="vertical"
                        style="@style/card_3_style"
                        android:gravity="center"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/sleep_score_layout"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        >
                        <TextView
                            android:id="@+id/tv_sleep_date"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_color_fc_60"
                            android:lineHeight="42dp"
                            android:textSize="@dimen/card_title_text_size"
                            android:text="@{cardShowInfo.fetchTime}"
                            />
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="24dp"
                            android:orientation="horizontal"
                            android:gravity="center"
                            >
                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                >
                                <TextView
                                    android:id="@+id/sleep_hour_value"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_color_fc_80"
                                    android:textSize="@dimen/card_content_value_text_size"
                                    android:textStyle="bold"
                                    android:text="@{cardShowInfo.minMax !=null?cardShowInfo.minMax:`--`}"
                                    />
                                <TextView
                                    android:id="@+id/privacy_placeholder_c11"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_color_fc_80"
                                    android:textSize="@dimen/card_content_value_text_size"
                                    android:textStyle="bold"
                                    android:layout_toRightOf="@+id/sleep_hour_value"
                                    android:text="-"
                                    android:visibility="gone"
                                    />
                                <TextView
                                    android:id="@+id/privacy_placeholder_c12"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_color_fc_80"
                                    android:textSize="@dimen/card_content_value_text_size"
                                    android:textStyle="bold"
                                    android:layout_toRightOf="@+id/privacy_placeholder_c11"
                                    android:visibility="gone"
                                    android:text=""
                                    />

                                <TextView
                                    android:id="@+id/sleep_hour_unit"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_color_fc_60"
                                    android:textSize="24sp"
                                    android:layout_toRightOf="@+id/privacy_placeholder_c12"
                                    android:layout_alignBaseline="@+id/sleep_hour_value"
                                    android:text="℃"
                                    android:visibility="visible"
                                    />
                            </RelativeLayout>
                        </LinearLayout>
                    </LinearLayout>
                    <LinearLayout
                        android:id="@+id/sleep_score_layout"
                        android:orientation="vertical"
                        style="@style/card_3_style"
                        android:gravity="center"
                        app:layout_constraintStart_toEndOf="@+id/sleep_day_data_center"
                        app:layout_constraintEnd_toStartOf="@+id/sleep_count"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintHorizontal_bias="0.5"
                        >
                        <TextView
                            android:id="@+id/tv_sleep_intro_range"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_color_fc_60"
                            android:lineHeight="42dp"
                            android:textSize="@dimen/card_title_text_size"
                            android:text="体温范围"
                            />
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="24dp"
                            android:orientation="horizontal"
                            android:gravity="center"
                            >
                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                >
                                <TextView
                                    android:id="@+id/tv_spo2_min_value"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_color_fc_80"
                                    android:textSize="@dimen/card_content_value_text_size"
                                    android:textStyle="bold"
                                    android:text="@{tempRangeDTO.fullMinMax!=null?tempRangeDTO.fullMinMax:`--`}"
                                    />

                                <TextView
                                    android:id="@+id/privacy_placeholder_c21"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_color_fc_80"
                                    android:textSize="@dimen/card_content_value_text_size"
                                    android:textStyle="bold"
                                    android:layout_toRightOf="@+id/tv_spo2_min_value"
                                    android:text="-"
                                    android:visibility="gone"
                                    />
                                <TextView
                                    android:id="@+id/privacy_placeholder_c22"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_color_fc_80"
                                    android:textSize="@dimen/card_content_value_text_size"
                                    android:textStyle="bold"
                                    android:layout_toRightOf="@+id/privacy_placeholder_c21"
                                    android:visibility="gone"
                                    android:text=""
                                    />
                                <TextView
                                    android:id="@+id/tv_spo2_min_unit"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_color_fc_60"
                                    android:textSize="24sp"
                                    android:layout_toRightOf="@+id/privacy_placeholder_c22"
                                    android:layout_alignBaseline="@+id/tv_spo2_min_value"
                                    android:text="℃"
                                    android:visibility="visible"
                                    />
                            </RelativeLayout>
                        </LinearLayout>
                    </LinearLayout>
                    <LinearLayout
                        android:id="@+id/sleep_count"
                        android:orientation="vertical"
                        style="@style/card_3_style"
                        android:gravity="center"
                        app:layout_constraintStart_toEndOf="@+id/sleep_score_layout"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        >
                        <FrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">
                            <LinearLayout
                                android:orientation="horizontal"
                                android:layout_gravity="center_horizontal"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent">
                            <LinearLayout
                                android:orientation="vertical"
                                android:layout_gravity="center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content">
                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="42dp"
                                    android:gravity="end|center_vertical"
                                    android:textColor="@color/text_color_fc_60"
                                    android:textSize="@dimen/card_last_content_text_size"
                                    android:text="正常"
                                    />
                                <TextView
                                    android:layout_width="match_parent"
                                    android:gravity="end|center_vertical"
                                    android:layout_height="42dp"
                                    android:layout_marginTop="8dp"
                                    android:textColor="@color/text_color_fc_60"
                                    android:textSize="@dimen/card_last_content_text_size"
                                    android:text="低热"
                                    />
                                <TextView
                                    android:layout_width="wrap_content"
                                    android:gravity="end|center_vertical"
                                    android:layout_height="42dp"
                                    android:layout_marginTop="8dp"
                                    android:text="中高热"
                                    android:textColor="@color/text_color_fc_60"
                                    android:textSize="@dimen/card_last_content_text_size" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_marginStart="40dp"
                                android:orientation="vertical"
                                android:gravity="start"
                                android:layout_gravity="center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content">
                                <TextView
                                    android:id="@+id/tv_normal_per"
                                    android:layout_width="wrap_content"
                                    android:gravity="start|center_vertical"
                                    android:layout_height="42dp"
                                    android:textSize="@dimen/card_last_content_text_size"
                                    android:textColor="@color/text_color_fc_100"
                                    android:text="@{(cardShowInfo2.normalProp!= null ? cardShowInfo2.normalProp : `0`)+`%`}" />
                                <TextView
                                    android:id="@+id/tv_low_per"
                                    android:layout_width="wrap_content"
                                    android:layout_marginTop="8dp"
                                    android:gravity="start|center_vertical"
                                    android:layout_height="42dp"
                                    android:textSize="@dimen/card_last_content_text_size"
                                    android:textColor="@color/text_color_fc_100"
                                    android:text="@{(cardShowInfo2.lowProp!= null ? cardShowInfo2.lowProp : `0`)+`%`}"
                                    />

                                <TextView
                                    android:id="@+id/tv_high_per"
                                    android:layout_width="wrap_content"
                                    android:gravity="start|center_vertical"
                                    android:layout_height="42dp"
                                    android:layout_marginTop="8dp"
                                    android:textSize="@dimen/card_last_content_text_size"
                                    android:textColor="@color/text_color_fc_100"
                                    android:text="@{(cardShowInfo2.mediumProp!= null ? cardShowInfo2.mediumProp : `0`)+`%`}"/>
                            </LinearLayout>
                            </LinearLayout>

                            <com.gwm.widget.GwmButton
                                app:btnStyle="flat_forward"
                                app:iconSize="32dp"
                                android:id="@+id/iv_intro_tips"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="top|end"
                                android:layout_marginTop="24dp"
                                android:layout_marginEnd="24dp"
                                app:icon="@drawable/ic_introduce_circle"
                                android:visibility="visible" />
                        </FrameLayout>

                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!--风险建议提示容器-->
                <include
                    android:visibility="@{baseFragment.Companion.isHealthAdviceVisible(healthSpO2SummeryVo) == true ? View.VISIBLE : View.GONE}"
                    layout="@layout/layout_card_detail_risk_advice"
                    android:id="@+id/health_risk_all"
                    app:baseDTO="@{healthSpO2SummeryVo}"/>

                <include layout="@layout/fragment_card_footer" />
            </LinearLayout>

        </com.healthlink.hms.views.CustomBarStretchScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>