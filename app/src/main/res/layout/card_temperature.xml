<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root_view"
    android:layout_width="@dimen/main_data_card_width"
    android:layout_height="@dimen/main_data_card_height"
    android:orientation="vertical">

    <!--    高斯模糊背景-->
    <include layout="@layout/include_bg_card_blur" />

    <!--    卡片正常数据界面  -->
    <FrameLayout
        android:visibility="gone"
        android:id="@+id/card_normal_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <View
            android:id="@+id/iv_bg_card_exception"
            style="@style/car_exception_style"
            android:visibility="gone"/>

        <LinearLayout
            style="@style/hms_card_container_bound"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            >
                <ImageView
                    style="@style/hms_card_icon"
                    android:id="@+id/icon"
                    android:src="@drawable/card_icon_temperature"
                    />

                <TextView
                    style="@style/hms_card_title"
                    android:layout_toEndOf="@+id/icon"
                    android:text="体温" />

                <TextView
                    style="@style/hms_card_title_status"
                    android:id="@+id/tv_main_body_temperature_status"
                    android:visibility="visible"/>
            </RelativeLayout>
            <LinearLayout
                style="@style/hms_card_value_container">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    >
                <TextView
                    android:id="@+id/tv_main_body_temperature"
                    style="@style/hms_data_card_main_data"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="--" />

                <TextView
                    style="@style/hms_data_car_data_unit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toEndOf="@+id/tv_main_body_temperature"
                    android:layout_alignBaseline="@+id/tv_main_body_temperature"
                    android:text="\u00B0C"
                    />
                </RelativeLayout>

                </LinearLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="26dp"
                android:layout_marginTop="32dp"
                android:orientation="vertical">
                <com.healthlink.hms.views.CustomProgressBar
                    android:id="@+id/temperature_progress_bar"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="368dp"
                    android:layout_height="16dp"
                    android:progress="40"
                    android:visibility="visible"
                    android:layout_centerVertical="true"
                        android:max="40" />

                    <ImageView
                    android:id="@+id/temperature_progress_bar_dot"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:src="@drawable/ring_shape_for_progress_bar"
                    android:layout_centerVertical="true"
                    android:visibility="gone" />

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:visibility="gone"
                        android:src="@mipmap/img_thermometer_data"/>
            </RelativeLayout>
        </LinearLayout>
        <TextView
            style="@style/hms_card_last_update_text"
            android:id="@+id/tv_data_year_or_day"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:text="@string/card_last_update_time_str" />
    </FrameLayout>

    <!--    卡片无数据界面  -->
    <FrameLayout
        android:visibility="visible"
        android:id="@+id/card_no_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            style="@style/hms_card_no_data_image"
            android:src="@drawable/card_icon_temperature_default"
            android:contentDescription="@string/card_bg_temperature_no_data" />

        <LinearLayout
            style="@style/hms_card_container_bound"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >
                <ImageView
                    style="@style/hms_card_icon"
                    android:id="@+id/icon_no_data"
                    android:src="@drawable/card_icon_temperature"
                    />

                <TextView
                    style="@style/hms_card_title"
                    android:layout_toEndOf="@+id/icon_no_data"
                    android:text="体温" />
            </RelativeLayout>

            <TextView
                style="@style/hms_card_no_data_desc"
                android:text="体温检测 体温记录"/>

        </LinearLayout>

    </FrameLayout>

    <!--    卡片正常数据界面（私密模式）  -->
    <FrameLayout
            android:visibility="gone"
            android:id="@+id/card_privacy_data"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                style="@style/hms_card_container_bound"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    >
                    <TextView
                        style="@style/hms_card_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="体温"
                        android:gravity="center_vertical"
                        android:layout_centerVertical="true"
                        android:layout_alignParentStart="true"
                        />
                </RelativeLayout>
                <LinearLayout
                    style="@style/hms_card_value_container_secrect_mode"
                    >
                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        >
                        <TextView
                            android:id="@+id/tv_main_body_temperature_privacy"
                            style="@style/hms_data_card_main_data_privacy"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="***" />

                        <TextView
                            style="@style/hms_data_car_data_unit_privacy"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_toEndOf="@+id/tv_main_body_temperature_privacy"
                            android:text="\u00B0C"
                            />
                    </RelativeLayout>

                </LinearLayout>
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="26dp"
                    android:layout_marginTop="37dp"
                    android:orientation="vertical">
                    <ProgressBar
                        android:id="@+id/temperature_progress_bar_privacy"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="368dp"
                        android:layout_height="16dp"
                        android:progress="40"
                        android:visibility="visible"
                        android:layout_centerVertical="true"
                        android:background="@mipmap/img_thermometer_data"
                        android:progressDrawable="@drawable/progressbar_gradient_temperature"
                        android:max="40" />
                </RelativeLayout>
            </LinearLayout>
        </FrameLayout>
</FrameLayout>