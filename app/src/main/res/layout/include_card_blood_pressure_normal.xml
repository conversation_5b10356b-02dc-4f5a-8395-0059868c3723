<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:visibility="gone"
    android:id="@+id/card_normal_data"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:showIn="@layout/card_blood_pressure">

    <View
        android:id="@+id/iv_bg_card_exception"
        style="@style/car_exception_style"
        android:visibility="gone" />

    <LinearLayout
        style="@style/hms_card_container_bound"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                style="@style/hms_card_icon"
                android:id="@+id/icon"
                android:src="@drawable/card_icon_blood_pressure" />

            <TextView
                style="@style/hms_card_title"
                android:layout_toEndOf="@+id/icon"
                android:text="血压" />

            <TextView
                android:id="@+id/tv_main_blood_pressure_status"
                style="@style/hms_card_title_status"
                android:background="@drawable/health_index_status_warning_bg_fill"
                android:text="待测量" />
        </RelativeLayout>

        <LinearLayout style="@style/hms_card_value_container">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    style="@style/hms_data_card_main_data"
                    android:id="@+id/tv_main_blood_pressure"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="118/72" />

                <TextView
                    style="@style/hms_data_car_data_unit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/unit_blood_pressure"
                    android:layout_toEndOf="@+id/tv_main_blood_pressure"
                    android:layout_alignBaseline="@+id/tv_main_blood_pressure" />
            </RelativeLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="vertical"
            android:gravity="center_vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:gravity="center">

                <com.healthlink.hms.views.CustomProgressBar
                    android:id="@+id/card_progress_1"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="324dp"
                    android:layout_height="15dp"
                    android:progress="100"
                    android:visibility="visible"
                    android:layout_centerVertical="true"
                    android:max="100" />

                <ImageView
                    android:id="@+id/card_dot_1"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:src="@drawable/ring_shape_for_progress_bar"
                    android:layout_centerVertical="true"
                    android:visibility="gone"
                    android:layout_marginStart="50dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="高压"
                    android:textColor="@color/text_color_fc_80"
                    android:gravity="center"
                    android:layout_marginStart="8dp"
                    android:layout_alignParentEnd="true"
                    android:textSize="18sp" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="33dp"
                android:layout_marginTop="8dp"
                android:orientation="vertical"
                android:gravity="center">

                <com.healthlink.hms.views.CustomProgressBar
                    android:id="@+id/card_progress_2"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="324dp"
                    android:layout_height="15dp"
                    android:progress="100"
                    android:visibility="visible"
                    android:layout_centerVertical="true"
                    android:max="100" />

                <ImageView
                    android:id="@+id/card_dot_2"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:src="@drawable/ring_shape_for_progress_bar"
                    android:layout_centerVertical="true"
                    android:visibility="gone"
                    android:layout_marginStart="60dp" />

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    android:src="@mipmap/img_thermometer_data" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="低压"
                    android:textColor="@color/text_color_fc_80"
                    android:gravity="center"
                    android:layout_marginStart="8dp"
                    android:layout_alignParentEnd="true"
                    android:textSize="18sp" />

            </RelativeLayout>

        </LinearLayout>
    </LinearLayout>

    <TextView
        style="@style/hms_card_last_update_text"
        android:id="@+id/tv_data_year_or_day"
        android:layout_width="wrap_content"
        android:layout_height="28dp"
        android:text="@string/card_last_update_time_str" />

</FrameLayout>