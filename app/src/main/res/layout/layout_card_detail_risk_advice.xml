<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <import type="android.view.View"/>
        <variable
            name="baseDTO"
            type="com.healthlink.hms.server.data.dto.charts.HealthSummeryBaseDTO" />
    </data>
<LinearLayout
    android:id="@+id/card_advice_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="40dp"
    android:orientation="vertical"
    app:layout_constraintTop_toBottomOf="@+id/ll_intro_container"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintBottom_toBottomOf="parent"
    tools:showIn="@layout/fragment_card_time">

    <LinearLayout
        android:visibility="@{baseDTO.singleDeclaration == null ? View.GONE : View.VISIBLE}"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/card_bg"
        android:paddingStart="40dp"
        android:paddingEnd="40dp"
        android:paddingTop="22dp"
        android:paddingBottom="22dp"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="46dp"
            android:layout_height="46dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_interest" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="22sp"
            android:layout_gravity="center"
            android:layout_marginStart="16dp"
            android:lineSpacingExtra="16dp"
            android:textColor="@color/text_color_fc_80"
            android:text="@{baseDTO.singleDeclaration}" />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:orientation="vertical">
        <RelativeLayout
            android:visibility="@{baseDTO.riskDescription == null ? View.GONE : View.VISIBLE}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="40dp"
            android:paddingEnd="40dp"
            android:paddingTop="30dp"
            android:paddingBottom="30dp"
            android:background="@drawable/card_bg">
            <LinearLayout
                android:id="@+id/health_risk_linear"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="46dp"
                    android:layout_height="46dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_perception" />

                <TextView
                    android:layout_marginStart="16dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="26sp"
                    android:layout_gravity="center"
                    android:textColor="@color/text_color_fc_100"
                    android:text="健康风险" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_tips_content"
                android:layout_below="@+id/health_risk_linear"
                android:layout_marginTop="26dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@{baseDTO.riskDescription}"
                android:textSize="22sp"
                android:lineHeight="42dp"
                android:textColor="@color/text_color_fc_80" />

            <TextView
                android:visibility="@{baseDTO.riskSource == null ? View.GONE : View.VISIBLE}"
                android:id="@+id/tv_tips_from"
                android:layout_alignParentBottom="true"
                android:layout_alignParentEnd="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAlignment="textEnd"
                android:text="@{baseDTO.riskSource}"
                android:textSize="18sp"
                android:textColor="@color/text_color_fc_60" />

        </RelativeLayout>
        <RelativeLayout
            android:visibility="@{baseDTO.healthAdvice!=null? View.VISIBLE : View.GONE}"
            android:layout_width="match_parent"
            android:layout_height="340dp"
            android:orientation="vertical"
            android:paddingStart="40dp"
            android:paddingEnd="40dp"
            android:layout_marginTop="40dp"
            android:paddingTop="30dp"
            android:paddingBottom="30dp"
            android:background="@drawable/card_bg">

            <LinearLayout
                android:id="@+id/health_advise_linear"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="46dp"
                    android:layout_height="46dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_bike" />

                <TextView
                    android:layout_marginStart="16dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="26sp"
                    android:layout_gravity="center"
                    android:textColor="@color/text_color_fc_100"
                    android:text="健康建议" />
            </LinearLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="210dp"
                android:layout_marginTop="26dp"
                android:layout_below="@+id/health_advise_linear">
                <com.healthlink.hms.views.MiddleEllipsesTextView
                    android:id="@+id/tv_health_advice_content"
                    android:layout_width="match_parent"
                    android:layout_height="210dp"
                    android:maxLines="4"
                    android:ellipsize="middle"
                    android:textSize="22sp"
                    android:lineHeight="42dp"
                    android:textColor="@color/text_color_333"
                    android:text="@{baseDTO.healthAdvice}" />
            </RelativeLayout>
            <LinearLayout
                android:id="@+id/card_advice_more"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_alignParentBottom="true"
                android:layout_alignParentRight="true"
                android:layout_marginBottom="39dp"
                android:orientation="horizontal"
                android:visibility="gone"
                >
                <TextView
                    android:id="@+id/tv_see_more"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="查看更多"
                    android:textSize="22sp"
                    android:textColor="@color/card_more" />
            </LinearLayout>

            <TextView
                android:visibility="@{baseDTO.healthSource!=null? View.VISIBLE : View.GONE }"
                android:id="@+id/tv_advice_from"
                android:layout_alignParentBottom="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:textAlignment="textEnd"
                android:text="@{baseDTO.healthSource}"
                android:textSize="18sp"
                android:textColor="@color/text_color_666" />

        </RelativeLayout>
    </LinearLayout>



</LinearLayout>
</layout>