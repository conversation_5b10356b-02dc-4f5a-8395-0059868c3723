<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:id="@+id/rl_main_root"
    android:background="@drawable/bg_home"
    >

<!--    首页中间-->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="72dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        >
        <!-- 服务列表卡片-->
        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/main_body_service_list"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentStart="true"
            />
        <!-- 数据卡片 -->
        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/center_fragment_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="20dp"
            android:layout_toEndOf="@id/main_body_service_list"
            />

    </RelativeLayout>

         <!-- Compose弹窗容器，建议放在最后 -->
    <FrameLayout
        android:id="@+id/global_compose_alert_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"/>

</FrameLayout>