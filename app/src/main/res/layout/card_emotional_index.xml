<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/main_data_card_width"
    android:layout_height="@dimen/main_data_card_height"
    android:orientation="vertical">

    <!--    高斯模糊背景-->
    <include layout="@layout/include_bg_card_blur" />

    <!--卡片正常数据界面-->
    <FrameLayout
        android:visibility="visible"
        android:id="@+id/card_normal_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >
    <!--异常卡片背景-->
        <View
            android:id="@+id/iv_bg_card_exception"
            style="@style/car_exception_style"
            android:visibility="gone"/>
        <LinearLayout
            style="@style/hms_card_container_bound"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <!--卡片标题 及 卡片状态文字：正常，过速等-->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <ImageView
                    style="@style/hms_card_icon"
                    android:id="@+id/icon"
                    android:src="@drawable/card_icon_emotional"
                    />
                <TextView
                    style="@style/hms_card_title"
                    android:layout_toEndOf="@+id/icon"
                    android:text="心情指数" />

                <TextView
                    style="@style/hms_card_title_status"
                    android:id="@+id/tv_main_body_heart_rate_status" />
            </RelativeLayout>
            <!--卡片值-->
            <LinearLayout
                style="@style/hms_card_value_container">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    >
                <TextView
                    android:id="@+id/tv_main_body_heart_rate"
                    style="@style/hms_data_card_main_data"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="80"/>

                <TextView
                    style="@style/hms_data_car_data_unit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toEndOf="@+id/tv_main_body_heart_rate"
                    android:layout_alignBaseline="@+id/tv_main_body_heart_rate"
                    android:text="分"/>
                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="105dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/card_emotional_chart"
                    android:visibility="visible"/>

                <com.github.mikephil.charting.charts.LineChart
                    android:id="@+id/chart_heart_rate_line"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="-25dp"
                    android:layout_marginEnd="-15dp"
                    android:visibility="gone" />
            </LinearLayout>
        </LinearLayout>
        <TextView
            style="@style/hms_card_last_update_text"
            android:id="@+id/tv_data_year_or_day"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:text="@string/card_last_update_time_str"
            />
    </FrameLayout>

<!--    卡片无数据界面-->
    <FrameLayout
        android:visibility="gone"
        android:id="@+id/card_no_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            style="@style/hms_card_no_data_image"
            android:src="@drawable/card_icon_heart_default"
            android:contentDescription="@string/card_bg_heart_no_data" />

        <LinearLayout
            style="@style/hms_card_container_bound"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >
                <ImageView
                    style="@style/hms_card_icon"
                    android:id="@+id/icon_no_data"
                    android:src="@drawable/card_icon_emotional"
                    />

                <TextView
                    style="@style/hms_card_title"
                    android:layout_toEndOf="@+id/icon_no_data"
                    android:text="心情指数" />
            </RelativeLayout>

            <TextView
                style="@style/hms_card_no_data_desc"
                android:text="心率检测 心电图解读"/>

        </LinearLayout>

    </FrameLayout>

    <!-- 私密模式界面-->
    <FrameLayout
        android:visibility="gone"
        android:id="@+id/card_privacy_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >
        <LinearLayout
            style="@style/hms_card_container_bound"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">
            <!--卡片标题 及 卡片状态文字：正常，过速登-->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >
                <TextView
                    style="@style/hms_card_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:gravity="center_vertical"
                    android:text="心率" />

            </RelativeLayout>
            <!--卡片值-->
            <LinearLayout
                style="@style/hms_card_value_container_secrect_mode"
                >
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    >
                    <TextView
                        android:id="@+id/tv_main_body_heart_rate_privacy"
                        style="@style/hms_data_card_main_data_privacy"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="***"/>

                    <TextView
                        style="@style/hms_data_car_data_unit_privacy"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="0dp"
                        android:layout_toEndOf="@+id/tv_main_body_heart_rate_privacy"
                        android:text="次/分钟" />
                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="105dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@mipmap/img_heart_rate_line_data"
                    android:visibility="gone"/>

                <com.github.mikephil.charting.charts.LineChart
                    android:id="@+id/chart_heart_rate_line_privacy"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="5dp"
                    android:layout_marginStart="-25dp"
                    android:layout_marginEnd="-15dp"
                    android:visibility="visible" />
            </LinearLayout>
        </LinearLayout>

    </FrameLayout>
</FrameLayout>
