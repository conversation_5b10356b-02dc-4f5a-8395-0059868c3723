<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:id="@+id/main_body_service_list"
        android:layout_width="960dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingStart="68dp"
        android:paddingEnd="20dp">

        <FrameLayout
            android:layout_width="872dp"
            android:layout_height="240dp">

        <net.center.blurview.ShapeBlurView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:blur_overlay_color="#4DFFFFFF"
            app:blur_corner_radius="30dp"
            app:blur_radius="15dp" />

        <RelativeLayout
            android:id="@+id/rl_home_live_status_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <FrameLayout
                android:id="@+id/iv_health_status"
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:layout_marginStart="32dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/health_status_disable">

                <com.healthlink.hms.views.HealthStatusGradientRingView
                    android:id="@+id/hs_view"
                    android:layout_marginTop="10dp"
                    android:layout_width="180dp"
                    android:layout_height="180dp"
                    android:padding="8dp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tv_home_score"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="100dp"
                    android:text=""
                    android:textColor="@color/text_color_333"
                    android:textSize="36sp"
                    android:visibility="gone" />
            </FrameLayout>


            <TextView
                android:id="@+id/tv_privacy_title_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="40dp"
                android:layout_toRightOf="@+id/iv_health_status"
                android:text="系统已开启私密模式"
                android:textColor="@color/text_color_fc_100"
                android:textSize="26sp"
                android:visibility="gone" />

            <LinearLayout
                android:id="@+id/home_top_left_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="40dp"
                android:layout_marginTop="54dp"
                android:layout_toRightOf="@id/iv_health_status"
                android:orientation="vertical"
                android:visibility="visible">

                <TextView
                    android:id="@+id/tv_data_update_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="更新日期：2024-06-06 14:00:00"
                    android:textColor="@color/text_color_333_a80"
                    android:textSize="20sp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tv_health_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="104dp"
                    android:text="@string/hms_widget_tip_for_no_login"
                    android:textColor="@color/text_color_333"
                    android:textSize="26sp" />

                <com.gwm.widget.GwmButton
                    android:id="@+id/btn_health_report"
                    android:layout_width="@dimen/service_list_item_1_btn_w"
                    android:layout_height="40dp"
                    android:layout_marginTop="30dp"
                    android:background="@drawable/text_bg_fill_selector"
                    android:text="查看报告"
                    android:textColor="@color/hms_white_color_100"
                    android:textSize="18sp"
                    android:accessibilityPaneTitle=""
                    android:visibility="gone" />

                <com.gwm.widget.GwmButton
                    android:id="@+id/btn_to_login"
                    android:layout_width="@dimen/service_list_item_1_btn_w"
                    android:layout_height="40dp"
                    android:layout_marginTop="30dp"
                    android:background="@drawable/text_bg_fill_selector"
                    android:text="点击登录"
                    android:textColor="@color/hms_white_color_100"
                    android:textSize="18sp"
                    android:accessibilityPaneTitle=""
                    android:visibility="gone" />

                <com.gwm.widget.GwmButton
                    android:id="@+id/btn_to_authorization"
                    android:layout_width="@dimen/service_list_item_1_btn_w"
                    android:layout_height="40dp"
                    android:layout_marginTop="30dp"
                    android:background="@drawable/text_bg_fill_selector"
                    android:text="去授权"
                    android:accessibilityPaneTitle=""
                    android:textColor="@color/hms_white_color_100"
                    android:textSize="18sp"
                    android:visibility="gone" />

            </LinearLayout>

            <com.gwm.widget.GwmButton
                app:btnStyle="flat_forward"
                app:iconSize="40dp"
                android:id="@+id/iv_settings"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="32dp"
                android:layout_marginRight="32dp"
                app:icon="@drawable/ic_system_setting"
                android:visibility="gone" />

            <com.gwm.widget.GwmButton
                android:id="@+id/btn_to_live_show"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:layout_marginBottom="50dp"
                android:layout_marginEnd="330dp"
                android:layout_width="@dimen/service_list_item_1_btn_w"
                android:layout_height="40dp"
                android:background="@drawable/text_bg_fill_selector"
                android:text="演示场景"
                android:accessibilityPaneTitle=""
                android:textColor="@color/hms_white_color_100"
                android:textSize="18sp"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/iv_hms_logo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/hms_logo"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:layout_marginBottom="30dp"
                android:layout_marginEnd="30dp"/>

        </RelativeLayout>

        </FrameLayout>

        <LinearLayout
            android:id="@+id/ll_health_tips_play_bar"
            android:layout_width="872dp"
            android:layout_height="152dp"
            android:layout_marginTop="40dp"
            android:orientation="horizontal">
            <!--健康小贴士-->
            <include
                android:id="@+id/include_rl_health_tips"
                layout="@layout/include_rl_health_tips" />

            <!--急救小课堂-->
            <include
                android:id="@+id/include_rl_emergency_tips"
                layout="@layout/include_rl_emergency_tips" />


        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:visibility="gone"
            android:id="@+id/recyclerView"
            android:layout_width="872dp"
            android:layout_height="@dimen/card_vehicle_container_height"
            android:layout_marginTop="40dp" />

        <LinearLayout
            android:id="@+id/low_cards"
            android:layout_width="872dp"
            android:layout_height="290dp"
            android:layout_marginTop="40dp"
            android:visibility="gone">

            <androidx.cardview.widget.CardView
                android:id="@+id/low_card1"
                android:layout_width="416dp"
                android:layout_height="match_parent"
                app:cardBackgroundColor="@color/hms_card_background_color"
                app:cardCornerRadius="24dp"
                app:cardElevation="0dp">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/low_card1" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="25dp"
                    android:layout_marginTop="25dp"
                    android:layout_marginEnd="136dp"
                    android:alpha="0.6"
                    android:lineSpacingExtra="8dp"
                    android:text="@string/tips_low_card1"
                    android:textColor="@color/text_color_fc_60"
                    android:textSize="18sp" />

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/low_card2"
                android:layout_width="416dp"
                android:layout_height="match_parent"
                android:layout_marginStart="40dp"
                app:cardBackgroundColor="@color/hms_card_background_color"
                app:cardCornerRadius="24dp"
                app:cardElevation="0dp">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/low_card2" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="25dp"
                    android:layout_marginTop="25dp"
                    android:layout_marginEnd="136dp"
                    android:alpha="0.6"
                    android:lineSpacingExtra="8dp"
                    android:text="@string/tips_low_card2"
                    android:textColor="@color/text_color_fc_60"
                    android:textSize="18sp" />

            </androidx.cardview.widget.CardView>


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="240dp"
            android:layout_centerInParent="true"
            android:layout_marginTop="40dp"
            android:orientation="horizontal">
<!--人工电话咨询-->
    <include
        android:id="@+id/include_telephone_doctor"
        layout="@layout/include_main_body_service_telephone_doctor"/>
<!--AI健康咨询-->
    <include
        android:id="@+id/include_ai_health_doctor"
        layout="@layout/include_main_body_service_ai_doctor"/>
        </LinearLayout>
        
    </LinearLayout>

</layout>