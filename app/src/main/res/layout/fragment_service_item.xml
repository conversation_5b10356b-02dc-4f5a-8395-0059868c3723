<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mode_container"
    android:layout_width="@dimen/card_vehicle_item_width"
    android:layout_height="@dimen/card_vehicle_container_height">

    <include layout="@layout/include_bg_card_blur" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center_horizontal"
        >
        <ImageView
            android:id="@+id/mode_icon"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_marginTop="35dp"
            android:src="@drawable/bg_for_warn_normal"/>

        <TextView
            android:id="@+id/mode_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="39dp"
            android:text="加热模式"
            android:textSize="22sp"
            android:textAlignment="center"
            android:textColor="@color/text_color_333"/>
    </LinearLayout>

</FrameLayout>