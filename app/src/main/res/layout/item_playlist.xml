<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="416dp"
    android:layout_height="442dp"
    android:layout_marginEnd="12dp"
    android:background="@drawable/card_bg">

    <!-- 视频缩略图 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/video_thumbnail_container"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_video_thumbnail"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_video_thumbnail"
            android:clipToOutline="true"
            android:scaleType="fitXY"
            android:src="@drawable/icon_video_default" />

        <ImageView
            android:id="@+id/iv_play_icon"
            android:layout_width="76dp"
            android:layout_height="76dp"
            android:src="@drawable/icon_play_video"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

<!--        持续时间-->
        <LinearLayout
            android:id="@+id/duration_container"
            android:layout_width="wrap_content"
            android:layout_height="42dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="15dp"
            android:layout_marginBottom="15dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="@drawable/card_duration_bg">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/icon_duration"
                />
            <TextView
                android:id="@+id/tv_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="10dp"
                android:paddingVertical="6dp"
                android:textColor="@color/white"
                android:textSize="22sp"
                tools:text="00:00" />
        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 视频信息 -->
    <TextView
        android:id="@+id/tv_video_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="30dp"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="40dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="@color/text_color_333"
        android:textSize="26sp"
        android:textAlignment="textStart"
        app:layout_constraintTop_toBottomOf="@id/video_thumbnail_container"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="意外骨折不要慌" />

</androidx.constraintlayout.widget.ConstraintLayout> 