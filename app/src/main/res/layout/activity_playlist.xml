<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/hms_view_bg_color">

    <!-- 顶部标题栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/back_area_container"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="68dp"
            app:layout_constraintTop_toTopOf="parent"
            android:gravity="center_vertical"
            >
            <ImageView
                android:id="@+id/btnBack"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:contentDescription="@string/back"
                android:src="@drawable/ic_arrow_left" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:text="急救小课堂"
                android:textColor="@color/text_color_333"
                android:textSize="30sp"
                android:textStyle="bold"/>
        </LinearLayout>

        <TextView
            android:id="@+id/btn_apply_practice"
            android:layout_width="280dp"
            android:layout_height="64dp"
            android:layout_marginEnd="68dp"
            android:background="@drawable/text_bg_fill"
            android:gravity="center"
            android:text="申请现场学习急救知识"
            android:textColor="@color/hms_white_color_100"
            android:textSize="24sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 播放列表 RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_playlist"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="68dp"
        android:layout_marginEnd="68dp"
        android:layout_marginTop="40dp"
        android:layout_marginBottom="40dp"
        android:clipToPadding="false"
        android:nestedScrollingEnabled="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_bar" />

</androidx.constraintlayout.widget.ConstraintLayout> 