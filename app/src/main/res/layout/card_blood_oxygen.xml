<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/main_data_card_width"
    android:layout_height="@dimen/main_data_card_height"
    android:orientation="vertical">

    <!--    高斯模糊背景-->
    <include layout="@layout/include_bg_card_blur" />

    <FrameLayout
        android:id="@+id/card_normal_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <View
            android:id="@+id/iv_bg_card_exception"
            style="@style/car_exception_style"
            android:visibility="gone"/>

        <LinearLayout
            style="@style/hms_card_container_bound"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    style="@style/hms_card_icon"
                    android:id="@+id/icon"
                    android:src="@drawable/card_icon_spo2"
                    />

                <TextView
                    style="@style/hms_card_title"
                    android:layout_toEndOf="@+id/icon"
                    android:text="血氧" />

                <TextView
                    android:id="@+id/tv_main_body_blood_oxygen_status"
                    style="@style/hms_card_title_status"/>

            </RelativeLayout>
            <!--        卡片值-->
            <LinearLayout style="@style/hms_card_value_container">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tv_main_body_blood_oxygen"
                        style="@style/hms_data_card_main_data"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="--" />

                    <TextView
                        style="@style/hms_data_car_data_unit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignBaseline="@+id/tv_main_body_blood_oxygen"
                        android:layout_toEndOf="@+id/tv_main_body_blood_oxygen"
                        android:text="%"></TextView>
                </RelativeLayout>

            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="24dp"
                android:layout_marginTop="36dp"
                android:visibility="visible">

                <com.healthlink.hms.views.CustomProgressBar
                    android:id="@+id/blood_oxygen_progress_bar"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="368dp"
                    android:layout_height="16dp"
                    android:layout_centerVertical="true"
                    android:max="100"
                    android:progress="100"
                    android:visibility="visible" />

                <ImageView
                    android:id="@+id/blood_oxygen_progress_bar_dot"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:layout_centerVertical="true"
                    android:src="@drawable/ring_shape_for_progress_bar"
                    android:visibility="gone" />

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@mipmap/img_oxygen_saturation_data"
                    android:visibility="gone"/>
            </RelativeLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/tv_data_year_or_day"
            style="@style/hms_card_last_update_text"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:text="@string/card_last_update_time_str" />
    </FrameLayout>
    <!--卡片无数据界面-->
    <FrameLayout
        android:id="@+id/card_no_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible">

        <ImageView
            style="@style/hms_card_no_data_image"
            android:contentDescription="@string/card_bg_blood_oxygen_no_data"
            android:src="@drawable/card_icon_spo2_default" />

        <LinearLayout
            style="@style/hms_card_container_bound"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >
                <ImageView
                    style="@style/hms_card_icon"
                    android:id="@+id/icon_no_data"
                    android:src="@drawable/card_icon_spo2"
                    />

                <TextView
                    style="@style/hms_card_title"
                    android:layout_toEndOf="@+id/icon_no_data"
                    android:text="血氧" />
            </RelativeLayout>

            <TextView
                style="@style/hms_card_no_data_desc"
                android:text="连续血氧 高原血氧检测" />

        </LinearLayout>

    </FrameLayout>
    <!--卡片私密模式界面-->
    <FrameLayout
        android:visibility="gone"
        android:id="@+id/card_privacy_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            style="@style/hms_card_container_bound"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >
                <TextView
                    style="@style/hms_card_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="血氧"
                    android:gravity="center_vertical"
                    android:layout_centerVertical="true"
                    android:layout_alignParentStart="true"
                    />
            </RelativeLayout>
            <!--        卡片值-->
            <LinearLayout
                style="@style/hms_card_value_container_secrect_mode">
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    >
                    <TextView
                        android:id="@+id/tv_main_body_blood_oxygen_privacy"
                        style="@style/hms_data_card_main_data_privacy"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="***"/>

                    <TextView
                        style="@style/hms_data_car_data_unit_privacy"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_toEndOf="@+id/tv_main_body_blood_oxygen_privacy"
                        android:text="%">
                    </TextView>
                </RelativeLayout>

            </LinearLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="24dp"
                android:visibility="visible"
                android:layout_marginTop="39dp">
                <ProgressBar
                    android:id="@+id/blood_oxygen_progress_bar_privacy"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="368dp"
                    android:layout_height="16dp"
                    android:progress="100"
                    android:visibility="visible"
                    android:background="@mipmap/img_thermometer_data"
                    android:progressDrawable="@drawable/shape_blood_oxygen_bg"
                    android:layout_centerVertical="true"
                    android:max="100" />
            </RelativeLayout>
        </LinearLayout>
    </FrameLayout>

</FrameLayout>