<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="baseFragment"
            type="com.healthlink.hms.fragment.BaseCardFragment" />

        <variable
            name="heartRateStatVo"
            type="com.healthlink.hms.server.data.dto.charts.heartrate.HeartRateStatDTO" />

        <variable
            name="card3DTO"
            type="com.healthlink.hms.server.data.dto.charts.HealthSummeryDTO" />

        <variable
            name="healthSummeryDTO"
            type="com.healthlink.hms.server.data.dto.charts.HealthSummeryDTO" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout

        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.healthlink.hms.views.CustomBarStretchScrollView
            android:visibility="invisible"
            android:id="@+id/sv_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none"
            android:layout_marginEnd="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                style="@style/fragment_card_container_style">
                <!--    图表容器 -->
                <LinearLayout
                    android:id="@+id/ll_chart_container"
                    android:layout_width="match_parent"
                    android:layout_height="450dp"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toTopOf="@+id/ll_intro_container"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.healthlink.hms.views.charts.HeartDayChart
                        android:id="@+id/c_line_chart"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:visibility="gone" />

                    <com.healthlink.hms.views.charts.HeartBarChart
                        android:id="@+id/c_bar_chart"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tv_no_data_text"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="暂无心率数据"
                        android:textAlignment="center"
                        android:textColor="@color/text_color_fc_80"
                        android:textSize="26sp"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tv_privacy_text"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="系统已开启私密模式"
                        android:textAlignment="center"
                        android:textColor="@color/text_color_fc_60"
                        android:textSize="26sp"
                        android:visibility="gone" />


                </LinearLayout>
                <!--    图表解释容器 -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ll_intro_container"
                    android:layout_marginTop="0dp"
                    style="@style/fragment_card_metrics_intro_style">

                    <LinearLayout
                        android:id="@+id/ll_intro_left"
                        style="@style/card_3_style"
                        android:gravity="center"
                        android:orientation="vertical"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/ll_intro_center"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        >

                        <TextView
                            android:id="@+id/tv_fetch_time"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:lineHeight="42dp"
                            android:text=""
                            android:textColor="@color/text_color_fc_60"
                            android:textSize="22sp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/tv_fetch_time"
                            android:layout_marginTop="24dp"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tv_intro_time_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{String.format(`%.0f`, heartRateStatVo.last)}"
                                android:textColor="@color/text_color_fc_80"
                                android:textSize="@dimen/card_content_value_text_size"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_intro_time_value_unit"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_toEndOf="@+id/tv_intro_time_value"
                                android:text="次/分钟"
                                android:textColor="@color/text_color_fc_60"
                                android:textSize="@dimen/card_content_unit_text_size" />
                        </LinearLayout>
                    </LinearLayout>

                    <RelativeLayout
                        android:id="@+id/ll_intro_center"
                        style="@style/card_3_style"
                        android:orientation="vertical"
                        app:layout_constraintStart_toEndOf="@+id/ll_intro_left"
                        app:layout_constraintEnd_toStartOf="@+id/ll_intro_right"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintHorizontal_bias="0.5">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_intro_range"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:lineHeight="42dp"
                                android:text="心率范围"
                                android:textColor="@color/text_color_fc_60"
                                android:textSize="22sp" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/tv_intro_range"
                                android:layout_marginTop="24dp"
                                android:gravity="center"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/tv_intro_range_value"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{String.format(`%.0f`, heartRateStatVo.min) + `-` + String.format(`%.0f`,heartRateStatVo.max)}"
                                    android:textColor="@color/text_color_fc_80"
                                    android:textSize="@dimen/card_content_value_text_size"
                                    android:textStyle="bold" />

                                <TextView
                                    android:id="@+id/tv_intro_range_value_unit"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_toRightOf="@+id/tv_intro_range_value"
                                    android:text="次/分钟"
                                    android:textColor="@color/text_color_fc_60"
                                    android:textSize="24sp" />
                            </LinearLayout>
                        </LinearLayout>

                    </RelativeLayout>

                    <LinearLayout
                        android:id="@+id/ll_intro_right"
                        style="@style/card_3_style"
                        android:gravity="center"
                        android:orientation="vertical"
                        app:layout_constraintStart_toEndOf="@+id/ll_intro_center"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <FrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_horizontal"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:gravity="end"
                                    android:orientation="vertical">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="40dp"
                                        android:gravity="end|center_vertical"
                                        android:text="心动过速"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="22sp" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="40dp"
                                        android:layout_marginTop="8dp"
                                        android:gravity="end|center_vertical"
                                        android:text="心动过缓"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="22sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="40dp"
                                        android:layout_marginTop="8dp"
                                        android:gravity="end|center_vertical"
                                        android:text="正常"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="22sp" />


                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginStart="40dp"
                                    android:gravity="start"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/tv_tachycardia_per"
                                        android:layout_width="wrap_content"
                                        android:layout_height="40dp"
                                        android:gravity="start|center_vertical"
                                        android:text="@{(card3DTO.highProp != null ? card3DTO.highProp : `0`) + `%`}"
                                        android:textColor="@color/text_color_fc_100"
                                        android:textSize="22sp" />

                                    <TextView
                                        android:id="@+id/tv_bradycardia_per"
                                        android:layout_width="wrap_content"
                                        android:layout_height="40dp"
                                        android:layout_marginTop="8dp"
                                        android:gravity="start|center_vertical"
                                        android:text="@{(card3DTO.lowProp != null ? card3DTO.lowProp : `0`) + `%`}"
                                        android:textColor="@color/text_color_fc_100"
                                        android:textSize="22sp" />

                                    <TextView
                                        android:id="@+id/tv_normal_per"
                                        android:layout_width="wrap_content"
                                        android:layout_height="40dp"
                                        android:layout_marginTop="8dp"
                                        android:gravity="start|center_vertical"
                                        android:text="@{(card3DTO.normalProp != null ? card3DTO.normalProp : `0`) + `%`}"
                                        android:textColor="@color/text_color_fc_100"
                                        android:textSize="22sp" />

                                </LinearLayout>

                            </LinearLayout>

                            <com.gwm.widget.GwmButton
                                app:btnStyle="flat_forward"
                                app:iconSize="32dp"
                                android:id="@+id/iv_intro_tips"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="top|end"
                                android:layout_marginTop="24dp"
                                android:layout_marginEnd="24dp"
                                app:icon="@drawable/ic_introduce_circle"
                                android:visibility="visible" />
                        </FrameLayout>

                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!--    风险建议提示容器-->
                <include
                    android:id="@+id/health_risk_all"
                    layout="@layout/layout_card_detail_risk_advice"
                    android:visibility="@{baseFragment.Companion.isHealthAdviceVisible(healthSummeryDTO) == true ? View.VISIBLE : View.GONE}"
                    app:baseDTO="@{healthSummeryDTO}" />

                <include layout="@layout/fragment_card_footer" />
            </LinearLayout>

        </com.healthlink.hms.views.CustomBarStretchScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>