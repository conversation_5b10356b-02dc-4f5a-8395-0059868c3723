<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="baseFragment"
            type="com.healthlink.hms.fragment.BaseCardFragment" />

        <variable
            name="heartRateStatVo"
            type="com.healthlink.hms.server.data.dto.charts.heartrate.HeartRateStatDTO" />

        <variable
            name="card3DTO"
            type="com.healthlink.hms.server.data.dto.charts.HealthSummeryDTO" />

        <variable
            name="healthSummeryDTO"
            type="com.healthlink.hms.server.data.dto.charts.HealthSummeryDTO" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout

        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:src="@drawable/bg_emotional_index"
            android:scaleType="fitCenter"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>