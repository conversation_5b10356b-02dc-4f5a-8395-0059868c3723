<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_body_service_ai_doctor"
    android:layout_marginStart="40dp"
    android:layout_width="416dp"
    android:layout_height="match_parent"

    tools:showIn="@layout/fragment_service_list">

    <include layout="@layout/include_bg_card_blur" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:layout_gravity="center_horizontal"
        >

    <ImageView
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="13dp"
        android:layout_width="130dp"
        android:layout_height="130dp"
        android:src="@drawable/card_ai_doctor" />


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerVertical="true"
        android:layout_marginStart="30dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:text="@string/main_body_service_ai_doctor_title"
            android:textColor="@color/text_color_333"
            android:textSize="22sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="20dp"
            android:text="@string/main_body_service_ai_doctor_desc"
            android:textColor="@color/text_color_666"
            android:textSize="16sp" />

        <TextView
            android:layout_width="120dp"
            android:layout_height="@dimen/service_list_item_1_btn_h"
            android:layout_marginTop="32dp"
            android:background="@drawable/text_bg_fill_blue"
            android:clickable="false"
            android:gravity="center"
            android:focusable="false"
            android:text="@string/main_body_service_ai_doctor_button"
            android:textColor="@color/hms_white_color_100"
            android:textSize="18sp" />
    </LinearLayout>

    </RelativeLayout>

</FrameLayout>