<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_round_bg"
    android:orientation="vertical"
    android:paddingHorizontal="40dp"
    android:paddingTop="32dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="线下实践，解锁实战急救技能"
        android:textColor="@color/text_color_333"
        android:textSize="30sp"
        android:textStyle="bold" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:lineSpacingExtra="6dp"
        android:text="意外突发，黄金救援分秒必争！急救力场提供专业急救视频库，覆盖心肺复苏、止血包扎等场景，由医护专家拆解关键步骤，助你快速掌握理论知识。"
        android:textColor="@color/text_color_666"
        android:textSize="22sp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:lineSpacingExtra="6dp"
        android:text="一键申请线下实战培训，我们联合专业机构，小班教学+导师一对一指导，模拟真实场景实操，考核通过还能拿权威证书。从知识到技能，为生命救援上双重保险！"
        android:textColor="@color/text_color_666"
        android:textSize="22sp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="32dp"
        android:lineSpacingExtra="6dp"
        android:text="提交信息后，我们会有专业人士联系您，确认培训时间和地点"
        android:textColor="@color/text_color_666"
        android:textSize="22sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp">

        <EditText
            android:id="@+id/etName"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:background="@drawable/shape_edit_text_bg"
            android:hint="姓名"
            android:paddingHorizontal="16dp"
            android:textColorHint="@color/text_color_333"
            android:textSize="22sp" />

        <EditText
            android:id="@+id/etPhone"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginStart="24dp"
            android:background="@drawable/shape_edit_text_bg"
            android:hint="手机号"
            android:inputType="phone"
            android:paddingHorizontal="16dp"
            android:textColorHint="@color/text_color_333"
            android:textSize="22sp" />

        <EditText
            android:id="@+id/etCity"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:background="@drawable/shape_edit_text_bg"
            android:hint="所在城市"
            android:layout_marginStart="24dp"
            android:paddingHorizontal="16dp"
            android:textColorHint="@color/text_color_333"
            android:textSize="22sp" />

        <EditText
            android:id="@+id/etExpectedTime"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginStart="24dp"
            android:background="@drawable/shape_edit_text_bg"
            android:hint="期望时间"
            android:paddingHorizontal="16dp"
            android:textColorHint="@color/text_color_333"
            android:textSize="22sp" />

    </LinearLayout>


    <Button
        android:id="@+id/btnSubmit"
        android:layout_width="280dp"
        android:layout_height="64dp"
        android:layout_marginTop="50dp"
        android:background="@drawable/shape_green_button_bg"
        android:text="提交信息"
        android:layout_gravity="center"
        android:textColor="#FFFFFF"
        android:textSize="24sp" />

</LinearLayout> 