<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:visibility="gone"
    android:id="@+id/card_privacy_data"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:showIn="@layout/card_blood_pressure">

    <LinearLayout
        style="@style/hms_card_container_bound"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                style="@style/hms_card_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="血压"
                android:gravity="center_vertical"
                android:layout_centerVertical="true"
                android:layout_alignParentStart="true"

                />

        </RelativeLayout>

        <LinearLayout
            android:layout_marginTop="20dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    style="@style/hms_data_card_main_data_privacy"
                    android:id="@+id/tv_main_blood_pressure_privacy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="***" />

                <TextView
                    style="@style/hms_data_car_data_unit_privacy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/unit_blood_pressure"
                    android:layout_toEndOf="@+id/tv_main_blood_pressure_privacy" />
            </RelativeLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:layout_marginTop="11dp"
                android:gravity="center">

                <ProgressBar
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="324dp"
                    android:layout_height="16dp"
                    android:progress="100"
                    android:visibility="visible"
                    android:layout_centerVertical="true"
                    android:background="@mipmap/img_thermometer_data"
                    android:progressDrawable="@drawable/progressbar_gradient_temperature"
                    android:max="100" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="高压"
                    android:textColor="@color/text_color_fc_80"
                    android:gravity="center"
                    android:layout_marginStart="8dp"
                    android:layout_alignParentEnd="true"
                    android:textSize="18sp" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_marginTop="9dp"
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:orientation="vertical"
                android:gravity="center">

                <ProgressBar
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="324dp"
                    android:layout_height="16dp"
                    android:progress="100"
                    android:visibility="visible"
                    android:layout_centerVertical="true"
                    android:background="@mipmap/img_thermometer_data"
                    android:progressDrawable="@drawable/progressbar_gradient_temperature"
                    android:max="100" />

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    android:src="@mipmap/img_thermometer_data" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="低压"
                    android:textColor="@color/text_color_fc_80"
                    android:gravity="center"
                    android:layout_marginStart="8dp"
                    android:layout_alignParentEnd="true"
                    android:textSize="18sp" />

            </RelativeLayout>

        </LinearLayout>
    </LinearLayout>

</FrameLayout>