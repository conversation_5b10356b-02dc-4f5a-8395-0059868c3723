<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <data>
        <import type="android.view.View"/>
        <variable
            name="curFragment"
            type="com.healthlink.hms.fragment.CardPressureFragment" />
        <variable
            name="baseFragment"
            type="com.healthlink.hms.fragment.BaseCardFragment" />
        <variable
            name="card1DTO"
            type="com.healthlink.hms.server.data.dto.charts.pressure.PressureCard1DTO" />
        <variable
            name="card2DTO"
            type="com.healthlink.hms.server.data.dto.charts.pressure.PressureCard2DTO" />
        <variable
            name="card3DTO"
            type="com.healthlink.hms.server.data.dto.charts.pressure.PressureCard3DTO" />
        <variable
            name="pressureSummaryVo"
            type="com.healthlink.hms.server.data.dto.charts.pressure.PressureSummaryDTO" />
    </data>
<androidx.constraintlayout.widget.ConstraintLayout

    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >
    <com.healthlink.hms.views.CustomBarStretchScrollView
        android:visibility="invisible"
        android:id="@+id/sv_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="10dp"
        android:scrollbarThumbVertical="@drawable/scrollbar_ver_thumb"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        >
        <LinearLayout
            style="@style/fragment_card_container_style">
            <!--图表容器 -->
            <LinearLayout
                android:id="@+id/ll_chart_container"
                android:layout_width="match_parent"
                android:layout_height="420dp"
                android:orientation="vertical"
                app:layout_constraintBottom_toTopOf="@+id/ll_intro_container"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.healthlink.hms.views.charts.PressureBarChart
                    android:id="@+id/c_bar_chart"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"/>
                <TextView
                    android:id="@+id/tv_no_data_text"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="暂无压力数据"
                    android:textSize="26sp"
                    android:textAlignment="center"
                    android:gravity="center"
                    android:visibility="gone"
                    android:textColor="@color/text_color_fc_80"
                    />
                <TextView
                    android:id="@+id/tv_privacy_text"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="系统已开启私密模式"
                    android:textSize="26sp"
                    android:textAlignment="center"
                    android:gravity="center"
                    android:visibility="gone"
                    android:textColor="@color/text_color_fc_60"
                    />

            </LinearLayout>
            <!--压力颜色图例-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:orientation="horizontal">
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    >
                    <com.healthlink.hms.views.HMSCircleView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        app:circleColor="@color/card_press_bar_bg_color_relax"
                        />
                    <TextView
                        android:layout_marginStart="8dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="22sp"
                        android:layout_gravity="center"
                        android:textColor="@color/text_color_fc_80"
                        android:text="放松"/>
                </LinearLayout>
                <LinearLayout
                    android:layout_marginStart="200dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    >
                    <com.healthlink.hms.views.HMSCircleView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        app:circleColor="@color/card_press_bar_bg_color_normal"/>
                    <TextView
                        android:layout_marginStart="8dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="22sp"
                        android:layout_gravity="center"
                        android:textColor="@color/text_color_fc_80"
                        android:text="正常"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_marginStart="200dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    >
                    <com.healthlink.hms.views.HMSCircleView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        app:circleColor="@color/card_press_bar_bg_color_middle"
                        />
                    <TextView
                        android:layout_marginStart="8dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="22sp"
                        android:layout_gravity="center"
                        android:textColor="@color/text_color_fc_80"
                        android:text="中等"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_marginStart="200dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    >
                    <com.healthlink.hms.views.HMSCircleView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        app:circleColor="@color/card_press_bar_bg_color_high"
                        />
                    <TextView
                        android:layout_marginStart="8dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="22sp"
                        android:layout_gravity="center"
                        android:textColor="@color/text_color_fc_80"
                        android:text="偏高"/>
                </LinearLayout>

            </LinearLayout>
            <!--图表解释容器 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ll_intro_container"
                android:layout_marginTop="40dp"
                style="@style/fragment_card_metrics_intro_style">

                <RelativeLayout
                    android:id="@+id/ll_intro_left"
                    style="@style/card_3_style"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/ll_intro_center"
                    app:layout_constraintHorizontal_chainStyle="spread_inside">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:layout_centerVertical="true"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_fetch_time"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_color_fc_60"
                            android:textSize="@dimen/card_title_text_size"
                            android:gravity="center"
                            android:lineHeight="42dp"
                            android:text="@{card1DTO.pressureTime}"
                            />
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center"
                            android:layout_marginTop="24dp"
                            >
                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                >
                                <TextView
                                    android:id="@+id/tip"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_color_fc_60"
                                    android:textSize="@dimen/card_content_unit_text_size"
                                    android:text="平均"
                                    android:visibility="gone"
                                    android:layout_alignBaseline="@id/tv_intro_time_value"
                                    />
                                <TextView
                                    android:id="@+id/tv_intro_time_value"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@{card1DTO.pressureValue}"
                                    android:textColor="@color/text_color_fc_80"
                                    android:textSize="@dimen/card_content_value_text_size"
                                    android:layout_toEndOf="@+id/tip"
                                    android:textStyle="bold" />
                                <TextView
                                    android:id="@+id/tv_intro_time_value_unit"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_color_fc_60"
                                    android:textSize="@dimen/card_content_unit_text_size"
                                    android:layout_toEndOf="@+id/tv_intro_time_value"
                                    android:layout_alignBaseline="@+id/tv_intro_time_value"
                                    android:text="@{card1DTO.pressureType}"
                                    />
                            </RelativeLayout>
                        </LinearLayout>
                    </LinearLayout>

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/ll_intro_center"
                    android:orientation="vertical"
                    style="@style/card_3_style"
                    app:layout_constraintStart_toEndOf="@+id/ll_intro_left"
                    app:layout_constraintEnd_toStartOf="@+id/ll_intro_right"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    >
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_centerVertical="true"
                        android:gravity="center">
                        <TextView
                            android:id="@+id/tv_intro_range"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_color_fc_60"
                            android:textSize="@dimen/card_title_text_size"
                            android:gravity="center"
                            android:lineHeight="42dp"
                            android:text="@{card2DTO.pressureAvgTitle ?? curFragment.getCenterCardTitleDefault()}"/>
                        <LinearLayout
                            android:layout_marginTop="24dp"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center"
                            >
                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                >
                                <TextView
                                    android:id="@+id/tv_intro_range_value"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_color_fc_80"
                                    android:textSize="@dimen/card_content_value_text_size"
                                    android:textStyle="bold"
                                    android:text="@{card2DTO.pressureAvg}"/>
                                <TextView
                                    android:id="@+id/tv_intro_range_value_unit"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_color_fc_60"
                                    android:textSize="@dimen/card_content_unit_text_size"
                                    android:layout_toRightOf="@+id/tv_intro_range_value"
                                    android:layout_alignBaseline="@+id/tv_intro_range_value"
                                    android:text="@{card2DTO.pressureAvgType}"/>
                            </RelativeLayout>
                        </LinearLayout>
                    </LinearLayout>

                </RelativeLayout>
                <!--第三张卡片 压力偏高、中等、正常、放松比例-->
                <LinearLayout
                    android:id="@+id/ll_intro_right"
                    android:orientation="vertical"
                    style="@style/card_3_style"
                    android:gravity="center"
                    app:layout_constraintStart_toEndOf="@+id/ll_intro_center"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    >
                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">
                        <LinearLayout
                            android:orientation="horizontal"
                            android:layout_gravity="center"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent">
                            <LinearLayout
                                android:id="@+id/lo_card4"
                                android:orientation="vertical"
                                android:gravity="end"
                                android:layout_gravity="center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:visibility="gone">
                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="42dp"
                                    android:textColor="@color/text_color_fc_60"
                                    android:gravity="center_vertical"
                                    android:textSize="@dimen/card_last_content_text_size"
                                    android:text="偏高"
                                    />
                                <TextView
                                    android:layout_marginTop="3dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="42dp"
                                    android:textColor="@color/text_color_fc_60"
                                    android:gravity="center_vertical"
                                    android:textSize="@dimen/card_last_content_text_size"
                                    android:lineHeight="42dp"
                                    android:text="中等"
                                    android:layout_gravity="center_vertical"
                                    />
                                <TextView
                                    android:layout_marginTop="3dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="42dp"
                                    android:textColor="@color/text_color_fc_60"
                                    android:gravity="center_vertical"
                                    android:textSize="@dimen/card_last_content_text_size"
                                    android:lineHeight="42dp"
                                    android:text="正常"
                                    android:layout_gravity="center_vertical"
                                    />
                                <TextView
                                    android:layout_marginTop="3dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="42dp"
                                    android:textColor="@color/text_color_fc_60"
                                    android:gravity="center_vertical"
                                    android:lineHeight="42dp"
                                    android:textSize="@dimen/card_last_content_text_size"
                                    android:text="放松"
                                    />
                            </LinearLayout>
                            <LinearLayout
                                android:layout_marginStart="40dp"
                                android:orientation="vertical"
                                android:gravity="start"
                                android:layout_gravity="center_vertical"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content">
                                <TextView
                                    android:id="@+id/tv_high_per"
                                    android:layout_width="wrap_content"
                                    android:layout_height="42dp"
                                    android:textSize="@dimen/card_last_content_text_size"
                                    android:lineHeight="42dp"
                                    android:textColor="@color/text_color_fc_100"
                                    android:gravity="center_vertical"
                                    android:text="@{(card3DTO.highProp != null ? card3DTO.highProp : `0`) + `%`}"
                                    android:visibility="gone"
                                    />
                                <TextView
                                    android:id="@+id/tv_middle_per"
                                    android:layout_width="wrap_content"
                                    android:layout_height="42dp"
                                    android:layout_marginTop="3dp"
                                    android:textSize="@dimen/card_last_content_text_size"
                                    android:lineHeight="42dp"
                                    android:gravity="center_vertical"
                                    android:textColor="@color/text_color_fc_100"
                                    android:text="@{(card3DTO.mediumProp != null ? card3DTO.mediumProp : `0`) + `%`}"
                                    android:visibility="gone"
                                    />
                                <TextView
                                    android:id="@+id/tv_normal_per"
                                    android:layout_width="wrap_content"
                                    android:layout_height="42dp"
                                    android:textSize="@dimen/card_last_content_text_size"
                                    android:textColor="@color/text_color_fc_100"
                                    android:layout_marginTop="3dp"
                                    android:lineHeight="42dp"
                                    android:gravity="center_vertical"
                                    android:text="@{(card3DTO.normalProp != null ? card3DTO.normalProp : `0`) + `%`}"
                                    android:visibility="gone"
                                    />
                                <TextView
                                    android:id="@+id/tv_relax_per"
                                    android:layout_width="wrap_content"
                                    android:layout_height="42dp"
                                    android:textSize="@dimen/card_last_content_text_size"
                                    android:textColor="@color/text_color_fc_100"
                                    android:layout_marginTop="3dp"
                                    android:lineHeight="42dp"
                                    android:gravity="center_vertical"
                                    android:text="@{(card3DTO.relaxProp != null ? card3DTO.relaxProp : `0`) + `%`}"
                                    android:visibility="gone"
                                    />
                            </LinearLayout>
                        </LinearLayout>
                        <com.gwm.widget.GwmButton
                            app:btnStyle="flat_forward"
                            app:iconSize="32dp"
                            android:id="@+id/iv_intro_tips"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="top|end"
                            android:layout_marginTop="24dp"
                            android:layout_marginEnd="24dp"
                            app:icon="@drawable/ic_introduce_circle"
                            android:visibility="visible" />
                    </FrameLayout>

                </LinearLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>
            <!--风险建议提示容器-->
            <include
                android:visibility="@{baseFragment.Companion.isHealthAdviceVisible(pressureSummaryVo) == true ? View.VISIBLE : View.GONE}"
                layout="@layout/layout_card_detail_risk_advice"
                android:id="@+id/health_risk_all"
                app:baseDTO="@{pressureSummaryVo}" />
            <include layout="@layout/fragment_card_footer" />
        </LinearLayout>

    </com.healthlink.hms.views.CustomBarStretchScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>

</layout>