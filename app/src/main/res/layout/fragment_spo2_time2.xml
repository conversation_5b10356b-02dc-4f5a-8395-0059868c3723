<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="com.healthlink.hms.utils.TimeUtils" />

        <variable
            name="baseFragment"
            type="com.healthlink.hms.fragment.BaseCardFragment" />

        <variable
            name="cardShowInfo"
            type="com.healthlink.hms.server.data.dto.SpO2Card1DTO" />

        <variable
            name="cardShowInfo1"
            type="com.healthlink.hms.server.data.dto.SpO2Card1DTO" />

        <variable
            name="cardShowInfo2"
            type="com.healthlink.hms.server.data.dto.SpO2Card2DTO" />

        <variable
            name="healthSpO2SummeryVo"
            type="com.healthlink.hms.server.data.dto.HealthSpO2SummaryDTO" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout

        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.healthlink.hms.views.CustomBarStretchScrollView
            android:visibility="invisible"
            android:id="@+id/sv_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginEnd="10dp"
            android:scrollbars="none"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                style="@style/fragment_card_container_style">
                <!--    图表容器 -->
                <!--    图表解释容器 -->
                <LinearLayout
                    android:id="@+id/sleep_chart_container"
                    android:layout_width="match_parent"
                    android:layout_height="420dp"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toTopOf="@+id/ll_intro_container"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">


                    <!--sleep-->
                    <com.healthlink.hms.views.charts.SpO2BarChart
                        android:id="@+id/c_SpO2_wmy"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/spo2_no_data_text"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="暂无血氧数据"
                        android:textAlignment="center"
                        android:textColor="@color/text_color_fc_80"
                        android:textSize="26sp"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/s_privacy_text"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="系统已开启私密模式"
                        android:textAlignment="center"
                        android:textColor="@color/text_color_fc_80"
                        android:textSize="26sp"
                        android:visibility="gone" />


                </LinearLayout>
                <!--        睡眠颜色图例-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.healthlink.hms.views.HMSCircleView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            app:circleColor="@color/card_spo2_low" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="8dp"
                            android:text="&lt;70%"
                            android:textColor="@color/text_color_fc_80"
                            android:textSize="22sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="200dp"
                        android:orientation="horizontal">

                        <com.healthlink.hms.views.HMSCircleView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            app:circleColor="@color/card_spo2_middle" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="8dp"
                            android:text="70%-89%"
                            android:textColor="@color/text_color_fc_80"
                            android:textSize="22sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="200dp"
                        android:orientation="horizontal">

                        <com.healthlink.hms.views.HMSCircleView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            app:circleColor="@color/card_spo2_high" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="8dp"
                            android:text="≥90%"
                            android:textColor="@color/text_color_fc_80"
                            android:textSize="22sp" />
                    </LinearLayout>

                </LinearLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ll_intro_container"
                    android:layout_marginTop="40dp"
                    style="@style/fragment_card_metrics_intro_style">

                    <LinearLayout
                        android:id="@+id/sleep_day_data_center"
                        style="@style/card_3_style"
                        android:gravity="center"
                        android:orientation="vertical"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/sleep_score_layout"
                        app:layout_constraintHorizontal_chainStyle="spread_inside"
                        >

                        <TextView
                            android:id="@+id/tv_sleep_date"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:lineHeight="42dp"
                            android:text="@{cardShowInfo.fetchTime}"
                            android:textColor="@color/text_color_fc_60"
                            android:textSize="@dimen/card_title_text_size" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="24dp"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tv_spo2_cur_min_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{cardShowInfo.min != null ? (cardShowInfo.min.equals(``)?``:String.valueOf(cardShowInfo.min)) : `--`}"
                                android:textColor="@color/text_color_fc_80"
                                android:textSize="@dimen/card_content_value_text_size"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tv_spo2_cur_min_unit"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="%"
                                android:textColor="@color/text_color_fc_60"
                                android:textSize="@dimen/card_content_unit_text_size"
                                android:visibility="visible" />

                            <TextView
                                android:id="@+id/tv_spo2_cur_max_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{cardShowInfo.max != null ? (cardShowInfo.max.equals(``)?``:`-`+cardShowInfo.max) : `--`}"
                                android:textColor="@color/text_color_fc_80"
                                android:textSize="@dimen/card_content_value_text_size"
                                android:textStyle="bold"

                                />

                            <TextView
                                android:id="@+id/tv_spo2_cur_max_unit"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="%"
                                android:textColor="@color/text_color_fc_60"
                                android:textSize="@dimen/card_content_unit_text_size" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/sleep_score_layout"
                        style="@style/card_3_style"
                        android:gravity="center"
                        android:orientation="vertical"
                        app:layout_constraintStart_toEndOf="@+id/sleep_day_data_center"
                        app:layout_constraintEnd_toStartOf="@+id/sleep_count"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintHorizontal_bias="0.5">

                        <TextView
                            android:id="@+id/tv_sleep_intro_range"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:lineHeight="42dp"
                            android:text="血氧范围"
                            android:textColor="@color/text_color_fc_60"
                            android:textSize="@dimen/card_title_text_size" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="24dp"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tv_spo2_min_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{cardShowInfo1.min != null ? String.valueOf(cardShowInfo1.min) : `--`}"
                                android:textColor="@color/text_color_fc_80"
                                android:textSize="@dimen/card_content_value_text_size"
                                android:textStyle="bold" />


                            <TextView
                                android:id="@+id/tv_spo2_min_unit"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="%"
                                android:textColor="@color/text_color_fc_60"
                                android:textSize="@dimen/card_content_unit_text_size"
                                android:visibility="visible" />

                            <TextView
                                android:id="@+id/tv_spo2_max_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{cardShowInfo1.max != null ? (cardShowInfo1.max.equals(``)?``:`-`+cardShowInfo1.max) : `--`}"
                                android:textColor="@color/text_color_fc_80"
                                android:textSize="@dimen/card_content_value_text_size"
                                android:textStyle="bold" />


                            <TextView
                                android:id="@+id/tv_spo2_max_unit"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="%"
                                android:textColor="@color/text_color_fc_60"
                                android:textSize="@dimen/card_content_unit_text_size" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/sleep_count"
                        style="@style/card_3_style"
                        android:gravity="center"
                        android:orientation="vertical"
                        app:layout_constraintStart_toEndOf="@+id/sleep_score_layout"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <FrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_horizontal"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:orientation="vertical">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="42dp"
                                        android:gravity="end|center_vertical"
                                        android:text="血氧过低"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="@dimen/card_last_content_text_size" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="42dp"
                                        android:layout_marginTop="8dp"
                                        android:gravity="end|center_vertical"
                                        android:text="低血氧症"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="@dimen/card_last_content_text_size" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="42dp"
                                        android:layout_marginTop="8dp"
                                        android:gravity="end|center_vertical"
                                        android:text="正常"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="@dimen/card_last_content_text_size" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginStart="40dp"
                                    android:gravity="start"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/tv_c41_per"
                                        android:layout_width="wrap_content"
                                        android:layout_height="42dp"
                                        android:gravity="start|center_vertical"
                                        android:text="@{(cardShowInfo2.tooLowProp!= null ? cardShowInfo2.tooLowProp : `0`)+ `%`}"
                                        android:textColor="@color/text_color_fc_100"
                                        android:textSize="@dimen/card_last_content_text_size" />

                                    <TextView
                                        android:id="@+id/tv_c42_per"
                                        android:layout_width="wrap_content"
                                        android:layout_height="42dp"
                                        android:layout_marginTop="8dp"
                                        android:gravity="start|center_vertical"
                                        android:text="@{(cardShowInfo2.lowProp!= null ? cardShowInfo2.lowProp : `0`) + `%`}"
                                        android:textColor="@color/text_color_fc_100"
                                        android:textSize="@dimen/card_last_content_text_size" />

                                    <TextView
                                        android:id="@+id/tv_c43_per"
                                        android:layout_width="wrap_content"
                                        android:layout_height="42dp"
                                        android:layout_marginTop="8dp"
                                        android:gravity="start|center_vertical"
                                        android:text="@{(cardShowInfo2.normalProp!= null ? cardShowInfo2.normalProp : `0`)+ `%`}"
                                        android:textColor="@color/text_color_fc_100"
                                        android:textSize="@dimen/card_last_content_text_size" />
                                </LinearLayout>

                            </LinearLayout>

                            <com.gwm.widget.GwmButton
                                app:btnStyle="flat_forward"
                                app:iconSize="32dp"
                                android:id="@+id/iv_intro_tips"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="top|end"
                                android:layout_marginTop="24dp"
                                android:layout_marginEnd="24dp"
                                app:icon="@drawable/ic_introduce_circle"
                                android:visibility="visible" />
                        </FrameLayout>

                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!--风险建议提示容器-->
                <include
                    android:id="@+id/health_risk_all"
                    layout="@layout/layout_card_detail_risk_advice"
                    android:visibility="@{baseFragment.Companion.isHealthAdviceVisible(healthSpO2SummeryVo) == true ? View.VISIBLE : View.GONE}"
                    app:baseDTO="@{healthSpO2SummeryVo}" />

                <include layout="@layout/fragment_card_footer" />
            </LinearLayout>

        </com.healthlink.hms.views.CustomBarStretchScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>