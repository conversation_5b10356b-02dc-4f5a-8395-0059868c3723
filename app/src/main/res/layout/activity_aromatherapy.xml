<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:fitsSystemWindows="true"
    android:background="@color/hms_view_bg_color">

    <!-- 顶部标题栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/back_area_container"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="68dp"
            app:layout_constraintTop_toTopOf="parent"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/btnBack"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:contentDescription="@string/back"
                android:src="@drawable/ic_arrow_left" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:text="@string/str_med_aromatherapy_title"
                android:textColor="@color/text_color_333"
                android:textSize="30sp"
                android:textStyle="bold"/>
        </LinearLayout>

        <!-- 右侧按钮（如果需要的话） -->
        <ImageButton
            android:visibility="gone"
            android:id="@+id/btnSettings"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="68dp"
            android:contentDescription="@string/settings"
            android:src="@drawable/ic_system_setting"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 内容区域 - 图片 -->
    <ImageView
        android:id="@+id/iv_aromatherapy"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="fitCenter"
        tools:src="@drawable/img_aromatherapy"
        android:layout_marginStart="68dp"
        android:layout_marginEnd="68dp"
        android:layout_marginBottom="40dp"
        android:contentDescription="中药香薰图片"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_bar" />

</androidx.constraintlayout.widget.ConstraintLayout> 