<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/hms_view_bg_color">

    <!-- 顶部区域 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/topArea"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/btnBack"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="68dp"
            android:contentDescription="@string/back"
            android:src="@drawable/ic_arrow_left"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:text="@string/spine_care"
            android:textColor="@color/text_color_333"
            android:textSize="30sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/btnBack"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 设置按钮 -->
        <ImageButton
            android:id="@+id/btnSettings"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="68dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/settings"
            android:src="@drawable/ic_system_setting"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- TabLayout -->
        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="wrap_content"
            android:layout_height="68dp"
            android:layout_marginEnd="30dp"
            style="@style/TimeTabLayout"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btnSettings"
            app:layout_constraintTop_toTopOf="parent"
            android:background="@android:color/transparent"
            app:tabGravity="fill"
            android:textSize="30sp"
            app:tabIndicatorColor="@color/hms_color_primary"
            app:tabIndicator="@drawable/tab_indicator"
            app:tabMode="fixed"
            app:tabIndicatorFullWidth="false"
            app:tabSelectedTextColor="@color/hms_color_primary"
            app:tabTextColor="@color/text_color_333"
            app:tabTextAppearance="@style/TabLayoutTextUnSelected"
             />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 内容区域 -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topArea" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@color/hms_view_bg_color"
        android:layout_width="0dp"
        android:layout_height="0dp">

        <ImageView
            android:id="@+id/iv_placeholder"
            android:layout_width="600dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/ll_content"
            >

        </ImageView>

        <LinearLayout
            android:id="@+id/ll_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/iv_placeholder"
            app:layout_constraintEnd_toEndOf="parent"
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/tv_ifsit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="24sp"
                android:textStyle="bold"
                />
            <TextView
                android:layout_marginTop="20dp"
                android:id="@+id/tv_ifnormal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="24sp"
                android:textStyle="bold"
                />
            <TextView
                android:layout_marginTop="20dp"
                android:id="@+id/tv_about"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="24sp"
                android:textStyle="bold"
                />
            <TextView
                android:id="@+id/tv_around"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="24sp"
                android:textStyle="bold"
                />
            <TextView
                android:layout_marginTop="20dp"
                android:id="@+id/tv_disability"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="24sp"
                android:textStyle="bold"
                />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>