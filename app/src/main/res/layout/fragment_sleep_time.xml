<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <import type="com.healthlink.hms.utils.TimeUtils" />

        <variable
            name="baseFragment"
            type="com.healthlink.hms.fragment.BaseCardFragment" />

        <variable
            name="cardShowInfo"
            type="com.healthlink.hms.server.data.dto.SleepCardShowDTO" />

        <variable
            name="cardShowInfo1"
            type="com.healthlink.hms.server.data.dto.SleepCardShowDTO" />

        <variable
            name="cardShowInfo2"
            type="com.healthlink.hms.server.data.dto.SleepCardShow2DTO" />

        <variable
            name="healthSleepSummeryVo"
            type="com.healthlink.hms.server.data.dto.charts.HealthSleepSummeryDTO" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout

        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.healthlink.hms.views.CustomBarStretchScrollView
            android:visibility="invisible"
            android:id="@+id/sv_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginEnd="10dp"
            android:scrollbarThumbVertical="@drawable/scrollbar_ver_thumb"
            android:scrollbars="none"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <LinearLayout
                style="@style/fragment_card_container_style">
                <!--    图表容器 -->
                <!--    图表解释容器 -->
                <LinearLayout
                    android:id="@+id/sleep_chart_container"
                    android:layout_width="match_parent"
                    android:layout_height="420dp"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toTopOf="@+id/ll_intro_container"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">


                    <!--sleep-->
                    <com.healthlink.hms.views.charts.SleepDayChart
                        android:id="@+id/c_sleep_day"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:visibility="gone" />

                    <com.healthlink.hms.views.charts.SleepBarChart
                        android:id="@+id/c_sleep_wmy"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/s_privacy_text"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:text="系统已开启私密模式"
                        android:textAlignment="center"
                        android:textColor="@color/text_color_fc_80"
                        android:textSize="26sp"
                        android:visibility="gone" />


                </LinearLayout>
                <!--        睡眠颜色图例-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.healthlink.hms.views.HMSCircleView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            app:circleColor="@color/sleep_deep_color" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="8dp"
                            android:text="深睡"
                            android:textColor="@color/text_color_fc_80"
                            android:textSize="22sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="200dp"

                        android:orientation="horizontal">

                        <com.healthlink.hms.views.HMSCircleView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            app:circleColor="@color/sleep_light_color" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="8dp"
                            android:text="浅睡"
                            android:textColor="@color/text_color_fc_80"
                            android:textSize="22sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="200dp"
                        android:orientation="horizontal">

                        <com.healthlink.hms.views.HMSCircleView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            app:circleColor="@color/sleep_dream_color" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="8dp"
                            android:text="快速眼动"
                            android:textColor="@color/text_color_fc_80"
                            android:textSize="22sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/legend_sleep_of_sleep_little"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="200dp"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <com.healthlink.hms.views.HMSCircleView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            app:circleColor="@color/sleep_oddments_color" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="8dp"
                            android:text="零星小睡"
                            android:textColor="@color/text_color_fc_80"
                            android:textSize="22sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="200dp"
                        android:orientation="horizontal">

                        <com.healthlink.hms.views.HMSCircleView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            app:circleColor="@color/sleep_awake_color" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="8dp"
                            android:text="清醒"
                            android:textColor="@color/text_color_fc_80"
                            android:textSize="22sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/legend_sleep"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="200dp"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <com.healthlink.hms.views.HMSCircleView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center"
                            app:circleColor="@color/sleep_sleep_color" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="8dp"
                            android:text="睡眠"
                            android:textColor="@color/text_color_fc_80"
                            android:textSize="22sp" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_intro_container"
                    android:layout_marginTop="20dp"
                    style="@style/fragment_card_metrics_intro_style">

                    <LinearLayout
                        android:id="@+id/sleep_day_data_center"
                        style="@style/card_3_style"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_sleep_date"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:lineHeight="42dp"
                            android:text="@{cardShowInfo.fetchTime}"
                            android:textColor="@color/text_color_fc_60"
                            android:textSize="@dimen/card_title_text_size" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="24dp"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/sleep_all_time"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text=""
                                android:textColor="@color/text_color_fc_60"
                                android:textSize="22sp" />

                            <TextView
                                android:id="@+id/sleep_hour_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{cardShowInfo.allSleepTimeHour !=null ? cardShowInfo.allSleepTimeHour:`--`}"
                                android:textColor="@color/text_color_fc_80"
                                android:textSize="@dimen/card_content_value_text_size"
                                android:textStyle="bold" />


                            <TextView
                                android:id="@+id/sleep_hour_unit"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="小时"
                                android:textColor="@color/text_color_fc_60"
                                android:textSize="22sp" />

                            <TextView
                                android:id="@+id/sleep_min_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{cardShowInfo.allSleepTimeMin!=null?cardShowInfo.allSleepTimeMin:`--`}"
                                android:textColor="@color/text_color_fc_80"
                                android:textSize="@dimen/card_content_value_text_size"
                                android:textStyle="bold" />


                            <TextView
                                android:id="@+id/sleep_min_unit"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="分钟"
                                android:textColor="@color/text_color_fc_60"
                                android:textSize="22sp" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/sleep_score_layout"
                        style="@style/card_3_style"
                        android:layout_marginStart="40dp"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_sleep_intro_range"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:lineHeight="42dp"
                            android:text="@{cardShowInfo1.sleepTitle}"
                            android:textColor="@color/text_color_fc_60"
                            android:textSize="@dimen/card_title_text_size" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="24dp"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tv_sleep_score_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@{cardShowInfo1.sleep != null?cardShowInfo1.sleep :`--`}"
                                android:textColor="@color/text_color_fc_80"
                                android:textSize="@dimen/card_content_value_text_size"
                                android:textStyle="bold" />


                            <TextView
                                android:id="@+id/tv_sleep_score_unit"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="分"
                                android:textColor="@color/text_color_fc_60"
                                android:textSize="22sp" />
                        </LinearLayout>
                    </LinearLayout>
                    <!-- !!!第三张卡片有两套数据 周月年视图第三张卡片sleep_count 和 日视图第三张卡片sleep_count2-->
                    <LinearLayout
                        android:id="@+id/sleep_count"
                        style="@style/card_3_style"
                        android:layout_marginStart="40dp"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <FrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_horizontal"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:orientation="vertical">

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="34dp"
                                        android:gravity="end|center_vertical"
                                        android:text="深睡"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="20sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="34dp"
                                        android:gravity="end|center_vertical"
                                        android:text="浅睡"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="20sp" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="34dp"
                                        android:gravity="end|center_vertical"
                                        android:text="快速眼动"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="20sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="34dp"
                                        android:gravity="end|center_vertical"
                                        android:text="清醒"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="20sp" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:gravity="end|center_vertical"
                                        android:text="零星小睡"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="20sp" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginStart="40dp"
                                    android:gravity="start"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/cl_deep_sleep_time"
                                        android:layout_width="wrap_content"
                                        android:layout_height="34dp"
                                        android:gravity="start|center_vertical"
                                        android:text="@{TimeUtils.formatMinutes(cardShowInfo2.deepSleepTime)}"
                                        android:textColor="@color/text_color_fc_100"
                                        android:textSize="20sp" />

                                    <TextView
                                        android:id="@+id/cl_light_sleep_time"
                                        android:layout_width="wrap_content"
                                        android:layout_height="34dp"
                                        android:gravity="start|center_vertical"
                                        android:text="@{TimeUtils.formatMinutes(cardShowInfo2.lightSleepTime)}"
                                        android:textColor="@color/text_color_fc_100"
                                        android:textSize="20sp" />

                                    <TextView
                                        android:id="@+id/cl_rem_time"
                                        android:layout_width="wrap_content"
                                        android:layout_height="34dp"
                                        android:gravity="start|center_vertical"
                                        android:text="@{TimeUtils.formatMinutes(cardShowInfo2.dreamTime)}"
                                        android:textColor="@color/text_color_fc_100"
                                        android:textSize="20sp" />

                                    <TextView
                                        android:id="@+id/cl_awake_time"
                                        android:layout_width="match_parent"
                                        android:layout_height="34dp"
                                        android:gravity="start|center_vertical"
                                        android:text="@{TimeUtils.formatMinutes(cardShowInfo2.awakeTime)}"
                                        android:textColor="@color/text_color_fc_100"
                                        android:textSize="20sp" />

                                    <TextView
                                        android:id="@+id/cl_sporadic_time"
                                        android:layout_width="wrap_content"
                                        android:layout_height="34dp"
                                        android:gravity="start|center_vertical"
                                        android:text="@{TimeUtils.formatMinutes(cardShowInfo2.sporadicSleepTime)}"
                                        android:textColor="@color/text_color_fc_100"
                                        android:textSize="20sp" />
                                </LinearLayout>
                            </LinearLayout>

                            <com.gwm.widget.GwmButton
                                app:btnStyle="flat_forward"
                                app:iconSize="32dp"
                                android:id="@+id/iv_intro_tips"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="top|end"
                                android:layout_marginTop="24dp"
                                android:layout_marginEnd="24dp"
                                app:icon="@drawable/ic_introduce_circle"
                                android:visibility="visible" />
                        </FrameLayout>

                    </LinearLayout>
                    <!-- !!!第三张卡片有两套数据 日视图第三张卡片sleep_count2 和 周月年视图第三张卡片sleep_count -->
                    <LinearLayout
                        android:id="@+id/sleep_count2"
                        style="@style/card_3_style"
                        android:layout_marginStart="40dp"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <FrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_horizontal"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:orientation="vertical">

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="42dp"
                                        android:gravity="end|center_vertical"
                                        android:text="深睡"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="22sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="42dp"
                                        android:layout_marginTop="3dp"
                                        android:gravity="end|center_vertical"
                                        android:text="浅睡"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="22sp" />

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="42dp"
                                        android:layout_marginTop="3dp"
                                        android:gravity="end|center_vertical"
                                        android:text="快速眼动"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="22sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="42dp"
                                        android:layout_marginTop="3dp"
                                        android:gravity="end|center_vertical"
                                        android:text="清醒"
                                        android:textColor="@color/text_color_fc_60"
                                        android:textSize="22sp" />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginStart="40dp"
                                    android:gravity="start"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/tv_sleep_day_c4_deep"
                                        android:layout_width="wrap_content"
                                        android:layout_height="42dp"
                                        android:gravity="start|center_vertical"
                                        android:text="@{TimeUtils.formatMinutes(cardShowInfo2.deepSleepTime)}"
                                        android:textColor="@color/text_color_fc_100"
                                        android:textSize="22sp" />

                                    <TextView
                                        android:id="@+id/tv_sleep_day_c4_light"
                                        android:layout_width="wrap_content"
                                        android:layout_height="42dp"
                                        android:layout_marginTop="3dp"
                                        android:gravity="start|center_vertical"
                                        android:text="@{TimeUtils.formatMinutes(cardShowInfo2.lightSleepTime)}"
                                        android:textColor="@color/text_color_fc_100"
                                        android:textSize="22sp" />

                                    <TextView
                                        android:id="@+id/tv_sleep_day_c4_dream"
                                        android:layout_width="wrap_content"
                                        android:layout_height="42dp"
                                        android:layout_marginTop="3dp"
                                        android:gravity="start|center_vertical"
                                        android:text="@{TimeUtils.formatMinutes(cardShowInfo2.dreamTime)}"
                                        android:textColor="@color/text_color_fc_100"
                                        android:textSize="22sp" />

                                    <TextView
                                        android:id="@+id/tv_sleep_day_c4_awake"
                                        android:layout_width="wrap_content"
                                        android:layout_height="42dp"
                                        android:layout_marginTop="3dp"
                                        android:gravity="start|center_vertical"
                                        android:text="@{TimeUtils.formatMinutes(cardShowInfo2.awakeTime)}"
                                        android:textColor="@color/text_color_fc_100"
                                        android:textSize="22sp" />

                                </LinearLayout>

                            </LinearLayout>


                            <com.gwm.widget.GwmButton
                                app:btnStyle="flat_forward"
                                app:iconSize="32dp"
                                android:id="@+id/iv_intro_tips2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="top|end"
                                android:layout_marginTop="24dp"
                                android:layout_marginEnd="24dp"
                                app:icon="@drawable/ic_introduce_circle"
                                android:visibility="visible" />
                        </FrameLayout>

                    </LinearLayout>

                </LinearLayout>

                <!--风险建议提示容器-->
                <include
                    android:id="@+id/health_risk_all"
                    layout="@layout/layout_card_detail_risk_advice"
                    android:visibility="@{baseFragment.Companion.isHealthAdviceVisible(healthSleepSummeryVo) == true ? View.VISIBLE : View.GONE}"
                    app:baseDTO="@{healthSleepSummeryVo}" />
                <include layout="@layout/fragment_card_footer" />
            </LinearLayout>

        </com.healthlink.hms.views.CustomBarStretchScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>