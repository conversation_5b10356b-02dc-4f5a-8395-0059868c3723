import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone

plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("org.jetbrains.kotlin.plugin.compose")
    id("org.jetbrains.kotlin.kapt")
    id("com.google.dagger.hilt.android")
    id("org.jetbrains.kotlin.plugin.parcelize")
    id("org.jetbrains.kotlin.plugin.serialization")
//    id("walle")
}

//walle {
//    // 指定渠道包输出路径
//    apkOutputFolder = file("${project.buildDir}/outputs/channels")
//    // 定制渠道包的APK的文件名称
//    // 渠道配置文件
//    channelFile = file("${project.getProjectDir()}/channel")
//}

android {
    aaptOptions.cruncherEnabled = false // 是否开启PNG图片优化检查
    namespace = "com.healthlink.hms"
    compileSdk = 35
    ndkVersion = "26.1.10909125"
    val timestamp = SimpleDateFormat("yyMMdd", Locale.US).run {
//        setTimeZone(TimeZone.getTimeZone("UTC"))
        format(Date())
    }

    defaultConfig {
        applicationId = "com.healthlink.hms"
        minSdk = 28
        targetSdk = 33
        versionCode = 21
        versionName = "1.1.17"
        multiDexEnabled = true
        manifestPlaceholders["VERSIONNAME"] = "${versionName}"
        ndk {
//            abiFilters("armeabi-v7a", "arm64-v8a", "x86") // 指定支持的ABI
        }

    }
    // 签名配置
    signingConfigs {
        create("V4") {
            storeFile = file("../../platform.keystore")
            storePassword = "android"
            keyAlias = "platform"
            keyPassword = "android"
            enableV1Signing = true
            enableV2Signing = true
        }

        create("V35") {
            storeFile = file("../../platform.keystore")
            storePassword = "android"
            keyAlias = "platform"
            keyPassword = "android"
            enableV1Signing = true
            enableV2Signing = true
        }
    }
    //环境配置
    buildTypes {

        debug {
            isDebuggable = true
            isMinifyEnabled = false
            resValue("string","app_name","数字健康")
            buildConfigField("String", "BASE_DOMAIN_NAME", "\"gwm-cockpit-test.healthlinkiot.com\"")
            buildConfigField("String", "BASE_URL", "\"https://gwm-cockpit-test.healthlinkiot.com\"")
            buildConfigField("String", "BASE_H5_URL", "\"https://gwm-cockpit-test.healthlinkiot.com:48087/\"")
            buildConfigField("String", "BUILD_TIME", "\"${timestamp}\"")
            buildConfigField("String", "HUAWEI_APP_ID", "\"110740871\"")
            buildConfigField("String", "SIP_URL", "\"https://iectest.healthlinkiot.com:5049/ymdata/init/v2\"")
            buildConfigField("String", "SIP_CHANNEL_ID", "\"HEALTH-EC-BJS2221001C026-TEST\"")
            signingConfig = signingConfigs.getByName("V4")

//            buildConfigField("String", "HUAWEI_APP_ID", "\"111798819\"")
//            buildConfigField("String", "BASE_DOMAIN_NAME", "\"gwm-cockpit.healthlinkiot.com\"")
//            buildConfigField("String", "BASE_URL", "\"https://gwm-cockpit.healthlinkiot.com\"")

        }

        // 预发环境
        create("preRelease") {
            isDebuggable = true
            isMinifyEnabled = false
            resValue("string","app_name","数字健康")
            buildConfigField("String", "BASE_DOMAIN_NAME", "\"gwm-cockpit-test.healthlinkiot.com\"")
            buildConfigField("String", "BASE_URL", "\"https://gwm-cockpit-test.healthlinkiot.com\"")
            buildConfigField("String", "BASE_H5_URL", "\"https://gwm-cockpit-test.healthlinkiot.com:48087/\"")
            buildConfigField("String", "BUILD_TIME", "\"${timestamp}\"")
            buildConfigField("String", "HUAWEI_APP_ID", "\"111798819\"")
            buildConfigField("String", "SIP_URL", "\"https://iectest.healthlinkiot.com:5049/ymdata/init/v2\"")
            buildConfigField("String", "SIP_CHANNEL_ID", "\"GWM-EC-BJS2221001C026-TEST\"")
//            buildConfigField("String", "SIP_URL", "\"https://iec.healthlinkiot.com:5049/ymdata/init/v2\"")
            signingConfig = signingConfigs.getByName("V4")
        }

        release {
            isMinifyEnabled = true
            isShrinkResources = true
            isDebuggable = true
            resValue("string","app_name","数字健康")
            buildConfigField("String", "BASE_DOMAIN_NAME", "\"gwm-cockpit.healthlinkiot.com\"")
            buildConfigField("String", "BASE_URL", "\"https://gwm-cockpit.healthlinkiot.com\"")
            buildConfigField("String", "BASE_H5_URL", "\"https://hms-test.healthlinkiot.com\"")
            buildConfigField("String", "BUILD_TIME", "\"${timestamp}\"")
//            buildConfigField("String", "HUAWEI_APP_ID", "\"111693185\"")
            buildConfigField("String", "HUAWEI_APP_ID", "\"111798819\"")
//            buildConfigField("String", "SIP_URL", "\"https://iectest.healthlinkiot.com:5049/ymdata/init/v2\"")
            buildConfigField("String", "SIP_URL", "\"https://iec.healthlinkiot.com:5049/ymdata/init/v2\"")
            buildConfigField("String", "SIP_CHANNEL_ID", "\"GWM-EC-BJS2221001C026-PRO\"")
            buildConfigField("boolean", "DEBUG", "true")
            signingConfig = signingConfigs.getByName("V4")
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )

        }
    }
    //渠道配置
    flavorDimensions += "version"
    productFlavors {

        create("V4") {
            buildConfigField("String", "PLATFORM_CODE", "\"V4\"")
//            signingConfig = signingConfigs.getByName("V4")
        }

        create("V35") {
            buildConfigField("String", "PLATFORM_CODE", "\"V35\"")
//            signingConfig = signingConfigs.getByName("V4")
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
        freeCompilerArgs = listOf("-Xjvm-default=all")
    }
    kapt {
        correctErrorTypes = true
    }
    buildFeatures {
        viewBinding = true
        buildConfig = true
        compose = true
        dataBinding = true
    }

    applicationVariants.all {
        val variant = this
        val dateFormat = SimpleDateFormat("yyyyMMddHHmm")
        val currentDateTime = dateFormat.format(Date())
        variant.outputs
            .map { it as com.android.build.gradle.internal.api.BaseVariantOutputImpl }
            .forEach {
                it.outputFileName = "hms_v${variant.versionName}-${variant.flavorName}-${variant.buildType.name}-${currentDateTime}.apk"
            }
    }

    lint {
        baseline = file("lint-baseline.xml")
        abortOnError = false
    }
}

dependencies {
    implementation(fileTree("libs"))
    
    // 排除SLF4J冲突的依赖，因为reduce-release-sit-20241224v1.aar已经包含了slf4j
    configurations.all {
        exclude(group = "org.slf4j", module = "slf4j-api")
    }

    // Hilt
    implementation("com.google.dagger:hilt-android:2.53.1")
    kapt("com.google.dagger:hilt-android-compiler:2.53.1")

    // compose 依赖配置
//    val composeBom = platform("androidx.compose:compose-bom:2024.05.00")
    implementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(platform(libs.androidx.compose.bom))
//    implementation(composeBom)
//    androidTestImplementation(composeBom)

    implementation("androidx.work:work-runtime-ktx:2.9.0")
    // Choose one of the following:
    // Material Design 3
    implementation("androidx.compose.material3:material3")
    // or Material Design 2
    implementation("androidx.compose.material:material")
    // or skip Material Design and build directly on top of foundational components
    implementation("androidx.compose.foundation:foundation")
    // or only import the main APIs for the underlying toolkit systems,
    // such as input and measurement/layout
    implementation("androidx.compose.ui:ui")

    // compose 动画
    implementation("androidx.compose.animation:animation:1.8.2")
    implementation("androidx.compose.animation:animation-core:1.8.2")

    // Android Studio Preview support
    implementation("androidx.compose.ui:ui-tooling-preview")
    debugImplementation("androidx.compose.ui:ui-tooling")

    // UI Tests
    androidTestImplementation("androidx.compose.ui:ui-test-junit4")
    debugImplementation("androidx.compose.ui:ui-test-manifest")

    // Optional - Included automatically by material, only add when you need
    // the icons but not the material library (e.g. when using Material3 or a
    // custom design system based on Foundation)
    implementation("androidx.compose.material:material-icons-core")
    // Optional - Add full set of material icons
    implementation("androidx.compose.material:material-icons-extended")
    // Optional - Add window size utils
    implementation("androidx.compose.material3:material3-window-size-class")

    // Optional - Integration with activities
    implementation(libs.androidx.activity.compose)
    // Optional - Integration with ViewModels
    implementation(libs.androidx.lifecycle.viewmodel.compose)

    ////////////////////android//////////////////////
    implementation (libs.androidx.core.splashscreen)

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.leanback)
    implementation(libs.material)
    implementation(libs.androidx.appcompat)
    implementation(libs.androidx.fragment)
    implementation(libs.androidx.constraintlayout)
    implementation(libs.androidx.navigation.fragment.ktx)
    implementation(libs.androidx.navigation.ui.ktx)
    implementation(libs.androidx.viewpager2)
    // lifecycle
    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.7.0")
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0")
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")
    implementation("androidx.lifecycle:lifecycle-common-java8:2.7.0")

    // 加入协程
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.10.2")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2")

    // retrofit
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
    implementation("com.jakewharton.retrofit:retrofit2-kotlin-coroutines-adapter:0.9.2")
    //日志拦截器
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")
    implementation("com.squareup.retrofit2:adapter-rxjava2:2.9.0")
    // rxjava
    implementation("io.reactivex.rxjava2:rxandroid:2.1.1")
    implementation("io.reactivex.rxjava2:rxjava:2.2.12")
    // Glide 图片加载框架
    implementation("com.github.bumptech.glide:glide:4.16.0")
    kapt("com.github.bumptech.glide:compiler:4.16.0")

    // 安全存储
    implementation("androidx.security:security-crypto:1.1.0-alpha06")

    implementation(libs.gson)
    //网络组件
    implementation(libs.okhttp)
    //图表组件库
    implementation(libs.mpandroidchart)
    //agentWeb  webView
    implementation(libs.agentweb.core)

    // 下拉刷新
    implementation(libs.refresh.layout.kernel)        //核心必须依赖
    implementation(libs.refresh.header.classics)    //经典刷新头
    implementation("io.github.everythingme:overscroll-decor-android:1.1.1")

    //TTS SDK
    implementation(files("libs/tts.commonlib-1.2.2.aar"))
    implementation(files("libs/tts.client-1.2.2.aar"))
    //MAP SDK
    implementation(files("libs/GWMMapSDK-2.0.4.aar"))
    //GwmAdapterClient SDK
    implementation(files("libs/both-1.1.3.aar"))
    implementation(files("libs/libVrWidget-2.0.8.jar"))
    //NumberPicker修改版
    implementation(files("libs/NumberPicker-2.4.13.aar"))
    implementation(files("libs/libWidgetCux-1.1.5-SOP-20240816.031318-1.aar"))
    implementation("com.airbnb.android:lottie:6.0.0")
    //Gif 解码库
    implementation(libs.android.gif.drawable)
    // apng
    implementation(libs.apng)
    // walle
//    implementation(libs.library)
    // 界面适配
    implementation("com.github.JessYanCoding:AndroidAutoSize:v1.2.1")

    // 列表多item
    implementation("com.drakeet.multitype:multitype:4.3.0")
    // 通用工具类
    implementation("com.blankj:utilcodex:1.31.1")
    // 腾讯MMKV本地存储
    implementation("com.tencent:mmkv-static:1.3.7")
    //引入埋点sdk
    implementation(files("libs/GWMDataTrackSDK-2.5.10.aar"))

    // 判断小憩模式是否可用
    compileOnly(files("libs/gwmhmiframework-0.4.0.jar"))
    // SIP电话
    compileOnly(files("libs/reduce-release-sit-20241224v1.aar"))

    // 滚动条
    implementation ("com.github.I3eyonder:android-standalone-scroll-bar:1.1.3")

    implementation("io.github.petterpx:floatingx:2.3.4")
//    implementation ("com.vmadalin:easypermissions-ktx:1.0.0")

    implementation("com.github.amikoj:ShadowView:1.0.1")

    implementation("com.kyleduo.switchbutton:library:2.1.0")
    // unpeek livedata数据回流
    implementation("com.kunminx.arch:unpeek-livedata:7.8.0")

    //banner组件
    implementation (libs.banner)

    // 播放器
    implementation("androidx.media3:media3-exoplayer:1.2.1")
    implementation("androidx.media3:media3-exoplayer-dash:1.2.1")
    implementation("androidx.media3:media3-ui:1.2.1")



    // third party
    // ktor
    implementation(libs.ktor.client.core)
    implementation(libs.ktor.client.cio)
    implementation(libs.ktor.client.android)
    implementation(libs.ktor.client.websockets)
    implementation(libs.ktor.client.okhttp)
    implementation(libs.ktor.client.logging)
    implementation(libs.ktor.client.serialization)

    // 毛玻璃效果
    implementation("com.github.centerzx:ShapeBlurView:1.0.5")

    // debug 添加内存泄露检查工具
//    debugImplementation ("com.squareup.leakcanary:leakcanary-android:2.14")

    // multiDex
    implementation("androidx.multidex:multidex:2.0.1")
}
