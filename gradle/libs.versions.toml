[versions]
activityCompose = "1.9.0"
agentwebCore = "v5.1.1-androidx"
androidGifDrawable = "1.2.26"
apng = "3.0.1"
appcompat = "1.6.1"
banner = "2.2.3"
constraintlayout = "2.1.4"
coreKtx = "1.9.0"
composeBom = "2025.05.00"
coreSplashscreen = "1.0.1"
fragment = "1.4.0"
gson = "2.8.9"
ktor = "3.1.3"
ktorClientWebsockets = "3.1.3"
leanback = "1.0.0"
lifecycleViewmodelCompose = "2.6.1"
material = "1.11.0"
multidex = "2.0.1"
navigationFragmentKtx = "2.6.0"
okhttp = "4.9.0"
refreshLayoutKernel = "2.1.0"
timber = "4.5.1"
wallePlugin = "1.1.7"
versionName = "1.0.0"
versionCode = "1"
viewpager2 = "1.1.0"
mpandroidchart = "3.1.0"
kotlin = "1.9.0"
[libraries]
agentweb-core = { module = "io.github.justson:agentweb-core", version.ref = "agentwebCore" }
android-gif-drawable = { module = "pl.droidsonroids.gif:android-gif-drawable", version.ref = "androidGifDrawable" }
androidx-activity-compose = { module = "androidx.activity:activity-compose", version.ref = "activityCompose" }
androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "appcompat" }
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "constraintlayout" }
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "coreKtx" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-core-splashscreen = { module = "androidx.core:core-splashscreen", version.ref = "coreSplashscreen" }
androidx-fragment = { module = "androidx.fragment:fragment", version.ref = "fragment" }
androidx-leanback = { module = "androidx.leanback:leanback", version.ref = "leanback" }
androidx-lifecycle-viewmodel-compose = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "lifecycleViewmodelCompose" }
androidx-multidex = { module = "androidx.multidex:multidex", version.ref = "multidex" }
androidx-navigation-fragment-ktx = { module = "androidx.navigation:navigation-fragment-ktx", version.ref = "navigationFragmentKtx" }
androidx-navigation-ui-ktx = { module = "androidx.navigation:navigation-ui-ktx", version.ref = "navigationFragmentKtx" }
androidx-viewpager2 = { module = "androidx.viewpager2:viewpager2", version.ref = "viewpager2" }
apng = { module = "com.github.penfeizhou.android.animation:apng", version.ref = "apng" }
banner = { module = "io.github.youth5201314:banner", version.ref = "banner" }
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
ktor-client-android = { module = "io.ktor:ktor-client-android", version.ref = "ktor" }
ktor-client-cio = { module = "io.ktor:ktor-client-cio", version.ref = "ktor" }
ktor-client-core = { module = "io.ktor:ktor-client-core", version.ref = "ktor" }
ktor-client-logging = { module = "io.ktor:ktor-client-logging", version.ref = "ktor" }
ktor-client-okhttp = { module = "io.ktor:ktor-client-okhttp", version.ref = "ktor" }
ktor-client-serialization = { module = "io.ktor:ktor-client-serialization", version.ref = "ktor" }
ktor-client-websockets = { module = "io.ktor:ktor-client-websockets", version.ref = "ktor" }
library = { module = "com.meituan.android.walle:library", version.ref = "wallePlugin" }
material = { module = "com.google.android.material:material", version.ref = "material" }
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
mpandroidchart = { module = "com.github.PhilJay:MPAndroidChart", version.ref = "mpandroidchart" }
refresh-header-classics = { module = "io.github.scwang90:refresh-header-classics", version.ref = "refreshLayoutKernel" }
refresh-layout-kernel = { module = "io.github.scwang90:refresh-layout-kernel", version.ref = "refreshLayoutKernel" }
timber = { module = "com.jakewharton.timber:timber", version.ref = "timber" }
walle-plugin = { module = "com.meituan.android.walle:plugin", version.ref = "wallePlugin" }
[plugins]
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }



